{"dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@aws-sdk/client-s3": "3.437.0", "@base-ui-components/react": "1.0.0-beta.0", "@deck.gl/core": "^9.1.12", "@deck.gl/geo-layers": "^9.1.12", "@deck.gl/layers": "^9.1.12", "@deck.gl/mapbox": "^9.1.12", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@edgestore/react": "^0.5.2", "@edgestore/server": "^0.5.2", "@hookform/resolvers": "^4.1.2", "@keyv/etcd": "^2.1.1", "@keyv/mongo": "^3.0.3", "@keyv/mysql": "^2.1.3", "@keyv/offline": "^4.0.2", "@keyv/postgres": "^2.1.6", "@keyv/redis": "^4.4.1", "@keyv/sqlite": "^4.0.5", "@keyv/tiered": "^2.0.2", "@loaders.gl/core": "^4.3.4", "@loaders.gl/csv": "^4.3.4", "@mapbox/mapbox-gl-draw": "^1.5.0", "@mapbox/mapbox-gl-geocoder": "^5.0.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@sindresorhus/slugify": "^2.2.1", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.66.9", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.12", "@tiptap/core": "^2.14.0", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-code-block-lowlight": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-mention": "^2.23.1", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-subscript": "^2.14.0", "@tiptap/extension-superscript": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-typography": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@tiptap/suggestion": "^2.14.0", "@turf/turf": "^7.1.0", "@vercel/analytics": "^1.5.0", "ai": "4.3.16", "better-auth": "1.2.9", "boring-avatars": "^1.11.2", "canvas-confetti": "^1.9.3", "chrono-node": "^2.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cropperjs": "1.6.2", "d3-scale": "^4.0.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.19.1", "frimousse": "^0.2.0", "fumadocs-core": "^15.0.13", "fumadocs-ui": "^15.0.13", "fuse.js": "^7.1.0", "geist": "^1.3.1", "h3-js": "^4.2.1", "hono": "^4.7.2", "input-otp": "^1.4.2", "jotai": "2.12.1", "js-cookie": "^3.0.5", "loops": "^5.0.1", "lowlight": "^3.3.0", "lucide-react": "^0.476.0", "mapbox-gl": "^3.8.0", "marked": "^15.0.12", "motion": "^12.18.1", "next": "15.3.3", "next-intl": "3.26.5", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nprogress": "^0.2.0", "nuqs": "^2.4.0", "oslo": "^1.2.1", "prettier": "3.4.2", "react": "19.0.0", "react-cropper": "^2.3.3", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.2", "react-textarea-autosize": "^8.5.9", "recharts": "^2.15.3", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "sonner": "^2.0.1", "tailwind-merge": "^3.3.1", "tippy.js": "^6.3.7", "ufo": "^1.5.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.64", "zod-openapi": "^4.2.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.51.0", "@repo/auth": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@tailwindcss/postcss": "^4.0.12", "@types/canvas-confetti": "^1.9.0", "@types/js-cookie": "^3.0.4", "@types/mapbox-gl": "^3.1.0", "@types/mapbox__mapbox-gl-draw": "^1.4.6", "@types/node": "22.13.10", "@types/nprogress": "^0.2.3", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.21", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "postcss": "8.5.3", "start-server-and-test": "^2.0.10", "tailwindcss": "4.0.12", "tw-animate-css": "^1.3.4"}, "name": "@repo/app", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "e2e": "pnpm exec playwright test --ui", "e2e:ci": "pnpm exec playwright install && pnpm exec playwright test", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit"}, "version": "0.0.0"}