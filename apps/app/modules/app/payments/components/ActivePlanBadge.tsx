"use client";

import { usePlanData } from "@app/payments/hooks/plan-data";
import { usePurchases } from "@app/payments/hooks/purchases";
import { Badge } from "@ui/components/badge";

export function ActivePlanBadge({
	organizationId,
}: {
	organizationId?: string;
}) {
	const { planData } = usePlanData();
	const { activePlan } = usePurchases(organizationId);

	if (!activePlan) {
		return null;
	}

	const activePlanData = planData[activePlan.id as keyof typeof planData];

	if (!activePlanData) {
		return null;
	}
	return (
		<Badge className="flex items-center gap-1 px-1.5 py-0.5 text-[10px] w-fit">
			{activePlanData.title}
		</Badge>
	);
}
