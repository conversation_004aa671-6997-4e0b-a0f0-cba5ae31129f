import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";

export interface UnitMix {
	id: string;
	propertyId: string;
	organizationId: string;
	name: string;
	units?: number | null;
	minSquareFootage?: number | null;
	maxSquareFootage?: number | null;
	minPrice?: number | null;
	maxPrice?: number | null;
	minRent?: number | null;
	maxRent?: number | null;
	createdAt: Date;
	updatedAt: Date;
	property: {
		id: string;
		name: string;
		propertyType?: string | null;
		market?: string | null;
		subMarket?: string | null;
		status?: string | null;
	};
}

export interface CreateUnitMixData {
	organizationId: string;
	propertyId: string;
	name: string;
	units?: number;
	minSquareFootage?: number;
	maxSquareFootage?: number;
	minPrice?: number;
	maxPrice?: number;
	minRent?: number;
	maxRent?: number;
}

export interface UpdateUnitMixData {
	name?: string;
	units?: number;
	minSquareFootage?: number;
	maxSquareFootage?: number;
	minPrice?: number;
	maxPrice?: number;
	minRent?: number;
	maxRent?: number;
}

export async function fetchUnitMixes(organizationId: string): Promise<UnitMix[]> {
	const res = await fetch(`/api/unit-mixes?organizationId=${organizationId}`);
	if (!res.ok) {
		throw new Error("Failed to fetch unit mixes");
	}
	return res.json();
}

export async function createUnitMix(data: CreateUnitMixData): Promise<UnitMix> {
	const res = await fetch("/api/unit-mixes", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create unit mix");
	}
	return res.json();
}

export async function updateUnitMix(id: string, data: UpdateUnitMixData): Promise<UnitMix> {
	const res = await fetch(`/api/unit-mixes/${id}`, {
		method: "PUT",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update unit mix");
	}
	return res.json();
}

export async function deleteUnitMix(id: string): Promise<{ message: string }> {
	const res = await fetch(`/api/unit-mixes/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete unit mix");
	}
	return res.json();
}

export function useUnitMixes(organizationId: string | undefined) {
	return useQuery<UnitMix[]>({
		queryKey: ["unit-mixes", organizationId],
		queryFn: () =>
			organizationId
				? fetchUnitMixes(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		refetchOnWindowFocus: false,
		refetchOnMount: false,
	});
}

export function useCreateUnitMix() {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: createUnitMix,
		onSuccess: (data) => {
			// Invalidate and refetch unit mixes
			queryClient.invalidateQueries({
				queryKey: ["unit-mixes", data.organizationId],
			});
		},
	});
}

export function useUpdateUnitMix() {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: ({ id, data }: { id: string; data: UpdateUnitMixData }) =>
			updateUnitMix(id, data),
		onSuccess: (data) => {
			// Invalidate and refetch unit mixes
			queryClient.invalidateQueries({
				queryKey: ["unit-mixes", data.organizationId],
			});
		},
	});
}

export function useDeleteUnitMix() {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: ({ id, organizationId }: { id: string; organizationId: string }) =>
			deleteUnitMix(id),
		onSuccess: (_, variables) => {
			// Invalidate and refetch unit mixes
			queryClient.invalidateQueries({
				queryKey: ["unit-mixes", variables.organizationId],
			});
		},
	});
}
