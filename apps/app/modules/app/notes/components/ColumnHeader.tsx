import { ColumnHeader } from "@app/shared/components/ColumnHeader";
import {
	IconAt,
	IconCalendarTime,
	IconGlobe,
	IconId,
	IconLetterT,
	IconLetterTSmall,
	IconLink,
	IconUser,
	IconUserCircle,
	IconUsersGroup,
} from "@tabler/icons-react";
import type { Column } from "@tanstack/react-table";
import { cn } from "@ui/lib";

const iconMapping = {
	Title: IconLetterTSmall,
	"Created at": IconCalendarTime,
	"Created by": IconUserCircle,
	Record: IconId,
	Status: IconGlobe,
};

interface DataTableColumnHeaderProps<TData, TValue>
	extends React.HTMLAttributes<HTMLDivElement> {
	column: Column<TData, TValue>;
	title: string;
}

export function DataTableColumnHeader<TData, TValue>({
	column,
	title,
	className,
}: DataTableColumnHeaderProps<TData, TValue>) {
	if (!column.getCanSort()) {
		return <div className={cn(className)}>{title}</div>;
	}

	const Icon = iconMapping[title as keyof typeof iconMapping];

	return (
		<div className={cn("flex items-center space-x-2", className)}>
			<ColumnHeader title={title} icon={Icon} />
		</div>
	);
}
