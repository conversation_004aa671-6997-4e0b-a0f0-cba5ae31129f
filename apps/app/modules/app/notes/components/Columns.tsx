import { ToggleStar } from "@app/favorites/components/ToggleStar";
import { fetchFavorites, useToggleFavorite } from "@app/favorites/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconWorld } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@ui/components/checkbox";
import { ContactBadge, CompanyBadge, PropertyBadge } from "@ui/components/badge";
import { formatDistanceToNow } from "date-fns";
import React from "react";
import { DataTableColumnHeader } from "./ColumnHeader";

const TitleCell = React.memo(({ note }: { note: any }) => {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;
	const noteId = note?.id;

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	const isFavorited = favorites.some((f: any) => f.objectId === noteId);

	return (
		<div className="flex flex-row items-center space-x-2 px-2 group">
			{noteId && (
				<ToggleStar
					id={noteId}
					isFavorite={isFavorited}
					objectType="note"
				/>
			)}
			<span className="max-w-[500px] truncate font-medium cursor-pointer">
				{note?.title}
			</span>
		</div>
	);
});
TitleCell.displayName = "TitleCell";

const CreatedByCell = React.memo(({ note }: { note: any }) => {
	const user = note.user || { name: note.userId || "Unknown", image: null };
	return (
		<div className="flex items-center gap-1">
			<UserAvatar
				name={user.name}
				avatarUrl={user.image}
				className="size-5"
			/>
			<span className="text-sm">{user.name}</span>
		</div>
	);
});
CreatedByCell.displayName = "CreatedByCell";

const RecordCell = React.memo(({ note }: { note: any }) => {
	if (!note.objectId || !note.objectType || !note.objectRecord) return null;
	
	// Use the same logic as NoteCard for displaying record badges
	if (note.objectRecord.type === "contact") {
		return (
			<ContactBadge 
				className="!text-sm" 
				size="sm" 
				value={note.objectRecord.name} 
				avatar={note.objectRecord.image || undefined} 
			/>
		);
	} else if (note.objectRecord.type === "company") {
		return (
			<CompanyBadge 
				className="!text-sm"
				size="sm"
				value={note.objectRecord.name} 
				logo={note.objectRecord.logo || undefined} 
			/>
		);
	} else if (note.objectRecord.type === "property") {
		return (
			<PropertyBadge 
				className="!text-sm"
				size="sm"
				value={note.objectRecord.name} 
				avatar={note.objectRecord.image || undefined} 
			/>
		);
	}
	
	// Fallback for unknown types
	return (
		<span className="text-xs text-muted-foreground">
			{note.objectType}: {note.objectId}
		</span>
	);
});
RecordCell.displayName = "RecordCell";

const CreatedAtCell = React.memo(({ note }: { note: any }) => {
	const createdAt = note.createdAt ? new Date(note.createdAt) : null;
	if (!createdAt) return null;
	return (
		<span className="text-xs text-muted-foreground">
			{formatDistanceToNow(createdAt, { addSuffix: true })}
		</span>
	);
});
CreatedAtCell.displayName = "CreatedAtCell";

const StatusCell = React.memo(({ note }: { note: any }) => {
	if (note.isArchived) return <span className="text-red-500">Archived</span>;
	if (note.isPublished)
		return (
			<div className="flex items-center gap-1">
				<IconWorld className="h-4 w-4 text-blue-500" />
				<span>Published</span>
			</div>
		);
	return <span className="text-muted-foreground">Private</span>;
});
StatusCell.displayName = "StatusCell";

export const columns: ColumnDef<any, any>[] = [
	{
		accessorKey: "title",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Title" />
		),
		cell: ({ row }) => <TitleCell note={row.original} />,
		enableSorting: true,
		sortingFn: "alphanumeric",
	},
	{
		accessorKey: "userId",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Created by" />
		),
		cell: ({ row }) => <CreatedByCell note={row.original} />,
	},
	{
		accessorKey: "objectId",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Record" />
		),
		cell: ({ row }) => <RecordCell note={row.original} />,
	},
	{
		accessorKey: "createdAt",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Created at" />
		),
		cell: ({ row }) => <CreatedAtCell note={row.original} />,
		enableSorting: true,
		sortingFn: (rowA, rowB) => {
			const a = rowA.original?.createdAt
				? new Date(rowA.original.createdAt).getTime()
				: 0;
			const b = rowB.original?.createdAt
				? new Date(rowB.original.createdAt).getTime()
				: 0;
			return a - b;
		},
	},
	{
		accessorKey: "isPublished",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Status" />
		),
		cell: ({ row }) => <StatusCell note={row.original} />,
		enableSorting: false,
	},
];
