import { SortDropdown } from "@app/notes/components/SortDropdown";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	clearTaskPreferences,
	getTaskPreferences,
} from "@app/tasks/lib/preferences";
import {
	IconCalendar,
	IconCalendarTime,
	IconId,
	IconLetterTSmall,
	IconUser,
} from "@tabler/icons-react";
import type { Table } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import type { Filter, FilterType } from "@ui/components/filters";
import Filters, {
	AnimateChangeInHeight,
	DueDate,
	FilterOperator,
	filterViewOptions,
	filterViewToFilterOptions,
} from "@ui/components/filters";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { ListFilter } from "lucide-react";
import React from "react";
import { v4 as uuidv4 } from "uuid";

// import { UserAvatar } from "@app/shared/components/UserAvatar"; // Uncomment and use in the future when Filters supports richer objects

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
	groupBy: string;
	onGroupByChange: (value: string) => void;
	filters: Filter[];
	setFilters: React.Dispatch<React.SetStateAction<Filter[]>>;
	sortField: string;
	sortDirection: "asc" | "desc";
	onSortFieldChange: (field: string) => void;
	onSortDirectionChange: (dir: "asc" | "desc") => void;
}

// Helper to get initials from a name
function getInitials(name: string) {
	return name
		.split(" ")
		.map((n) => n[0])
		.join("")
		.toUpperCase();
}

export function TableToolbar<TData>({
	table,
	groupBy,
	onGroupByChange,
	filters,
	setFilters,
	sortField,
	sortDirection,
	onSortFieldChange,
	onSortDirectionChange,
}: DataTableToolbarProps<TData>) {
	const { activeOrganization } = useActiveOrganization();
	const members = activeOrganization?.members || [];

	const memberOptions = [
		{
			id: "no-assignee",
			name: "No assignee",
			image: undefined,
			initials: "NA",
		},
		...members.map((member: any) => ({
			id: member.user.id,
			name: member.user.name,
			image: member.user.image,
			initials: getInitials(member.user.name),
		})),
	];

	React.useEffect(() => {
		table.setColumnFilters(
			filters.map((filter) => ({
				id: filter.type.toLowerCase(),
				value: filter.value,
			})),
		);
	}, [filters, table]);

	return (
		<div className="flex items-center justify-between">
			<div className="flex flex-1 items-center">
				<Input
					placeholder="Filter notes..."
					value={
						(table
							.getColumn("title")
							?.getFilterValue() as string) ?? ""
					}
					onChange={(event) =>
						table
							.getColumn("title")
							?.setFilterValue(event.target.value)
					}
					className="h-8 w-[150px] lg:w-[250px] text-xs"
				/>
				<Separator
					orientation="vertical"
					className="mx-4 data-[orientation=vertical]:h-4"
				/>
				<SortDropdown
					sortField={sortField}
					sortDirection={sortDirection}
					onSortFieldChange={onSortFieldChange}
					onSortDirectionChange={onSortDirectionChange}
					fields={[
						{ 
							label: "Created at", 
							value: "createdAt",
							icon: <IconCalendarTime className="w-4 h-4" />
						},
						{ label: "Title", value: "title", icon: <IconLetterTSmall className="w-4 h-4" /> },
						{ label: "Created by", value: "createdBy", icon: <IconUser className="w-4 h-4" /> },
						{ label: "Status", value: "status", icon: <IconId className="w-4 h-4" /> },
					]}
				/>
			</div>
		</div>
	);
}
