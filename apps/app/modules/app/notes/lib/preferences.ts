import { type NotePreferences, STORAGE_KEYS } from "@app/shared/lib/constants";
import {
	getLocalStorage,
	setLocalStorage,
} from "@app/shared/lib/local-storage";

const defaultPreferences: NotePreferences = {
	view: "list",
	sortBy: "createdAt",
	sortOrder: "asc",
	groupBy: "createdAt",
	showFavorites: false,
};

export const getNotePreferences = (): NotePreferences => {
	const stored = getLocalStorage(STORAGE_KEYS.NOTES_PREFERENCES);
	if (!stored) return defaultPreferences;

	try {
		return JSON.parse(stored) as NotePreferences;
	} catch (e) {
		console.error("Error parsing note preferences", e);
		return defaultPreferences;
	}
};

export const updateNotePreferences = (
	updates: Partial<NotePreferences>,
): NotePreferences => {
	const current = getNotePreferences();
	const updated = {
		...current,
		...updates,
	};

	setLocalStorage(STORAGE_KEYS.NOTES_PREFERENCES, JSON.stringify(updated));
	return updated;
};

export const clearNotePreferences = () => {
	setLocalStorage(
		STORAGE_KEYS.NOTES_PREFERENCES,
		JSON.stringify(defaultPreferences),
	);
	return defaultPreferences;
};
