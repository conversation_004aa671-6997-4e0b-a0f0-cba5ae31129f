"use client";


import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import type { Note } from "@repo/database/src/zod";
import { useQueryClient } from "@tanstack/react-query";
import * as React from "react";
import { NewNoteModal } from "../../../../app/(authenticated)/app/(organizations)/[organizationSlug]/notes/NewNoteModal";
import { NoteModal } from "../../../../app/(authenticated)/app/(organizations)/[organizationSlug]/notes/NoteModal";

interface NotesContextType {
	openCreateNote: () => void;
	openEditNote: (note: Note) => void;
}

const NotesContext = React.createContext<NotesContextType | undefined>(
	undefined,
);

export function useNotes() {
	const context = React.useContext(NotesContext);
	if (!context) {
		throw new Error("useNotes must be used within a NotesProvider");
	}
	return context;
}

export function NotesProvider({ children }: { children: React.ReactNode }) {
	const [isCreateModalOpen, setIsCreateModalOpen] = React.useState(false);
	const [noteToEdit, setNoteToEdit] = React.useState<Note | null>(null);
	const [newNote, setNewNote] = React.useState<Note | null>(null);
	const [isNoteModalOpen, setIsNoteModalOpen] = React.useState(false);
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	useHotkeys([
		{
			key: "n",
			callback: () => {
				if (!isCreateModalOpen && !isNoteModalOpen) {
					setIsCreateModalOpen(true);
				}
			},
			ignoreInputs: true,
		},
	]);

	const openCreateNote = React.useCallback(() => {
		setNoteToEdit(null);
		setIsNoteModalOpen(false);
		setIsCreateModalOpen(true);
	}, []);

	const openEditNote = React.useCallback((note: Note) => {
		setNoteToEdit(note);
		setIsCreateModalOpen(false);
		setIsNoteModalOpen(true);
	}, []);

	const handleCreateModalOpenChange = (open: boolean) => {
		setIsCreateModalOpen(open);
	};

	const handleNoteModalOpenChange = (open: boolean) => {
		setIsNoteModalOpen(open);
		if (!open) {
			setNewNote(null);
			setNoteToEdit(null);
		}
	};

	return (
		<NotesContext.Provider value={{ openCreateNote, openEditNote }}>
			{children}
			<NewNoteModal
				open={isCreateModalOpen}
				onOpenChange={handleCreateModalOpenChange}
				onSelectRecord={() => {}}
				onNoteCreated={(note) => {
					setIsCreateModalOpen(false);
					setNewNote(note);
					setIsNoteModalOpen(true);
					// Invalidate specific organization notes query instead of all queries
					queryClient.invalidateQueries({
						queryKey: ["notes", activeOrganization?.id],
					});
				}}
			/>
			<NoteModal
				open={isNoteModalOpen}
				onOpenChange={handleNoteModalOpenChange}
				note={newNote}
				noteToEdit={noteToEdit}
				isFavorite={false}
			/>
		</NotesContext.Provider>
	);
}
