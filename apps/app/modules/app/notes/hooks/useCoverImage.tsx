import { atom, useAtom } from "jotai";

interface CoverImageStore {
	isOpen: boolean;
	url?: string;
}

const coverImageAtom = atom<CoverImageStore>({
	isOpen: false,
	url: undefined,
});

export function useCoverImage() {
	const [state, setState] = useAtom(coverImageAtom);

	function onOpen() {
		setState({ isOpen: true, url: undefined });
	}

	function onClose() {
		setState({ isOpen: false, url: undefined });
	}

	function onReplace(url: string) {
		setState({ isOpen: true, url });
	}

	return {
		isOpen: state.isOpen,
		url: state.url,
		onOpen,
		onClose,
		onReplace,
	};
}
