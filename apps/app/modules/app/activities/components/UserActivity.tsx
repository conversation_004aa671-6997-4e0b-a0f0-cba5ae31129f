"use client";

import React, { useState, useMemo } from 'react';
import { formatDistanceToNow, format } from 'date-fns';
import { cn } from '@ui/lib';
import { UserAvatar } from '@shared/components/UserAvatar';
import { Button } from '@ui/components/button';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { MoreHorizontal, Edit, Trash2, MessageSquare, CheckCircle, Users } from 'lucide-react';
import { useUpdateActivity, useDeleteActivity, useCreateActivityReply } from '../lib/api';
import { createMentionNotification } from '@app/notifications/lib/api';
import type { ActiveOrganization } from "@repo/auth";
import type { Activity } from '../lib/api';
import ActivityReplies from './ActivityReplies';
import { Badge } from '@ui/components/badge';
import { formatPhoneNumber } from '@shared/lib/utils';
import { Textarea } from '@ui/components/textarea';
import { IconArrowBackUp, IconBrandLine, IconCircleCheck, IconCircleCheckFilled, IconPencil, IconTrash } from '@tabler/icons-react';
import { useEditor, EditorContent, ReactRenderer } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Mention from '@tiptap/extension-mention';
import { useFullOrganizationQuery } from '@app/organizations/lib/api';
import { MentionList } from '@app/activities/components/MentionList';
import tippy from 'tippy.js';

type UserActivityProps = {
	activity: Activity;
	organization: ActiveOrganization;
	isEdit?: any;
	handleStateChange: any;
	currentUser: any;
	highlightedActivityId?: string;
};

const CALL_RESULT_COLORS: Record<string, string> = {
	'Connected': '#0FC27B', // Green
	'No Answer': '#F5A623', // Orange
	'Voicemail': '#3B82F6', // Blue
	'Busy': '#EF4444', // Red
	'Wrong Number': '#6B7280', // Gray
	'Callback Requested': '#8B5CF6', // Purple
	'Not Interested': '#EF4444', // Red
	'Interested': '#0FC27B', // Green
	'Meeting Scheduled': '#3B82F6', // Blue
};

const UserActivity = ({ activity, organization, isEdit, handleStateChange, currentUser, highlightedActivityId }: UserActivityProps) => {
	const [showReplies, setShowReplies] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [isReplying, setIsReplying] = useState(false);
	const { mutateAsync: createReply } = useCreateActivityReply(activity.id, organization?.id, activity.recordType, activity.recordId);
	const { data: organizationData } = useFullOrganizationQuery(organization?.id || '');
	const { mutateAsync: updateActivity } = useUpdateActivity(organization?.id);
	const { mutateAsync: deleteActivity } = useDeleteActivity(organization?.id);

	// Transform organization members for mentions
	const organizationMembers = useMemo(() => {
		if (!organizationData?.members || !Array.isArray(organizationData.members)) return [];
		return organizationData.members
			.filter((member: any) => member.user && member.user.id && member.user.name)
			.map((member: any) => ({
				id: member.user.id,
				label: member.user.name,
				image: member.user.image,
			}));
	}, [organizationData?.members]);

	const editor = useEditor({
		extensions: [
			StarterKit,
			Mention.configure({
				HTMLAttributes: {
					class: 'mention',
				},
				suggestion: {
					items: ({ query }) => {
						return organizationMembers
							.filter((member) =>
								member.label.toLowerCase().includes(query.toLowerCase())
							)
							.slice(0, 5);
					},
					char: '@',
					command: ({ editor, range, props }) => {
						editor
							.chain()
							.focus()
							.insertContentAt(range, [
								{
									type: 'mention',
									attrs: props,
								},
								{
									type: 'text',
									text: ' ',
								},
							])
							.run();
					},
					allow: ({ editor, range }) => {
						return editor.can().insertContentAt(range, { type: 'mention' });
					},
					render: () => {
						let component: ReactRenderer | null = null;
						let popup: any[] | null = null;

						return {
							onStart: (props: any) => {
								component = new ReactRenderer(MentionList, {
									props,
									editor: props.editor,
								});

								if (!props.clientRect) {
									return;
								}

								popup = tippy('body', {
									getReferenceClientRect: props.clientRect,
									appendTo: () => document.body,
									content: component.element,
									showOnCreate: true,
									interactive: true,
									trigger: 'manual',
									placement: 'bottom-start',
								});
							},

							onUpdate(props: any) {
								component?.updateProps(props);

								if (!props.clientRect) {
									return;
								}

								popup?.[0].setProps({
									getReferenceClientRect: props.clientRect,
								});
							},

							onKeyDown(props: any) {
								if (props.event.key === 'Escape') {
									popup?.[0].hide();
									return true;
								}
								if (component?.ref && typeof component.ref === 'object' && 'onKeyDown' in component.ref) {
									return (component.ref.onKeyDown as (props: any) => boolean)(props);
								}
								return false;
							},

							onExit() {
								popup?.[0].destroy();
								component?.destroy();
							},
						};
					},
				},
			}),
		],
		content: '',
		immediatelyRender: false,
	});

	const isOwner = activity.userId === currentUser?.id;
	const isResolved = activity.resolved;

	const handleResolve = async () => {
		try {
			await updateActivity({
				id: activity.id,
				resolved: !isResolved,
				organizationId: organization?.id || '',
			});
		} catch (error) {
			console.error('Error resolving activity:', error);
		}
	};

	const handleEdit = () => {
		handleStateChange('isEdit', activity);
	};

	const handleDelete = async () => {
		try {
			await deleteActivity({
				id: activity.id,
				organizationId: organization?.id || '',
			});
			setShowDeleteDialog(false);
		} catch (error) {
			console.error('Error deleting activity:', error);
		}
	};

	const handleToggleReplies = () => {
		setShowReplies(!showReplies);
	};

	const handleReply = () => {
		setIsReplying(!isReplying);
	};

	const handleSubmitReply = async () => {
		if (!editor || !editor.getText().trim() || !organization?.id) return;

		try {
			// Extract mentions from the editor content
			const mentionedUsers: { id: string; name: string; image?: string }[] = [];
			editor.state.doc.descendants(node => {
				if (node.type.name === 'mention') {
					const userId = node.attrs.id;
					const userName = node.attrs.label;
					const userImage = node.attrs.image;
					
					// Don't add duplicates
					if (!mentionedUsers.some(user => user.id === userId)) {
						mentionedUsers.push({
							id: userId,
							name: userName,
							image: userImage
						});
					}
				}
				return false;
			});

			const htmlContent = editor.getHTML();
			
			const replyData = {
				message: htmlContent,
				organizationId: organization.id,
				mentionedUsers: mentionedUsers.map(user => user.id)
			};
			
			const reply = await createReply(replyData);

			for (const mentionedUser of mentionedUsers) {
				if (mentionedUser.id !== currentUser?.id) {
					await createMentionNotification({
						userId: mentionedUser.id,
						mentionedById: currentUser?.id || '',
						mentionedByName: currentUser?.name || '',
						mentionedByImage: currentUser?.image,
						activityId: activity.id,
						organizationId: organization.id,
						organizationName: organization.name,
						message: htmlContent,
						recordType: activity.recordType,
						recordId: activity.recordId,
						replyId: reply.id,
					});
				}
			}
			
			editor.commands.clearContent();
			setIsReplying(false);
			
			handleStateChange();
		} catch (error) {
			console.error('Error submitting reply:', error);
		}
	};

	const getIndicatorColor = () => {
		if (activity.type !== 'call' || !activity.result) return '#6B7280';
		return CALL_RESULT_COLORS[activity.result] || '#6B7280';
	};

	const ActivityOptions = () => {
		const isOriginalPoster = activity.userId === currentUser?.id;

		return (
			<div className="flex items-center gap-1 border border-border rounded-lg p-0.5 bg-sidebar shadow-sm">
				{isOriginalPoster && (
				<Button
					variant="ghost"
					size="sm"
					onClick={handleResolve}
					className="h-6 w-6 p-1.5 hover:bg-muted"
					tooltip={activity.resolved ? 'Unresolve' : 'Resolve'}
				>
					{activity.resolved ? (
						<IconCircleCheckFilled className={cn('size-5', activity.resolved && 'text-green-700')} />
					) : (
						<IconCircleCheck className={cn('size-5', activity.resolved && 'text-green-700')} />
					)}
				</Button>
				)}

				<Button
					variant="ghost"
					size="sm"
					onClick={handleReply}
					className="h-6 w-6 p-1.5 hover:bg-muted"
					tooltip="Reply"
				>
					<IconArrowBackUp className="h-3 w-3" />
				</Button>

				{isOriginalPoster && (
					<Button
						variant="ghost"
						size="sm"
						onClick={() => handleStateChange('isEdit', activity)}
						className="h-6 w-6 p-1.5 hover:bg-muted"
						tooltip="Edit"
					>
						<IconPencil className="h-3 w-3" />
					</Button>
				)}

				{isOriginalPoster && (
					<Button
						variant="ghost"
						size="sm"
						onClick={() => setShowDeleteDialog(true)}
						className="h-6 w-6 p-1.5 hover:bg-muted"
						tooltip="Delete"
					>
						<IconTrash className="h-3 w-3" />
					</Button>
				)}
			</div>
		);
	};

	return (
		<div className={cn("group relative flex flex-col gap-2 -mt-2", activity.resolved === true && 'opacity-50')}>
			<div className="absolute right-1 top-8 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
				<ActivityOptions />
			</div>
			
			<div className="flex items-baseline gap-2 ml-1">
				<span className="text-sm text-muted-foreground gap-1">
					<strong className="text-primary">{activity.user?.name}</strong>
					{activity.type === 'call' ? ' made a call' : ' added a note'}
					{activity.edited && (
						<span className="text-xs text-muted-foreground whitespace-nowrap ml-1 italic">
							(edited)
						</span>
					)}
				</span>
					<Button variant="ghost" size="sm" tooltip={format(new Date(activity.createdAt), 'MMMM dd, yyyy, h:mm a')} className="ml-auto h-auto p-0 hover:bg-transparent text-xs text-muted-foreground cursor-default">
						{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
					</Button>
			</div>

			{activity.type === 'call' && (
				<div className={cn(
					"flex flex-col bg-muted/50 hover:bg-muted/80 transition-all duration-200 border border-border rounded-xl px-3", 
					highlightedActivityId === activity.id && 'bg-blue-500/5 border-blue-500 animate-pulse',
					activity.resolved && 'opacity-50 cursor-default'
				)}
				>
					<div className="flex flex-col py-2.5 pl-3 relative">
						<div 
							className="absolute left-0 top-2 bottom-2 w-[3px] rounded-[2px]"
							style={{ backgroundColor: getIndicatorColor() }}
						/>
						
						<div className="flex justify-between items-start">
							<div className="flex flex-col justify-start space-y-6">
								<div className="flex flex-col">
									<span className="text-sm text-primary truncate">
										<div dangerouslySetInnerHTML={{ __html: activity.message }} />
									</span>
									<span className="flex text-[10px] text-muted-foreground truncate">
										{formatPhoneNumber(activity.phone || '')}
									</span>
								</div>
								<Badge className="text-xs text-muted-foreground !h-auto !p-0.5 !border !border-border !rounded-md max-w-fit">
									{activity.result || 'No result'}
								</Badge>
							</div>
						</div>
					</div>
				</div>
			)}

			{activity.type !== 'call' && (
				<div className={cn(
					"text-sm text-primary ml-1 border border-border rounded-xl p-2 hover:bg-muted/50 transition-all duration-200",
					highlightedActivityId === activity.id && "bg-yellow-500/5 border-yellow-500 animate-pulse"
				)}>
					<div className="flex flex-col gap-1">
					{activity.resolved && (() => {
						const resolvedByUser = organizationMembers.find(member => member.id === activity.resolvedBy);
						const resolvedByName = resolvedByUser?.label || 'unknown user';
						
						return (
							<span className="text-[10px] uppercase text-muted-foreground mr-2">
								{activity.resolvedBy === currentUser?.id
									? "resolved by you"
									: `resolved by ${resolvedByName}`}
							</span>
						);
					})()}
					<div dangerouslySetInnerHTML={{ __html: activity.message }} />
					</div>
				</div>
			)}

			{activity.replies && activity.replies.length > 0 && (
				<div className="ml-1 mt-2">
					{!showReplies ? (
						<Button 
							variant="ghost" 
								size="sm" 
								onClick={handleToggleReplies}
								className="text-xs text-muted-foreground hover:text-primary w-full justify-start h-8 !border !border-transparent hover:!bg-muted/50 hover:!border-border !rounded-lg"
							>
								{activity.replies.length === 1 ? '1 reply' : `${activity.replies.length} replies`}
							</Button>
					) : (
						<div className="space-y-2">
							<Button 
								variant="ghost" 
								size="sm" 
								onClick={handleToggleReplies}
								className="text-xs text-muted-foreground hover:text-primary w-full justify-start h-8 !border !border-transparent hover:!bg-muted/50 hover:!border-border !rounded-lg"
							>
								Hide replies
							</Button>
							{activity.replies.map((reply) => (
								<div key={reply.id} className="flex items-start gap-2 pl-4 border-l border-border pt-2">
									<UserAvatar
										name={reply.user.name}
										avatarUrl={reply.user.image}
										className="h-5 w-5"
									/>
									<div className="flex-1">
										<div className="flex items-baseline gap-2">
											<span className="text-sm font-medium text-primary">
												{reply.user.name}
											</span>
											<span className="text-xs text-muted-foreground">
												{formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
											</span>
										</div>
										<div className="text-sm text-primary">
											<div dangerouslySetInnerHTML={{ __html: reply.message }} />
										</div>
									</div>
								</div>
							))}
						</div>
					)}
				</div>
			)}

			{isReplying && (
				<div className="ml-1 mt-2 space-y-2">
					<div className="flex items-start gap-2 pl-4 border-l border-border">
						<UserAvatar
							name={currentUser?.name}
							avatarUrl={currentUser?.image}
							className="h-5 w-5"
						/>
						<div className="flex-1 space-y-2">
							<EditorContent
								editor={editor}
								className="tiptap min-h-[60px] rounded-xl border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
							/>
							<div className="flex justify-end gap-2">
								<Button
									variant="ghost"
									size="sm"
									onClick={() => {
										setIsReplying(false);
										editor?.commands.clearContent();
									}}
								>
									Cancel
								</Button>
								<Button
									variant="action"
									size="sm"
									onClick={handleSubmitReply}
									disabled={!editor?.getText().trim()}
								>
									Reply
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}

			<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Activity</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete this activity? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default UserActivity; 