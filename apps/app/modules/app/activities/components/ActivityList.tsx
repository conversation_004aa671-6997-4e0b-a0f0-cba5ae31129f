"use client";

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { format, formatDistanceToNow } from 'date-fns';
import { useActivities } from '../lib/api';
import type { Activity } from '../lib/api';
import type { ActiveOrganization } from "@repo/auth";
import UserActivity from './UserActivity';
import { Skeleton } from '@ui/components/skeleton';
import { ChevronRight, Settings, Mail, Phone } from 'lucide-react';
import { cn } from '@ui/lib';
import { Button } from '@ui/components/button';
import { UserAvatar } from '@shared/components/UserAvatar';
import { Popover, PopoverContent, PopoverTrigger } from '@ui/components/popover';
import { CopyableValue } from '@shared/components/CopyableValue';
import { formatPhoneNumber } from '@shared/lib/utils';
import { IconId, IconMapPin, IconCopy } from '@tabler/icons-react';

type ActivityListProps = {
	data: any;
	user: any;
	organization: ActiveOrganization;
	isEdit?: any;
	handleStateChange: any;
};

interface GroupedActivities {
	[year: string]: {
		[month: string]: Activity[];
	};
}

const ActivityList = ({ data, user, organization, isEdit, handleStateChange }: ActivityListProps) => {
	const { data: activities, isLoading } = useActivities(data?.id, organization?.id);
	const [collapsedGroups, setCollapsedGroups] = React.useState<Record<string, boolean>>({});
	
	const searchParams = useSearchParams();
	const highlightActivityId = searchParams.get('highlight');
	const [highlightedActivityId, setHighlightedActivityId] = useState<string | null>(null);
	
	useEffect(() => {
		if (highlightActivityId) {
			setHighlightedActivityId(highlightActivityId);
			const timer = setTimeout(() => {
				setHighlightedActivityId(null);
			}, 5000);
			return () => clearTimeout(timer);
		}
	}, [highlightActivityId]);

	if (isLoading) {
		return (
			<div className="space-y-6 p-4">
				{[...Array(3)].map((_, index) => (
					<div key={index} className="flex items-start gap-3 border border-border rounded-2xl p-3">
						<div className="relative">
							<Skeleton className="h-8 w-8 rounded-full" />
							<div className="absolute -bottom-1 -right-1 rounded-full border-2 border-background">
								<Skeleton className="h-4 w-4 rounded-full" />
							</div>
						</div>
						<div className="flex-1 space-y-3">
							<div className="space-y-2">
								<Skeleton className="h-4 w-2/3" />
								<Skeleton className="h-3 w-1/4" />
							</div>
							<div className="pl-4 border-l-2 border-border">
								<Skeleton className="h-16 w-full" />
							</div>
						</div>
					</div>
				))}
			</div>
		);
	}

	if (!activities || activities.length === 0) {
		return (
			<div className='flex items-center justify-center py-12'>
				<div className='text-center text-muted-foreground'>
					<div className='text-sm'>No activities yet</div>
					<div className='text-xs mt-1'>Start by adding a note or call log above</div>
				</div>
			</div>
		);
	}

	const groupedActivities = activities.reduce<GroupedActivities>((acc, activity) => {
		const date = new Date(activity.createdAt);
		const year = date.getFullYear().toString();
		const month = format(date, 'MMMM');

		if (!acc[year]) {
			acc[year] = {};
		}
		if (!acc[year][month]) {
			acc[year][month] = [];
		}
		acc[year][month].push(activity);
		return acc;
	}, {});

	const toggleGroup = (groupId: string) => {
		setCollapsedGroups(prev => ({
			...prev,
			[groupId]: !prev[groupId]
		}));
	};

	return (
		<div className='space-y-4 p-2'>
			{Object.entries(groupedActivities).map(([year, months]) => {
				const monthEntries = Object.entries(months);
				const isYearCollapsed = collapsedGroups[year];

				return (
					<div key={year} className="mb-8">
						<div className="flex items-center gap-2 mb-6">
							<div className="text-sm font-medium text-muted-foreground">
								{year}
							</div>
							<div className="relative flex-1">
								<div className="h-[1px] w-[97%] bg-border" />
								<Button
									variant="ghost"
									size="sm"
									className="absolute top-1/2 right-0 -translate-y-1/2 h-6 w-6 p-0"
									onClick={() => toggleGroup(year)}
								>
									<ChevronRight className={cn(
										"h-4 w-4 transition-transform",
										isYearCollapsed ? "rotate-90" : "-rotate-90"
									)} />
								</Button>
							</div>
						</div>

						{!isYearCollapsed && (
							<div className="space-y-6">
								{monthEntries.map(([month, monthActivities]) => {
									const monthGroupId = `${year}-${month}`;
									const isMonthCollapsed = collapsedGroups[monthGroupId];

									return (
										<div key={monthGroupId} className="ml-4">
											{/* Month Header */}
											<div className="flex items-center gap-2 mb-4">
												<div className="flex items-center gap-2 bg-sidebar h-auto rounded-md border border-border py-0.5 px-2">
													<span className="text-xs font-medium">{month}</span>
												</div>
												<div className="relative flex-1">
													<div className="h-[1px] w-[95%] bg-border" />
													<Button
														variant="ghost"
														size="sm"
														className="absolute top-1/2 right-0 -translate-y-1/2 h-6 w-6 p-0"
														onClick={() => toggleGroup(monthGroupId)}
													>
														<ChevronRight className={cn(
															"h-4 w-4 transition-transform",
															isMonthCollapsed ? "rotate-90" : "-rotate-90"
														)} />
													</Button>
												</div>
											</div>

											{!isMonthCollapsed && (
												<div className="space-y-4">
													{monthActivities.map((activity: Activity) => (
														<div 
															key={activity.id} 
															className={cn("grid grid-cols-[16px_1fr] gap-2 mb-8 transition-colors duration-300")}
														>
															<div className="relative flex justify-center">
																<div className="absolute top-0 bottom-0 left-1/2 w-[1px] bg-border -z-10" />
																<div className="relative flex items-center justify-center w-5 h-5 -mt-2 ">
																	{activity.system ? (
																		<div className="flex items-center justify-center w-4 h-4 rounded-sm border border-border text-muted-foreground">
																			<Settings className="h-3 w-3" />
																		</div>
																	) : (
																		<UserAvatar
																			name={activity.user?.name}
																			avatarUrl={activity.user?.image}
																			className="w-5 h-5"
																		/>
																	)}
																</div>
															</div>

															<div>
																{activity.system ? (
																	<SystemActivity activity={activity} />
																) : (
																	<UserActivity
																		activity={activity}
																		organization={organization}
																		isEdit={isEdit}
																		handleStateChange={handleStateChange}
																		currentUser={user}
																		highlightedActivityId={highlightedActivityId || undefined}
																	/>
																)}
															</div>
														</div>
													))}
												</div>
											)}
										</div>
									);
								})}
							</div>
						)}
					</div>
				);
			})}
		</div>
	);
};

// Types for field change parsing
interface FieldChange {
	fieldName: string;
	fieldType: 'email' | 'phone' | 'text' | 'address';
	action: 'added' | 'changed' | 'cleared' | 'set';
	values: string[];
	oldValues?: string[];
}

// Parse field change messages into structured data
function parseFieldChange(message: string): FieldChange | null {
	// Handle email changes
	if (message.includes('Email') || message.includes('email')) {
		if (message.includes(' was set to ')) {
			const match = message.match(/Email.*?was set to "(.+?)"/);
			if (match) {
				return {
					fieldName: 'Email addresses',
					fieldType: 'email',
					action: 'set',
					values: [match[1]]
				};
			}
		}
		if (message.includes(' changed from ') && message.includes(' to ')) {
			const match = message.match(/Email.*?changed from "(.+?)" to "(.+?)"/);
			if (match) {
				return {
					fieldName: 'Email addresses',
					fieldType: 'email',
					action: 'changed',
					values: [match[2]],
					oldValues: [match[1]]
				};
			}
		}
		if (message.includes(' was added') || message.includes(' were added')) {
			// Extract all email addresses from the message
			const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
			const matches = message.match(emailRegex);
			if (matches) {
				return {
					fieldName: 'Email addresses',
					fieldType: 'email',
					action: 'added',
					values: matches
				};
			}
		}
		// Generic email updates
		if (message.includes('Email addresses updated') || message.includes('email')) {
			return {
				fieldName: 'Email addresses',
				fieldType: 'email',
				action: 'changed',
				values: []
			};
		}
	}

	// Handle phone changes
	if (message.includes('Phone') || message.includes('phone')) {
		if (message.includes(' was set to ')) {
			const match = message.match(/Phone.*?was set to "(.+?)"/);
			if (match) {
				return {
					fieldName: 'Phone numbers',
					fieldType: 'phone',
					action: 'set',
					values: [formatPhoneNumber(match[1])]
				};
			}
		}
		if (message.includes(' changed from ') && message.includes(' to ')) {
			const match = message.match(/Phone.*?changed from "(.+?)" to "(.+?)"/);
			if (match) {
				return {
					fieldName: 'Phone numbers',
					fieldType: 'phone',
					action: 'changed',
					values: [formatPhoneNumber(match[2])],
					oldValues: [formatPhoneNumber(match[1])]
				};
			}
		}
		if (message.includes(' was added') || message.includes(' were added')) {
			// Extract phone numbers from the message (various formats)
			const phoneRegex = /([+]?[\d\s\-\(\)\.]{7,})/g;
			const matches = message.match(phoneRegex);
			if (matches) {
				return {
					fieldName: 'Phone numbers',
					fieldType: 'phone',
					action: 'added',
					values: matches.map(phone => formatPhoneNumber(phone.trim()))
				};
			}
		}
		// Generic phone updates
		return {
			fieldName: 'Phone numbers',
			fieldType: 'phone',
			action: 'changed',
			values: []
		};
	}

	// Handle address changes
	if (message.includes('Address') || message.includes('address')) {
		if (message.includes(' was set to ') || message.includes(' was added')) {
			// Extract address from "Address was set to 'address'" or "Added address"
			const addressMatch = message.match(/(?:Address.*?was set to "(.+?)"|Added (.+))/);
			if (addressMatch) {
				const address = addressMatch[1] || addressMatch[2];
				return {
					fieldName: 'Address',
					fieldType: 'address',
					action: message.includes('Added') ? 'added' : 'set',
					values: [address]
				};
			}
		}
		if (message.includes(' changed from ') && message.includes(' to ')) {
			const match = message.match(/Address.*?changed from "(.+?)" to "(.+?)"/);
			if (match) {
				return {
					fieldName: 'Address',
					fieldType: 'address',
					action: 'changed',
					values: [match[2]],
					oldValues: [match[1]]
				};
			}
		}
		return {
			fieldName: 'Address',
			fieldType: 'address',
			action: 'changed',
			values: []
		};
	}

	// Handle other field changes (text fields)
	const textFieldMatch = message.match(/(.+?) changed from "(.+?)" to "(.+?)"/);
	if (textFieldMatch) {
		return {
			fieldName: textFieldMatch[1],
			fieldType: 'text',
			action: 'changed',
			values: [textFieldMatch[3]],
			oldValues: [textFieldMatch[2]]
		};
	}

	const setMatch = message.match(/(.+?) was set to "(.+?)"/);
	if (setMatch) {
		return {
			fieldName: setMatch[1],
			fieldType: 'text',
			action: 'set',
			values: [setMatch[2]]
		};
	}

	// Handle "Added" messages (like addresses)
	const addedMatch = message.match(/^Added (.+)$/);
	if (addedMatch) {
		return {
			fieldName: 'Address',
			fieldType: 'address',
			action: 'added',
			values: [addedMatch[1]]
		};
	}

	return null;
}

// Component for displaying field changes with popover for email/phone
function FieldChangeButton({ fieldChange }: { fieldChange: FieldChange }) {
	const getIcon = () => {
		switch (fieldChange.fieldType) {
			case 'email':
				return <Mail className="h-3.5 w-3.5" />;
			case 'phone':
				return <Phone className="h-3.5 w-3.5" />;
			case 'text':
				return <IconId className="h-3.5 w-3.5" />;
			case 'address':
				return <IconMapPin className="h-3.5 w-3.5" />;
			default:
				return <Settings className="h-3.5 w-3.5" />;
		}
	};

	const getActionText = () => {
		switch (fieldChange.action) {
			case 'added':
				return 'Added';
			case 'set':
				return 'Added';
			case 'changed':
				return 'Changed to';
			case 'cleared':
				return 'Cleared';
			default:
				return 'Changed';
		}
	};

	// For text fields, show inline (without redundant action text)
	if (fieldChange.fieldType === 'text') {
		return (
			<div className="text-sm font-medium text-foreground">
				{fieldChange.values[0]}
			</div>
		);
	}

	// For email/phone fields, show button with popover
	if (fieldChange.values.length === 0) {
		// No specific values to show, just field name
		return (
			<button className="inline-flex items-center gap-1 text-sm font-medium text-foreground border border-transparent rounded-lg px-1 hover:bg-accent/50 transition-colors">
				<div className="inline-flex">
					{getIcon()}
				</div>
				<span className="relative">
					{fieldChange.fieldName}
					<div className="absolute left-0 right-0 bottom-0 h-px bg-current opacity-40 bg-repeat-x" 
						 style={{ backgroundImage: 'repeating-linear-gradient(to right, currentColor 0, currentColor 3px, transparent 3px, transparent 4px)' }} />
				</span>
			</button>
		);
	}

	return (
		<Popover>
			<PopoverTrigger asChild>
				<button className="-mt-2 inline-flex items-center gap-1 text-sm font-medium text-foreground border border-transparent rounded-lg px-1 hover:bg-accent/50 transition-colors">
					<div className="inline-flex">
						{getIcon()}
					</div>
					<span className="relative">
						{fieldChange.fieldName}
						<div className="absolute left-0 right-0 bottom-0 h-px bg-current opacity-40 bg-repeat-x" 
							 style={{ backgroundImage: 'repeating-linear-gradient(to right, currentColor 0, currentColor 3px, transparent 3px, transparent 4px)' }} />
					</span>
				</button>
			</PopoverTrigger>
			<PopoverContent className="p-2 max-w-fit min-w-fit" align="start" side="top">
				<div className="flex items-center gap-2">
					<div className="text-sm font-medium text-foreground">
						{getActionText()}
					</div>
					<div className="space-y-2">
						{fieldChange.values.map((value, index) => (
							fieldChange.fieldType === 'address' ? (
								<div 
									key={index}
								>
									<CopyableValue value={value} type="url" className="w-full" />
								</div>
							) : (
								<CopyableValue
									key={index}
									value={value}
									type={fieldChange.fieldType as 'email' | 'phone'}
									className="w-full"
								/>
							)
						))}
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}

// System activity component for system-generated activities
const SystemActivity = ({ activity }: { activity: Activity }) => {
	const message = activity.message;
	const userName = activity.user?.name || 'Attio system';

	// Parse creation activities: "John Doe was created" -> "**John Doe** was created by **User Name**"
	if (message.includes(' was created')) {
		const recordName = message.replace(' was created', '');
		return (
			<div className='flex flex-col gap-1'>
				<div className='flex items-baseline gap-2 -mt-2'>
					<div className='text-sm font-medium leading-5 text-muted-foreground'>
						<strong className="font-medium text-foreground">{recordName}</strong> was created by <strong className="font-medium text-foreground">{userName}</strong>
					</div>
					<Button variant="ghost" size="sm" tooltip={format(new Date(activity.createdAt), 'MMMM dd, yyyy, h:mm a')} className="ml-auto h-auto p-0 hover:bg-transparent text-xs text-muted-foreground cursor-default">
						{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
					</Button>
				</div>
			</div>
		);
	}

	// Parse deletion activities: "John Doe was deleted" -> "**John Doe** was deleted by **User Name**"
	if (message.includes(' was deleted')) {
		const recordName = message.replace(' was deleted', '');
		return (
			<div className='flex flex-col gap-1'>
				<div className='flex items-baseline gap-2 -mt-2'>
					<div className='text-sm font-medium leading-5 text-muted-foreground'>
						<strong className="font-medium text-foreground">{recordName}</strong> was deleted by <strong className="font-medium text-foreground">{userName}</strong>
					</div>
					<Button variant="ghost" size="sm" tooltip={format(new Date(activity.createdAt), 'MMMM dd, yyyy, h:mm a')} className="ml-auto h-auto p-0 hover:bg-transparent text-xs text-muted-foreground cursor-default">
						{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
					</Button>
				</div>
			</div>
		);
	}

	// Handle batch deletion: "3 contacts were deleted" -> "3 contacts were deleted by **User Name**"
	if (message.includes(' were deleted')) {
		return (
			<div className='flex flex-col gap-1'>
				<div className='flex items-baseline gap-2 -mt-2'>
					<div className='text-sm font-medium leading-5 text-muted-foreground'>
						{message} by <strong className="font-medium text-foreground">{userName}</strong>
					</div>
					<Button variant="ghost" size="sm" tooltip={format(new Date(activity.createdAt), 'MMMM dd, yyyy, h:mm a')} className="ml-auto h-auto p-0 hover:bg-transparent text-xs text-muted-foreground cursor-default">
						{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
					</Button>
				</div>
			</div>
		);
	}

	// Parse field changes and create appropriate UI
	const fieldChange = parseFieldChange(message);
	
	if (fieldChange) {
		const getActionVerb = () => {
			switch (fieldChange.action) {
				case 'added':
					return 'added';
				case 'set':
					return 'added';
				case 'changed':
					return 'changed';
				case 'cleared':
					return 'cleared';
				default:
					return 'changed';
			}
		};

		return (
			<div className='flex flex-col gap-1'>
				<div className='flex items-baseline gap-2 -mt-2'>
					<div className='flex items-baseline gap-2'>
						<div className='text-sm font-medium leading-5 text-muted-foreground'>
							<strong className="font-medium text-foreground">{userName}</strong> {getActionVerb()}
						</div>
						<FieldChangeButton fieldChange={fieldChange} />
					</div>
					<Button variant="ghost" size="sm" tooltip={format(new Date(activity.createdAt), 'MMMM dd, yyyy, h:mm a')} className="ml-auto h-auto p-0 hover:bg-transparent text-xs text-muted-foreground cursor-default">
						{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
					</Button>
				</div>
			</div>
		);
	}

	// For all other activities (field changes, etc.), show: "**User Name** changed X"
	return (
		<div className='flex flex-col gap-1'>
			<div className='flex items-baseline gap-2 -mt-2'>
				<div className='text-sm font-medium leading-5 text-muted-foreground'>
					<strong className="font-medium text-foreground">{userName}</strong> {message}
				</div>
				<Button variant="ghost" size="sm" tooltip={format(new Date(activity.createdAt), 'MMMM dd, yyyy, h:mm a')} className="ml-auto h-auto p-0 hover:bg-transparent text-xs text-muted-foreground cursor-default">
					{formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
				</Button>
			</div>
		</div>
	);
};

export default ActivityList; 