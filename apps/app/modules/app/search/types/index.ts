export interface BaseSearchResultDetails {
	id: string;
	title: string;
	url: string;
	description?: string;
	icon?: string;
	createdAt?: string;
	updatedAt?: string;
	createdById?: string;
	createdByName?: string;
	createdByAvatarUrl?: string;
}

export interface TaskSearchResultDetails extends BaseSearchResultDetails {
	type: "task";
	status?: string;
	priority?: string;
	assigneeId?: string;
	assigneeName?: string;
	assigneeAvatarUrl?: string;
}

export interface NoteSearchResultDetails extends BaseSearchResultDetails {
	type: "note";
	content?: string;
	coverImage?: string;
	isPublished?: boolean;
	objectId?: string;
	objectType?: string;
}

export interface ContactSearchResultDetails extends BaseSearchResultDetails {
	type: "contact";
	firstName?: string;
	lastName?: string;
	name: string;
	email?: string;
	phone?: string;
	jobTitle?: string;
	company?: {
		id: string;
		name: string;
	};
	status?: string;
	avatarUrl?: string;
}

export interface UserSearchResultDetails extends BaseSearchResultDetails {
	type: "user";
	name: string;
	email?: string;
	username?: string;
	role?: string;
	avatarUrl?: string;
	memberSince?: string;
	lastLogin?: string;
}

export interface PropertySearchResultDetails extends BaseSearchResultDetails {
	type: "property";
	propertyType?: string;
	market?: string;
	status?: string;
	location?: {
		address?: {
			street?: string;
			street2?: string;
			city?: string;
			state?: string;
			zip?: string;
			country?: string;
		};
		location?: any;
	};
	creator?: {
		id: string;
		name: string;
		image?: string;
	};
}

export type SearchResultDetails =
	| TaskSearchResultDetails
	| NoteSearchResultDetails
	| ContactSearchResultDetails
	| UserSearchResultDetails
	| PropertySearchResultDetails;
