export * from "./components";
export * from "./lib/api";
export * from "./types";
export * from "./components/ContactDetailPanel";
export * from "./components/NoteDetailPanel";
export * from "./components/TaskDetailPanel";
export * from "./components/UserDetailPanel";
export * from "./components/PropertyDetailPanel";

export type SearchResultDetails = {
	id: string;
	title: string;
	type: "task" | "note" | "contact" | "user" | "property";
	url: string;
	content?: string;
	description?: string;
	status?: string;
	icon?: string;
	createdAt?: string;
	updatedAt?: string;
	priority?: string;
	assigneeId?: string;
	assigneeName?: string;
	assigneeAvatarUrl?: string;
	createdById?: string;
	createdByName?: string;
	// Note-specific fields
	isPublished?: boolean;
	coverImage?: string;
	objectId?: string;
	objectType?: string;
	// Contact-specific fields
	firstName?: string;
	lastName?: string;
	name: string;
	email?: string;
	phone?: string;
	jobTitle?: string;
	company?: any;
	avatarUrl?: string;
	// User-specific fields
	role?: string;
	username?: string;
	memberSince?: string;
	// Property-specific fields
	propertyType?: string;
	market?: string;
	location?: {
		address?: {
			street?: string;
			street2?: string;
			city?: string;
			state?: string;
			zip?: string;
			country?: string;
		};
		location?: any;
	};
	creator?: {
		id: string;
		name: string;
		image?: string;
	};
};

export type TaskSearchResultDetails = SearchResultDetails & {
	type: "task";
};

export type NoteSearchResultDetails = SearchResultDetails & {
	type: "note";
};

export type ContactSearchResultDetails = SearchResultDetails & {
	type: "contact";
};

export type UserSearchResultDetails = SearchResultDetails & {
	type: "user";
};

export type PropertySearchResultDetails = SearchResultDetails & {
	type: "property";
};
