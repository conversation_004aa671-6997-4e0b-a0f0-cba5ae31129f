import { TASK_PRIORITY, TASK_STATUS } from "@app/shared/lib/constants";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconNote } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import type { TaskSearchResultDetails } from "../types";

interface TaskDetailPanelProps {
	task: TaskSearchResultDetails;
	onNavigate?: () => void;
	onEdit?: (task: TaskSearchResultDetails) => void;
}

const HeadingText = ({ text }: { text: string }) => {
	return <h4 className="text-xs text-muted-foreground mb-1">{text}</h4>;
};

const getTaskStatusIcon = (status?: string) => {
	const taskStatus = TASK_STATUS.find((s) => s.value === status);
	return {
		icon: taskStatus?.icon || IconNote,
		color: taskStatus?.color || "text-zinc-500",
	};
};

const getTaskPriorityIcon = (priority?: string) => {
	const taskPriority = TASK_PRIORITY.find((p) => p.value === priority);
	return {
		icon: taskPriority?.icon || IconNote,
		color: taskPriority?.color || "text-zinc-500",
	};
};

export function TaskDetailPanel({
	task,
	onNavigate,
	onEdit,
}: TaskDetailPanelProps) {
	const { icon: TaskStatusIcon, color } = getTaskStatusIcon(task.status);
	const { icon: TaskPriorityIcon, color: priorityColor } =
		getTaskPriorityIcon(task.priority);

	const handleOpenTask = () => {
		onNavigate?.();
		if (onEdit) {
			onEdit(task);
		}
	};

	return (
		<div
			className="w-80 border-l border-border bg-secondary/20"
			style={{
				height: "450px",
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* Fixed Header */}
			<div className="flex-shrink-0 p-4 border-b border-border">
				<div className="flex items-center gap-2">
					<div>
						<TaskStatusIcon className={`h-4 w-4 ${color}`} />
					</div>
					<div className="flex items-center gap-2">
						<span className="font-medium text-sm">
							{task.title}
						</span>
					</div>
				</div>
			</div>

			{/* Scrollable Content */}
			<div
				className="p-4"
				style={{
					flex: 1,
					overflowY: "auto",
					minHeight: 0,
					maxHeight: "calc(450px - 80px - 30px)",
				}}
			>
				<div className="space-y-3">
					{task.description && (
						<div>
							<HeadingText text="Description" />
							<p className="text-sm">{task.description}</p>
						</div>
					)}

					{task.status && (
						<div>
							<HeadingText text="Status" />
							<div className="flex items-center gap-2">
								<TaskStatusIcon
									className={`h-4 w-4 ${color}`}
								/>
								<span className="text-sm capitalize">
									{task.status}
								</span>
							</div>
						</div>
					)}

					{task.priority && (
						<div>
							<HeadingText text="Priority" />
							<div className="flex items-center gap-2">
								<TaskPriorityIcon
									className={`h-4 w-4 ${priorityColor}`}
								/>
								<span className="text-sm capitalize">
									{task.priority}
								</span>
							</div>
						</div>
					)}

					{task.assigneeName && (
						<div>
							<HeadingText text="Assigned to" />
							<div className="flex items-center gap-2">
								<UserAvatar
									className="h-6 w-6"
									name={task.assigneeName}
									avatarUrl={task.assigneeAvatarUrl}
								/>
								<span className="text-sm">
									{task.assigneeName}
								</span>
							</div>
						</div>
					)}

					{task.createdAt && (
						<div>
							<HeadingText text="Created" />
							<span className="text-sm">
								{new Date(task.createdAt)
									.toLocaleString("en-US", {
										month: "long",
										day: "numeric",
										year: "numeric",
										hour: "numeric",
										minute: "2-digit",
										hour12: true,
									})
									.replace(/(\d+),/, (match, day) => {
										// Add ordinal suffix to day
										const n = Number.parseInt(day, 10);
										const s = ["th", "st", "nd", "rd"],
											v = n % 100;
										const ordinal =
											s[(v - 20) % 10] || s[v] || s[0];
										return ` ${n}${ordinal},`;
									})}
							</span>
						</div>
					)}

					{task.updatedAt && (
						<div>
							<HeadingText text="Last Updated" />
							<span className="text-sm">
								{new Date(task.updatedAt)
									.toLocaleString("en-US", {
										month: "long",
										day: "numeric",
										year: "numeric",
										hour: "numeric",
										minute: "2-digit",
										hour12: true,
									})
									.replace(/(\d+),/, (match, day) => {
										// Add ordinal suffix to day
										const n = Number.parseInt(day, 10);
										const s = ["th", "st", "nd", "rd"],
											v = n % 100;
										const ordinal =
											s[(v - 20) % 10] || s[v] || s[0];
										return ` ${n}${ordinal},`;
									})}
							</span>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
