import { UserAvatar } from "@shared/components/UserAvatar";
import { IconNote, IconWorld } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import type { NoteSearchResultDetails } from "../types";

interface NoteDetailPanelProps {
	note: NoteSearchResultDetails;
	onNavigate?: () => void;
}

const HeadingText = ({ text }: { text: string }) => {
	return <h4 className="text-xs text-muted-foreground mb-1">{text}</h4>;
};

const extractTextFromContent = (content: string | undefined): string => {
	if (!content) return "";

	try {
		const parsed = JSON.parse(content);
		const extractText = (node: any): string => {
			if (node.type === "text") {
				return node.text || "";
			}
			if (node.content && Array.isArray(node.content)) {
				return node.content.map(extractText).join("");
			}
			return "";
		};

		if (parsed.content && Array.isArray(parsed.content)) {
			const text = parsed.content.map(extractText).join(" ").trim();
			return text.length > 100 ? `${text.substring(0, 100)}...` : text;
		}
	} catch (error) {
		// If parsing fails, return the raw content (truncated)
		return content.length > 100
			? `${content.substring(0, 100)}...`
			: content;
	}

	return "";
};

export function NoteDetailPanel({ note, onNavigate }: NoteDetailPanelProps) {
	const router = useRouter();

	const handleOpenNote = () => {
		onNavigate?.();
		router.push(note.url);
	};

	const contentText = extractTextFromContent(note.content);

	return (
		<div
			className="w-80 border-l border-border bg-secondary/20"
			style={{
				height: "450px",
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* Fixed Header */}
			<div className="flex-shrink-0 p-4 border-b border-border">
				<div className="flex items-center gap-2">
					<div>
						{note.icon ? (
							<span className="text-base">{note.icon}</span>
						) : (
							<IconNote className="h-4 w-4" />
						)}
					</div>
					<div className="flex items-center justify-between w-full gap-2">
						<span className="font-medium text-sm">
							{note.title}
						</span>
						{note.isPublished && (
							<div className="flex items-center gap-1">
								<IconWorld className="h-4 w-4 text-blue-500" />
							</div>
						)}
					</div>
				</div>
			</div>

			{/* Scrollable Content */}
			<div
				className="p-4"
				style={{
					flex: 1,
					overflowY: "auto",
					minHeight: 0,
					maxHeight: "calc(450px - 80px - 30px)",
				}}
			>
				{note.coverImage && (
					<div className="mb-3">
						<div className="rounded border overflow-hidden">
							<img
								src={note.coverImage}
								alt="Note cover"
								className="w-full h-24 object-cover"
							/>
						</div>
					</div>
				)}

				<div className="space-y-3">
					{note.description && (
						<div>
							<HeadingText text="Description" />
							<p className="text-sm">{note.description}</p>
						</div>
					)}

					{contentText && (
						<div>
							<HeadingText text="Content" />
							<div className="text-sm bg-muted/50 p-2 rounded border">
								{contentText}
							</div>
						</div>
					)}

					{/* Object Details */}
					{(note.objectType || note.objectId) && (
						<div>
							<HeadingText text="Related Object" />
							<div className="text-sm">
								{note.objectType && (
									<div>
										<span className="text-muted-foreground">
											Type:{" "}
										</span>
										<span className="capitalize">
											{note.objectType}
										</span>
									</div>
								)}
								{note.objectId && (
									<div>
										<span className="text-muted-foreground">
											ID:{" "}
										</span>
										<span className="font-mono text-xs">
											{note.objectId}
										</span>
									</div>
								)}
							</div>
						</div>
					)}

					{note.createdByName && (
						<div>
							<HeadingText text="Created by" />
							<div className="flex items-center gap-2">
								<UserAvatar
									className="h-6 w-6"
									name={note.createdByName}
									avatarUrl={note.createdByAvatarUrl}
								/>
								<span className="text-sm">
									{note.createdByName}
								</span>
							</div>
						</div>
					)}

					{note.createdAt && (
						<div>
							<HeadingText text="Created" />
							<span className="text-sm">
								{new Date(note.createdAt)
									.toLocaleString("en-US", {
										month: "long",
										day: "numeric",
										year: "numeric",
										hour: "numeric",
										minute: "2-digit",
										hour12: true,
									})
									.replace(/(\d+),/, (match, day) => {
										// Add ordinal suffix to day
										const n = Number.parseInt(day, 10);
										const s = ["th", "st", "nd", "rd"],
											v = n % 100;
										const ordinal =
											s[(v - 20) % 10] || s[v] || s[0];
										return ` ${n}${ordinal},`;
									})}
							</span>
						</div>
					)}

					{note.updatedAt && (
						<div>
							<HeadingText text="Last Updated" />
							<span className="text-sm">
								{new Date(note.updatedAt)
									.toLocaleString("en-US", {
										month: "long",
										day: "numeric",
										year: "numeric",
										hour: "numeric",
										minute: "2-digit",
										hour12: true,
									})
									.replace(/(\d+),/, (match, day) => {
										// Add ordinal suffix to day
										const n = Number.parseInt(day, 10);
										const s = ["th", "st", "nd", "rd"],
											v = n % 100;
										const ordinal =
											s[(v - 20) % 10] || s[v] || s[0];
										return ` ${n}${ordinal},`;
									})}
							</span>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
