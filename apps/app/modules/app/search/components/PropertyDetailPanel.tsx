"use client";

import { type PropertySearchResultDetails } from "@app/search/types";
import {
	IconBuilding,
	IconMapPin,
	IconCalendar,
	IconCategory,
	IconChartBar,
	IconCircleCheck,
} from "@tabler/icons-react";
import { UserAvatar } from "@shared/components/UserAvatar";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

interface PropertyDetailPanelProps {
	property: PropertySearchResultDetails;
	onNavigate?: () => void;
}

const HeadingText = ({ text }: { text: string }) => {
	return <h4 className="text-xs text-muted-foreground mb-1 cursor-default">{text}</h4>;
};

interface MapPreviewProps {
	coordinates: [number, number];
	address: string;
}

function MapPreview({ coordinates, address }: MapPreviewProps) {
	const mapContainer = useRef<HTMLDivElement>(null);
	const map = useRef<mapboxgl.Map | null>(null);

	useEffect(() => {
		if (!mapContainer.current || map.current) return;

		// Set the access token
		mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "";

		if (!mapboxgl.accessToken) {
			console.warn("Mapbox access token not found");
			return;
		}

		try {
			// Initialize the map
			map.current = new mapboxgl.Map({
				container: mapContainer.current,
				style: "mapbox://styles/mapbox/streets-v12",
				center: coordinates,
				zoom: 15,
				interactive: false, // Disable interaction for preview
				attributionControl: false, // Hide attribution for clean look
			});

			// Add a marker for the property
			new mapboxgl.Marker({ color: "#3FB1CE" })
				.setLngLat(coordinates)
				.addTo(map.current);

		} catch (error) {
			console.error("Failed to initialize map preview:", error);
		}

		// Cleanup function
		return () => {
			if (map.current) {
				map.current.remove();
				map.current = null;
			}
		};
	}, [coordinates]);

	return (
		<div className="w-full h-32 rounded-xl overflow-hidden bg-muted/20 border border-border ring-2 ring-border/50">
			<div 
				ref={mapContainer} 
				className="w-full h-full" 
				style={{ minHeight: "128px" }}
			/>
		</div>
	);
}

export function PropertyDetailPanel({ property, onNavigate }: PropertyDetailPanelProps) {
	const router = useRouter();

	const handleOpenProperty = () => {
		onNavigate?.();
		router.push(property.url);
	};

	// Helper function to extract coordinates from various property structures
	const getPropertyCoordinates = (): [number, number] | null => {
		const location = property.location;
		if (!location) return null;

		// Try different coordinate structures
		const coords = 
			location.location?.coordinates ||  // GeoJSON format
			(location as any).coordinates ||   // Direct coordinates
			null;

		if (coords && Array.isArray(coords) && coords.length === 2) {
			const [lng, lat] = coords;
			if (typeof lng === 'number' && typeof lat === 'number' && 
				!isNaN(lng) && !isNaN(lat)) {
				return [lng, lat];
			}
		}

		return null;
	};

	const coordinates = getPropertyCoordinates();

	return (
		<div
			className="w-80 border-l border-border bg-secondary/20"
			style={{
				height: "450px",
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* Fixed Header */}
			<div className="flex-shrink-0 p-4 border-b border-border">
				<div className="flex items-center gap-2">
						<PropertyAvatar name={property.title} className="h-8 w-8" />
					<div className="flex flex-col">
						<span className="font-medium text-sm">
							{property.title}
						</span>
						{property.status && (
							<span className="text-xs text-muted-foreground capitalize">
								{property.status.replace(/_/g, " ")}
							</span>
						)}
					</div>
				</div>
			</div>

			{/* Scrollable Content */}
			<div
				className="p-4"
				style={{
					flex: 1,
					overflowY: "auto",
					minHeight: 0,
					maxHeight: "calc(450px - 80px - 30px)",
				}}
			>
				<div className="space-y-3">
					{/* Map Preview */}
					{coordinates && (
							<MapPreview 
								coordinates={coordinates}
								address={`${property.location?.address?.street || ''} ${property.location?.address?.city || ''}`}
							/>
					)}

					{property.location?.address && (
						<div>
							<HeadingText text="Address" />
							<div className="flex items-start gap-2">
								<IconMapPin className="h-4 w-4 mt-0.5 flex-shrink-0 text-muted-foreground" />
								<span className="text-sm">
									{property.location.address.street}
									{property.location.address.street2 && (
										<>, {property.location.address.street2}</>
									)}
									<br />
									{property.location.address.city}, {property.location.address.state} {property.location.address.zip}
									{property.location.address.country && (
										<><br />{property.location.address.country}</>
									)}
								</span>
							</div>
						</div>
					)}

					{property.propertyType && (
						<div>
							<HeadingText text="Property Type" />
							<div className="flex items-center gap-2">
								<IconCategory className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm capitalize">
									{property.propertyType.replace(/_/g, " ")}
								</span>
							</div>
						</div>
					)}

					{property.market && (
						<div>
							<HeadingText text="Market" />
							<div className="flex items-center gap-2">
								<IconChartBar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{property.market}
								</span>
							</div>
						</div>
					)}

					{property.status && (
						<div>
							<HeadingText text="Status" />
							<div className="flex items-center gap-2">
								<IconCircleCheck className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm capitalize">
									{property.status.replace(/_/g, " ")}
								</span>
							</div>
						</div>
					)}

					{property.creator && (
						<div>
							<HeadingText text="Created by" />
							<div className="flex items-center gap-2">
								<UserAvatar
									className="h-6 w-6"
									name={property.creator.name}
									avatarUrl={property.creator.image}
								/>
								<span className="text-sm">
									{property.creator.name}
								</span>
							</div>
						</div>
					)}

					{property.createdAt && (
						<div>
							<HeadingText text="Created" />
							<div className="flex items-center gap-2">
								<IconCalendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{new Date(property.createdAt)
										.toLocaleString("en-US", {
											month: "long",
											day: "numeric",
											year: "numeric",
											hour: "numeric",
											minute: "2-digit",
											hour12: true,
										})
										.replace(/(\d+),/, (match, day) => {
											// Add ordinal suffix to day
											const n = Number.parseInt(day, 10);
											const s = ["th", "st", "nd", "rd"],
												v = n % 100;
											const ordinal =
												s[(v - 20) % 10] ||
												s[v] ||
												s[0];
											return ` ${n}${ordinal},`;
										})}
								</span>
							</div>
						</div>
					)}

					{property.updatedAt && (
						<div>
							<HeadingText text="Last Updated" />
							<div className="flex items-center gap-2">
								<IconCalendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{new Date(property.updatedAt)
										.toLocaleString("en-US", {
											month: "long",
											day: "numeric",
											year: "numeric",
											hour: "numeric",
											minute: "2-digit",
											hour12: true,
										})
										.replace(/(\d+),/, (match, day) => {
											// Add ordinal suffix to day
											const n = Number.parseInt(day, 10);
											const s = ["th", "st", "nd", "rd"],
												v = n % 100;
											const ordinal =
												s[(v - 20) % 10] ||
												s[v] ||
												s[0];
											return ` ${n}${ordinal},`;
										})}
								</span>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
} 