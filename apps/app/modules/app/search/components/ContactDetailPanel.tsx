import { ContactAvatar } from "@shared/components/ContactAvatar";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconBriefcase,
	IconBuilding,
	IconCalendar,
	IconMail,
	IconPhone,
	IconUser,
} from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import type { ContactSearchResultDetails } from "../types";

interface ContactDetailPanelProps {
	contact: ContactSearchResultDetails;
	onNavigate?: () => void;
}

const HeadingText = ({ text }: { text: string }) => {
	return <h4 className="text-xs text-muted-foreground mb-1 cursor-default">{text}</h4>;
};

export function ContactDetailPanel({
	contact,
	onNavigate,
}: ContactDetailPanelProps) {
	const router = useRouter();

	const handleOpenContact = () => {
		onNavigate?.();
		router.push(contact.url);
	};

	return (
		<div
			className="w-80 border-l border-border bg-secondary/20"
			style={{
				height: "450px",
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* Fixed Header */}
			<div className="flex-shrink-0 p-4 border-b border-border">
				<div className="flex items-center gap-2">
					<div>
						<ContactAvatar
							name={contact.name}
							avatarUrl={contact.avatarUrl}
							className="h-8 w-8"
						/>
					</div>
					<div className="flex flex-col">
						<span className="font-medium text-sm">
							{contact.name}
						</span>
						{contact.jobTitle && (
							<span className="text-xs text-muted-foreground">
								{contact.jobTitle}
							</span>
						)}
					</div>
				</div>
			</div>

			{/* Scrollable Content */}
			<div
				className="p-4"
				style={{
					flex: 1,
					overflowY: "auto",
					minHeight: 0,
					maxHeight: "calc(450px - 80px - 30px)",
				}}
			>
				<div className="space-y-3">
					{contact.email && (
						<div>
							<HeadingText text="Email" />
							<div className="flex items-center gap-2">
								<IconMail className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">{contact.email}</span>
							</div>
						</div>
					)}

					{contact.phone && (
						<div>
							<HeadingText text="Phone" />
							<div className="flex items-center gap-2">
								<IconPhone className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">{contact.phone}</span>
							</div>
						</div>
					)}

					{contact.jobTitle && (
						<div>
							<HeadingText text="Job Title" />
							<div className="flex items-center gap-2">
								<IconBriefcase className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{contact.jobTitle}
								</span>
							</div>
						</div>
					)}

					{contact.company && (
						<div>
							<HeadingText text="Company" />
							<div className="flex items-center gap-2">
								<IconBuilding className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{contact.company.name}
								</span>
							</div>
						</div>
					)}

					{contact.status && (
						<div>
							<HeadingText text="Status" />
							<div className="flex items-center gap-2">
								<IconUser className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm capitalize">
									{contact.status}
								</span>
							</div>
						</div>
					)}

					{contact.description && (
						<div>
							<HeadingText text="Description" />
							<p className="text-sm">{contact.description}</p>
						</div>
					)}

					{contact.createdByName && (
						<div>
							<HeadingText text="Created by" />
							<div className="flex items-center gap-2">
								<UserAvatar
									className="h-6 w-6"
									name={contact.createdByName}
									avatarUrl={contact.createdByAvatarUrl}
								/>
								<span className="text-sm">
									{contact.createdByName}
								</span>
							</div>
						</div>
					)}

					{contact.createdAt && (
						<div>
							<HeadingText text="Created" />
							<div className="flex items-center gap-2">
								<IconCalendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{new Date(contact.createdAt)
										.toLocaleString("en-US", {
											month: "long",
											day: "numeric",
											year: "numeric",
											hour: "numeric",
											minute: "2-digit",
											hour12: true,
										})
										.replace(/(\d+),/, (match, day) => {
											// Add ordinal suffix to day
											const n = Number.parseInt(day, 10);
											const s = ["th", "st", "nd", "rd"],
												v = n % 100;
											const ordinal =
												s[(v - 20) % 10] ||
												s[v] ||
												s[0];
											return ` ${n}${ordinal},`;
										})}
								</span>
							</div>
						</div>
					)}

					{contact.updatedAt && (
						<div>
							<HeadingText text="Last Updated" />
							<div className="flex items-center gap-2">
								<IconCalendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{new Date(contact.updatedAt)
										.toLocaleString("en-US", {
											month: "long",
											day: "numeric",
											year: "numeric",
											hour: "numeric",
											minute: "2-digit",
											hour12: true,
										})
										.replace(/(\d+),/, (match, day) => {
											// Add ordinal suffix to day
											const n = Number.parseInt(day, 10);
											const s = ["th", "st", "nd", "rd"],
												v = n % 100;
											const ordinal =
												s[(v - 20) % 10] ||
												s[v] ||
												s[0];
											return ` ${n}${ordinal},`;
										})}
								</span>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
