import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";

export interface SaleHistory {
	id: string;
	propertyId: string;
	organizationId: string;
	seller?: string | null;
	buyer?: string | null;
	saleDate?: Date | null;
	salePrice?: number | null;
	askingPrice?: number | null;
	transactionType?: string | null;
	pricePerSquareFoot?: number | null;
	pricePerUnit?: number | null;
	transferredOwnershipPercentage?: number | null;
	capRate?: number | null;
	grmRate?: number | null;
	createdAt: Date;
	updatedAt: Date;
	property: {
		id: string;
		name: string;
		propertyType?: string | null;
		market?: string | null;
		subMarket?: string | null;
		status?: string | null;
	};
}

export interface CreateSaleHistoryData {
	organizationId: string;
	propertyId: string;
	seller?: string;
	buyer?: string;
	saleDate?: Date;
	salePrice?: number;
	askingPrice?: number;
	transactionType?: string;
	pricePerSquareFoot?: number;
	pricePerUnit?: number;
	transferredOwnershipPercentage?: number;
	capRate?: number;
	grmRate?: number;
}

export interface UpdateSaleHistoryData {
	seller?: string;
	buyer?: string;
	saleDate?: Date;
	salePrice?: number;
	askingPrice?: number;
	transactionType?: string;
	pricePerSquareFoot?: number;
	pricePerUnit?: number;
	transferredOwnershipPercentage?: number;
	capRate?: number;
	grmRate?: number;
}

export async function fetchSaleHistory(organizationId: string): Promise<SaleHistory[]> {
	const res = await fetch(`/api/sale-history?organizationId=${organizationId}`);
	if (!res.ok) {
		throw new Error("Failed to fetch sale history");
	}
	return res.json();
}

export async function createSaleHistory(data: CreateSaleHistoryData): Promise<SaleHistory> {
	const res = await fetch("/api/sale-history", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create sale history");
	}
	return res.json();
}

export async function updateSaleHistory(id: string, data: UpdateSaleHistoryData): Promise<SaleHistory> {
	const res = await fetch(`/api/sale-history/${id}`, {
		method: "PUT",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update sale history");
	}
	return res.json();
}

export async function deleteSaleHistory(id: string): Promise<{ message: string }> {
	const res = await fetch(`/api/sale-history/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete sale history");
	}
	return res.json();
}

export function useSaleHistory(organizationId: string | undefined) {
	return useQuery<SaleHistory[]>({
		queryKey: ["sale-history", organizationId],
		queryFn: () =>
			organizationId
				? fetchSaleHistory(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		refetchOnWindowFocus: false,
		refetchOnMount: false,
	});
}

export function useCreateSaleHistory() {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: createSaleHistory,
		onSuccess: (data) => {
			// Invalidate and refetch sale history
			queryClient.invalidateQueries({
				queryKey: ["sale-history", data.organizationId],
			});
		},
	});
}

export function useUpdateSaleHistory() {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: ({ id, data }: { id: string; data: UpdateSaleHistoryData }) =>
			updateSaleHistory(id, data),
		onSuccess: (data) => {
			// Invalidate and refetch sale history
			queryClient.invalidateQueries({
				queryKey: ["sale-history", data.organizationId],
			});
		},
	});
}

export function useDeleteSaleHistory() {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: ({ id, organizationId }: { id: string; organizationId: string }) =>
			deleteSaleHistory(id),
		onSuccess: (_, variables) => {
			// Invalidate and refetch sale history
			queryClient.invalidateQueries({
				queryKey: ["sale-history", variables.organizationId],
			});
		},
	});
} 