import { useSession } from "@app/auth/hooks/use-session";
import { TASK_PRIORITY, TASK_STATUS } from "@app/shared/lib/constants";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import * as React from "react";

interface BottomBarProps {
	selectedTasks?: any[];
	onDeselectAll?: () => void;
}

const BottomBar = ({ selectedTasks, onDeselectAll }: BottomBarProps) => {
	const { user } = useSession();

	if (!selectedTasks || selectedTasks.length === 0) return null;

	return (
		<div className="fixed bottom-5 left-1/2 transform -translate-x-1/2 app-bg/75 border border-muted rounded-xl shadow-lg p-1 pl-4 flex justify-between items-center w-11/12 max-w-3xl h-12 backdrop-blur-xl z-50">
			<div className="flex items-center">
				<span className="rounded-sm bg-zinc-100 dark:bg-sidebar px-1 py-0 text-xs font-medium border border-input font-mono mr-2">
					{selectedTasks.length}
				</span>
				<span className="text-xs text-muted-foreground">selected</span>
			</div>
			<div className="flex items-center space-x-1">
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="outline"
							className="rounded-lg"
							size="sm"
						>
							Priority
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="rounded-xl space-y-1 mb-4 -mr-4"
						align="end"
					>
						{TASK_PRIORITY.map((priority) => (
							<DropdownMenuItem
								key={priority.value}
								className="rounded-lg flex items-center"
								onClick={() => {
									console.warn("Priority", priority);
									// TODO: Create api call for updating task priority
									// updateBatchTasks({
									//   taskIds: selectedTasks?.map((task) => task?.id) as Id<"tasks">[],
									//   priority: priority.value
									// })
								}}
							>
								{React.createElement(priority.icon, {
									className: `h-3 w-3 mr-2 ${priority.color}`,
								})}
								{priority.label}
							</DropdownMenuItem>
						))}
					</DropdownMenuContent>
				</DropdownMenu>

				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="outline"
							className="rounded-lg"
							size="sm"
						>
							Status
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="rounded-xl space-y-1 mb-4 -mr-4"
						align="end"
					>
						{TASK_STATUS.map((status) => (
							<DropdownMenuItem
								key={status.value}
								className="rounded-lg flex items-center"
								onClick={() => {
									console.warn("Status", status);
									// TODO: Create api call for updating task status
									// updateBatchTasks({
									//   taskIds: selectedTasks?.map((task) => task?.id) as Id<"tasks">[],
									//   status: status.value
									// });
								}}
							>
								{React.createElement(status.icon, {
									className: `h-3 w-3 mr-2 ${status.color}`,
								})}
								{status.label}
							</DropdownMenuItem>
						))}
					</DropdownMenuContent>
				</DropdownMenu>

				{/* TODO: Add once we create api call for getting organization users */}
				{/* <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className='rounded-lg'
              size="sm"
            >
              Assign
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className='rounded-xl space-y-1 mb-4 -mr-4' align="end">
            {users?.map((user: User) => {
              return (
                <DropdownMenuItem
                  key={user.id}
                  className='rounded-lg flex items-center'
                  onClick={() => {
                    updateBatchTasks({
                      taskIds: selectedTasks?.map((task) => task?.id) as Id<"tasks">[],
                      assignee: user.id
                    });
                  }}
              >
                <Avatar className={'h-5 w-5'}>
                  <AvatarImage src={user.image} alt={user.name} />
                  <AvatarFallback className={'text-[8px]'}>{user.name?.charAt(0)}</AvatarFallback>
                </Avatar>
                {user.name}
              </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu> */}

				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="outline"
							className="rounded-lg"
							size="sm"
						>
							More
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="rounded-xl space-y-1 mb-4 -mr-4"
						align="end"
					>
						<DropdownMenuItem
							className="rounded-lg flex items-center"
							onClick={onDeselectAll}
						>
							Deselect All
						</DropdownMenuItem>
						<DropdownMenuItem
							className="rounded-lg !bg-destructive hover:!bg-red-800 text-white hover:text-white"
							onClick={() => {
								console.warn("Delete", selectedTasks);
								// TODO: Create api call for deleting tasks
								// updateBatchTasks({
								//   taskIds: selectedTasks?.map((task) => task?.id) as Id<"tasks">[],
								//   isDelete: true
								// })
							}}
						>
							Delete {selectedTasks?.length} task
							{selectedTasks && selectedTasks?.length > 1
								? "s"
								: ""}
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</div>
		</div>
	);
};

export default BottomBar;
