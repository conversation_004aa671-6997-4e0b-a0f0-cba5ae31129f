import {
	fetchFavoriteFolders,
	fetchFavorites,
	useUpdateFavorite,
} from "@app/favorites/lib/api";
import {
	TASK_PRIORITY,
	TASK_STATUS,
	type TaskPriority,
	type TaskStatus,
} from "@app/shared/lib/constants";
import {
	IconAntennaBars5,
	IconEdit,
	IconFolder,
	IconProgress,
	IconSquareRoundedCheckFilled,
	IconStar,
	IconStarFilled,
	IconStarOff,
	IconTrash,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { CustomContextMenu } from "@ui/components/context-menu";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import React from "react";
import { toast } from "sonner";

interface TaskContextMenuProps {
	children: React.ReactNode;
	task: any;
	isFavorite: boolean;
	organizationId?: string;
	onEdit: (e: React.MouseEvent, taskId: string) => void;
	onFavorite: (e: React.MouseEvent, taskId: string) => void;
	onStatusChange?: (taskId: string, newStatus: TaskStatus) => void;
	onPriorityChange?: (taskId: string, newPriority: TaskPriority) => void;
	onDelete: (e: React.MouseEvent, taskId: string) => void;
}

export function TaskContextMenu({
	children,
	task,
	isFavorite,
	organizationId,
	onEdit,
	onFavorite,
	onStatusChange,
	onPriorityChange,
	onDelete,
	asChild = false,
}: TaskContextMenuProps & { asChild?: boolean }) {
	// Fetch favorite folders and current favorite for this task
	const {
		data: favoriteFolders = [],
		isLoading: foldersLoading,
		error: foldersError,
	} = useQuery({
		queryKey: ["favoriteFolders", organizationId],
		queryFn: () => {
			if (!organizationId) {
				console.warn(
					"No organizationId provided for fetching favorite folders",
				);
				return Promise.resolve([]);
			}
			return fetchFavoriteFolders(organizationId);
		},
		enabled: !!organizationId && isFavorite,
	});

	const {
		data: favorites = [],
		isLoading: favoritesLoading,
		error: favoritesError,
	} = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () => {
			if (!organizationId) {
				console.warn(
					"No organizationId provided for fetching favorites",
				);
				return Promise.resolve([]);
			}
			return fetchFavorites(organizationId);
		},
		enabled: !!organizationId && isFavorite,
	});

	const updateFavoriteMutation = useUpdateFavorite(organizationId);

	const currentFavorite = favorites.find(
		(f: any) => f.objectId === task.id && f.objectType === "task",
	);
	const currentFolderId = currentFavorite?.folderId;

	function handleMoveToFolder(folderId: string | null) {
		if (!currentFavorite) {
			console.error("No current favorite found for task:", task.id);
			toast.error("Favorite not found");
			return;
		}

		const payload = {
			id: currentFavorite.id,
			folderId,
		};

		updateFavoriteMutation.mutate(payload, {
			onSuccess: () => {
				toast.success("Favorite moved to folder");
			},
			onError: (error) => {
				console.error("Failed to move favorite to folder:", error);
				toast.error("Failed to move favorite to folder");
			},
		});
	}

	const favoriteMenuItems = () => {
		if (!isFavorite) {
			return [
				{
					id: "favorite",
					label: "Add to favorites",
					icon: IconStar,
					onClick: (e: any) => onFavorite(e, task.id),
				},
			];
		}

		const items = [];

		const moveToFolderItems: any[] = [
			{
				id: "move-favorite-none",
				label: "No folder",
				icon: undefined,
				onClick: () => handleMoveToFolder(null),
				indicator: {
					show: !currentFolderId,
					color: "bg-blue-500",
				},
			},
		];

		// Add existing folders if any
		if (favoriteFolders.length > 0) {
			moveToFolderItems.push(
				...favoriteFolders.map((folder: any) => ({
					id: `move-favorite-folder-${folder.id}`,
					label: folder.name,
					icon: IconFolder,
					onClick: () => handleMoveToFolder(folder.id),
					indicator: {
						show: currentFolderId === folder.id,
						color: "bg-blue-500",
					},
				})),
			);
		}

		items.push({
			id: "move-favorite",
			label: "Move favorite to",
			icon: IconStar,
			items: moveToFolderItems,
		});

		items.push({
			id: "favorite",
			label: "Remove from favorites",
			icon: IconStarOff,
			onClick: (e: any) => onFavorite(e, task.id),
		});

		return items;
	};

	const menuItems: (any | "separator")[] = [
		{
			id: "edit",
			label: "Edit",
			icon: IconEdit,
			onClick: (e: any) => onEdit(e, task.id),
		},
		...favoriteMenuItems(),
		"separator",
		{
			id: "status",
			label: "Status",
			icon: IconProgress,
			indicator: {
				show: true,
				color: TASK_STATUS.find((s) => s.value === task.status)?.color,
			},
			items: TASK_STATUS.map((status) => ({
				id: `status-${status.value}`,
				label: status.label,
				icon: status.icon,
				iconClassName: status.color,
				onClick: () => onStatusChange?.(task.id, status.value),
				indicator: {
					show: task.status === status.value,
					color: status.color,
				},
			})),
		},
		{
			id: "priority",
			label: "Priority",
			icon: IconAntennaBars5,
			indicator: {
				show: task.priority && task.priority !== "no_priority",
				color: TASK_PRIORITY.find((p) => p.value === task.priority)
					?.color,
			},
			items: TASK_PRIORITY.map((priority) => ({
				id: `priority-${priority.value}`,
				label: priority.label,
				icon: priority.icon,
				iconClassName: priority.color,
				onClick: () => onPriorityChange?.(task.id, priority.value),
				indicator: {
					show: task.priority === priority.value,
					color: priority.color,
				},
			})),
		},
		"separator",
		{
			id: "delete",
			label: "Delete task",
			icon: IconTrash,
			iconClassName: "text-red-400",
			onClick: (e: any) => onDelete(e, task.id),
		},
	].filter(Boolean);

	return <CustomContextMenu items={menuItems}>{children}</CustomContextMenu>;
}
