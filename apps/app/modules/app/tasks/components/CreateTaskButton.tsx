"use client";

import { useK<PERSON>ban } from "@app/tasks/lib/kanban-provider";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import { IconPlus } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

interface CreateTaskButtonProps {
	icon?: boolean;
	className?: string;
}

export function CreateTaskButton({
	icon = true,
	className,
}: CreateTaskButtonProps) {
	const { openCreateTask } = useTasks();
	let hoveredColumn = null;

	try {
		const kanbanContext = useKanban();
		hoveredColumn = kanbanContext.hoveredColumn;
	} catch (e) {
		console.error("Not in Kanban context", e);
	}

	const handleClick = () => {
		openCreateTask(hoveredColumn || undefined);
	};

	return (
		<>
			{icon ? (
				<Button
					size="icon"
					className={cn(
						"transition group h-7 w-7 text-xs rounded-sm flex gap-1.5 items-center border !border-border !bg-muted/50 hover:!border hover:!border-border hover:!bg-muted/70",
					)}
					onClick={handleClick}
				>
					<IconPlus className="size-3 shrink-0 transition-all text-muted-foreground group-hover:text-primary" />
				</Button>
			) : (
				<Button
					variant="outline"
					onClick={handleClick}
					className="bg-blue-500 hover:bg-blue-600 !border !border-blue-500 text-white !py-1 px-2 rounded-lg w-fit !h-7"
				>
					Create Task
				</Button>
			)}
		</>
	);
}
