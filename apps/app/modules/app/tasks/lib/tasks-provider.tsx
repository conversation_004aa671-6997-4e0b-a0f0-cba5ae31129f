"use client";

import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import type { TaskStatus } from "@app/shared/lib/constants";
import { CreateTaskModal } from "@app/tasks/components/CreateTaskModal";
import type { Task } from "@repo/database/src/zod";
import * as React from "react";

interface CreateTaskOptions {
	defaultStatus?: TaskStatus;
	relatedObject?: {
		id: string;
		name: string;
		recordType: "contact" | "company" | "property";
		subtitle?: string;
	};
}

interface TasksContextType {
	openCreateTask: (optionsOrStatus?: TaskStatus | CreateTaskOptions) => void;
	openEditTask: (task: Task) => void;
}

const TasksContext = React.createContext<TasksContextType | undefined>(
	undefined,
);

export function useTasks() {
	const context = React.useContext(TasksContext);
	if (!context) {
		throw new Error("useTasks must be used within a TasksProvider");
	}
	return context;
}

export function TasksProvider({ children }: { children: React.ReactNode }) {
	const [isCreateModalOpen, setIsCreateModalOpen] = React.useState(false);
	const [defaultStatus, setDefaultStatus] = React.useState<
		TaskStatus | undefined
	>();
	const [defaultRelatedObject, setDefaultRelatedObject] = React.useState<{
		id: string;
		name: string;
		recordType: "contact" | "company" | "property";
		subtitle?: string;
	} | undefined>();
	const [taskToEdit, setTaskToEdit] = React.useState<Task | undefined>(
		undefined,
	);

	useHotkeys([
		{
			key: "t",
			callback: () => {
				if (!isCreateModalOpen) {
					setIsCreateModalOpen(true);
				}
			},
			ignoreInputs: true,
		},
	]);

	const openCreateTask = React.useCallback((optionsOrStatus?: TaskStatus | CreateTaskOptions) => {
		setTaskToEdit(undefined);
		
		// Handle backward compatibility - if it's a string, treat it as status
		if (typeof optionsOrStatus === "string") {
			setDefaultStatus(optionsOrStatus);
			setDefaultRelatedObject(undefined);
		} else if (optionsOrStatus) {
			setDefaultStatus(optionsOrStatus.defaultStatus);
			setDefaultRelatedObject(optionsOrStatus.relatedObject);
		} else {
			setDefaultStatus(undefined);
			setDefaultRelatedObject(undefined);
		}
		
		setIsCreateModalOpen(true);
	}, []);

	const openEditTask = React.useCallback((task: Task) => {
		setTaskToEdit(task);
		setIsCreateModalOpen(true);
	}, []);

	const handleModalOpenChange = (open: boolean) => {
		setIsCreateModalOpen(open);
		if (!open) {
			setDefaultStatus(undefined);
			setDefaultRelatedObject(undefined);
			setTaskToEdit(undefined);
		}
	};

	return (
		<TasksContext.Provider value={{ openCreateTask, openEditTask }}>
			{children}
			<CreateTaskModal
				open={isCreateModalOpen}
				onOpenChange={handleModalOpenChange}
				defaultStatus={defaultStatus}
				relatedObject={defaultRelatedObject}
				taskToEdit={taskToEdit}
			/>
		</TasksContext.Provider>
	);
}
