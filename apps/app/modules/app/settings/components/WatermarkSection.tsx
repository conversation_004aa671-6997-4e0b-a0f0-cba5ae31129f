"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { usePurchases } from "@app/payments/hooks/purchases";
import { SettingsItem } from "@app/shared/components/SettingsItem";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Switch } from "@ui/components/switch";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// API functions
async function getWatermarkSettings(organizationId: string) {
	const response = await fetch(`/api/organizations/watermark/${organizationId}`);
	if (!response.ok) {
		throw new Error('Failed to fetch watermark settings');
	}
	return response.json();
}

async function updateWatermarkSettings(organizationId: string, enabled: boolean) {
	const response = await fetch('/api/organizations/watermark', {
		method: 'PUT',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({ organizationId, enabled }),
	});
	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.message || 'Failed to update watermark settings');
	}
	return response.json();
}

export function WatermarkSection() {
	const { activeOrganization } = useActiveOrganization();
	const { activePlan } = usePurchases(activeOrganization?.id);
	const queryClient = useQueryClient();
	const router = useRouter();

	// Determine if organization is on a free plan
	const isFreePlan = !activePlan || activePlan.id === "free";

	// Fetch watermark settings
	const { data: settings, isLoading } = useQuery({
		queryKey: ['watermark-settings', activeOrganization?.id],
		queryFn: () => getWatermarkSettings(activeOrganization!.id),
		enabled: !!activeOrganization?.id,
	});

	// Update mutation
	const updateMutation = useMutation({
		mutationFn: ({ enabled }: { enabled: boolean }) => 
			updateWatermarkSettings(activeOrganization!.id, enabled),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ['watermark-settings', activeOrganization?.id]
			});
			toast.success('Watermark settings updated');
		},
		onError: (error: Error) => {
			toast.error(error.message);
		},
	});

	const handleToggle = (enabled: boolean) => {
		if (!enabled && isFreePlan) {
			// Prevent disabling on free plan, but the switch itself should be disabled
			return;
		}
		updateMutation.mutate({ enabled });
	};

	const handleUpgradeClick = () => {
		router.push(`/app/${activeOrganization?.slug}/settings/organization/billing`);
	};

	if (!activeOrganization || isLoading) {
		return (
			<SettingsItem
				title="Relio watermark"
				description="Loading..."
			>
				<div className="flex items-center justify-between">
					<div className="flex-1" />
					<Switch disabled checked={true} />
				</div>
			</SettingsItem>
		);
	}

	return (
		<div className="space-y-4">
			<h3 className="text-lg font-semibold">Relio watermark</h3>
			<div className="rounded-2xl bg-transparent border border-border p-4">
				<div className="flex items-center justify-between gap-4">
					<div className="flex-1 space-y-1">
						<div className="text-sm text-foreground">
							"Sent with Relio" will be added to the end of emails you send from Relio.
						</div>
						<div className="text-xs text-muted-foreground">
							{isFreePlan ? (
								<>
									You can remove the watermark by{" "}
									<button 
										onClick={handleUpgradeClick}
										className="text-foreground hover:underline font-medium"
									>
										upgrading
									</button>{" "}
									now.
								</>
							) : (
								"You can toggle this setting since you're on a paid plan."
							)}
						</div>
					</div>
					<Switch
						checked={settings?.enabled ?? true}
						onCheckedChange={handleToggle}
						disabled={isFreePlan || updateMutation.isPending}
					/>
				</div>
			</div>
		</div>
	);
} 