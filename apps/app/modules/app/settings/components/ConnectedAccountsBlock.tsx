"use client";
import {
	type OAuthProvider,
	oAuthProviders,
} from "@app/auth/constants/oauth-providers";
import { SettingsItem } from "@app/shared/components/SettingsItem";
import { authClient } from "@repo/auth/client";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { ExternalLinkIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export function ConnectedAccountsBlock() {
	const t = useTranslations();

	const { data, isPending } = useQuery({
		queryKey: ["userAccounts"],
		queryFn: async () => {
			const { data, error } = await authClient.listAccounts();

			if (error) {
				throw error;
			}

			return data;
		},
	});

	const isProviderLinked = (provider: OAuthProvider) =>
		data?.some((account) => account.provider === provider);

	const linkProvider = (provider: OAuthProvider) => {
		const callbackURL = window.location.href;
		if (!isProviderLinked(provider)) {
			authClient.linkSocial({
				provider,
				callbackURL,
			});
		}
	};

	return (
		<SettingsItem
			title={t("settings.account.security.connectedAccounts.title")}
			description="We take your privacy very seriously. Read our Privacy Policy"
		>
			<div className="grid grid-cols-1 gap-4">
				{Object.entries(oAuthProviders).map(
					([provider, providerData]) => {
						const isLinked = isProviderLinked(
							provider as OAuthProvider,
						);

						return (
							<div
								key={provider}
								className={cn(
									"flex items-center justify-between gap-4 rounded-lg border p-4",
									"bg-background/50 hover:bg-background/80",
								)}
							>
								<div className="flex items-center gap-3">
									<providerData.icon className="size-5" />
									<span className="font-medium">
										{t(
											"settings.account.security.connectedAccounts.connectAccount",
											{
												providerName: providerData.name,
											},
										)}
									</span>
								</div>
								{isPending ? (
									<Skeleton className="h-9 w-28" />
								) : (
									<Button
										variant="secondary"
										size="sm"
										className="gap-1.5"
										onClick={() =>
											linkProvider(
												provider as OAuthProvider,
											)
										}
									>
										<ExternalLinkIcon className="size-4" />
										{isLinked ? "Connected" : "Connect"}
									</Button>
								)}
							</div>
						);
					},
				)}
			</div>
		</SettingsItem>
	);
}
