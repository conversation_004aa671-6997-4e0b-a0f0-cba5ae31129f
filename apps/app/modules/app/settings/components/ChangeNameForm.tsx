"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { SettingsItem } from "@app/shared/components/SettingsItem";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
	name: z.string().min(3),
});

type FormSchema = z.infer<typeof formSchema>;

export function ChangeNameForm() {
	const { user, reloadSession } = useSession();
	const [submitting, setSubmitting] = useState(false);
	const t = useTranslations();

	const form = useForm<FormSchema>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: user?.name ?? "",
		},
	});

	const onSubmit = form.handleSubmit(async ({ name }) => {
		setSubmitting(true);

		await authClient.updateUser(
			{ name },
			{
				onSuccess: () => {
					toast.success(
						t("settings.account.changeName.notifications.success"),
					);

					reloadSession();
					form.reset({
						name,
					});
				},
				onError: () => {
					toast.error(
						t("settings.account.changeName.notifications.error"),
					);
				},
				onResponse: () => {
					setSubmitting(false);
				},
			},
		);
	});

	return (
		<SettingsItem title={t("settings.account.changeName.title")}>
			<form onSubmit={onSubmit} className="flex items-center gap-2 !w-full">
				<Input type="text" {...form.register("name")} className="!w-full" />
				{form.formState.dirtyFields.name && (
					<div className="flex justify-end">
						<Button
							type="submit"
							loading={submitting}
							disabled={
								!(
									form.formState.isValid &&
									form.formState.dirtyFields.name
								)
							}
						>
							{t("settings.save")}
						</Button>
					</div>
				)}
			</form>
		</SettingsItem>
	);
}
