"use client";

import { useCustomFieldDefinitions } from "@app/custom-field-definitions/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	type DragStartEvent,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { EllipsisDropdown } from "@shared/components/EllipsisDropdown";
import {
	IconChevronLeft,
	IconChevronRight,
	IconColumns3,
	IconEye,
	IconEyeOff,
	IconGripVertical,
	IconPlus,
	IconSquareRoundedCheckFilled,
	IconTag,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import { Switch } from "@ui/components/switch";
import { cn } from "@ui/lib";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import {
	AVAILABLE_ATTRIBUTES,
	getFieldIcon,
} from "./constants/available-attributes";
import { ObjectType } from "@repo/database";

interface KanbanViewSettingsProps {
	view: {
		id: string;
		name: string;
		viewType?: string;
		statusAttribute?: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		cardRowFields?: Array<{
			field: string;
			headerName: string;
			type?: string;
		}>;
		showAttributeLabels?: boolean;
		kanbanConfig?: {
			customStatuses?: Array<{
				label: string;
				value: string;
				color: string;
				trackTime?: boolean;
				showConfetti?: boolean;
				targetTime?: number;
				targetTimeUnit?: string;
			}>;
			hiddenColumns?: string[];
		};
	};
	objectType: ObjectType;
	onUpdateView?: (updatedView: any) => void;
	showAttributeLabels?: boolean;
	onShowAttributeLabelsChange?: (show: boolean) => void;
	setAddColumnOpen: (open: boolean) => void;
}

// Sortable card row field item component for kanban view
function SortableCardRowFieldItem({
	field,
	index,
	onRemoveField,
	objectType,
}: {
	field: any;
	index: number;
	onRemoveField: (fieldName: string) => void;
	objectType: ObjectType;
}) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: field.field,
		data: {
			type: "cardRowField",
			field,
			index,
		},
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={cn(
				"group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border",
				isDragging && "opacity-50",
			)}
		>
			<div
				{...attributes}
				{...listeners}
				className="cursor-grab active:cursor-grabbing"
			>
				<IconGripVertical className="h-3 w-3 text-muted-foreground" />
			</div>
			<div className="flex items-center gap-2 flex-1 min-w-0">
				{(() => {
					const FieldIcon = getFieldIcon(field.field, objectType);
					return (
						<FieldIcon className="h-4 w-4 text-muted-foreground" />
					);
				})()}

				<span className="text-sm truncate cursor-default">
					{field.headerName}
				</span>
			</div>
			<EllipsisDropdown className="p-1">
				<EllipsisDropdown.Item className="gap-1">
					<IconTag className="h-3 w-3" />
					Change attribute title
				</EllipsisDropdown.Item>
				<EllipsisDropdown.Item
					className="gap-1"
					onClick={() => onRemoveField(field.field)}
				>
					<IconEyeOff className="h-3 w-3" />
					Hide attribute
				</EllipsisDropdown.Item>
			</EllipsisDropdown>
		</div>
	);
}

export const KanbanViewSettings = ({
	view,
	objectType,
	onUpdateView,
	showAttributeLabels = true,
	onShowAttributeLabelsChange,
	setAddColumnOpen,
}: KanbanViewSettingsProps) => {
	const [searchQuery, setSearchQuery] = useState("");
	const [activeId, setActiveId] = useState<string | null>(null);

	// Kanban-specific state
	const [kanbanScreen, setKanbanScreen] = useState<
		"main" | "groupBy" | "visibleColumns" | "addCardRow"
	>("main");

	const queryClient = useQueryClient();
	const { activeOrganization } = useActiveOrganization();

	// Fetch custom field definitions for this object type
	const { data: customFieldDefinitions = [] } = useCustomFieldDefinitions(
		objectType,
		activeOrganization?.id,
		!!activeOrganization?.id, // Only fetch when we have an organization
	);

	// DnD sensors
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
	);

	// Mutation to update view columns and card row fields
	const updateViewMutation = useMutation({
		mutationFn: async (updates: {
			columnDefs?: any[];
			cardRowFields?: any[];
			showAttributeLabels?: boolean;
			kanbanConfig?: any;
		}) => {
			if (!view?.id) return;

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify(updates),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update view");
			}

			return response.json();
		},
		onMutate: async (updates) => {
			if (!view?.id || !activeOrganization?.id) return;

			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["objectView", view.id],
			});
			await queryClient.cancelQueries({
				queryKey: ["objectViews", activeOrganization.id, objectType],
			});

			// Snapshot the previous values
			const previousView = queryClient.getQueryData([
				"objectView",
				view.id,
			]);
			const previousViews = queryClient.getQueryData([
				"objectViews",
				activeOrganization.id,
				objectType,
			]);

			// Optimistically update the single view
			queryClient.setQueryData(["objectView", view.id], (old: any) => {
				if (!old) return old;
				return {
					...old,
					...updates,
					updatedAt: new Date().toISOString(),
				};
			});

			// Optimistically update the views list
			queryClient.setQueryData(
				["objectViews", activeOrganization.id, objectType],
				(old: any[]) => {
					if (!old || !Array.isArray(old)) return old;
					return old.map((v: any) =>
						v.id === view.id
							? {
									...v,
									...(updates.columnDefs && {
										columnDefs: [...updates.columnDefs],
									}), // Ensure new array reference
									...(updates.cardRowFields && {
										cardRowFields: [
											...updates.cardRowFields,
										],
									}),
									...(updates.kanbanConfig && {
										kanbanConfig: {
											...updates.kanbanConfig,
										},
									}),
									updatedAt: new Date().toISOString(),
									// Force a new reference by adding a cache invalidation key
									_cacheKey: Date.now(),
								}
							: v,
					);
				},
			);

			return { previousView, previousViews };
		},
		onSuccess: (data, variables, context) => {
			if (onUpdateView && data) {
				onUpdateView(data);
			}

			// Invalidate queries to ensure fresh data
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: [
						"objectViews",
						activeOrganization.id,
						objectType,
					],
				});
				// Also invalidate all views for this organization to be safe
				queryClient.invalidateQueries({
					queryKey: ["objectViews", activeOrganization.id],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}
		},
		onError: (error, variables, context) => {
			toast.error(`Failed to update view: ${error.message}`);

			// Rollback optimistic updates
			if (context?.previousView && view?.id) {
				queryClient.setQueryData(
					["objectView", view.id],
					context.previousView,
				);
			}
			if (context?.previousViews && activeOrganization?.id) {
				queryClient.setQueryData(
					["objectViews", activeOrganization.id, objectType],
					context.previousViews,
				);
			}
		},
	});

	const availableAttributes = AVAILABLE_ATTRIBUTES[objectType] || [];
	const currentCardRowFields = new Set(
		view?.cardRowFields?.map((field) => field.field) || [],
	);

	// Get available status fields for kanban grouping
	const availableStatusFields = useMemo(() => {
		return availableAttributes.filter(
			(attr) =>
				attr.field === "status" ||
				attr.field === "stage" ||
				attr.field.includes("status") ||
				attr.field.includes("stage"),
		);
	}, [availableAttributes]);

	// Get the custom field definition for the current status attribute
	const statusFieldDefinition = useMemo(() => {
		if (!view?.statusAttribute) return null;
		return customFieldDefinitions.find(
			(def) => def.name === view.statusAttribute && def.type === "select",
		);
	}, [view?.statusAttribute, customFieldDefinitions]);

	// Get visible kanban columns from the custom field definition
	const kanbanColumns = useMemo(() => {
		if (!statusFieldDefinition?.options?.choices)
			return [];
		return statusFieldDefinition.options.choices;
	}, [statusFieldDefinition]);

	// Get visible kanban columns count
	const visibleKanbanColumns = useMemo(() => {
		const hiddenColumns = new Set(view?.kanbanConfig?.hiddenColumns || []);
		const visibleColumns = kanbanColumns.filter(
			(col) => !hiddenColumns.has(col.value),
		);
		return visibleColumns.length || 1; // Default fallback
	}, [kanbanColumns, view?.kanbanConfig?.hiddenColumns]);

	// Filter available attributes for card rows (exclude already added card row fields)
	const filteredCardRowAttributes = useMemo(() => {
		return availableAttributes.filter((attr) => {
			const matchesSearch =
				attr.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
				attr.field.toLowerCase().includes(searchQuery.toLowerCase());
			const notAlreadyAdded = !currentCardRowFields.has(attr.field);
			return matchesSearch && notAlreadyAdded;
		});
	}, [availableAttributes, currentCardRowFields, searchQuery]);

	// Group filtered card row attributes by category
	const groupedCardRowAttributes = useMemo(() => {
		const groups: Record<string, typeof filteredCardRowAttributes> = {};
		filteredCardRowAttributes.forEach((attr) => {
			if (!groups[attr.category]) {
				groups[attr.category] = [];
			}
			groups[attr.category].push(attr);
		});
		return groups;
	}, [filteredCardRowAttributes]);

	// Handle status field change for kanban grouping
	const handleStatusFieldChange = (field: string) => {
		if (!view?.id || !onUpdateView) return;

		const updatedView = {
			...view,
			statusAttribute: field,
		};

		onUpdateView(updatedView);
		setKanbanScreen("main");
	};

	// Handle kanban column visibility toggle
	const handleKanbanColumnToggle = (
		columnValue: string,
		visible: boolean,
	) => {
		if (!view?.id) return;

		// Update the view's kanban config to track hidden columns
		const currentHiddenColumns = view.kanbanConfig?.hiddenColumns || [];
		let updatedHiddenColumns;

		if (visible) {
			// Remove from hidden columns
			updatedHiddenColumns = currentHiddenColumns.filter(
				(col: string) => col !== columnValue,
			);
		} else {
			// Add to hidden columns
			updatedHiddenColumns = [...currentHiddenColumns, columnValue];
		}

		const updatedKanbanConfig = {
			...view.kanbanConfig,
			hiddenColumns: updatedHiddenColumns,
		};

		// Persist to database using mutation
		updateViewMutation.mutate({
			kanbanConfig: updatedKanbanConfig,
		});

		toast.success(`Column ${visible ? "shown" : "hidden"}`);
	};

	// Handle adding card row field
	const handleAddCardRow = (field: string, label: string) => {
		if (!view?.id) return;

		const newCardRowField = {
			field,
			headerName: label,
		};

		const updatedCardRowFields = [
			...(view.cardRowFields || []),
			newCardRowField,
		];

		// Save to database
		updateViewMutation.mutate({ cardRowFields: updatedCardRowFields });

		setKanbanScreen("main");
		setSearchQuery("");
		toast.success(`Added "${label}" to card rows`);
	};

	// Handle show attribute labels change
	const handleShowAttributeLabelsChange = (show: boolean) => {
		if (!view?.id) return;

		// Save to database
		updateViewMutation.mutate({ showAttributeLabels: show });

		// Also call the prop callback to update parent state immediately
		onShowAttributeLabelsChange?.(show);

		toast.success(`Attribute labels ${show ? "shown" : "hidden"}`);
	};

	const handleRemoveCardRowField = (field: string) => {
		if (!view?.cardRowFields) return;

		const updatedCardRowFields = view.cardRowFields.filter(
			(f) => f.field !== field,
		);
		updateViewMutation.mutate({ cardRowFields: updatedCardRowFields });
		toast.success("Card row field removed successfully");
	};

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		setActiveId(null);

		if (!over) return;

		// Handle card row field reordering for kanban view
		if (
			active.data.current?.type === "cardRowField" &&
			over.data.current?.type === "cardRowField"
		) {
			if (!view?.cardRowFields) return;

			const oldIndex = view.cardRowFields.findIndex(
				(field) => field.field === active.id,
			);
			const newIndex = view.cardRowFields.findIndex(
				(field) => field.field === over.id,
			);

			if (oldIndex !== newIndex) {
				// Reorder the card row fields
				const reorderedFields = arrayMove(
					view.cardRowFields,
					oldIndex,
					newIndex,
				);
				updateViewMutation.mutate({ cardRowFields: reorderedFields });
				toast.success("Card row fields reordered successfully");
			}
			return;
		}
	};

	return (
		<div className="p-1">
			{kanbanScreen === "main" && (
				<>
					<div className="text-sm text-muted-foreground my-2 mx-2">
						View settings
					</div>

					{/* Grouped by option */}
					<div
						className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
						onClick={() => setKanbanScreen("groupBy")}
					>
						<IconColumns3 className="h-4 w-4 text-muted-foreground" />
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Grouped by{" "}
								{view?.statusAttribute
									? view.statusAttribute
											.charAt(0)
											.toUpperCase() +
										view.statusAttribute.slice(
											1,
										)
									: "Status"}
							</span>
						</div>
						<IconChevronRight className="h-4 w-4 text-muted-foreground" />
					</div>

					{/* Visible columns option */}
					<div
						className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
						onClick={() =>
							setKanbanScreen("visibleColumns")
						}
					>
						<IconEye className="h-4 w-4 text-muted-foreground" />
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Visible columns
							</span>
						</div>
						<div className="flex items-center gap-2 mr-1">
							<span className="text-xs text-muted-foreground">
								{visibleKanbanColumns}
							</span>
							<IconChevronRight className="h-4 w-4 text-muted-foreground" />
						</div>
					</div>

					{/* Show attribute labels option */}
					<div className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border">
						<IconTag className="h-4 w-4 text-muted-foreground" />
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Show attribute labels
							</span>
						</div>
						<Switch
							checked={showAttributeLabels}
							onCheckedChange={
								handleShowAttributeLabelsChange
							}
						/>
					</div>

					<Separator className="my-1" />

					{/* Visible attributes section */}
					{view?.cardRowFields &&
						view.cardRowFields.length > 0 && (
							<>
								<div className="text-xs text-muted-foreground my-2 mx-2">
									Visible attributes
								</div>

								<DndContext
									sensors={sensors}
									collisionDetection={
										closestCenter
									}
									onDragStart={handleDragStart}
									onDragEnd={handleDragEnd}
								>
									<SortableContext
										items={view.cardRowFields.map(
											(field) => field.field,
										)}
										strategy={
											verticalListSortingStrategy
										}
									>
										<div className="space-y-1">
											{view.cardRowFields.map(
												(field, index) => (
													<SortableCardRowFieldItem
														key={
															field.field
														}
														field={
															field
														}
														index={
															index
														}
														onRemoveField={
															handleRemoveCardRowField
														}
														objectType={
															objectType as ObjectType
														}
													/>
												),
											)}
										</div>
									</SortableContext>
								</DndContext>

								<Separator className="my-1" />
							</>
						)}

					{/* Add card row option */}
					<div
						className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
						onClick={() =>
							setKanbanScreen("addCardRow")
						}
					>
						<IconPlus className="h-4 w-4 text-muted-foreground" />
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Add card attribute
							</span>
						</div>
						<IconChevronRight className="h-4 w-4 text-muted-foreground" />
					</div>
				</>
			)}

			{kanbanScreen === "groupBy" && (
				<>
					<div className="flex items-center gap-2 mb-2 p-1">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setKanbanScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Choose status field
						</div>
					</div>

					<div className="p-1">
						<p className="text-sm text-muted-foreground mb-2 mx-1">
							Which pipeline would you like to base
							this kanban on? Your cards will be
							grouped by this pipeline's stages.
						</p>

						<div className="space-y-1">
							{availableStatusFields.map((field) => (
								<div
									key={field.field}
									className="group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
									onClick={() =>
										handleStatusFieldChange(
											field.field,
										)
									}
								>
									<span className="text-sm flex-1">
										{field.label}
									</span>
									{view?.statusAttribute ===
										field.field && (
										<IconSquareRoundedCheckFilled className="h-4 w-4 text-blue-400" />
									)}
								</div>
							))}
						</div>

						<Separator className="my-1" />

						<div
							className={cn(
								"group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer",
							)}
							onClick={() => setAddColumnOpen(true)}
							data-testid="add-column-button"
						>
							<div>
								<IconPlus className="h-4 w-4 text-muted-foreground" />
							</div>
							<div className="flex items-center gap-2 flex-1 min-w-0">
								<span className="text-sm truncate">
									Create new attribute
								</span>
							</div>
						</div>
					</div>
				</>
			)}

			{kanbanScreen === "visibleColumns" && (
				<>
					<div className="flex items-center gap-2 p-1">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setKanbanScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Visible columns
						</div>
					</div>

					<div className="p-1 space-y-1">
						{kanbanColumns.length ? (
							kanbanColumns.map((choice) => {
								const isVisible =
									!view?.kanbanConfig?.hiddenColumns?.includes(
										choice.value,
									);
								return (
									<div
										key={choice.value}
										className="flex h-8 items-center justify-between p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
									>
										<div className="flex items-center gap-2">
											<div
												className="w-3 h-3 rounded-sm"
												style={{
													backgroundColor:
														choice.color ||
														"#6b7280",
												}}
											/>
											<span className="text-sm">
												{choice.label}
											</span>
										</div>
										<Switch
											checked={isVisible}
											onCheckedChange={(
												checked,
											) =>
												handleKanbanColumnToggle(
													choice.value,
													checked,
												)
											}
										/>
									</div>
								);
							})
						) : (
							<div className="text-center py-8 text-muted-foreground">
								<p className="text-sm">
									No kanban columns configured
								</p>
								<p className="text-xs">
									Configure status field options
									to see columns
								</p>
							</div>
						)}
					</div>
				</>
			)}

			{kanbanScreen === "addCardRow" && (
				<div className="p-2">
					<div className="flex items-center gap-2 mb-2">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setKanbanScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Add card attribute
						</div>
					</div>

					<Input
						placeholder="Search attributes..."
						value={searchQuery}
						onChange={(e) =>
							setSearchQuery(e.target.value)
						}
						className="mb-4"
						autoFocus
					/>

					<div className="max-h-80 overflow-y-auto">
						{Object.entries(
							groupedCardRowAttributes,
						).map(([category, attributes]) => (
							<div key={category} className="mb-4">
								{attributes.length > 0 && (
									<>
										<div className="text-[10px] font-mono text-muted-foreground mb-2 px-2">
											{category}
										</div>
										<div className="space-y-1 w-full">
											{attributes.map(
												(attr) => {
													const Icon =
														attr.icon;
													return (
														<button
															key={
																attr.field
															}
															className="group w-full cursor-pointer flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border"
															onClick={() =>
																handleAddCardRow(
																	attr.field,
																	attr.label,
																)
															}
														>
															<Icon className="h-4 w-4 text-muted-foreground" />
															<span className="text-sm">
																{
																	attr.label
																}
															</span>
														</button>
													);
												},
											)}
										</div>
									</>
								)}
							</div>
						))}

						{Object.keys(groupedCardRowAttributes)
							.length === 0 && (
							<div className="text-center py-8 text-muted-foreground">
								<p className="text-sm">
									No attributes found
								</p>
								<p className="text-xs">
									Try a different search term
								</p>
							</div>
						)}
					</div>

					<Separator className="my-1" />

					<div
						className={cn(
							"group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer",
						)}
						onClick={() => setAddColumnOpen(true)}
						data-testid="add-column-button"
					>
						<div>
							<IconPlus className="h-4 w-4 text-muted-foreground" />
						</div>
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Create new attribute
							</span>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}; 