import { z } from "zod";

export const PROPERTY_TYPES = [
	{ label: "Residential", value: "residential" },
	{ label: "Commercial", value: "commercial" },
	{ label: "Industrial", value: "industrial" },
	{ label: "Land", value: "land" },
	{ label: "Mixed Use", value: "mixed_use" },
	{ label: "Multi-Family", value: "multi_family" },
	{ label: "Single Family", value: "single_family" },
	{ label: "Condo", value: "condo" },
	{ label: "Townhouse", value: "townhouse" },
	{ label: "Other", value: "other" },
];

export const PROPERTY_STATUSES = [
	{ label: "Active", value: "active" },
	{ label: "Under Contract", value: "under_contract" },
	{ label: "Pending", value: "pending" },
	{ label: "Sold", value: "sold" },
	{ label: "Off Market", value: "off_market" },
	{ label: "Coming Soon", value: "coming_soon" },
	{ label: "Withdrawn", value: "withdrawn" },
	{ label: "Expired", value: "expired" },
];

export const CONSTRUCTION_TYPES = [
	{ label: "Brick", value: "brick" },
	{ label: "Wood Frame", value: "wood_frame" },
	{ label: "Steel Frame", value: "steel_frame" },
	{ label: "Concrete", value: "concrete" },
	{ label: "Stone", value: "stone" },
	{ label: "Vinyl Siding", value: "vinyl_siding" },
	{ label: "Stucco", value: "stucco" },
	{ label: "Log", value: "log" },
	{ label: "Other", value: "other" },
];

export const PROPERTY_USES = [
	{ label: "Owner Occupied", value: "owner_occupied" },
	{ label: "Investment Property", value: "investment" },
	{ label: "Vacation Home", value: "vacation" },
	{ label: "Rental Property", value: "rental" },
	{ label: "Commercial Use", value: "commercial" },
	{ label: "Mixed Use", value: "mixed" },
	{ label: "Agricultural", value: "agricultural" },
	{ label: "Industrial", value: "industrial" },
	{ label: "Other", value: "other" },
];

export const HEATING_TYPES = [
	{ label: "Gas", value: "gas" },
	{ label: "Electric", value: "electric" },
	{ label: "Oil", value: "oil" },
	{ label: "Solar", value: "solar" },
	{ label: "Geothermal", value: "geothermal" },
	{ label: "Heat Pump", value: "heat_pump" },
	{ label: "Radiant", value: "radiant" },
	{ label: "Steam", value: "steam" },
	{ label: "None", value: "none" },
	{ label: "Other", value: "other" },
];

export const PARKING_TYPES = [
	{ label: "Garage", value: "garage" },
	{ label: "Carport", value: "carport" },
	{ label: "Driveway", value: "driveway" },
	{ label: "Street", value: "street" },
	{ label: "Covered", value: "covered" },
	{ label: "Assigned", value: "assigned" },
	{ label: "Open", value: "open" },
	{ label: "None", value: "none" },
	{ label: "Other", value: "other" },
];

export const GARAGE_TYPES = [
	{ label: "Attached", value: "attached" },
	{ label: "Detached", value: "detached" },
	{ label: "Built-in", value: "built_in" },
	{ label: "Carport", value: "carport" },
	{ label: "Basement", value: "basement" },
	{ label: "None", value: "none" },
	{ label: "Other", value: "other" },
];

export const LOT_TYPES = [
	{ label: "Regular", value: "regular" },
	{ label: "Corner", value: "corner" },
	{ label: "Cul-de-sac", value: "cul_de_sac" },
	{ label: "Through", value: "through" },
	{ label: "Flag", value: "flag" },
	{ label: "Irregular", value: "irregular" },
	{ label: "Waterfront", value: "waterfront" },
	{ label: "Golf Course", value: "golf_course" },
	{ label: "Other", value: "other" },
];

export const MLS_STATUSES = [
	{ label: "Active", value: "active" },
	{ label: "Pending", value: "pending" },
	{ label: "Sold", value: "sold" },
	{ label: "Withdrawn", value: "withdrawn" },
	{ label: "Expired", value: "expired" },
	{ label: "Cancelled", value: "cancelled" },
	{ label: "Coming Soon", value: "coming_soon" },
	{ label: "Active Under Contract", value: "active_under_contract" },
];

export const MLS_TYPES = [
	{ label: "For Sale", value: "for_sale" },
	{ label: "For Rent", value: "for_rent" },
	{ label: "Sold", value: "sold" },
	{ label: "Rental", value: "rental" },
	{ label: "Commercial Sale", value: "commercial_sale" },
	{ label: "Commercial Lease", value: "commercial_lease" },
	{ label: "Land", value: "land" },
	{ label: "Other", value: "other" },
];

export const FLOOD_ZONE_TYPES = [
	{ label: "Zone A", value: "zone_a" },
	{ label: "Zone AE", value: "zone_ae" },
	{ label: "Zone AH", value: "zone_ah" },
	{ label: "Zone AO", value: "zone_ao" },
	{ label: "Zone AR", value: "zone_ar" },
	{ label: "Zone V", value: "zone_v" },
	{ label: "Zone VE", value: "zone_ve" },
	{ label: "Zone X", value: "zone_x" },
	{ label: "Zone B", value: "zone_b" },
	{ label: "Zone C", value: "zone_c" },
	{ label: "Zone D", value: "zone_d" },
	{ label: "Other", value: "other" },
];

export const NOTICE_TYPES = [
	{ label: "Default", value: "default" },
	{ label: "Foreclosure", value: "foreclosure" },
	{ label: "Auction", value: "auction" },
	{ label: "Tax Lien", value: "tax_lien" },
	{ label: "Notice of Sale", value: "notice_of_sale" },
	{ label: "Lis Pendens", value: "lis_pendens" },
	{ label: "Other", value: "other" },
];

// Address schema
const AddressSchema = z
	.object({
		street: z.string().optional(),
		street2: z.string().optional(),
		city: z.string().optional(),
		state: z.string().optional(),
		zip: z.number().optional(),
		county: z.string().optional(),
		country: z.string().optional(),
	})
	.optional();

// Location schema for geographic coordinates
const LocationSchema = z
	.object({
		type: z.literal("Point"),
		coordinates: z.array(z.number()),
	})
	.optional();

// Tax information schema
const TaxInfoSchema = z
	.object({
		assessedImprovementValue: z.number().optional(),
		assessedLandValue: z.number().optional(),
		assessedValue: z.number().optional(),
		assessmentYear: z.number().optional(),
		estimatedValue: z.any().optional(),
		marketImprovementValue: z.number().optional(),
		marketLandValue: z.number().optional(),
		marketValue: z.number().optional(),
		propertyId: z.number().optional(),
		taxAmount: z.number().optional(),
		taxDelinquentYear: z.any().optional(),
		year: z.number().optional(),
	})
	.optional();

// Neighborhood schema
const NeighborhoodSchema = z
	.object({
		location: LocationSchema.optional(),
		id: z.string().optional(),
		name: z.string().optional(),
		type: z.string().optional(),
	})
	.optional();

export const PropertySchema = z.object({
	id: z.string(),
	apolloId: z.string().optional(),
	name: z.string(),
	recordType: z.literal("property").default("property"),
	image: z.string().optional(),
	organizationId: z.string(), // orgId in Convex
	propertyType: z.string().optional(),
	propertySubType: z.string().optional(),
	market: z.string().optional(),
	subMarket: z.string().optional(),
	listingId: z.string().optional(),
	address: AddressSchema,
	location: LocationSchema,
	website: z.string().optional(),
	updatedTime: z.string().optional(),
	updatedBy: z.string().optional(),
	lastViewedTime: z.number().optional(),
	lastViewedBy: z.string().optional(),
	isDeleted: z.boolean().default(false),
	deletedTime: z.string().optional(),
	deletedBy: z.string().optional(),

	createdBy: z.string(),
	createdAt: z.date(),
	updatedAt: z.date(),

	// Property details
	yearBuilt: z.number().optional(),
	squareFootage: z.number().optional(),
	units: z.number().optional(),
	price: z.number().optional(),
	equity: z.number().optional(),
	exchange: z.boolean().optional(),
	exchangeId: z.string().optional(),
	cap: z.number().optional(),
	parcelNumber: z.string().optional(),
	saleDate: z.coerce.date().optional(),
	salePrice: z.number().optional(),
	floors: z.number().optional(),
	landValue: z.number().optional(),
	buildingValue: z.number().optional(),
	status: z.string().optional(),
	primaryUse: z.string().optional(),
	construction: z.string().optional(),
	lotSize: z.number().optional(),
	lotType: z.string().optional(),
	zoning: z.string().optional(),
	meterType: z.string().optional(),
	class: z.string().optional(),
	structures: z.number().optional(),
	parking: z.string().optional(),
	reaId: z.string().optional(),

	// Boolean flags
	absenteeOwner: z.boolean().optional(),
	auction: z.boolean().optional(),
	cashBuyer: z.boolean().optional(),
	corporateOwned: z.boolean().optional(),
	deedInLieu: z.boolean().optional(),
	floodZone: z.boolean().optional(),
	freeClear: z.boolean().optional(),
	highEquity: z.boolean().optional(),
	inStateAbsenteeOwner: z.boolean().optional(),
	inherited: z.boolean().optional(),
	investorBuyer: z.boolean().optional(),
	lien: z.boolean().optional(),
	mlsActive: z.boolean().optional(),
	mlsCancelled: z.boolean().optional(),
	mlsFailed: z.boolean().optional(),
	mlsHasPhotos: z.boolean().optional(),
	mlsPending: z.boolean().optional(),
	mlsSold: z.boolean().optional(),
	mobileHome: z.boolean().optional(),
	outOfStateAbsenteeOwner: z.boolean().optional(),
	ownerOccupied: z.boolean().optional(),
	preForeclosure: z.boolean().optional(),
	privateLender: z.boolean().optional(),
	quitClaim: z.boolean().optional(),
	sheriffsDeed: z.boolean().optional(),
	spousalDeath: z.boolean().optional(),
	taxLien: z.boolean().optional(),
	trusteeSale: z.boolean().optional(),
	vacant: z.boolean().optional(),
	warrantyDeed: z.boolean().optional(),
	carport: z.boolean().optional(),

	// Percentage and estimated values
	equityPercent: z.number().optional(),
	estimatedEquity: z.number().optional(),
	estimatedValue: z.number().optional(),
	floodZoneDescription: z.string().optional(),
	floodZoneType: z.string().optional(),
	lastSaleDate: z.coerce.date().optional(),
	lastSalePrice: z.number().optional(),
	lastUpdateDate: z.string().optional(),
	mlsDaysOnMarket: z.number().optional(),
	mlsListingDate: z.string().optional(),
	mlsListingPrice: z.number().optional(),
	mlsListingPricePerSquareFoot: z.number().optional(),
	mlsSoldPrice: z.number().optional(),
	mlsStatus: z.string().optional(),
	mlsType: z.string().optional(),
	noticeType: z.string().optional(),

	// Residential-specific fields
	bathrooms: z.number().optional(),
	bedrooms: z.number().optional(),
	buildingSquareFeet: z.number().optional(),
	garageSquareFeet: z.number().optional(),
	garageType: z.string().optional(),
	heatingType: z.string().optional(),
	livingSquareFeet: z.number().optional(),
	lotSquareFeet: z.number().optional(),
	parkingSpaces: z.number().optional(),
	pricePerSquareFoot: z.number().optional(),
	propertyUse: z.string().optional(),
	roomsCount: z.number().optional(),
	legalDescription: z.string().optional(),
	lotAcres: z.number().optional(),
	lotNumber: z.string().optional(),
	subdivision: z.union([z.string(), z.number()]).optional(),

	// Complex nested objects
	taxInfo: TaxInfoSchema,
	neighborhood: NeighborhoodSchema,

	// Relationship IDs for related data
	unitMixes: z.array(z.string()).default([]),
	saleHistory: z.array(z.string()).default([]),
	demographics: z.string().optional(),
	foreclosureInfo: z.array(z.string()).default([]),
	mlsHistory: z.array(z.string()).default([]),
	mortgages: z.array(z.string()).default([]),

	// Creator relationship (for UI)
	creator: z
		.object({
			id: z.string(),
			name: z.string().nullable(),
			email: z.string(),
			image: z.string().nullable(),
		})
		.optional(),
});

export type Property = z.infer<typeof PropertySchema>;

export const PropertyCreateSchema = PropertySchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
	isDeleted: true,
	creator: true,
})
	.partial()
	.extend({
		name: z.string().min(1, "Property name is required"),
		organizationId: z.string().min(1, "Organization ID is required"),
	});

export const PropertyUpdateSchema = PropertySchema.omit({
	id: true,
	organizationId: true,
	createdAt: true,
	updatedAt: true,
	createdBy: true,
	creator: true,
}).partial();
