import {
	createDateRangeParam,
	createDynamicStringArrayParam,
	createNumberRangeParam,
	createSearchParamsParser,
	createStringArrayParam,
	createStringParam,
} from "../../../lib/search-params-factory";
import { PROPERTY_STATUSES, PROPERTY_TYPES } from "./schema";

// Property-specific search parameters
const propertySpecificParams = {
	// Property filters
	name: createStringParam(),
	propertyType: createStringArrayParam(PROPERTY_TYPES.map(type => type.value)),
	status: createStringArrayParam(PROPERTY_STATUSES.map(status => status.value)),
	
	// Date range filters
	createdAt: createDateRangeParam(),
	
	// Address filters (matching backend nested structure)
	"address.location.street": createStringParam(),
	"address.location.city": createStringParam(),
	"address.location.state": createStringParam(),
	"address.location.zip": createStringParam(),
	
	// Numeric filters (matching backend field names)
	price: createNumberRangeParam(),
	bedrooms: createNumberRangeParam(),
	bathrooms: createNumberRangeParam(),
	squareFootage: createNumberRangeParam(),
	yearBuilt: createNumberRangeParam(),
	lotSize: createNumberRangeParam(),
	units: createNumberRangeParam(),
	
	// Physical details (nested fields from available attributes)
	"physicalDetails.units": createNumberRangeParam(),
	"physicalDetails.bedrooms": createNumberRangeParam(),
	"physicalDetails.bathrooms": createNumberRangeParam(),
	"physicalDetails.squareFootage": createNumberRangeParam(),
	"physicalDetails.yearBuilt": createNumberRangeParam(),
	"physicalDetails.lotSize": createNumberRangeParam(),
	
	// Financial details (nested fields from available attributes)
	"financials.price": createNumberRangeParam(),
	
	// Tags
	tags: createDynamicStringArrayParam(),
};

// Create the complete search params parser for properties
export const propertySearchParams = createSearchParamsParser(
	propertySpecificParams,
);

// Export the parser components using correct property names
export const searchParamsParser = propertySearchParams.parser;
export const searchParamsCache = propertySearchParams.cache;
export const searchParamsSerializer = propertySearchParams.serializer;

export type PropertySearchParamsType = typeof propertySearchParams.type;
