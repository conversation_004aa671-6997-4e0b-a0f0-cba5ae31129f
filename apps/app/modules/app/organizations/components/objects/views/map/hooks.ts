import MapboxDraw from "@mapbox/mapbox-gl-draw";
import { useInfiniteQuery as useInfiniteTanstackQuery } from "@tanstack/react-query";
import mapboxgl from "mapbox-gl";
import { useQueryStates } from "nuqs";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTheme } from "next-themes";
import {
	propertiesConfig,
	propertyQueryOptions,
} from "../../properties/config";
import type { Property } from "../../properties/schema";
import { createDynamicColumns } from "../../shared/DynamicColumnFactory";
import {
	createDebouncedAutocomplete,
	fetchAutocomplete,
	fetchPropertyBoundary,
	fetchPropertyInfo,
	geocodeAddress,
} from "./api";
import type {
	AutocompleteResult,
	MapControlsState,
	PropertyBoundary,
	PropertyBoundaryPayload,
	SelectedProperty,
} from "./types";

// Helper function to convert WKT POLYGON to GeoJSON
function convertWKTToGeoJSON(wkt: string): GeoJSON.Polygon | null {
	try {
		// Extract coordinates from WKT POLYGON string
		// Format: "POLYGON((-117.859608 33.655203, -117.859896 33.655606, ...))"
		const match = wkt.match(/POLYGON\(\(([^)]+)\)\)/);
		if (!match) return null;

		const coordsString = match[1];
		const coordinates = coordsString.split(", ").map((coordPair) => {
			const [lng, lat] = coordPair.trim().split(" ").map(Number);
			return [lng, lat];
		});

		// GeoJSON Polygon requires coordinates to be an array of linear rings
		// The first ring is the exterior boundary
		return {
			type: "Polygon",
			coordinates: [coordinates],
		};
	} catch (error) {
		console.error("Error converting WKT to GeoJSON:", error);
		return null;
	}
}

// MAP INITIALIZATION HOOK
export function useMapbox(
	containerRef: React.RefObject<HTMLDivElement | null>,
) {
	const { theme } = useTheme();
	const map = useRef<mapboxgl.Map | null>(null);
	const drawRef = useRef<MapboxDraw | null>(null);
	const [mapLoaded, setMapLoaded] = useState(false);

	useEffect(() => {
		if (
			containerRef.current &&
			!map.current &&
			typeof window !== "undefined"
		) {
			const initializeMap = () => {
				try {
					// Ensure container is clean
					if (containerRef.current) {
						containerRef.current.innerHTML = "";
					}

					// Set the access token
					mapboxgl.accessToken =
						process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "";

					if (!mapboxgl.accessToken) {
						console.warn(
							"Mapbox access token not found. Please set NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN environment variable.",
						);
						return;
					}

					map.current = new mapboxgl.Map({
						container: containerRef.current!,
						style: theme === "dark" ? "mapbox://styles/bobbyalv/cm0lsz7fr027p01qre07udu5u" : "mapbox://styles/mapbox/streets-v12",
						center: [-98.5795, 39.8283],
						zoom: 4,
						preserveDrawingBuffer: true,
						// Enable drag rotation and pitch control for 3D navigation
						dragRotate: true,
						dragPan: true,
						scrollZoom: true,
						doubleClickZoom: true,
						keyboard: true,
						pitchWithRotate: true,
						touchZoomRotate: true
					});

					map.current.on("load", () => {
						setMapLoaded(true);
					});

					// Add draw control
					drawRef.current = new MapboxDraw({
						displayControlsDefault: false,
						controls: {},
						defaultMode: "simple_select",
						styles: [
							// Drawing styles configuration...
							{
								id: "gl-draw-polygon-fill-inactive",
								type: "fill",
								filter: [
									"all",
									["==", "active", "false"],
									["==", "$type", "Polygon"],
									["!=", "mode", "static"],
								],
								paint: {
									"fill-color": "#3fb1ce",
									"fill-outline-color": "#3fb1ce",
									"fill-opacity": 0.1,
								},
							},
							// ... other styles
						],
					});

					map.current.addControl(drawRef.current);

					map.current.on("error", (e: any) => {
						console.error("🗺️ Map error:", e);
					});
				} catch (error) {
					console.error("Failed to initialize Mapbox:", error);
				}
			};

			initializeMap();
		}

		return () => {
			if (map.current) {
				map.current.remove();
				map.current = null;
			}
			if (drawRef.current) {
				drawRef.current = null;
			}
		};
	}, [containerRef]);

	return { map: map.current, draw: drawRef.current, mapLoaded };
}

// MAP CONTROLS HOOK
export function useMapControls() {
	const [controlsState, setControlsState] = useState<MapControlsState>({
		isControlsOpen: false,
		isDrawControlsOpen: false,
		searchValue: "",
		isSearching: false,
		autocompleteResults: [],
		showAutocomplete: false,
		showSatellite: false,
		showTraffic: false,
		show3D: false,
		isDrawMode: false,
		showBoundaries: true, // Default to true for automatic boundary display
	});

	const updateControls = useCallback((updates: Partial<MapControlsState>) => {
		setControlsState((prev) => ({ ...prev, ...updates }));
	}, []);

	return { controlsState, updateControls };
}

// PROPERTY DATA HOOK
export function usePropertyData(organizationId: string, view?: any) {
	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				"property",
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			const filteredFilterFields = propertiesConfig.filterFields.filter(
				(field) => availableColumnIds.has(field.value as string),
			);

			return {
				...propertiesConfig,
				columns: dynamicColumns,
				filterFields: filteredFilterFields,
			};
		}
		return propertiesConfig;
	}, [view?.columnDefs, view?.id, view?.updatedAt]);

	const [search] = useQueryStates(dynamicConfig.searchParamsParser);

	const filteredSearch = useMemo(() => {
		const { size, start, direction, cursor, live, id, ...cleanSearch } =
			search;
		// Add mapView flag to indicate this is being used for map rendering
		return { ...cleanSearch, mapView: true };
	}, [search]);

	const {
		data: queryData,
		isLoading,
		isFetching,
	} = useInfiniteTanstackQuery(
		propertyQueryOptions(filteredSearch as any, organizationId),
	);

	const properties = useMemo(() => {
		if (!queryData?.pages) return [];
		return queryData.pages.flatMap((page) => page.data || []) as Property[];
	}, [queryData]);

	const mappableProperties = useMemo(() => {
		return properties.filter((property) => {
			const location = property.location as any;
			const hasLocation = location && typeof location === "object";
			
			// Only try to access coordinates if location exists
			if (!hasLocation) {
				return false;
			}
			
			// Handle both direct coordinates and nested location structure
			const coordinates = location.coordinates || location.location?.coordinates;
			const hasCoordinates = Array.isArray(coordinates);
			const hasTwoCoords = hasCoordinates && coordinates.length === 2;
			const hasValidNumbers =
				hasTwoCoords &&
				typeof coordinates[0] === "number" &&
				typeof coordinates[1] === "number" &&
				!isNaN(coordinates[0]) &&
				!isNaN(coordinates[1]) &&
				coordinates[0] !== null &&
				coordinates[1] !== null;

			return hasCoordinates && hasTwoCoords && hasValidNumbers;
		});
	}, [properties]);

	return { properties, mappableProperties, isLoading, isFetching };
}

// AUTOCOMPLETE HOOK
export function useAutocomplete() {
	const [results, setResults] = useState<AutocompleteResult[]>([]);
	const [isVisible, setIsVisible] = useState(false);

	const handleAutocomplete = useCallback(async (query: string) => {
		try {
			const data = await fetchAutocomplete({
				query,
			});
			setResults(data);
			setIsVisible(data.length > 0);
		} catch (error) {
			console.error("Autocomplete error:", error);
			setResults([]);
			setIsVisible(false);
		}
	}, []);

	const debouncedAutocomplete = useMemo(
		() => createDebouncedAutocomplete(handleAutocomplete),
		[handleAutocomplete],
	);

	const clearResults = useCallback(() => {
		setResults([]);
		setIsVisible(false);
	}, []);

	return {
		results,
		isVisible,
		setVisible: setIsVisible,
		handleAutocomplete: debouncedAutocomplete,
		clearResults,
	};
}

// PROPERTY SEARCH HOOK
export function usePropertySearch(mappableProperties: Property[]) {
	const [selectedProperty, setSelectedProperty] =
		useState<SelectedProperty | null>(null);

	const searchProperty = useCallback(
		async (searchValue: string) => {
			if (!searchValue.trim()) return;

			try {
				// Step 1: Geocode the address
				if (!mapboxgl.accessToken) {
					throw new Error("Mapbox access token not found");
				}
				const feature = await geocodeAddress(
					searchValue,
					mapboxgl.accessToken,
				);
				const coordinates = feature.center; // [lng, lat]
				const formattedAddress = feature.place_name.replace(
					/, United States/g,
					"",
				);

				// Step 2: Check if property exists in database
				const foundProperty = mappableProperties.find((property) => {
					const location = property.location as any;
					const propCoordinates = location?.coordinates || location?.location?.coordinates;
					if (
						propCoordinates &&
						Array.isArray(propCoordinates) &&
						propCoordinates.length === 2
					) {
						const [propLng, propLat] = propCoordinates;
						const [searchLng, searchLat] = coordinates;

						const lngDiff = Math.abs(propLng - searchLng);
						const latDiff = Math.abs(propLat - searchLat);
						const tolerance = 0.0005; // roughly 50 meters

						if (lngDiff < tolerance && latDiff < tolerance) {
							return true;
						}
					}

					// Text matching
					if (
						property.name
							?.toLowerCase()
							.includes(searchValue.toLowerCase())
					) {
						return true;
					}

					if (
						property.address &&
						typeof property.address === "object"
					) {
						const address = property.address as any;
						const addressText = Object.values(address)
							.join(" ")
							.toLowerCase();
						if (addressText.includes(searchValue.toLowerCase())) {
							return true;
						}
					}

					return false;
				});

				if (foundProperty) {
					setSelectedProperty({
						...foundProperty,
						source: "database",
					} as SelectedProperty);
					const location = (foundProperty.location as any);
					const foundCoordinates = location?.coordinates || location?.location?.coordinates;
					return {
						property: foundProperty,
						coordinates: foundCoordinates,
					};
				}
				// Fetch from REA API
				const result = await fetchPropertyInfo({
					address: formattedAddress,
				});

				const propertyData: SelectedProperty = {
					...({} as Property), // Base property structure
					source: "rea",
					coordinates: coordinates,
					searchAddress: formattedAddress,
					reaResponse: result,
					...(result.data && Object.keys(result.data).length > 0
						? result.data
						: {}),
					propertyInfo: {
						address: {
							address: formattedAddress,
							label: feature.place_name,
							coordinates: coordinates,
						},
						...(result.data && Object.keys(result.data).length > 0
							? result.data.propertyInfo || {}
							: {}),
					},
					statusCode: result.statusCode,
					statusMessage: result.statusMessage,
					live: result.live,
				};

				setSelectedProperty(propertyData);
				return { property: propertyData, coordinates };
			} catch (error) {
				console.error("Error searching for property:", error);
				throw error;
			}
		},
		[mappableProperties],
	);

	const clearSelection = useCallback(() => {
		setSelectedProperty(null);
	}, []);

	return {
		selectedProperty,
		searchProperty,
		clearSelection,
		setSelectedProperty,
	};
}

// MAP MARKERS HOOK
export function useMapMarkers(
	map: mapboxgl.Map | null,
	mapLoaded: boolean,
	mappableProperties: Property[],
	selectedProperty: SelectedProperty | null,
	onPropertyClick: (property: Property) => void,
) {
	useEffect(() => {
		if (!map || !mapLoaded || !mapboxgl) {
			return;
		}

		// Clear existing markers
		document
			.querySelectorAll(".mapboxgl-marker")
			.forEach((marker) => marker.remove());

		// Add markers for each property from database
		mappableProperties.forEach((property) => {
			const location = property.location as any;
			const coordinates = location?.coordinates || location?.location?.coordinates;

			if (!coordinates || coordinates.length !== 2) {
				return;
			}

			const [lng, lat] = coordinates;

			try {
				const propertyAny = property as any;
				const selectedAny = selectedProperty as any;
				const propertyId = propertyAny._id || property.id;
				const selectedId = selectedAny?._id || selectedProperty?.id;
				const markerColor =
					propertyId === selectedId ? "#FF0000" : "#3FB1CE";

				const marker = new mapboxgl.Marker({
					color: markerColor,
				})
					.setLngLat([lng, lat])
					.addTo(map);

				marker.getElement().addEventListener("click", () => {
					onPropertyClick(property);
				});
			} catch (error) {
				console.error("🗺️ Error adding marker:", error);
			}
		});

		// Add marker for selected property if it's not in mappableProperties (e.g., from search/REA)
		if (selectedProperty && selectedProperty.source !== "database") {
			const coordinates =
				(selectedProperty as any).coordinates ||
				(selectedProperty.location as any)?.coordinates;

			if (
				coordinates &&
				Array.isArray(coordinates) &&
				coordinates.length === 2
			) {
				const [lng, lat] = coordinates;

				try {
					const marker = new mapboxgl.Marker({
						color: "#FF0000", // Red for selected searched property
					})
						.setLngLat([lng, lat])
						.addTo(map);

					// Make it clickable but don't call onPropertyClick since it's already selected
					marker.getElement().style.cursor = "pointer";
				} catch (error) {
					console.error(
						"🗺️ Error adding searched property marker:",
						error,
					);
				}
			}
		}
	}, [map, mapLoaded, mappableProperties, selectedProperty, onPropertyClick]);
}

// PROPERTY BOUNDARIES HOOK
export function usePropertyBoundaries(map: mapboxgl.Map | null) {
	const [boundaries, setBoundaries] = useState<Map<string, PropertyBoundary>>(
		new Map(),
	);
	const [isLoading, setIsLoading] = useState(false);

	// Add boundary to map
	const addBoundaryToMap = useCallback(
		(boundary: PropertyBoundary) => {
			if (!map) return;

			const sourceId = `boundary-${boundary.id}`;
			const fillLayerId = `boundary-fill-${boundary.id}`;
			const lineLayerId = `boundary-line-${boundary.id}`;

			if (map.getSource(sourceId)) {
				if (map.getLayer(fillLayerId)) map.removeLayer(fillLayerId);
				if (map.getLayer(lineLayerId)) map.removeLayer(lineLayerId);
				map.removeSource(sourceId);
			}

			// Add source
			map.addSource(sourceId, {
				type: "geojson",
				data: {
					type: "Feature",
					geometry: boundary.geometry,
					properties: boundary.properties,
				},
			});

			// Add fill layer (background with opacity)
			map.addLayer({
				id: fillLayerId,
				type: "fill",
				source: sourceId,
				paint: {
					"fill-color": boundary.properties.isPinned
						? "#ffeb3b"
						: boundary.properties.isSelected
							? "#ff5722"
							: "#2196f3",
					"fill-opacity": 0.4,
				},
			});

			// Add line layer (boundary outline)
			map.addLayer({
				id: lineLayerId,
				type: "line",
				source: sourceId,
				paint: {
					"line-color": boundary.properties.isPinned
						? "#ffc107"
						: boundary.properties.isSelected
							? "#d32f2f"
							: "#1976d2",
					"line-width": 3,
					"line-opacity": 0.9,
				},
			});
		},
		[map],
	);

	// Remove boundary from map
	const removeBoundaryFromMap = useCallback(
		(boundaryId: string) => {
			if (!map) return;

			const sourceId = `boundary-${boundaryId}`;
			const fillLayerId = `boundary-fill-${boundaryId}`;
			const lineLayerId = `boundary-line-${boundaryId}`;

			if (map.getLayer(fillLayerId)) map.removeLayer(fillLayerId);
			if (map.getLayer(lineLayerId)) map.removeLayer(lineLayerId);
			if (map.getSource(sourceId)) map.removeSource(sourceId);
		},
		[map],
	);

	// Fetch and add boundary
	const fetchAndAddBoundary = useCallback(
		async (
			payload: PropertyBoundaryPayload,
			propertyId: string,
			isPinned = false,
			isSelected = false,
		) => {
			if (!map) return;

			setIsLoading(true);
			try {
				const boundaryData = await fetchPropertyBoundary(payload);
				
				if (boundaryData?.data?.geometry) {
					// Convert WKT polygon to GeoJSON
					const wktGeometry = boundaryData.data.geometry;

					const geoJsonGeometry = convertWKTToGeoJSON(wktGeometry);

					if (geoJsonGeometry) {
						const boundary: PropertyBoundary = {
							id: propertyId,
							geometry: geoJsonGeometry,
							properties: {
								propertyId,
								address: payload.address,
								isPinned,
								isSelected,
							},
						};

						setBoundaries(
							(prev) => new Map(prev.set(propertyId, boundary)),
						);
						addBoundaryToMap(boundary);
						
					} else {
					}
				} else {
				}
			} catch (error) {
				// Check if it's an authentication error
				const errorMessage = error instanceof Error ? error.message : String(error);
				if (errorMessage.includes("Authentication required") || errorMessage.includes("Unauthorized")) {
					console.warn("🔐 Boundary fetch failed due to authentication. This is expected if user session expired.");
					// Don't show error to user for auth issues, just fail silently
					// The user can still use the map without boundaries
				} else {
					// For other errors, could show a toast notification here if needed
					console.warn("⚠️ Boundary fetch failed:", errorMessage);
				}
			} finally {
				setIsLoading(false);
			}
		},
		[map, addBoundaryToMap],
	);

	// Remove boundary
	const removeBoundary = useCallback(
		(propertyId: string) => {
			setBoundaries((prev) => {
				const newMap = new Map(prev);
				newMap.delete(propertyId);
				return newMap;
			});
			removeBoundaryFromMap(propertyId);
		},
		[removeBoundaryFromMap],
	);

	// Update boundary status (pinned/selected)
	const updateBoundaryStatus = useCallback(
		(propertyId: string, isPinned?: boolean, isSelected?: boolean) => {
			setBoundaries((prev) => {
				const boundary = prev.get(propertyId);
				if (!boundary) return prev;

				const updatedBoundary = {
					...boundary,
					properties: {
						...boundary.properties,
						isPinned: isPinned ?? boundary.properties.isPinned,
						isSelected:
							isSelected ?? boundary.properties.isSelected,
					},
				};

				// Remove and re-add to update colors
				removeBoundaryFromMap(propertyId);
				addBoundaryToMap(updatedBoundary);

				const newMap = new Map(prev);
				newMap.set(propertyId, updatedBoundary);
				return newMap;
			});
		},
		[addBoundaryToMap, removeBoundaryFromMap],
	);

	// Clear all boundaries
	const clearBoundaries = useCallback(() => {
		setBoundaries((prev) => {
			prev.forEach((_, propertyId) => {
				removeBoundaryFromMap(propertyId);
			});
			return new Map();
		});
	}, [removeBoundaryFromMap]);

	return {
		boundaries,
		isLoading,
		fetchAndAddBoundary,
		removeBoundary,
		updateBoundaryStatus,
		clearBoundaries,
	};
}

// VIEWPORT-BASED PROPERTY DATA HOOK FOR MAP OPTIMIZATION
export function useViewportPropertyData(organizationId: string, view?: any) {
	const [viewportData, setViewportData] = useState<{
		properties: any[];
		clusters: any[];
		meta: any;
	}>({
		properties: [],
		clusters: [],
		meta: {},
	});
	const [isLoading, setIsLoading] = useState(false);
	const [currentBounds, setCurrentBounds] = useState<any>(null);
	const [currentZoom, setCurrentZoom] = useState<number>(10);
	
	// Cache for storing fetched data to avoid redundant requests
	const dataCache = useRef(new Map<string, any>());
	const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	const fetchViewportData = useCallback(async (
		bounds: { north: number; south: number; east: number; west: number },
		zoom: number,
		immediate = false
	) => {
		// Debounce rapid viewport changes
		if (fetchTimeoutRef.current) {
			clearTimeout(fetchTimeoutRef.current);
			fetchTimeoutRef.current = null;
		}

		const fetchData = async () => {
			// Create cache key for this viewport
			const cacheKey = `${bounds.north}-${bounds.south}-${bounds.east}-${bounds.west}-${Math.floor(zoom)}`;
			
			// Check cache first
			if (dataCache.current.has(cacheKey)) {
				const cached = dataCache.current.get(cacheKey);
				setViewportData(cached);
				return cached;
			}

			setIsLoading(true);
			try {
				const response = await fetch('/api/objects/properties/viewport', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify({
						organizationId,
						bounds,
						zoom,
						limit: zoom < 12 ? 2000 : 1000, // Fetch more data at lower zoom levels
					}),
				});

				if (!response.ok) {
					throw new Error(`Failed to fetch viewport properties: ${response.statusText}`);
				}

				const result = await response.json();
				
				// Separate properties and clusters
				const properties = result.data.filter((item: any) => !item.isCluster);
				const clusters = result.data.filter((item: any) => item.isCluster);

				const newData = {
					properties,
					clusters,
					meta: result.meta,
				};

				// Cache the result
				dataCache.current.set(cacheKey, newData);
				
				// Limit cache size to prevent memory issues
				if (dataCache.current.size > 20) {
					const firstKey = dataCache.current.keys().next().value;
					dataCache.current.delete(firstKey);
				}

				setViewportData(newData);
				return newData;
			} catch (error) {
				console.error('Error fetching viewport properties:', error);
				// Return empty data on error but don't clear existing data
				return { properties: [], clusters: [], meta: {} };
			} finally {
				setIsLoading(false);
			}
		};

		if (immediate) {
			return await fetchData();
		} else {
			fetchTimeoutRef.current = setTimeout(fetchData, 300); // 300ms debounce
		}
	}, [organizationId]);

	// Update viewport data when bounds or zoom change
	const updateViewport = useCallback((
		bounds: { north: number; south: number; east: number; west: number },
		zoom: number,
		immediate = false
	) => {
		setCurrentBounds(bounds);
		setCurrentZoom(zoom);
		return fetchViewportData(bounds, zoom, immediate);
	}, [fetchViewportData]);

	// Get all properties in current viewport (includes individual properties and properties within clusters)
	const allProperties = useMemo(() => {
		const individual = viewportData.properties;
		const fromClusters = viewportData.clusters.flatMap((cluster: any) => 
			cluster.properties || []
		);
		return [...individual, ...fromClusters];
	}, [viewportData]);

	// Get mappable properties (same as before but from viewport data)
	const mappableProperties = useMemo(() => {
		return allProperties.filter((property) => {
			const location = property.location as any;
			const hasLocation = location && typeof location === "object";
			
			if (!hasLocation) {
				return false;
			}
			
			const coordinates = location.coordinates || location.location?.coordinates;
			const hasCoordinates = Array.isArray(coordinates);
			const hasTwoCoords = hasCoordinates && coordinates.length === 2;
			const hasValidNumbers =
				hasTwoCoords &&
				typeof coordinates[0] === "number" &&
				typeof coordinates[1] === "number" &&
				!isNaN(coordinates[0]) &&
				!isNaN(coordinates[1]) &&
				coordinates[0] !== null &&
				coordinates[1] !== null;

			return hasCoordinates && hasTwoCoords && hasValidNumbers;
		});
	}, [allProperties]);

	// Clear cache when view changes
	useEffect(() => {
		dataCache.current.clear();
	}, [view?.id]);

	return {
		properties: allProperties,
		mappableProperties,
		clusters: viewportData.clusters,
		isLoading,
		updateViewport,
		currentBounds,
		currentZoom,
		meta: viewportData.meta,
	};
}

// ENHANCED MAP MARKERS HOOK WITH CLUSTERING SUPPORT
export function useOptimizedMapMarkers(
	map: mapboxgl.Map | null,
	mapLoaded: boolean,
	properties: any[],
	clusters: any[],
	selectedProperty: any,
	onPropertySelect: (property: any) => void,
	zoom: number
) {
	const markersRef = useRef<Map<string, mapboxgl.Marker>>(new Map());
	const clusterMarkersRef = useRef<Map<string, mapboxgl.Marker>>(new Map());

	useEffect(() => {
		if (!map || !mapLoaded) return;

		// Clear existing markers
		markersRef.current.forEach(marker => marker.remove());
		clusterMarkersRef.current.forEach(marker => marker.remove());
		markersRef.current.clear();
		clusterMarkersRef.current.clear();

		// Add property markers (individual properties)
		properties.forEach(property => {
			if (!property.id) return; // Skip properties without IDs
			
			const location = property.location as any;
			const coordinates = location?.coordinates || location?.location?.coordinates;
			
			if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2) {
				return;
			}

			const [lng, lat] = coordinates;
			const isSelected = selectedProperty?.id === property.id;

			// Create marker element
			const el = document.createElement('div');
			el.className = `property-marker ${isSelected ? 'selected' : ''}`;
			el.style.cssText = `
				width: ${isSelected ? '16px' : '12px'};
				height: ${isSelected ? '16px' : '12px'};
				background-color: ${getPropertyColor(property.propertyType)};
				border: 2px solid ${isSelected ? '#fff' : 'rgba(255,255,255,0.8)'};
				border-radius: 50%;
				cursor: pointer;
				box-shadow: 0 2px 4px rgba(0,0,0,0.3);
				transition: all 0.2s ease;
			`;

			el.addEventListener('mouseenter', () => {
				el.style.transform = 'scale(1.2)';
			});
			
			el.addEventListener('mouseleave', () => {
				el.style.transform = 'scale(1)';
			});

			el.addEventListener('click', () => {
				onPropertySelect(property);
			});

			const marker = new mapboxgl.Marker(el)
				.setLngLat([lng, lat])
				.addTo(map);

			if (property.id) {
				markersRef.current.set(property.id, marker);
			}
		});

		// Add cluster markers
		clusters.forEach(cluster => {
			if (!cluster.position || cluster.position.length !== 2) return;

			const [lng, lat] = cluster.position;

			// Create cluster marker element
			const el = document.createElement('div');
			el.className = 'cluster-marker';
			el.style.cssText = `
				width: ${Math.min(40 + Math.log(cluster.count) * 8, 60)}px;
				height: ${Math.min(40 + Math.log(cluster.count) * 8, 60)}px;
				background: linear-gradient(135deg, #4f46e5, #7c3aed);
				border: 3px solid white;
				border-radius: 50%;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-weight: bold;
				font-size: ${Math.min(14 + Math.log(cluster.count), 18)}px;
				box-shadow: 0 4px 8px rgba(0,0,0,0.3);
				transition: all 0.2s ease;
			`;
			el.textContent = cluster.count.toString();

			el.addEventListener('mouseenter', () => {
				el.style.transform = 'scale(1.1)';
			});
			
			el.addEventListener('mouseleave', () => {
				el.style.transform = 'scale(1)';
			});

			el.addEventListener('click', () => {
				// Zoom into cluster bounds
				if (cluster.bounds && map) {
					const bounds = new mapboxgl.LngLatBounds(
						[cluster.bounds.west, cluster.bounds.south],
						[cluster.bounds.east, cluster.bounds.north]
					);
					map.fitBounds(bounds, {
						padding: 50,
						maxZoom: 16,
					});
				}
			});

			const marker = new mapboxgl.Marker(el)
				.setLngLat([lng, lat])
				.addTo(map);

			clusterMarkersRef.current.set(cluster.id || `cluster-${lng}-${lat}`, marker);
		});

		return () => {
			markersRef.current.forEach(marker => marker.remove());
			clusterMarkersRef.current.forEach(marker => marker.remove());
			markersRef.current.clear();
			clusterMarkersRef.current.clear();
		};
	}, [map, mapLoaded, properties, clusters, selectedProperty, onPropertySelect, zoom]);
}

// Helper function to get property type colors
function getPropertyColor(propertyType: string | null): string {
	switch (propertyType?.toLowerCase()) {
		case 'residential': return '#3b82f6'; // Blue
		case 'commercial': return '#f59e0b'; // Orange
		case 'industrial': return '#6b7280'; // Gray
		case 'land': return '#10b981'; // Green
		case 'mixed-use': return '#8b5cf6'; // Purple
		default: return '#ef4444'; // Red for unknown
	}
}

// PERFORMANCE MONITORING HOOK
export function useMapPerformanceMonitor(
	properties: any[],
	clusters: any[],
	isLoading: boolean,
	zoom: number,
	bounds: any
) {
	const [performanceMetrics, setPerformanceMetrics] = useState<{
		renderTime: number;
		dataFetchTime: number;
		memoryUsage: number;
		fps: number;
		suggestions: string[];
	}>({
		renderTime: 0,
		dataFetchTime: 0,
		memoryUsage: 0,
		fps: 60,
		suggestions: [],
	});

	const renderStartTime = useRef<number>(0);
	const fetchStartTime = useRef<number>(0);
	const frameCount = useRef<number>(0);
	const lastFrameTime = useRef<number>(0);

	// Track data fetch performance
	useEffect(() => {
		if (isLoading) {
			fetchStartTime.current = performance.now();
		} else {
			const fetchTime = performance.now() - fetchStartTime.current;
			if (fetchTime > 0) {
				setPerformanceMetrics(prev => ({
					...prev,
					dataFetchTime: fetchTime,
				}));
			}
		}
	}, [isLoading]);

	// Track render performance
	useEffect(() => {
		renderStartTime.current = performance.now();
		
		// Use requestAnimationFrame to measure render completion
		const measureRender = () => {
			const renderTime = performance.now() - renderStartTime.current;
			setPerformanceMetrics(prev => ({
				...prev,
				renderTime: renderTime,
			}));
		};

		requestAnimationFrame(measureRender);
	}, [properties.length, clusters.length, zoom]);

	// Calculate memory usage
	useEffect(() => {
		const estimatedMemoryPerProperty = 2; // KB per property (rough estimate)
		const totalMemoryKB = (properties.length * estimatedMemoryPerProperty) + 
						   (clusters.length * 0.5); // Clusters use less memory

		setPerformanceMetrics(prev => ({
			...prev,
			memoryUsage: totalMemoryKB,
		}));
	}, [properties.length, clusters.length]);

	// Generate optimization suggestions
	useEffect(() => {
		const suggestions: string[] = [];

		if (properties.length > 5000) {
			suggestions.push('Consider enabling clustering for better performance');
		}

		if (performanceMetrics.renderTime > 100) {
			suggestions.push('Reduce visible properties by zooming in');
		}

		if (performanceMetrics.dataFetchTime > 2000) {
			suggestions.push('Data fetch is slow - check network connection');
		}

		if (zoom < 10 && properties.length > 1000) {
			suggestions.push('Zoom in to see individual properties');
		}

		if (performanceMetrics.memoryUsage > 20000) { // 20MB
			suggestions.push('High memory usage detected - apply filters');
		}

		setPerformanceMetrics(prev => ({
			...prev,
			suggestions,
		}));
	}, [properties.length, zoom, performanceMetrics.renderTime, performanceMetrics.dataFetchTime, performanceMetrics.memoryUsage]);

	// FPS monitoring
	useEffect(() => {
		let animationId: number;
		
		const measureFPS = () => {
			const now = performance.now();
			frameCount.current++;
			
			if (now - lastFrameTime.current >= 1000) { // Update every second
				const fps = Math.round((frameCount.current * 1000) / (now - lastFrameTime.current));
				setPerformanceMetrics(prev => ({
					...prev,
					fps: Math.min(fps, 60), // Cap at 60 FPS
				}));
				
				frameCount.current = 0;
				lastFrameTime.current = now;
			}
			
			animationId = requestAnimationFrame(measureFPS);
		};

		lastFrameTime.current = performance.now();
		animationId = requestAnimationFrame(measureFPS);

		return () => {
			if (animationId) {
				cancelAnimationFrame(animationId);
			}
		};
	}, []);

	return performanceMetrics;
}

// OPTIMIZATION RECOMMENDATIONS HOOK
export function useOptimizationRecommendations(
	properties: any[],
	clusters: any[],
	zoom: number,
	performanceMetrics: any
) {
	return useMemo(() => {
		const recommendations: Array<{
			type: 'info' | 'warning' | 'error';
			title: string;
			description: string;
			action?: string;
		}> = [];

		// Data density recommendations
		if (properties.length > 10000) {
			recommendations.push({
				type: 'error',
				title: 'Too Many Properties',
				description: `${properties.length.toLocaleString()} properties are loaded, which may cause performance issues.`,
				action: 'Apply filters or zoom in to reduce the dataset size.',
			});
		} else if (properties.length > 5000) {
			recommendations.push({
				type: 'warning',
				title: 'High Property Count',
				description: `${properties.length.toLocaleString()} properties loaded.`,
				action: 'Consider applying filters for better performance.',
			});
		}

		// Clustering recommendations
		if (zoom < 12 && properties.length > 1000 && clusters.length === 0) {
			recommendations.push({
				type: 'info',
				title: 'Clustering Recommended',
				description: 'At this zoom level, clustering would improve performance.',
				action: 'Enable clustering in map settings.',
			});
		}

		// Performance recommendations
		if (performanceMetrics?.renderTime > 200) {
			recommendations.push({
				type: 'warning',
				title: 'Slow Rendering',
				description: `Render time: ${performanceMetrics.renderTime.toFixed(0)}ms`,
				action: 'Reduce visible elements or enable hardware acceleration.',
			});
		}

		if (performanceMetrics?.fps < 30) {
			recommendations.push({
				type: 'warning',
				title: 'Low Frame Rate',
				description: `FPS: ${performanceMetrics.fps}`,
				action: 'Reduce map complexity or close other applications.',
			});
		}

		// Memory recommendations
		if (performanceMetrics?.memoryUsage > 50000) { // 50MB
			recommendations.push({
				type: 'error',
				title: 'High Memory Usage',
				description: `Using ~${(performanceMetrics.memoryUsage / 1024).toFixed(1)}MB`,
				action: 'Apply filters or refresh the page to clear cache.',
			});
		}

		return recommendations;
	}, [properties.length, clusters.length, zoom, performanceMetrics]);
}
