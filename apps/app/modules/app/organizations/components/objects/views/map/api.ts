import type {
	AutocompleteResult,
	PropertyInfoPayload,
	SearchPayload,
} from "./types";

// AUTOCOMPLETE API
export async function fetchAutocomplete(
	payload: SearchPayload,
): Promise<AutocompleteResult[]> {
	if (!payload.query.trim() || payload.query.length < 3) {
		return [];
	}

	try {
		const response = await fetch("/api/rea/autocomplete", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			credentials: "include",
			body: JSON.stringify({
				query: payload.query,
			}),
		});

		if (response.ok) {
			const result = await response.json();
			if (result.data && Array.isArray(result.data)) {
				return result.data.slice(0, 5); // Limit to 5 results
			}
		} else {
			console.error("Autocomplete API failed:", response.statusText);
		}
	} catch (error) {
		console.error("Error fetching autocomplete:", error);
	}

	return [];
}

// PROPERTY INFO API
export async function fetchPropertyInfo(
	payload: PropertyInfoPayload,
): Promise<any> {
	const response = await fetch("/api/rea/property-info", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		credentials: "include",
		body: JSON.stringify({
			address: payload.address,
		}),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch property info: ${response.statusText}`,
		);
	}

	return response.json();
}

// GEOCODING API
export async function geocodeAddress(
	address: string,
	accessToken: string,
): Promise<any> {
	const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=${accessToken}&country=us&limit=1`;

	const response = await fetch(geocodeUrl);
	if (!response.ok) {
		throw new Error("Geocoding failed");
	}

	const result = await response.json();

	if (!result.features || result.features.length === 0) {
		throw new Error("Address not found during geocoding");
	}

	return result.features[0];
}

// PROPERTY BOUNDARY API
export async function fetchPropertyBoundary(payload: {
	address?: string;
	lat?: number;
	lng?: number;
	id?: number;
}): Promise<any> {
	const response = await fetch("/api/rea/property-boundary", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		credentials: "include",
		body: JSON.stringify(payload),
	});

	if (!response.ok) {
		if (response.status === 401) {
			console.warn("🔐 Authentication failed for property boundary API. User may need to re-login.");
			throw new Error("Authentication required. Please refresh the page and try again.");
		}
		
		const errorText = await response.text().catch(() => response.statusText);
		throw new Error(
			`Failed to fetch property boundary: ${response.statusText}`,
		);
	}

	const data = await response.json();
	
	return data;
}

// DEBOUNCED AUTOCOMPLETE HELPER
export function createDebouncedAutocomplete(
	callback: (query: string) => Promise<void>,
	delay = 300,
) {
	let timeoutId: NodeJS.Timeout;

	return (query: string) => {
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => callback(query), delay);
	};
}
