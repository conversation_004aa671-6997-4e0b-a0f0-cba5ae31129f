"use client";

import { Icon<PERSON>ath, IconBed, IconMapPin, IconRuler } from "@tabler/icons-react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { ScrollArea } from "@ui/components/scroll-area";
import { cn } from "@ui/lib";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { DataTableProvider } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { DataTableFilterCommand } from "@app/organizations/components/objects/shared/data-table/data-table-filter-command";
import { propertySearchParams } from "@app/organizations/components/objects/properties/search-params";
import { propertiesConfig } from "@app/organizations/components/objects/properties/config";
import { useReactTable, getCoreRowModel, getFilteredRowModel } from "@tanstack/react-table";
import type { Property } from "@app/organizations/components/objects/properties/schema";
import { useState, useMemo } from "react";
import { createDynamicColumns } from "@app/organizations/components/objects/shared/DynamicColumnFactory";
import type { ColumnFiltersState } from "@tanstack/react-table";

interface PropertyGridProps {
  properties: Property[];
  isOpen: boolean;
  onClose: () => void;
  onPropertySelect: (property: Property) => void;
  view?: any;
  organizationId: string;
  selectedProperty: any | null;
  isLoading?: boolean;
  isFetching?: boolean;
}

export function PropertyGrid({ 
  properties, 
  isOpen, 
  onClose, 
  onPropertySelect,
  view,
  organizationId,
  selectedProperty,
  isLoading,
  isFetching
}: PropertyGridProps) {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "for sale":
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "sold":
        return "bg-red-100 text-red-800";
      case "off market":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Create dynamic columns based on view configuration
  const dynamicConfig = useMemo(() => {
    if (view?.columnDefs && view.columnDefs.length > 0) {
      const dynamicColumns = createDynamicColumns(
        view.columnDefs,
        "property",
      ) as any;

      return {
        ...propertiesConfig,
        columns: dynamicColumns,
      };
    }
    return propertiesConfig;
  }, [view?.columnDefs, view?.id, view?.updatedAt]);

  // Add table instance for DataTableProvider
  const table = useReactTable({
    data: properties,
    columns: dynamicConfig.columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      columnFilters,
    },
    onColumnFiltersChange: setColumnFilters,
    enableFilters: true,
  });

  const filteredRows = table.getFilteredRowModel().rows;

  return (
    <div
      className={cn(
        "absolute right-0 top-0 bottom-0 w-[400px] flex flex-col overflow-hidden bg-sidebar border-l border-border transition-transform duration-300",
        isOpen ? "translate-x-0" : "translate-x-full"
      )}
    >
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">Properties</h2>
        <div className="text-sm text-muted-foreground font-mono">{filteredRows.length} of {properties.length}</div>
      </div>

      <div className="p-4 border-b">
        <DataTableProvider
          table={table}
          columns={table.getAllColumns()}
          filterFields={propertiesConfig.filterFields as any}
          isLoading={isLoading}
          columnFilters={columnFilters}
        >
          <DataTableFilterCommand searchParamsParser={propertySearchParams.parser} />
        </DataTableProvider>
      </div>

      <ScrollArea className="flex-1">
        <div className="grid grid-cols-2 gap-4 p-4">
          {filteredRows.map((row) => {
            const property = row.original;
            const location = property.location as any;
            const address = location?.address;
            const formattedAddress = address ? 
              `${address.street}, ${address.city}, ${address.state} ${address.zip}` : 
              "No address available";

            return (
              <Card 
                key={property.id} 
                className={cn(
                  "hover:shadow-md transition-shadow border cursor-pointer",
                  selectedProperty?._id === property.id && "border-blue-500 ring-2 ring-blue-500/50"
                )}
                onClick={() => onPropertySelect(property)}
              >
                <CardContent className="p-0">
                  {/* Property Image */}
                  <div className="aspect-[16/10] relative">
                    <PropertyAvatar
                      name={property.name}
                      className="w-full h-full object-cover rounded-t-lg"
                    />
                    {property.status && (
                      <Badge 
                        className={cn(
                          "absolute top-2 right-2 text-[10px]",
                          getStatusColor(property.status)
                        )}
                      >
                        {property.status}
                      </Badge>
                    )}
                  </div>

                  <div className="p-3">
                    {/* Price */}
                    {property.price && (
                      <div className="text-lg font-bold text-primary mb-1">
                        {formatPrice(property.price)}
                      </div>
                    )}

                    {/* Property Name */}
                    <h3 className="font-medium mb-1 line-clamp-1">
                      {property.name}
                    </h3>

                    {/* Property Details */}
                    <div className="flex gap-3 text-xs text-muted-foreground mb-2">
                      {property.bedrooms && (
                        <div className="flex items-center gap-1">
                          <IconBed className="h-3 w-3" />
                          <span>{property.bedrooms} bd</span>
                        </div>
                      )}
                      {property.bathrooms && (
                        <div className="flex items-center gap-1">
                          <IconBath className="h-3 w-3" />
                          <span>{property.bathrooms} ba</span>
                        </div>
                      )}
                      {property.squareFootage && (
                        <div className="flex items-center gap-1">
                          <IconRuler className="h-3 w-3" />
                          <span>{property.squareFootage.toLocaleString()} sqft</span>
                        </div>
                      )}
                    </div>

                    {/* Address */}
                    <div className="flex items-start gap-1">
                      <IconMapPin className="h-3 w-3 mt-0.5 text-muted-foreground flex-shrink-0" />
                      <p className="text-xs text-muted-foreground line-clamp-1">
                        {formattedAddress}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
} 