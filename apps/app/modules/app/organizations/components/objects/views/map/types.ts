import { ObjectType } from "@repo/database";
import type { Property } from "../../properties/schema";

export interface MapViewProps {
	objectType: ObjectType;
	organizationId: string;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
		mapConfig?: {
			displayType?: 'table' | 'grid';
			rowDensity?: 'compact' | 'normal' | 'comfortable';
			showExportOptions?: boolean;
			allowColumnReorder?: boolean;
			showSearchBar?: boolean;
		};
	};
	user?: {
		name: string;
		avatarUrl?: string | null;
	};
	views?: any[];
	onViewChange?: (viewId: string) => void;
	onRenameView?: (viewId: string) => void;
	onDuplicateView?: (viewId: string) => void;
	onDeleteView?: (viewId: string) => void;
	onToggleViewFavorite?: (viewId: string) => void;
	onToggleViewPublic?: (viewId: string) => void;
	onSetViewDefault?: (viewId: string) => void;
	onViewRenamed?: (updatedView: any) => void;
}

export interface PropertiesMapProps {
	organizationId: string;
	renderHeader: () => React.ReactNode;
	view?: any;
	onViewRenamed?: (updatedView: any) => void;
}

export interface MapControlsState {
	isControlsOpen: boolean;
	isDrawControlsOpen: boolean;
	searchValue: string;
	isSearching: boolean;
	autocompleteResults: any[];
	showAutocomplete: boolean;
	showSatellite: boolean;
	showTraffic: boolean;
	show3D: boolean;
	isDrawMode: boolean;
	showBoundaries: boolean;
}

export interface SelectedProperty extends Property {
	source?: "database" | "rea";
	coordinates?: [number, number];
	searchAddress?: string;
	reaResponse?: any;
	propertyInfo?: any;
	statusCode?: number;
	statusMessage?: string;
	live?: boolean;
}

export interface AutocompleteResult {
	address: string;
	city?: string;
	state?: string;
	zip?: string;
}

export interface SearchPayload {
	query: string;
}

export interface PropertyInfoPayload {
	address: string;
}

export interface PropertyBoundaryPayload {
	address?: string;
	lat?: number;
	lng?: number;
	id?: number;
}

export interface PropertyBoundary {
	id: string;
	geometry: GeoJSON.Geometry;
	properties: {
		propertyId: string;
		address?: string;
		isPinned?: boolean;
		isSelected?: boolean;
	};
}
