import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";
import type { ObjectConfig } from "../../../../object-views/lib/types";
import { contactFilterFields, contactSheetFields } from "../../../lib/constants";
import { createInfiniteQueryOptions } from "../../../lib/query-options-factory";
import { columns } from "./columns";
import type { Contact } from "./schema";
import { contactSchema } from "./schema";
import { searchParamsParser, searchParamsSerializer } from "./search-params";

export interface ContactMeta {
	objectType: "contact";
	totalCount: number;
	hasNextPage: boolean;
}

export const contactConfig: ObjectConfig<Contact, ContactMeta> = {
	type: "contact",
	schema: contactSchema,
	columns,
	filterFields: contactFilterFields,
	sheetFields: contactSheetFields,
	searchParamsParser,
	apiEndpoint: "/api/objects/contacts",
	primaryColumn: "name",
	readonlyColumns: ["id", "createdAt", "updatedAt"],
};

export const contactSelectOptions = {
	status: CONTACT_STATUS,
	persona: CONTACT_PERSONA,
	stage: CONTACT_STAGE
};

export const contactQueryOptions = createInfiniteQueryOptions<
	Contact,
	ContactMeta
>({
	objectType: "contact",
	searchParamsSerializer,
	apiEndpoint: "/api/objects/contacts/infinite",
	queryKeyPrefix: "contacts-infinite",
});
