import {
	createDateRangeParam,
	createDynamicStringArrayParam,
	createSearchParamsParser,
	createStringArrayParam,
	createStringParam,
} from "../../../lib/search-params-factory";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

// Contact-specific search parameters
const contactSpecificParams = {
	// Contact filters
	name: createStringParam(),
	firstName: createStringParam(),
	lastName: createStringParam(),
	email: createStringParam(),
	phone: createStringParam(),
	status: createStringArrayParam(CONTACT_STATUS.map((status) => status.value)),
	persona: createStringArrayParam(CONTACT_PERSONA.map((persona) => persona.value)),
	stage: createStringArrayParam(CONTACT_STAGE.map((stage) => stage.value)),
	title: createStringParam(),
	company: createStringParam(),
	tags: createDynamicStringArrayParam(),
	createdAt: createDateRangeParam(),
};

// Create the complete search params parser for contacts
export const contactSearchParams = createSearchParamsParser(
	contactSpecificParams,
);

// Export the types and utilities
export const searchParamsParser = contactSearchParams.parser;
export const searchParamsCache = contactSearchParams.cache;
export const searchParamsSerializer = contactSearchParams.serializer;
export type ContactSearchParamsType = typeof contactSearchParams.type;
