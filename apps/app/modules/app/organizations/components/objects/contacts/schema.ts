import { z } from "zod";
import type { BaseObjectSchema } from "../../../../object-views/lib/types";
import { CONTACT_STAGE, CONTACT_PERSONA, CONTACT_STATUS } from "@app/shared/lib/constants";

export const contactSchema = z.object({
	id: z.string(),
	name: z.string(),
	firstName: z.string().optional(),
	lastName: z.string().optional(),
	image: z.string().optional(),
	email: z
		.array(
			z.object({
				address: z.string(),
				label: z.string().optional(),
				isPrimary: z.boolean().optional(),
				isBad: z.boolean().optional(),
			}),
		)
		.default([]),
	phone: z
		.array(
			z.object({
				number: z.string(),
				label: z.string().optional(),
				isPrimary: z.boolean().optional(),
				isBad: z.boolean().optional(),
			}),
		)
		.default([]),
	status: z.enum(CONTACT_STATUS.map((status) => status.value) as [string, ...string[]]).optional(),
	persona: z.enum(CONTACT_PERSONA.map((persona) => persona.value) as [string, ...string[]]).optional(),
	stage: z.enum(CONTACT_STAGE.map((stage) => stage.value) as [string, ...string[]]).optional(),
	title: z.string().optional(),
	company: z
		.object({
			name: z.string(),
			id: z.string().optional(),
		})
		.optional(),
	website: z.string().optional(),
	linkedin: z.string().optional(),
	facebook: z.string().optional(),
	twitter: z.string().optional(),
	instagram: z.string().optional(),
	address: z
		.object({
			street: z.string().optional(),
			city: z.string().optional(),
			state: z.string().optional(),
			zip: z.string().optional(),
			country: z.string().optional(),
		})
		.optional(),
	birthday: z.date().optional(),
	spouseName: z.string().optional(),
	summary: z.string().optional(),
	tags: z.array(z.string()).optional(),
	source: z.string().optional(),
	createdAt: z.date(),
	updatedAt: z.date(),
	buyerNeeds: z
		.array(
			z.object({
				propertyType: z.string(),
				priceRange: z.object({
					min: z.number(),
					max: z.number(),
				}),
				location: z.string(),
				timeline: z.string(),
			}),
		)
		.optional(),
});

export type ContactSchema = z.infer<typeof contactSchema>;

// Ensure ContactSchema extends BaseObjectSchema
export interface Contact extends BaseObjectSchema {
	name: string;
	firstName?: string;
	lastName?: string;
	image?: string;
	email: Array<{
		address: string;
		label?: string;
		isPrimary?: boolean;
		isBad?: boolean;
	}>;
	phone: Array<{
		number: string;
		label?: string;
		isPrimary?: boolean;
		isBad?: boolean;
	}>;
	status?: (typeof CONTACT_STATUS)[number]["value"];
	persona?: (typeof CONTACT_PERSONA)[number]["value"];
	stage?: (typeof CONTACT_STAGE)[number]["value"];
	title?: string;
	company?: {
		name: string;
		id?: string;
	};
	website?: string;
	linkedin?: string;
	facebook?: string;
	twitter?: string;
	instagram?: string;
	address?: {
		street?: string;
		city?: string;
		state?: string;
		zip?: string;
		country?: string;
	};
	birthday?: Date;
	spouseName?: string;
	summary?: string;
	tags?: string[];
	source?: string;
	buyerNeeds?: Array<{
		propertyType: string;
		priceRange: {
			min: number;
			max: number;
		};
		location: string;
		timeline: string;
	}>;
}
