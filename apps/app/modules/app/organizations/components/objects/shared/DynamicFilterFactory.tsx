import { ObjectType } from "@repo/database";

interface FilterConfig {
	label: string;
	value: string;
	type: "input" | "slider" | "checkbox";
	min?: number;
	max?: number;
}

interface ViewColumnDef {
	field: string;
	headerName: string;
	width: number;
}

/**
 * Creates dynamic filter fields for newly added columns based on object type and field characteristics
 */
export function createDynamicFilterFields(
	columnIds: string[],
	viewColumnDefs: ViewColumnDef[],
	objectType: ObjectType,
	existingFilterFieldIds: Set<string>
): FilterConfig[] {
	return columnIds
		.filter(
			(columnId) =>
				!existingFilterFieldIds.has(columnId) &&
				columnId !== "select" &&
				columnId !== "actions",
		)
		.map((columnId) => {
			// Find the column definition to get the header name
			const columnDef = viewColumnDefs.find(
				(def) => def.field === columnId,
			);
			const label = columnDef?.headerName || columnId;

			// Normalize field name for backend compatibility
			const normalizedFieldName = normalizeFieldName(columnId, objectType);

			// Determine the appropriate filter type based on field name
			const filterType = getFilterType(columnId, objectType);

			const filterConfig: FilterConfig = {
				label,
				value: normalizedFieldName,
				type: filterType,
			};

			// Add min/max values for slider fields
			if (filterConfig.type === "slider") {
				const sliderConfig = getSliderConfig(columnId);
				if (sliderConfig) {
					filterConfig.min = sliderConfig.min;
					filterConfig.max = sliderConfig.max;
				}
			}

			return filterConfig;
		});
}

/**
 * Normalizes field names for backend compatibility
 * Maps nested field names to their top-level equivalents where appropriate
 */
function normalizeFieldName(fieldName: string, objectType: ObjectType): string {
	if (objectType === "property") {
		// Map physicalDetails nested fields to top-level fields for backend compatibility
		if (fieldName === "physicalDetails.units") return "units";
		if (fieldName === "physicalDetails.bedrooms") return "bedrooms";
		if (fieldName === "physicalDetails.bathrooms") return "bathrooms";
		if (fieldName === "physicalDetails.squareFootage") return "squareFootage";
		if (fieldName === "physicalDetails.yearBuilt") return "yearBuilt";
		if (fieldName === "physicalDetails.lotSize") return "lotSize";
		
		// Map financials nested fields to top-level fields for backend compatibility
		if (fieldName === "financials.price") return "price";
		
		// Map address.location nested fields to address fields for backend compatibility
		if (fieldName === "location.address.street") return "address.location.street";
		if (fieldName === "location.address.city") return "address.location.city";
		if (fieldName === "location.address.state") return "address.location.state";
		if (fieldName === "location.address.zip") return "address.location.zip";
	}
	
	// Return original field name if no mapping needed
	return fieldName;
}

/**
 * Determines the appropriate filter type based on field name and object type
 */
function getFilterType(fieldName: string, objectType: ObjectType): "input" | "slider" | "checkbox" {
	if (objectType === "property") {
		// Slider fields for numeric property data
		if (fieldName.includes("units") || 
			fieldName.includes("bedrooms") || 
			fieldName.includes("bathrooms") ||
			fieldName.includes("squareFootage") ||
			fieldName.includes("yearBuilt") ||
			fieldName.includes("lotSize") ||
			fieldName.includes("price")) {
			return "slider";
		}
		// Status/enum fields
		if (fieldName.includes("status") || 
			fieldName.includes("propertyType") ||
			fieldName.includes("mlsStatus")) {
			return "checkbox";
		}
	}
	// Default to input for all other fields
	return "input";
}

/**
 * Gets min/max configuration for slider fields
 */
function getSliderConfig(fieldName: string): { min: number; max: number } | null {
	if (fieldName.includes("units")) {
		return { min: 0, max: 200 };
	} else if (fieldName.includes("bedrooms") || fieldName.includes("bathrooms")) {
		return { min: 0, max: 10 };
	} else if (fieldName.includes("squareFootage")) {
		return { min: 0, max: 50000 };
	} else if (fieldName.includes("yearBuilt")) {
		return { min: 1800, max: new Date().getFullYear() };
	} else if (fieldName.includes("price")) {
		return { min: 0, max: 10000000 };
	} else if (fieldName.includes("lotSize")) {
		return { min: 0, max: 100000 };
	}
	
	return null;
} 