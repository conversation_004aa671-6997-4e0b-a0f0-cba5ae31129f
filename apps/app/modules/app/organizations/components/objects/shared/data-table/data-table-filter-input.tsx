"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { useDebounce } from "@app/shared/hooks/useDebounce";
import { InputWithAddons } from "@ui/components/input-with-addons";
import { Label } from "@ui/components/label";
import { Search } from "lucide-react";
import { useEffect, useState } from "react";
import type { DataTableInputFilterField } from "./types";

function getFilter(filterValue: unknown) {
	return typeof filterValue === "string" ? filterValue : null;
}

export function DataTableFilterInput<TData>({
	value: _value,
}: DataTableInputFilterField<TData>) {
	const value = _value as string;
	const { table, columnFilters } = useDataTable();
	
	// Safe column lookup with fallback for server-side filtering
	let column = null;
	try {
		column = table.getColumn(value);
	} catch (error) {
		// Column doesn't exist, use server-side filtering fallback
		console.debug(`[Table] Column '${value}' not found, using server-side filtering`);
	}
	
	const filterValue = columnFilters.find((i) => i.id === value)?.value;
	const filters = getFilter(filterValue);
	const [input, setInput] = useState<string | null>(filters);

	const debouncedInput = useDebounce(input, 500);

	useEffect(() => {
		const newValue = debouncedInput?.trim() === "" ? null : debouncedInput;
		if (debouncedInput === null) return;

		// For server-side filtering, set filter even if no column exists
		if (column) {
			column.setFilterValue(newValue);
		} else {
			// Manually trigger filter change for server-side filtering
			const currentFilters = table.getState().columnFilters;
			const newFilters = currentFilters.filter(f => f.id !== value);
			if (newValue !== null) {
				newFilters.push({ id: value, value: newValue });
			}
			table.setColumnFilters(newFilters);
		}
	}, [debouncedInput, column, table, value]);

	useEffect(() => {
		if (debouncedInput?.trim() !== filters) {
			setInput(filters);
		}
	}, [filters]);

	return (
		<div className="grid w-full gap-1.5">
			<Label
				htmlFor={value}
				className="sr-only px-2 text-muted-foreground"
			>
				{value}
			</Label>
			<InputWithAddons
				placeholder="Search"
				leading={<Search className="mt-0.5 h-4 w-4" />}
				className="bg-muted/50 rounded-none border-none"
				containerClassName="h-9 bg-muted/50"
				name={value}
				id={value}
				value={input || ""}
				onChange={(e) => setInput(e.target.value)}
			/>
		</div>
	);
}
