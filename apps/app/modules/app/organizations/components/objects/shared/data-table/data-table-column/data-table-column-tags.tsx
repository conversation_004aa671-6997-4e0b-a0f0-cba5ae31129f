"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { useObjectTags } from "@app/shared/hooks/useTags";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import type { TaggableObjectType } from "@repo/database/src/types/object";
import { normalizeTagName } from "@repo/api/src/lib/tag-utils";

interface Tag {
  id: string;
  name: string;
  color: string | null;
}

interface DataTableColumnTagsProps {
  tags?: Tag[];
  objectType: TaggableObjectType;
  objectId: string;
  className?: string;
  maxVisible?: number;
  readOnly?: boolean; // Note: This component is now read-only by default; editing is handled by InlineCellEditor
}

export function DataTableColumnTags({ 
  tags = [], 
  objectType,
  objectId,
  className,
  maxVisible = 3,
  readOnly = true
}: DataTableColumnTagsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { user } = useSession();
  const { activeOrganization } = useActiveOrganization();

  const { data: objectTags, isLoading: isLoadingObjectTags } = useObjectTags(
    objectId,
    objectType
  );

  // Check if user has permission to manage tags
  const canManageTags = activeOrganization && user ? 
    isOrganizationAdmin(activeOrganization as any, user) || 
    activeOrganization.members?.some(member => member.userId === user.id) // All members can access tags
    : false;

  const handleTagClick = (tag: Tag, e: React.MouseEvent) => {

    if (e.button === 2) return;

    e.stopPropagation();
    
    const orgSlug = window.location.pathname.split('/')[2];
    
    let objectTypePlural = objectType + 's';
    if (objectType === 'company') objectTypePlural = 'companies';
    if (objectType === 'property') objectTypePlural = 'properties';

    const urlSegments = window.location.pathname.split('/');
    const viewIndex = urlSegments.findIndex(segment => segment === 'view');
    let currentViewId = viewIndex !== -1 && urlSegments[viewIndex + 1] ? urlSegments[viewIndex + 1] : null;

    if (!currentViewId) {
      currentViewId = searchParams.get("viewId");
    }

    const params = new URLSearchParams(searchParams);
    params.set("tags", normalizeTagName(tag.name));

    let viewUrl;
    if (currentViewId) {
      viewUrl = `/app/${orgSlug}/${objectTypePlural}/view/${currentViewId}?${params.toString()}`;
    } else {
      viewUrl = `/app/${orgSlug}/${objectTypePlural}?${params.toString()}`;
    }
    
    router.push(viewUrl);
  };

  if (isLoadingObjectTags) {
    return <div className="animate-pulse h-6 bg-muted rounded w-full" />;
  }

  const currentTags = objectTags?.map(ot => ot.tag) ?? [];

  if (!currentTags || currentTags.length === 0) {
    return "";
  }
  
  const visibleTags = currentTags.slice(0, maxVisible);
  const remainingCount = currentTags.length - maxVisible;

  return (
    <div className={cn("flex flex-wrap gap-1", className)}>
      {visibleTags.map((tag) => (
        <Badge
          key={tag.id}
          variant="views"
          className={cn(
            "text-xs px-2 py-0.5 cursor-pointer",
            "border border-border/50 hover:opacity-80 transition-opacity"
          )}
          style={{
            backgroundColor: `${tag.color || '#6b7280'}20`,
            borderColor: tag.color || '#6b7280',
            color: tag.color || '#6b7280',
          }}
          onClick={(e) => handleTagClick(tag, e)}
        >
          {tag.name}
        </Badge>
      ))}
      {remainingCount > 0 && (
        <Badge 
          variant="views" 
          className="text-xs px-2 py-0.5 text-muted-foreground border border-border/50"
        >
          +{remainingCount}
        </Badge>
      )}
    </div>
  );
} 