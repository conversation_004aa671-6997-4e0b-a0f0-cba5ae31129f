"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { formatCompactNumber } from "@app/organizations/lib/format";
import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import { STORAGE_KEYS } from "@app/shared/lib/constants";
import { useLocalStorage } from "@app/shared/lib/local-storage";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import { Kbd } from "@ui/components/kbd";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { formatDistanceToNow } from "date-fns";
import { LoaderCircle, PanelLeftClose, PanelLeftOpen, Search, X } from "lucide-react";
import type { ParserBuilder } from "nuqs";
import React, { useEffect, useMemo, useRef, useState } from "react";
import type { DataTableFilterField } from "../types";
import {
	columnFiltersParser,
	getFieldOptions,
	getFilterValue,
	getWordByCaretPosition,
	replaceInputByFieldType,
} from "./utils";
import { useControls } from "@app/organizations/lib/controls-provider";
import { Button } from "@ui/components/button";
import { IconLayoutSidebarLeftCollapse, IconLayoutSidebarLeftCollapseFilled, IconLayoutSidebarLeftExpand, IconLayoutSidebarRightCollapse } from "@tabler/icons-react";

// FIXME: there is an issue on cmdk if I wanna only set a single slider value...

interface DataTableFilterCommandProps {
	// TODO: maybe use generics for the parser
	searchParamsParser: Record<string, ParserBuilder<any>>;
}

// Add a helper function to generate unique keys
function generateUniqueKey(field: string, value: any): string {
	return `${field}:${String(value)}:${typeof value}`;
}

export function DataTableFilterCommand({
	searchParamsParser,
}: DataTableFilterCommandProps) {
	const {
		table,
		isLoading,
		filterFields: _filterFields,
		getFacetedUniqueValues,
	} = useDataTable();
	const columnFilters = table.getState().columnFilters;
	const inputRef = useRef<HTMLInputElement>(null);
	const [open, setOpen] = useState<boolean>(false);
	const { open: openControls, setOpen: setOpenControls } = useControls();
	const [currentWord, setCurrentWord] = useState<string>("");
	const filterFields = useMemo(
		() => _filterFields?.filter((i) => !i.commandDisabled),
		[_filterFields],
	);
	const columnParser = useMemo(
		() => columnFiltersParser({ searchParamsParser, filterFields }),
		[searchParamsParser, filterFields],
	);
	const [inputValue, setInputValue] = useState<string>(
		columnParser.serialize(columnFilters),
	);
	const [lastSearches, setLastSearches] = useLocalStorage<
		{
			search: string;
			timestamp: number;
		}[]
	>(STORAGE_KEYS.DATA_TABLE_COMMAND, []);

	const prevInputValueRef = useRef<string>("");
	const prevOpenRef = useRef<boolean>(false);
	const prevColumnFiltersRef = useRef<string>("");

	useEffect(() => {
		// TODO: we could check for ARRAY_DELIMITER or SLIDER_DELIMITER to auto-set filter when typing
		if (currentWord !== "" && open) return;
		if (inputValue.trim() === "" && !open) return;

		if (
			prevInputValueRef.current === inputValue &&
			prevOpenRef.current === open
		) {
			return;
		}

		prevInputValueRef.current = inputValue;
		prevOpenRef.current = open;

		const searchParams = columnParser.parse(inputValue);

		const currentFilters = table.getState().columnFilters;
		const currentEnabledFilters = currentFilters.filter((filter) => {
			const field = _filterFields?.find(
				(field) => field.value === filter.id,
			);
			return !field?.commandDisabled;
		});
		const currentDisabledFilters = currentFilters.filter((filter) => {
			const field = _filterFields?.find(
				(field) => field.value === filter.id,
			);
			return field?.commandDisabled;
		});

		const commandDisabledFilterKeys = currentDisabledFilters.reduce(
			(prev, curr) => {
				prev[curr.id] = curr.value;
				return prev;
			},
			{} as Record<string, unknown>,
		);

		const currentEnabledFiltersMap = currentEnabledFilters.reduce(
			(acc, filter) => {
				acc[filter.id] = filter.value;
				return acc;
			},
			{} as Record<string, unknown>,
		);

		const hasChanges =
			Object.keys(searchParams).some((key) => {
				return searchParams[key] !== currentEnabledFiltersMap[key];
			}) ||
			currentEnabledFilters.some((filter) => {
				return !(filter.id in searchParams);
			});

		if (!hasChanges) return;

		for (const key of Object.keys(searchParams)) {
			const value = searchParams[key as keyof typeof searchParams];
			// Safe column lookup with fallback for server-side filtering
			let column = null;
			try {
				column = table.getColumn(key);
			} catch (error) {
				// Column doesn't exist, use server-side filtering fallback
				console.debug(`[Table] Column '${key}' not found, using server-side filtering`);
			}
			
			if (column) {
				column.setFilterValue(value);
			} else {
				// For server-side filtering, manually set filter even if no column exists
				const currentFilters = table.getState().columnFilters;
				const newFilters = currentFilters.filter(f => f.id !== key);
				if (value !== undefined && value !== null) {
					newFilters.push({ id: key, value });
				}
				table.setColumnFilters(newFilters);
			}
		}
		const currentFiltersToReset = currentEnabledFilters.filter((filter) => {
			return !(filter.id in searchParams);
		});
		for (const filter of currentFiltersToReset) {
			// Safe column lookup with fallback for server-side filtering
			let column = null;
			try {
				column = table.getColumn(filter.id);
			} catch (error) {
				// Column doesn't exist, use server-side filtering fallback
				console.debug(`[Table] Column '${filter.id}' not found, using server-side filtering`);
			}
			
			if (column) {
				column.setFilterValue(undefined);
			} else {
				// For server-side filtering, manually clear filter even if no column exists
				const currentFilters = table.getState().columnFilters;
				const newFilters = currentFilters.filter(f => f.id !== filter.id);
				table.setColumnFilters(newFilters);
			}
		}
	}, [inputValue, open]);

	useEffect(() => {
		if (currentWord !== "" && !open) {
			setCurrentWord("");
		}
	}, [currentWord, open]);

	useEffect(() => {
		if (!open) {
			const serializedFilters = columnParser.serialize(columnFilters);

			if (serializedFilters !== prevColumnFiltersRef.current) {
				prevColumnFiltersRef.current = serializedFilters;
				setInputValue(serializedFilters);
			}
		}
	}, [columnFilters, filterFields, open, columnParser]);

	useHotkeys([
		{
			key: "f",
			callback: () => setOpen((open) => !open),
		},
	]);

	useEffect(() => {
		if (open) {
			inputRef?.current?.focus();
		}
	}, [open]);

	return (
		<div className="flex items-center gap-1">
			{!openControls && (
				<Button
					size="icon"
					variant="ghost"
					onClick={() => setOpenControls((prev) => !prev)}
				>
					{openControls ? (
						<IconLayoutSidebarLeftCollapseFilled className="h-4 w-4" />
					) : (
						<IconLayoutSidebarLeftExpand className="h-4 w-4" />
					)}
				</Button>
			)}
			<button
				type="button"
				className={cn(
					"group gap-2 cursor-pointer  flex w-full items-center rounded-lg border border-input bg-muted/50 px-3 text-muted-foreground ring-offset-background focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 hover:bg-accent/50 hover:text-accent-foreground",
					open ? "hidden" : "visible",
				)}
				onClick={() => setOpen(true)}
			>
				{isLoading ? (
					<LoaderCircle className="h-4 w-4 shrink-0 animate-spin text-muted-foreground opacity-50 group-hover:text-popover-foreground" />
				) : (
					<Search className="h-4 w-4 shrink-0 text-muted-foreground opacity-50 group-hover:text-popover-foreground" />
				)}
				<span className="h-11 w-full max-w-sm truncate py-3 text-left text-sm outline-none disabled:cursor-not-allowed disabled:opacity-50 md:max-w-xl lg:max-w-4xl xl:max-w-5xl">
					{inputValue.trim() ? (
						<span className="text-foreground">{inputValue}</span>
					) : (
						<span>Search data table...</span>
					)}
				</span>
				<Kbd className="ml-auto text-muted-foreground group-hover:text-accent-foreground">
					<span>F</span>
				</Kbd>
			</button>
			<Command
				className={cn(
					"overflow-visible rounded-lg border border-border shadow-md dark:bg-muted/50 [&>div]:border-none",
					open ? "visible" : "hidden",
				)}
				filter={(value, search, keywords) =>
					getFilterValue({ value, search, keywords, currentWord })
				}
				// loop
			>
				<CommandInput
					ref={inputRef}
					value={inputValue}
					onValueChange={setInputValue}
					onKeyDown={(e) => {
						if (e.key === "Escape") {
							inputRef?.current?.blur();
							setOpen(false);
						}
					}}
					onBlur={() => {
						setOpen(false);
						// FIXME: doesnt reflect the jumps
						// FIXME: will save non-existing searches
						// TODO: extract into function
						const search = inputValue.trim();
						if (!search) return;
						const timestamp = Date.now();
						const searchIndex = lastSearches.findIndex(
							(item) => item.search === search,
						);
						if (searchIndex !== -1) {
							lastSearches[searchIndex].timestamp = timestamp;
							setLastSearches(lastSearches);
							return;
						}
						setLastSearches([
							...lastSearches,
							{ search, timestamp },
						]);
						return;
					}}
					onInput={(e) => {
						const caretPosition =
							e.currentTarget?.selectionStart || -1;
						const value = e.currentTarget?.value || "";
						const word = getWordByCaretPosition({
							value,
							caretPosition,
						});
						setCurrentWord(word);
					}}
					placeholder="Search data table..."
					className="text-foreground"
				/>
				<div className="relative">
					<div className="absolute top-2 z-10 w-full overflow-hidden rounded-lg border-4 border-border bg-sidebar text-popover-foreground shadow-md outline-none animate-in">
						{/* default height is 300px but in case of more, we'd like to tease the user */}
						<CommandList className="max-h-[310px]">
							<CommandGroup heading="Filter">
								{filterFields.map((field) => {
									if (typeof field.value !== "string")
										return null;
									if (inputValue.includes(`${field.value}:`))
										return null;
									return (
										<CommandItem
											key={generateUniqueKey('field', field.value)}
											value={field.value}
											onMouseDown={(e) => {
												e.preventDefault();
												e.stopPropagation();
											}}
											onSelect={(value) => {
												setInputValue((prev) => {
													if (
														currentWord.trim() ===
														""
													) {
														const input = `${prev}${value}`;
														return `${input}:`;
													}
													// lots of cheat
													const isStarting =
														currentWord === prev;
													const prefix = isStarting
														? ""
														: " ";
													const input = prev.replace(
														`${prefix}${currentWord}`,
														`${prefix}${value}`,
													);
													return `${input}:`;
												});
												setCurrentWord(`${value}:`);
											}}
											className="group"
										>
											{field.value}
											<CommandItemSuggestions
												field={field}
											/>
										</CommandItem>
									);
								})}
							</CommandGroup>
							<CommandSeparator />
							<CommandGroup heading="Query">
								{filterFields?.map((field) => {
									if (typeof field.value !== "string")
										return null;
									if (
										!currentWord.includes(`${field.value}:`)
									)
										return null;

									// Safe column lookup with fallback for server-side filtering
									let column = null;
									try {
										column = table.getColumn(field.value);
									} catch (error) {
										// Column doesn't exist, use server-side filtering fallback
										console.debug(`[Table] Column '${field.value}' not found, using server-side filtering`);
									}
									
									const facetedValue =
										getFacetedUniqueValues?.(
											table,
											field.value,
										) || column?.getFacetedUniqueValues();

									const options = getFieldOptions({ field });

									return options.map((optionValue) => {
										return (
											<CommandItem
												key={generateUniqueKey(field.value, optionValue)}
												value={`${String(field.value)}:${optionValue}`}
												onMouseDown={(e) => {
													e.preventDefault();
													e.stopPropagation();
												}}
												onSelect={(value) => {
													setInputValue((prev) =>
														replaceInputByFieldType(
															{
																prev,
																currentWord,
																optionValue,
																value,
																field,
															},
														),
													);
													setCurrentWord("");
												}}
											>
												{`${optionValue}`}
												{facetedValue?.has(
													optionValue,
												) ? (
													<span className="ml-auto font-mono text-muted-foreground">
														{formatCompactNumber(
															facetedValue.get(
																optionValue,
															) || 0,
														)}
													</span>
												) : null}
											</CommandItem>
										);
									});
								})}
							</CommandGroup>
							<CommandSeparator />
							<CommandGroup heading="Suggestions">
								{lastSearches
									?.sort((a, b) => b.timestamp - a.timestamp)
									.slice(0, 5)
									.map((item) => {
										return (
											<CommandItem
												key={`suggestion:${item.search}:${item.timestamp}`}
												value={`suggestion:${item.search}`}
												onMouseDown={(e) => {
													e.preventDefault();
													e.stopPropagation();
												}}
												onSelect={(value) => {
													const search =
														value.replace(
															"suggestion:",
															"",
														);
													setInputValue(`${search} `);
													setCurrentWord("");
												}}
												className="group"
											>
												{item.search}
												<span className="ml-auto truncate text-muted-foreground/80 group-aria-[selected=true]:block">
													{formatDistanceToNow(
														item.timestamp,
														{
															addSuffix: true,
														},
													)}
												</span>
												<button
													type="button"
													onMouseDown={(e) => {
														e.preventDefault();
														e.stopPropagation();
													}}
													onClick={(e) => {
														e.preventDefault();
														e.stopPropagation();
														// TODO: extract into function
														setLastSearches(
															lastSearches.filter(
																(i) =>
																	i.search !==
																	item.search,
															),
														);
													}}
													className="ml-1 hidden rounded-md p-0.5 hover:bg-muted/50 group-aria-[selected=true]:block"
												>
													<X className="h-4 w-4" />
												</button>
											</CommandItem>
										);
									})}
							</CommandGroup>
							<CommandEmpty>No results found.</CommandEmpty>
						</CommandList>
						<div
							className="flex flex-wrap justify-between gap-3 border-t bg-accent p-4 text-accent-foreground text-xs z-50"
							cmdk-footer=""
						>
							<div className="flex flex-wrap gap-3">
								<span>
									Use <Kbd variant="outline">↑</Kbd>{" "}
									<Kbd variant="outline">↓</Kbd> to navigate
								</span>
								<span>
									<Kbd variant="outline">Enter</Kbd> to query
								</span>
								<span>
									<Kbd variant="outline">Esc</Kbd> to close
								</span>
								<Separator
									orientation="vertical"
									className="my-auto h-3"
								/>
								{/* TODO: Add examples */}
								{/* <span>
                  Union: <Kbd variant="outline">regions:a,b</Kbd>
                </span>
                <span>
                  Range: <Kbd variant="outline">p95:59-340</Kbd>
                </span> */}
							</div>
							{lastSearches.length ? (
								<button
									type="button"
									className="text-muted-foreground hover:text-accent-foreground"
									onMouseDown={(e) => {
										e.preventDefault();
										e.stopPropagation();
									}}
									onClick={() => setLastSearches([])}
								>
									Clear suggestions
								</button>
							) : null}
						</div>
					</div>
				</div>
			</Command>
		</div>
	);
}

// function CommandItemType<TData>

function CommandItemSuggestions<TData>({
	field,
}: {
	field: DataTableFilterField<TData>;
}) {
	const { table, getFacetedMinMaxValues, getFacetedUniqueValues } =
		useDataTable();
	const value = field.value as string;
	switch (field.type) {
		case "checkbox": {
			return (
				<span className="ml-1 hidden truncate text-muted-foreground/80 group-aria-[selected=true]:block">
					{getFacetedUniqueValues
						? Array.from(
								getFacetedUniqueValues(table, value)?.keys() ||
									[],
							)
								.map((value) => `[${value}]`)
								.join(" ")
						: field.options
								?.map(({ value }) => `[${value}]`)
								.join(" ")}
				</span>
			);
		}
		case "slider": {
			const [min, max] = getFacetedMinMaxValues?.(table, value) || [
				field.min,
				field.max,
			];
			return (
				<span className="ml-1 hidden truncate text-muted-foreground/80 group-aria-[selected=true]:block">
					[{min} - {max}]
				</span>
			);
		}
		case "input": {
			return (
				<span className="ml-1 hidden truncate text-muted-foreground/80 group-aria-[selected=true]:block">
					[{`${String(field.value)}`} input]
				</span>
			);
		}
		default: {
			return null;
		}
	}
}
