"use client";

import { useDroppable } from "@dnd-kit/core";
import {
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
	IconCircleFilled,
	IconClock,
	IconDotsVertical,
	IconEyeOff,
	IconGripVertical,
	IconPlus,
	IconSparkles,
	IconTrash,
} from "@tabler/icons-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { NumberBadge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { ColorPicker } from "@ui/components/color-picker";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import type {
	BaseObjectSchema,
	ObjectType,
} from "../../../../object-views/lib/types";
import { UniversalKanbanCard } from "./UniversalKanbanCard";

interface UniversalKanbanColumnProps<TData extends BaseObjectSchema> {
	id: string;
	title: string;
	icon: React.ComponentType<any>;
	color: string;
	items: TData[];
	selectedItems: Set<string>;
	objectType: ObjectType;
	statusAttribute: string;
	onSelectItem: (itemId: string) => void;
	onCreateNew?: (status: string) => void;
	onEdit?: (itemId: string) => void;
	onDelete?: (itemId: string) => void;
	onStatusVisibilityChange?: (status: string, visible: boolean) => void;
	onColumnUpdate?: (
		status: string,
		updates: {
			title?: string;
			color?: string;
			trackTime?: boolean;
			showConfetti?: boolean;
			targetTime?: number;
			targetTimeUnit?: string;
		},
	) => void;
	onColumnDelete?: (status: string) => void;
	onColumnDragStart?: (status: string) => void;
	onColumnDragEnd?: (fromStatus: string, toStatus: string) => void;
	organizationId: string;
	// Column metadata
	createdBy?: {
		name: string;
		image?: string;
	};
	createdAt?: Date;
	trackTime?: boolean;
	showConfetti?: boolean;
	targetTime?: number;
	targetTimeUnit?: string;
	// Card row fields
	cardRowFields?: Array<{
		field: string;
		headerName: string;
		type?: string;
	}>;
	// Show/hide attribute labels
	showAttributeLabels?: boolean;
	// Context menu props for cards
	onFavorite?: (e: React.MouseEvent, itemId: string) => void;
	onPin?: (e: React.MouseEvent, itemId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
	getFavoriteStatus?: (itemId: string) => boolean;
	getPinnedStatus?: (itemId: string) => boolean;
}

export function UniversalKanbanColumn<TData extends BaseObjectSchema>({
	id,
	title,
	icon: Icon,
	color,
	items,
	selectedItems,
	objectType,
	statusAttribute,
	onSelectItem,
	onCreateNew,
	onEdit,
	onDelete,
	onStatusVisibilityChange,
	onColumnUpdate,
	onColumnDelete,
	onColumnDragStart,
	onColumnDragEnd,
	organizationId,
	createdBy,
	createdAt,
	trackTime = false,
	showConfetti = false,
	targetTime = 1,
	targetTimeUnit = "days",
	cardRowFields = [],
	showAttributeLabels = true,
	onFavorite,
	onPin,
	onCopy,
	onHide,
	getFavoriteStatus,
	getPinnedStatus,
}: UniversalKanbanColumnProps<TData>) {
	const { setNodeRef, isOver, active } = useDroppable({
		id,
		data: {
			type: "column",
			status: id.replace("column-", ""),
		},
	});

	// Column dragging functionality
	const {
		attributes: columnAttributes,
		listeners: columnListeners,
		setNodeRef: setColumnNodeRef,
		transform: columnTransform,
		transition: columnTransition,
		isDragging: isColumnDragging,
	} = useSortable({
		id: `column-${id.replace("column-", "")}`,
		data: {
			type: "column",
			status: id.replace("column-", ""),
		},
	});

	const columnStyle = {
		transform: CSS.Transform.toString(columnTransform),
		transition: columnTransition,
	};

	const [isHovered, setIsHovered] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [editableTitle, setEditableTitle] = useState(title);
	const [confirmDelete, setConfirmDelete] = useState(false);
	const [targetTimeValue, setTargetTimeValue] = useState(targetTime);
	const [targetTimeUnitValue, setTargetTimeUnitValue] =
		useState(targetTimeUnit);

	// Sync editable title with prop changes
	useEffect(() => {
		setEditableTitle(title);
	}, [title]);

	// Sync target time with prop changes
	useEffect(() => {
		setTargetTimeValue(targetTime);
	}, [targetTime]);

	useEffect(() => {
		setTargetTimeUnitValue(targetTimeUnit);
	}, [targetTimeUnit]);

	// Add a class when column is being hovered over
	const columnClassName = `p-4 flex flex-col h-full overflow-y-auto rounded-xl border ${
		isOver
			? "border-blue-500 dark:bg-blue-900/10"
			: "border-zinc-200 bg-white dark:border-zinc-800 dark:bg-sidebar/50"
	} text-zinc-950 shadow-sm dark:text-zinc-50 ${
		isColumnDragging ? "opacity-50" : ""
	}`;

	const status = id.replace("column-", "");

	// Memoize the items array to prevent unnecessary re-renders
	const sortableItems = useMemo(() => items.map((item) => item.id), [items]);

	const showColumn = items.length > 0 || isOver || active;

	return (
		<div
			ref={(node) => {
				setNodeRef(node);
				setColumnNodeRef(node);
			}}
			className={columnClassName}
			style={columnStyle}
			data-droppable-id={id}
			data-status={status}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => {
				// Don't hide buttons if dropdown is open
				if (!isDropdownOpen) {
					setIsHovered(false);
				}
			}}
		>
			<div className="flex items-center justify-between gap-2 mb-4">
				<div className="flex items-center gap-2 flex-1 min-w-0">
					<ColorPicker
						value={color}
						onValueChange={(newColor) => {
							onColumnUpdate?.(status, { color: newColor });
						}}
					>
						{/* <div
							className="!w-5 !h-5 rounded-full border border-border cursor-pointer hover:scale-110 transition-transform"
							style={{ backgroundColor: color }}
						/> */}
						<IconCircleFilled
							className={
								"w-5 h-5 hover:scale-110 transition-transform cursor-pointer"
							}
							style={{ color: color }}
						/>
					</ColorPicker>

					<div className="flex items-center gap-2 flex-1 min-w-0">
						<span
							contentEditable
							suppressContentEditableWarning={true}
							onBlur={(e) => {
								const newTitle =
									e.currentTarget.textContent?.trim() ||
									title;
								if (newTitle !== title) {
									onColumnUpdate?.(status, {
										title: newTitle,
									});
								}
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									e.currentTarget.blur();
								}
							}}
							className={cn(
								"text-sm font-medium cursor-text hover:bg-muted/30 focus:bg-muted/50 focus:outline-none rounded px-1 py-0.5 flex-1 min-w-0",
							)}
						>
							{editableTitle}
						</span>

						{items.length > 0 && (
							<NumberBadge
								number={items.length}
								className="bg-zinc-100 dark:!bg-zinc-800 border-zinc-200 dark:!border-zinc-700"
							/>
						)}
					</div>
				</div>
				<div className="flex items-center gap-1">
					{(isHovered || isDropdownOpen) && (
						<>
							<Button
								variant="ghost"
								size="icon"
								className="h-6 w-6"
								onClick={() => onCreateNew?.(status)}
							>
								<IconPlus className="h-4 w-4" />
							</Button>

							<DropdownMenu
								onOpenChange={(open) => {
									setIsDropdownOpen(open);
									// If dropdown closes and we're not hovering, hide buttons
									if (!open && !isHovered) {
										setTimeout(
											() => setIsHovered(false),
											100,
										);
									}
								}}
							>
								<DropdownMenuTrigger asChild>
									<Button
										variant="ghost"
										size="icon"
										className="h-6 w-6"
										onClick={(e) => e.preventDefault()}
									>
										<IconDotsVertical className="h-4 w-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent
									align="end"
									className="w-56 rounded-xl z-50"
								>
									{(createdBy || createdAt) && (
										<>
											<div className="p-3">
												<span className="text-xs text-muted-foreground">
													Created By
												</span>
												{createdBy && (
													<div className="flex items-center gap-2 mt-1">
														<Avatar className="h-5 w-5">
															{createdBy.image && (
																<AvatarImage
																	src={
																		createdBy.image
																	}
																/>
															)}
															<AvatarFallback className="text-xs">
																{createdBy.name
																	.split(" ")
																	.map((n) =>
																		n.charAt(
																			0,
																		),
																	)
																	.join("")
																	.toUpperCase()}
															</AvatarFallback>
														</Avatar>
														<span className="text-xs font-medium">
															{createdBy.name}
														</span>
													</div>
												)}
												{createdAt && (
													<span className="text-xs text-muted-foreground">
														Created on{" "}
														{format(
															createdAt,
															"MMM d, yyyy",
														)}
													</span>
												)}
											</div>
											<DropdownMenuSeparator />
										</>
									)}

									<DropdownMenuItem
										className="rounded-lg flex items-center justify-between"
										onClick={(e) => e.preventDefault()}
									>
										<div className="flex items-center gap-2">
											<IconClock className="w-4 h-4" />
											Track time in status
										</div>
										<Switch
											checked={trackTime}
											onCheckedChange={(checked) => {
												onColumnUpdate?.(status, {
													trackTime: checked,
												});
											}}
										/>
									</DropdownMenuItem>

									{trackTime && (
										<div className="px-3 pb-3">
											<span className="text-xs font-mono text-muted-foreground mb-2 block">
												Target time
											</span>
											<div className="flex items-center gap-2">
												<Input
													type="number"
													value={targetTimeValue}
													onChange={(e) => {
														const value =
															Number.parseInt(
																e.target.value,
															) || 1;
														setTargetTimeValue(
															value,
														);
													}}
													onBlur={() => {
														if (
															targetTimeValue !==
															targetTime
														) {
															onColumnUpdate?.(
																status,
																{
																	targetTime:
																		targetTimeValue,
																	targetTimeUnit:
																		targetTimeUnitValue,
																},
															);
														}
													}}
													onKeyDown={(e) => {
														if (e.key === "Enter") {
															e.currentTarget.blur();
														}
													}}
													className="w-full h-8 text-sm font-mono"
													min="0"
												/>
												<Select
													value={targetTimeUnitValue}
													onValueChange={(value) => {
														setTargetTimeUnitValue(
															value,
														);
														onColumnUpdate?.(
															status,
															{
																targetTime:
																	targetTimeValue,
																targetTimeUnit:
																	value,
															},
														);
													}}
												>
													<SelectTrigger className="w-20 h-8 text-sm">
														<SelectValue />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="hours">
															Hours
														</SelectItem>
														<SelectItem value="days">
															Days
														</SelectItem>
														<SelectItem value="weeks">
															Weeks
														</SelectItem>
													</SelectContent>
												</Select>
											</div>
										</div>
									)}

									<DropdownMenuItem
										className="rounded-lg flex items-center justify-between"
										onClick={(e) => e.preventDefault()}
									>
										<div className="flex items-center gap-2">
											<IconSparkles className="w-4 h-4" />
											Show confetti
										</div>
										<Switch
											checked={showConfetti}
											onCheckedChange={(checked) => {
												onColumnUpdate?.(status, {
													showConfetti: checked,
												});
												if (checked) {
													// Trigger confetti animation
													import(
														"canvas-confetti"
													).then((confetti) => {
														confetti.default({
															particleCount: 100,
															spread: 70,
															origin: { y: 0.6 },
														});
													});
												}
											}}
										/>
									</DropdownMenuItem>

									<DropdownMenuSeparator />

									<DropdownMenuItem
										onClick={() =>
											onStatusVisibilityChange?.(
												status,
												false,
											)
										}
										className="rounded-lg flex items-center gap-2"
									>
										<IconEyeOff className="w-4 h-4" />
										Hide column
									</DropdownMenuItem>

									<DropdownMenuSeparator />

									<DropdownMenuItem
										className="rounded-lg !bg-destructive hover:!bg-red-600 dark:hover:!bg-red-800 text-white hover:!text-white flex items-center gap-2"
										onClick={(e) => {
											e.preventDefault();
											if (confirmDelete) {
												onColumnDelete?.(status);
												toast.success(
													"Column deleted successfully",
												);
												setConfirmDelete(false);
											} else {
												setConfirmDelete(true);
												// Reset confirmation after 3 seconds
												setTimeout(
													() =>
														setConfirmDelete(false),
													3000,
												);
											}
										}}
									>
										<IconTrash className="w-4 h-4" />
										{confirmDelete
											? "Are you sure?"
											: "Delete attribute"}
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>

							{/* TODO: Make drag better */}
							{/* <Button
								variant="ghost"
								size="icon"
								className="h-6 w-6 cursor-grab active:cursor-grabbing"
								data-column-drag-handle={status}
								{...columnAttributes}
								{...columnListeners}
							>
								<IconGripVertical className="h-4 w-4" />
							</Button> */}
						</>
					)}
				</div>
			</div>

			<div
				className="space-y-2 min-h-[200px] flex-grow relative rounded-lg overflow-y-auto"
				style={{
					transition: "background-color 150ms ease",
				}}
			>
				{showColumn ? (
					<SortableContext
						items={sortableItems}
						strategy={verticalListSortingStrategy}
					>
						<div className="space-y-2">
							{items.map((item) => (
								<UniversalKanbanCard
									key={item.id}
									item={item}
									objectType={objectType}
									statusAttribute={statusAttribute}
									isSelected={selectedItems.has(item.id)}
									selectedItems={selectedItems}
									onSelect={onSelectItem}
									onEdit={onEdit}
									onDelete={onDelete}
									trackTime={trackTime}
									targetTime={targetTime}
									targetTimeUnit={targetTimeUnit}
									cardRowFields={cardRowFields}
									showAttributeLabels={showAttributeLabels}
									organizationId={organizationId}
									isFavorite={
										getFavoriteStatus?.(item.id) || false
									}
									isPinned={
										getPinnedStatus?.(item.id) || false
									}
									onFavorite={onFavorite}
									onPin={onPin}
									onCopy={onCopy}
									onHide={onHide}
								/>
							))}
						</div>
					</SortableContext>
				) : (
					<div
						className="absolute inset-0 flex items-center justify-center text-zinc-400 dark:text-zinc-600 pointer-events-none"
						aria-hidden="true"
					>
						<div className="text-center">
							<div className="flex justify-center">
								<Icon
									className={`w-6 h-6 mb-2 opacity-30 ${color}`}
								/>
							</div>
							<p className="text-xs">Drop {objectType} here</p>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
