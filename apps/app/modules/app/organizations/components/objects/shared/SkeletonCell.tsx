"use client";

import { cn } from "@ui/lib";
import * as React from "react";

interface SkeletonCellProps {
	width?: number | string;
	height?: number | string;
	className?: string;
	variant?: "text" | "avatar" | "button" | "badge";
	isLoading?: boolean;
	children?: React.ReactNode;
}

export const SkeletonCell = React.memo(({
	width = "100%",
	height = 20,
	className,
	variant = "text",
	isLoading = true,
	children,
}: SkeletonCellProps) => {
	if (!isLoading && children) {
		return <>{children}</>;
	}

	const getVariantClasses = () => {
		switch (variant) {
			case "avatar":
				return "rounded-full w-6 h-6";
			case "button":
				return "rounded-md h-8";
			case "badge":
				return "rounded-full h-5";
			case "text":
			default:
				return "rounded-sm";
		}
	};

	return (
		<div
			className={cn(
				"animate-pulse bg-muted/50",
				getVariantClasses(),
				className
			)}
			style={{
				width: typeof width === "number" ? `${width}px` : width,
				height: typeof height === "number" ? `${height}px` : height,
			}}
		/>
	);
});

SkeletonCell.displayName = "SkeletonCell";

// Skeleton for entire table row
export const SkeletonRow = React.memo(({ 
	columnCount = 5,
	showSelect = true,
}: { 
	columnCount?: number; 
	showSelect?: boolean;
}) => {
	return (
		<tr className="h-10 border-b border-border">
			{showSelect && (
				<td className="w-10 p-0">
					<div className="flex items-center justify-center w-full h-full">
						<SkeletonCell variant="button" width={16} height={16} />
					</div>
				</td>
			)}
			{Array.from({ length: columnCount }, (_, index) => (
				<td key={index} className="p-2">
					<div className="flex items-center gap-2">
						{index === 0 && (
							<SkeletonCell variant="avatar" />
						)}
						<SkeletonCell 
							width={Math.random() * 100 + 80} 
							height={16}
						/>
					</div>
				</td>
			))}
		</tr>
	);
});

SkeletonRow.displayName = "SkeletonRow"; 