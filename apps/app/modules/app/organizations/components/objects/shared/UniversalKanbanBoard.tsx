"use client";

import BottomBar from "@app/contacts/components/BottomBar";
import {
	DndContext,
	type DragEndEvent,
	type DragOverEvent,
	DragOverlay,
	type DragStartEvent,
	MeasuringStrategy,
	MouseSensor,
	TouchSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	rectSortingStrategy,
	SortableContext,
} from "@dnd-kit/sortable";
import {
	IconCheck,
	IconCircleDotted,
	IconCircleFilled,
	IconPlus,
	IconSettings,
	IconX,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import {
	useCustomFieldDefinitions,
	useUpsertCustomFieldDefinition,
} from "../../../../custom-field-definitions/lib/api";
import { ObjectType } from "@repo/database";
import {
	createDynamicKanbanConfig,
	createNewStatus,
	getKanbanConfig,
	type KanbanConfig,
	type KanbanStatusConfig,
	mergeStatuses,
	NO_STATUS_VALUE,
	sanitizeStatusValue,
} from "../../../lib/kanban-config";
import { UniversalKanbanCard } from "./UniversalKanbanCard";
import { UniversalKanbanColumn } from "./UniversalKanbanColumn";

interface UniversalKanbanBoardProps {
	objectType: ObjectType;
	organizationId: string;
	data: any[];
	isLoading?: boolean;
	view?: {
		id: string;
		name: string;
		statusAttribute?: string;
		kanbanConfig?: {
			customStatuses?: KanbanStatusConfig[];
			hiddenColumns?: string[];
		};
		columnDefs?: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		cardRowFields?: Array<{
			field: string;
			headerName: string;
			type?: string;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
	};
	onStatusUpdate?: (objectId: string, newStatus: string) => void;
	onUpdateView?: (viewUpdate: any) => void;
	onCreateNew?: (status: string) => void;
	showAttributeLabels?: boolean;
}

export function UniversalKanbanBoard({
	objectType,
	organizationId,
	data = [],
	isLoading = false,
	view,
	onStatusUpdate,
	onUpdateView,
	onCreateNew,
	showAttributeLabels = true,
}: UniversalKanbanBoardProps) {
	const queryClient = useQueryClient();
	const [activeId, setActiveId] = useState<string | null>(null);
	const [activeItem, setActiveItem] = useState<any | null>(null);
	const [hiddenStatuses, setHiddenStatuses] = useState<Set<string>>(() => {
		// Initialize from view's hiddenColumns
		return new Set(view?.kanbanConfig?.hiddenColumns || []);
	});
	const [isManaging, setIsManaging] = useState(false);
	const [newStatusName, setNewStatusName] = useState("");
	const [isCreatingNewStatus, setIsCreatingNewStatus] = useState(false);
	const [editingStatusId, setEditingStatusId] = useState<string | null>(null);
	const [editingStatusName, setEditingStatusName] = useState("");
	const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

	// Fetch custom field definitions to get colors from database
	const { data: customFieldDefinitions = [] } = useCustomFieldDefinitions(
		objectType,
		organizationId,
	);

	// Sync hiddenStatuses with view's hiddenColumns when view changes
	useEffect(() => {
		const viewHiddenColumns = view?.kanbanConfig?.hiddenColumns || [];
		setHiddenStatuses(new Set(viewHiddenColumns));
	}, [view?.kanbanConfig?.hiddenColumns]);

	// Get unique status values from data to create dynamic configuration
	const uniqueStatusValues = useMemo(() => {
		if (!data.length || !view?.statusAttribute) return [];

		const statusField = view.statusAttribute;
		const values = new Set<string>();

		data.forEach((item) => {
			const value = item[statusField];
			if (value && typeof value === "string") {
				values.add(value);
			}
		});

		return Array.from(values);
	}, [data, view?.statusAttribute]);

	// Get kanban configuration with colors from database
	const kanbanConfig = useMemo((): KanbanConfig | null => {
		if (!view?.statusAttribute) return null;

		// Find the custom field definition for this status attribute
		const fieldDefinition = customFieldDefinitions.find(
			(def) => def.name === view.statusAttribute && def.type === "select",
		);

		// If we have a custom field definition, use ONLY its choices (ignore dynamic values from data)
		if (fieldDefinition?.options?.choices) {
			const databaseCustomStatuses: KanbanStatusConfig[] =
				fieldDefinition.options.choices.map((choice, index) => ({
					label: choice.label,
					value: choice.value,
					color: choice.color || "#6b7280", // Default color if none provided
					trackTime: choice.trackTime,
					showConfetti: choice.showConfetti,
					targetTime: choice.targetTime,
					targetTimeUnit: choice.targetTimeUnit,
					icon:
						view.kanbanConfig?.customStatuses?.find(
							(s) => s.value === choice.value,
						)?.icon || IconCircleDotted,
					order: index,
				}));

			// Merge database statuses with view statuses (database takes precedence for colors)
			const mergedCustomStatuses =
				view.kanbanConfig?.customStatuses?.map((viewStatus) => {
					const dbStatus = databaseCustomStatuses.find(
						(dbS) => dbS.value === viewStatus.value,
					);
					return dbStatus
						? { ...viewStatus, ...dbStatus }
						: viewStatus;
				}) || databaseCustomStatuses;

			const finalStatuses = mergeStatuses(
				[],
				mergedCustomStatuses,
				view.statusAttribute,
			);

			return {
				statusAttribute: view.statusAttribute,
				allowCustomStatuses: true,
				statuses: finalStatuses,
			};
		}

		// Fallback: Try to get config using existing logic with dynamic values
		let config = getKanbanConfig(
			objectType,
			view.statusAttribute,
			undefined, // Don't pass custom statuses here since we don't have field definition
			uniqueStatusValues,
		);

		// If no config found and we have data, create a dynamic one
		if (!config && uniqueStatusValues.length > 0) {
			config = createDynamicKanbanConfig(
				objectType,
				view.statusAttribute,
				uniqueStatusValues,
				undefined,
			);
		}

		return config;
	}, [
		objectType,
		view?.statusAttribute,
		view?.kanbanConfig?.customStatuses,
		uniqueStatusValues,
		customFieldDefinitions,
	]);

	// Filter visible statuses
	const visibleStatuses = useMemo(() => {
		if (!kanbanConfig) return [];
		return kanbanConfig.statuses.filter(
			(status) => !hiddenStatuses.has(status.value),
		);
	}, [kanbanConfig, hiddenStatuses]);

	// Group data by status
	const groupedData = useMemo(() => {
		if (!kanbanConfig || !view?.statusAttribute) return {};

		const statusField = view.statusAttribute;
		const groups: Record<string, any[]> = {};
		const validStatusValues = new Set(
			kanbanConfig.statuses.map((s) => s.value),
		);

		visibleStatuses.forEach((status) => {
			groups[status.value] = [];
		});

		// Group data by status
		data.forEach((item) => {
			const status = item[statusField];

			if (
				!status ||
				status === null ||
				status === undefined ||
				status === ""
			) {
				if (groups[NO_STATUS_VALUE]) {
					groups[NO_STATUS_VALUE].push(item);
				}
			} else if (validStatusValues.has(status) && groups[status]) {
				groups[status].push(item);
			}
		});

		return groups;
	}, [data, kanbanConfig, view?.statusAttribute, visibleStatuses]);

	// Sensors for drag and drop (matching tasks board configuration)
	const mouseSensor = useSensor(MouseSensor, {
		activationConstraint: {
			distance: 3, // Reduce distance to make it more responsive
		},
	});
	const touchSensor = useSensor(TouchSensor, {
		activationConstraint: {
			delay: 100, // Reduce delay to make it more responsive
			tolerance: 5,
		},
	});
	const sensors = useSensors(mouseSensor, touchSensor);

	// Mutation to save custom field definitions to database
	const saveCustomFieldDefinitionMutation = useUpsertCustomFieldDefinition();

	// Helper function to create custom field definition payload
	const createCustomFieldPayload = useCallback(
		(statusValues: string[], includeSettings = false) => {
			if (!view?.statusAttribute) return null;

			return {
				name: view.statusAttribute,
				label:
					view.statusAttribute.charAt(0).toUpperCase() +
					view.statusAttribute.slice(1),
				type: "select" as const,
				objectType: objectType,
				organizationId,
				options: {
					choices: statusValues.map((option) => {
						// Find corresponding status config for settings
						const statusConfig =
							includeSettings && kanbanConfig
								? kanbanConfig.statuses.find(
										(s) => s.value === option,
									)
								: null;

						return {
							label: option
								.split("_")
								.map(
									(word) =>
										word.charAt(0).toUpperCase() +
										word.slice(1),
								)
								.join(" "),
							value: option,
							...(statusConfig?.color && {
								color: statusConfig.color,
							}),
							...(statusConfig?.trackTime !== undefined && {
								trackTime: statusConfig.trackTime,
							}),
							...(statusConfig?.showConfetti !== undefined && {
								showConfetti: statusConfig.showConfetti,
							}),
							...(statusConfig?.targetTime !== undefined && {
								targetTime: statusConfig.targetTime,
							}),
							...(statusConfig?.targetTimeUnit !== undefined && {
								targetTimeUnit: statusConfig.targetTimeUnit,
							}),
						};
					}),
				},
			};
		},
		[view?.statusAttribute, objectType, organizationId, kanbanConfig],
	);

	const updateStatusMutation = useMutation({
		mutationFn: async ({
			objectId,
			newStatus,
		}: {
			objectId: string;
			newStatus: string;
		}) => {
			if (onStatusUpdate) {
				await onStatusUpdate(objectId, newStatus);
			}
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: [objectType, organizationId],
			});
			toast.success("Item status has been updated successfully.");
		},
		onError: (error) => {
			console.error("Failed to update status:", error);
			toast.error("Failed to update item status. Please try again.");
		},
	});

	const handleDragStart = useCallback(
		(event: DragStartEvent) => {
			const { active } = event;
			setActiveId(active.id as string);

			if (active.id.toString().startsWith("column-")) {
				// Do nothing
			} else {
				const item = data.find((d) => d.id === active.id);
				if (item) {
					setActiveItem(item);
				}
			}
		},
		[data],
	);

	const handleDragOver = useCallback((event: DragOverEvent) => {
		const { active, over } = event;
	}, []);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			if (!over) return;

			setActiveId(null);
			setActiveItem(null);

			const activeId = active.id as string;
			const overId = over.id as string;

			// Handle column reordering
			if (
				activeId.startsWith("column-") &&
				overId.startsWith("column-")
			) {
				const activeColumnStatus = activeId.replace("column-", "");
				const overColumnStatus = overId.replace("column-", "");

				if (activeColumnStatus !== overColumnStatus && kanbanConfig) {
					const activeIndex = visibleStatuses.findIndex(
						(s) => s.value === activeColumnStatus,
					);
					const overIndex = visibleStatuses.findIndex(
						(s) => s.value === overColumnStatus,
					);

					if (activeIndex !== -1 && overIndex !== -1) {
						const reorderedStatuses = arrayMove(
							kanbanConfig.statuses,
							activeIndex,
							overIndex,
						);

						const saveColumnOrder = async () => {
							try {
								const payload = {
									name: view?.statusAttribute || "status",
									label:
										(view?.statusAttribute || "status")
											.charAt(0)
											.toUpperCase() +
										(
											view?.statusAttribute || "status"
										).slice(1),
									type: "select" as const,
									objectType: objectType,
									organizationId,
									options: {
										choices: reorderedStatuses.map(
											(status, index) => ({
												label: status.label,
												value: status.value,
												color: status.color,
												...(status.trackTime !==
													undefined && {
													trackTime: status.trackTime,
												}),
												...(status.showConfetti !==
													undefined && {
													showConfetti:
														status.showConfetti,
												}),
												...(status.targetTime !==
													undefined && {
													targetTime:
														status.targetTime,
												}),
												...(status.targetTimeUnit !==
													undefined && {
													targetTimeUnit:
														status.targetTimeUnit,
												}),
											}),
										),
									},
								};

								await saveCustomFieldDefinitionMutation.mutateAsync(
									payload,
								);

								const updatedView = {
									...view,
									kanbanConfig: {
										...view?.kanbanConfig,
										customStatuses: reorderedStatuses,
									},
								};

								if (onUpdateView) {
									await onUpdateView(updatedView);
								}

								toast.success(
									"Column order updated successfully",
								);
							} catch (error) {
								console.error(
									"Failed to save column order:",
									error,
								);
								toast.error("Failed to save column order");
							}
						};

						saveColumnOrder();
					}
				}
				return;
			}

			// Handle item dragging (existing logic)
			// Find the active item
			const activeItem = data.find((d) => d.id === activeId);
			if (!activeItem || !view?.statusAttribute) return;

			const statusAttribute = view.statusAttribute;
			const currentStatus = activeItem[statusAttribute];

			// Determine which items to move (selected items if the dragged item is selected, otherwise just the dragged item)
			const itemsToMove = selectedItems.has(activeId)
				? data.filter((item) => selectedItems.has(item.id))
				: [activeItem];

			let newStatus = "";

			// Handle moving to a different column (direct column drop)
			if (over.id.toString().startsWith("column-")) {
				newStatus = over.id.toString().replace("column-", "");

				// Handle dropping on "No {field}" column
				if (newStatus === NO_STATUS_VALUE) {
					newStatus = ""; // Set to empty string to clear the status
				}

				if (currentStatus !== newStatus) {
					// Update all selected items
					itemsToMove.forEach((item) => {
						updateStatusMutation.mutate({
							objectId: item.id,
							newStatus: newStatus,
						});
					});
				}
			} else {
				// Handle dropping on another item
				const overItem = data.find((d) => d.id === over.id);
				if (overItem) {
					newStatus = overItem[statusAttribute];

					// Handle items in "No {field}" column
					if (
						!newStatus ||
						newStatus === null ||
						newStatus === undefined ||
						newStatus === ""
					) {
						newStatus = "";
					}

					if (currentStatus !== newStatus) {
						// Update all selected items
						itemsToMove.forEach((item) => {
							updateStatusMutation.mutate({
								objectId: item.id,
								newStatus: newStatus,
							});
						});
					}
				}
			}

			// Trigger confetti if the target column has confetti enabled
			if (newStatus && currentStatus !== newStatus && kanbanConfig) {
				const targetColumn = kanbanConfig.statuses.find(
					(s) => s.value === newStatus,
				);
				if (targetColumn?.showConfetti) {
					// Trigger confetti animation
					import("canvas-confetti").then((confetti) => {
						confetti.default({
							particleCount: 150,
							spread: 70,
							origin: { y: 0.6 },
							colors: [
								"#ef4444",
								"#f97316",
								"#f59e0b",
								"#84cc16",
								"#22c55e",
								"#06b6d4",
								"#3b82f6",
								"#8b5cf6",
								"#ec4899",
							],
						});
					});
				}
			}
		},
		[
			data,
			view?.statusAttribute,
			updateStatusMutation,
			selectedItems,
			kanbanConfig,
			visibleStatuses,
			objectType,
			organizationId,
			saveCustomFieldDefinitionMutation,
			onUpdateView,
		],
	);

	const handleHideStatus = useCallback(
		async (statusValue: string) => {
			// Update local state for immediate UI feedback
			setHiddenStatuses(
				(prev) => new Set([...Array.from(prev), statusValue]),
			);

			// Update view configuration to persist the change
			if (view && onUpdateView) {
				const currentHiddenColumns =
					view.kanbanConfig?.hiddenColumns || [];
				const updatedHiddenColumns = [
					...currentHiddenColumns,
					statusValue,
				];

				const updatedView = {
					...view,
					kanbanConfig: {
						...view.kanbanConfig,
						hiddenColumns: updatedHiddenColumns,
					},
				};

				try {
					await onUpdateView(updatedView);
					toast.success("Column hidden successfully");
				} catch (error) {
					console.error("Failed to hide column:", error);
					toast.error("Failed to hide column");
					// Revert local state on error
					setHiddenStatuses((prev) => {
						const newSet = new Set(Array.from(prev));
						newSet.delete(statusValue);
						return newSet;
					});
				}
			}
		},
		[view, onUpdateView],
	);

	const handleShowStatus = useCallback(
		async (statusValue: string) => {
			// Update local state for immediate UI feedback
			setHiddenStatuses((prev) => {
				const newSet = new Set(Array.from(prev));
				newSet.delete(statusValue);
				return newSet;
			});

			// Update view configuration to persist the change
			if (view && onUpdateView) {
				const currentHiddenColumns =
					view.kanbanConfig?.hiddenColumns || [];
				const updatedHiddenColumns = currentHiddenColumns.filter(
					(col: string) => col !== statusValue,
				);

				const updatedView = {
					...view,
					kanbanConfig: {
						...view.kanbanConfig,
						hiddenColumns: updatedHiddenColumns,
					},
				};

				try {
					await onUpdateView(updatedView);
					toast.success("Column shown successfully");
				} catch (error) {
					console.error("Failed to show column:", error);
					toast.error("Failed to show column");
					// Revert local state on error
					setHiddenStatuses(
						(prev) => new Set([...Array.from(prev), statusValue]),
					);
				}
			}
		},
		[view, onUpdateView],
	);

	const handleCreateNewStatus = useCallback(async () => {
		if (!newStatusName.trim() || !kanbanConfig) return;

		const newStatusValue = sanitizeStatusValue(newStatusName.trim());
		const newStatus = createNewStatus(
			newStatusName.trim(),
			kanbanConfig.statuses,
		);

		// Update the local kanban config
		const updatedStatuses = [...kanbanConfig.statuses, newStatus];

		// Save to database
		const allStatusValues = updatedStatuses.map((s) => s.value);
		const payload = createCustomFieldPayload(allStatusValues, true);
		if (payload) {
			await saveCustomFieldDefinitionMutation.mutateAsync(payload);
		}

		// Update the view configuration
		const updatedView = {
			...view,
			kanbanConfig: {
				...view?.kanbanConfig,
				customStatuses: updatedStatuses,
			},
		};

		if (onUpdateView) {
			await onUpdateView(updatedView);
		}

		setNewStatusName("");
		setIsCreatingNewStatus(false);
		toast.success(
			`"${newStatus.label}" has been added to the kanban board.`,
		);
	}, [
		newStatusName,
		kanbanConfig,
		view,
		onUpdateView,
		saveCustomFieldDefinitionMutation,
	]);

	const handleRenameStatus = useCallback(
		async (statusToRename: KanbanStatusConfig, newName: string) => {
			if (!newName.trim() || !kanbanConfig) return;

			// Check if status is being used
			const isInUse = data.some(
				(item) =>
					item[view?.statusAttribute || ""] === statusToRename.value,
			);

			if (isInUse) {
				// If in use, we need to update all records with the old value to the new value
				toast.error(
					"Cannot rename status that is currently in use. Please move items to another status first.",
				);
				return;
			}

			// Update the status in the config
			const updatedStatuses = kanbanConfig.statuses.map((status) =>
				status.value === statusToRename.value
					? { ...status, label: newName }
					: status,
			);

			// Save to database
			const allStatusValues = updatedStatuses.map((s) => s.value);
			const payload = createCustomFieldPayload(allStatusValues, true);
			if (payload) {
				await saveCustomFieldDefinitionMutation.mutateAsync(payload);
			}

			// Update the view configuration
			const updatedView = {
				...view,
				kanbanConfig: {
					...view?.kanbanConfig,
					customStatuses: updatedStatuses,
				},
			};

			if (onUpdateView) {
				await onUpdateView(updatedView);
			}

			setEditingStatusId(null);
			setEditingStatusName("");
			toast.success(`Status renamed to "${newName}".`);
		},
		[
			kanbanConfig,
			view,
			data,
			onUpdateView,
			saveCustomFieldDefinitionMutation,
		],
	);

	const handleDeleteStatus = useCallback(
		async (statusToDelete: KanbanStatusConfig) => {
			if (!kanbanConfig) return;

			// Check if status is being used
			const isInUse = data.some(
				(item) =>
					item[view?.statusAttribute || ""] === statusToDelete.value,
			);

			if (isInUse) {
				toast.error(
					"This status is currently being used by items. Move items to another status first.",
				);
				return;
			}

			// Remove from statuses
			const updatedStatuses = kanbanConfig.statuses.filter(
				(status) => status.value !== statusToDelete.value,
			);

			// Save to database
			const allStatusValues = updatedStatuses.map((s) => s.value);
			const payload = createCustomFieldPayload(allStatusValues, true);
			if (payload) {
				await saveCustomFieldDefinitionMutation.mutateAsync(payload);
			}

			// Update the view configuration
			const updatedView = {
				...view,
				kanbanConfig: {
					...view?.kanbanConfig,
					customStatuses: updatedStatuses,
				},
			};

			if (onUpdateView) {
				await onUpdateView(updatedView);
			}

			toast.success(
				`"${statusToDelete.label}" has been removed from the kanban board.`,
			);
		},
		[
			kanbanConfig,
			view,
			data,
			onUpdateView,
			saveCustomFieldDefinitionMutation,
		],
	);

	// Handle column updates (color, title, settings)
	const handleColumnUpdate = useCallback(
		async (
			statusValue: string,
			updates: {
				title?: string;
				color?: string;
				trackTime?: boolean;
				showConfetti?: boolean;
				targetTime?: number;
				targetTimeUnit?: string;
			},
		) => {
			if (!kanbanConfig) return;

			// Find the status to update
			const statusIndex = kanbanConfig.statuses.findIndex(
				(s) => s.value === statusValue,
			);
			if (statusIndex === -1) return;

			const updatedStatuses = [...kanbanConfig.statuses];
			const currentStatus = updatedStatuses[statusIndex];

			// Update the status with new properties
			updatedStatuses[statusIndex] = {
				...currentStatus,
				...(updates.title && { label: updates.title }),
				...(updates.color && { color: updates.color }),
				...(updates.trackTime !== undefined && {
					trackTime: updates.trackTime,
				}),
				...(updates.showConfetti !== undefined && {
					showConfetti: updates.showConfetti,
				}),
				...(updates.targetTime !== undefined && {
					targetTime: updates.targetTime,
				}),
				...(updates.targetTimeUnit !== undefined && {
					targetTimeUnit: updates.targetTimeUnit,
				}),
			};

			try {
				// Save custom field definition to database with updated colors and settings
				const payload = {
					name: view?.statusAttribute || "status",
					label:
						(view?.statusAttribute || "status")
							.charAt(0)
							.toUpperCase() +
						(view?.statusAttribute || "status").slice(1),
					type: "select" as const,
					objectType: objectType,
					organizationId,
					options: {
						choices: updatedStatuses.map((status) => ({
							label: status.label,
							value: status.value,
							color: status.color,
							...(status.trackTime !== undefined && {
								trackTime: status.trackTime,
							}),
							...(status.showConfetti !== undefined && {
								showConfetti: status.showConfetti,
							}),
							...(status.targetTime !== undefined && {
								targetTime: status.targetTime,
							}),
							...(status.targetTimeUnit !== undefined && {
								targetTimeUnit: status.targetTimeUnit,
							}),
						})),
					},
				};

				await saveCustomFieldDefinitionMutation.mutateAsync(payload);

				const updatedView = {
					...view,
					kanbanConfig: {
						...view?.kanbanConfig,
						customStatuses: updatedStatuses,
					},
				};

				if (onUpdateView) {
					await onUpdateView(updatedView);
				}

				if (updates.color) {
					toast.success("Column color updated successfully");
				}
				if (updates.title) {
					toast.success(`Column renamed to "${updates.title}"`);
				}
				if (updates.trackTime !== undefined) {
					toast.success(
						`Time tracking ${updates.trackTime ? "enabled" : "disabled"} for this column`,
					);
				}
				if (updates.showConfetti !== undefined) {
					toast.success(
						`Confetti ${updates.showConfetti ? "enabled" : "disabled"} for this column`,
					);
				}
				if (
					updates.targetTime !== undefined ||
					updates.targetTimeUnit !== undefined
				) {
					toast.success("Target time updated successfully");
				}
			} catch (error) {
				console.error("Failed to update column:", error);
				toast.error("Failed to update column settings");
			}
		},
		[
			kanbanConfig,
			view,
			objectType,
			organizationId,
			onUpdateView,
			saveCustomFieldDefinitionMutation,
		],
	);

	// Handle column deletion
	const handleColumnDelete = useCallback(
		async (statusValue: string) => {
			if (!kanbanConfig) return;

			const statusToDelete = kanbanConfig.statuses.find(
				(s) => s.value === statusValue,
			);
			if (!statusToDelete) return;

			await handleDeleteStatus(statusToDelete);
		},
		[kanbanConfig, handleDeleteStatus],
	);

	// Selected items array for BottomBar
	const selectedRecords = useMemo(() => {
		return data.filter((item) => selectedItems.has(item.id));
	}, [data, selectedItems]);

	// Handle item selection
	const handleSelectItem = useCallback((itemId: string) => {
		setSelectedItems((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(itemId)) {
				newSet.delete(itemId);
			} else {
				newSet.add(itemId);
			}
			return newSet;
		});
	}, []);

	// Clear selection (for when BottomBar actions complete)
	const handleClearSelection = useCallback(() => {
		setSelectedItems(new Set());
	}, []);

	if (!kanbanConfig) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						No kanban configuration found for this view.
					</p>
				</div>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col">
			{/* Kanban board */}
			<div className="flex-1 overflow-auto p-4">
				<DndContext
					sensors={sensors}
					onDragStart={handleDragStart}
					onDragOver={handleDragOver}
					onDragEnd={handleDragEnd}
					measuring={{
						droppable: {
							strategy: MeasuringStrategy.Always,
						},
					}}
				>
					<div
						className="grid gap-4 h-full max-w-full"
						style={{
							gridTemplateColumns: `repeat(${visibleStatuses.length + (isCreatingNewStatus ? 1 : 0) + 1}, minmax(300px, 1fr))`,
						}}
					>
						<SortableContext
							items={visibleStatuses.map(
								(s) => `column-${s.value}`,
							)}
							strategy={rectSortingStrategy}
						>
							{visibleStatuses.map((status) => (
								<UniversalKanbanColumn
									key={status.value}
									id={`column-${status.value}`}
									title={status.label}
									icon={status.icon}
									color={status.color}
									items={groupedData[status.value] || []}
									selectedItems={selectedItems}
									objectType={objectType}
									statusAttribute={
										kanbanConfig.statusAttribute
									}
									onSelectItem={handleSelectItem}
									onCreateNew={(status) => {
										if (onCreateNew) {
											// Convert NO_STATUS_VALUE back to empty string for creation
											const actualStatus =
												status === NO_STATUS_VALUE
													? ""
													: status;
											onCreateNew(actualStatus);
										}
									}}
									onEdit={(itemId: string) =>
										console.warn(
											`Edit ${objectType.slice(0, -1)} with id: ${itemId}`,
										)
									}
									onDelete={(itemId: string) =>
										console.warn(
											`Delete ${objectType.slice(0, -1)} with id: ${itemId}`,
										)
									}
									onStatusVisibilityChange={(
										statusValue,
										visible,
									) => {
										if (visible) {
											handleShowStatus(statusValue);
										} else {
											handleHideStatus(statusValue);
										}
									}}
									onColumnUpdate={handleColumnUpdate}
									onColumnDelete={handleColumnDelete}
									organizationId={organizationId}
									trackTime={status.trackTime}
									showConfetti={status.showConfetti}
									targetTime={status.targetTime}
									targetTimeUnit={status.targetTimeUnit}
									cardRowFields={view?.cardRowFields}
									showAttributeLabels={showAttributeLabels}
								/>
							))}

							{/* New Status Creation Column */}
							{isCreatingNewStatus ? (
								<div
									key="new-status-creation"
									className="flex flex-col"
								>
									<div className="flex items-center gap-2 mb-4 p-2">
										<IconCircleFilled
											className={
												"w-5 h-5 hover:scale-110 transition-transform cursor-pointer"
											}
											style={{ color: "#ffc107" }}
										/>
										<Input
											value={newStatusName}
											onChange={(
												e: React.ChangeEvent<HTMLInputElement>,
											) =>
												setNewStatusName(e.target.value)
											}
											onKeyDown={(
												e: React.KeyboardEvent<HTMLInputElement>,
											) => {
												if (e.key === "Enter") {
													handleCreateNewStatus();
												} else if (e.key === "Escape") {
													setIsCreatingNewStatus(
														false,
													);
													setNewStatusName("");
												}
											}}
											placeholder={`New ${view?.statusAttribute} name`}
											className="text-sm font-medium"
											autoFocus
										/>
										<Button
											size="sm"
											variant="ghost"
											onClick={handleCreateNewStatus}
											disabled={!newStatusName.trim()}
										>
											<IconCheck className="h-4 w-4" />
										</Button>
										<Button
											size="sm"
											variant="ghost"
											onClick={() => {
												setIsCreatingNewStatus(false);
												setNewStatusName("");
											}}
										>
											<IconX className="h-4 w-4" />
										</Button>
									</div>
								</div>
							) : (
								<div
									key="add-status-button"
									className="flex flex-col"
								>
									<div className="flex items-center justify-center mb-4 p-2">
										<Button
											variant="ghost"
											onClick={() =>
												setIsCreatingNewStatus(true)
											}
											className="w-full h-8 border border-dashed border-border hover:bg-muted/50 rounded-md"
											size="sm"
										>
											<IconPlus className="h-4 w-4" />
										</Button>
									</div>
								</div>
							)}
						</SortableContext>
					</div>

					<DragOverlay>
						{activeId?.startsWith("column-") ? (
							// Column drag overlay - smoother version
							(() => {
								const columnStatus = activeId.replace(
									"column-",
									"",
								);
								const columnConfig = kanbanConfig.statuses.find(
									(s) => s.value === columnStatus,
								);
								if (!columnConfig) return null;

								return (
									<div className="w-80 p-4 rounded-xl border-2 border-blue-500 bg-white/90 dark:bg-sidebar/90 shadow-2xl backdrop-blur-sm">
										<div className="flex items-center gap-2 mb-4">
											<div
												className="w-5 h-5 rounded-full border border-blue-200"
												style={{
													backgroundColor:
														columnConfig.color,
												}}
											/>
											<span className="text-sm font-medium text-blue-900 dark:text-blue-100">
												{columnConfig.label}
											</span>
											<span className="rounded-sm bg-blue-100 dark:bg-blue-900/50 px-1 py-0 text-xs font-medium border border-blue-200 font-mono text-blue-800 dark:text-blue-200">
												{groupedData[columnStatus]
													?.length || 0}
											</span>
										</div>
										<div className="space-y-2 max-h-32 overflow-hidden">
											{(groupedData[columnStatus] || [])
												.slice(0, 2)
												.map((item, index) => (
													<div
														key={item.id}
														className={`opacity-${80 - index * 20} pointer-events-none`}
													>
														<UniversalKanbanCard
															item={item}
															objectType={
																objectType
															}
															statusAttribute={
																kanbanConfig.statusAttribute
															}
															isSelected={false}
															selectedItems={
																new Set()
															}
															onSelect={() => {}}
															onEdit={() => {}}
															onDelete={() => {}}
															cardRowFields={
																view?.cardRowFields
															}
															showAttributeLabels={
																showAttributeLabels
															}
															organizationId={
																organizationId
															}
														/>
													</div>
												))}
											{(groupedData[columnStatus]
												?.length || 0) > 2 && (
												<div className="text-xs text-blue-600 dark:text-blue-400 text-center font-medium">
													+
													{(groupedData[columnStatus]
														?.length || 0) - 2}{" "}
													more items
												</div>
											)}
										</div>
									</div>
								);
							})()
						) : activeItem ? (
							// Card drag overlay - exact same appearance with blue styling
							selectedItems.has(activeItem.id) &&
							selectedItems.size > 1 ? (
								// Multiple selections - stack effect
								<div className="relative">
									{/* Main card - exact same as original but with blue styling */}
									<div className="border-2 border-blue-500 bg-white/95 dark:bg-sidebar/95 backdrop-blur-sm shadow-2xl rounded-lg pointer-events-none">
										<UniversalKanbanCard
											item={activeItem}
											objectType={objectType}
											statusAttribute={
												kanbanConfig.statusAttribute
											}
											isSelected={false}
											selectedItems={new Set()}
											onSelect={() => {}}
											onEdit={() => {}}
											onDelete={() => {}}
											cardRowFields={view?.cardRowFields}
											showAttributeLabels={
												showAttributeLabels
											}
											organizationId={organizationId}
										/>
									</div>
									{/* Stack indicators */}
									<div className="absolute inset-0 border-2 border-blue-400 bg-white/80 dark:bg-sidebar/80 rounded-lg -z-10 translate-x-1 translate-y-1" />
									{selectedItems.size > 2 && (
										<div className="absolute inset-0 border-2 border-blue-300 bg-white/70 dark:bg-sidebar/70 rounded-lg -z-20 translate-x-2 translate-y-2" />
									)}
									{/* Selection badge */}
									<div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-lg">
										{selectedItems.size}
									</div>
								</div>
							) : (
								// Single item drag - exact same as original but with blue styling
								<div className="border-2 border-blue-500 bg-white/95 dark:bg-sidebar/95 backdrop-blur-sm shadow-2xl rounded-lg pointer-events-none">
									<UniversalKanbanCard
										item={activeItem}
										objectType={objectType}
										statusAttribute={
											kanbanConfig.statusAttribute
										}
										isSelected={false}
										selectedItems={new Set()}
										onSelect={() => {}}
										onEdit={() => {}}
										onDelete={() => {}}
										cardRowFields={view?.cardRowFields}
										showAttributeLabels={
											showAttributeLabels
										}
										organizationId={organizationId}
									/>
								</div>
							)
						) : null}
					</DragOverlay>
				</DndContext>
			</div>

			{/* BottomBar for selected items */}
			<BottomBar
				selectedRecords={selectedRecords}
				objectType={objectType}
				setSelectedRecords={(records) => {
					setSelectedItems(
						new Set(records.map((record) => record.id)),
					);
				}}
			/>
		</div>
	);
}
