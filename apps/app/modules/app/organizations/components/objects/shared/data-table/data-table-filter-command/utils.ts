import {
	ARRAY_DELIMITER,
	RANGE_DELIMITER,
	SLIDER_DELIMITER,
} from "@app/organizations/lib/search-params-factory";
import { isArrayOfDates } from "@app/organizations/lib/is-array";
import type { ColumnFiltersState } from "@tanstack/react-table";
import type { <PERSON><PERSON><PERSON><PERSON>uilder } from "nuqs";
import type { DataTableFilterField } from "../types";

/**
 * Extracts the word from the given string at the specified caret position.
 */
export function getWordByCaretPosition({
	value,
	caretPosition,
}: {
	value: string;
	caretPosition: number;
}) {
	let start = caretPosition;
	let end = caretPosition;

	while (start > 0 && value[start - 1] !== " ") start--;
	while (end < value.length && value[end] !== " ") end++;

	const word = value.substring(start, end);
	return word;
}

export function replaceInputByFieldType<TData>({
	prev,
	currentWord,
	optionValue,
	value,
	field,
}: {
	prev: string;
	currentWord: string;
	optionValue?: string | number | boolean | undefined; // FIXME: use DataTableFilterField<TData>["options"][number];
	value: string;
	field: DataTableFilterField<TData>;
}) {
	switch (field.type) {
		case "checkbox": {
			if (currentWord.includes(ARRAY_DELIMITER)) {
				const words = currentWord.split(ARRAY_DELIMITER);
				words[words.length - 1] = `${optionValue}`;
				const input = prev.replace(
					currentWord,
					words.join(ARRAY_DELIMITER),
				);
				return `${input.trim()} `;
			}
			break;
		}
		case "slider": {
			if (currentWord.includes(SLIDER_DELIMITER)) {
				const words = currentWord.split(SLIDER_DELIMITER);
				words[words.length - 1] = `${optionValue}`;
				const input = prev.replace(
					currentWord,
					words.join(SLIDER_DELIMITER),
				);
				return `${input.trim()} `;
			}
			break;
		}
		case "timerange": {
			if (currentWord.includes(RANGE_DELIMITER)) {
				const words = currentWord.split(RANGE_DELIMITER);
				words[words.length - 1] = `${optionValue}`;
				const input = prev.replace(
					currentWord,
					words.join(RANGE_DELIMITER),
				);
				return `${input.trim()} `;
			}
			break;
		}
	}

	// Default case for all field types
	const input = prev.replace(currentWord, value);
	return `${input.trim()} `;
}

export function getFieldOptions<TData>({
	field,
}: {
	field: DataTableFilterField<TData>;
}) {
	switch (field.type) {
		case "slider": {
			return field.options?.length
				? field.options
						.map(({ value }) => value)
						.sort((a, b) => Number(a) - Number(b))
						.filter(notEmpty)
				: Array.from(
						{ length: field.max - field.min + 1 },
						(_, i) => field.min + i,
					) || [];
		}
		case "timerange": {
			// Return common date range presets as options
			const today = new Date();
			const yesterday = new Date(today);
			yesterday.setDate(yesterday.getDate() - 1);
			const weekAgo = new Date(today);
			weekAgo.setDate(weekAgo.getDate() - 7);
			const monthAgo = new Date(today);
			monthAgo.setDate(monthAgo.getDate() - 30);

			return [
				today.toISOString().split("T")[0], // Today
				yesterday.toISOString().split("T")[0], // Yesterday
				`${weekAgo.toISOString().split("T")[0]}${RANGE_DELIMITER}${today.toISOString().split("T")[0]}`, // Last 7 days
				`${monthAgo.toISOString().split("T")[0]}${RANGE_DELIMITER}${today.toISOString().split("T")[0]}`, // Last 30 days
			];
		}
		default: {
			return (
				field.options?.map(({ value }) => value).filter(notEmpty) || []
			);
		}
	}
}

export function getFilterValue({
	value,
	search,
	currentWord,
}: {
	value: string;
	search: string;
	keywords?: string[] | undefined;
	currentWord: string;
}): number {
	/**
	 * @example value "suggestion:public:true regions,ams,gru,fra"
	 */
	if (value.startsWith("suggestion:")) {
		const rawValue = value.toLowerCase().replace("suggestion:", "");
		if (rawValue.includes(search)) return 1;
		return 0;
	}

	/** */
	if (value.toLowerCase().includes(currentWord.toLowerCase())) return 1;

	/**
	 * @example checkbox [filter, query] = ["regions", "ams,gru,fra"]
	 * @example slider [filter, query] = ["p95", "0-3000"]
	 * @example input [filter, query] = ["name", "api"]
	 */
	const [filter, query] = currentWord.toLowerCase().split(":");
	if (query && value.startsWith(`${filter}:`)) {
		if (query.includes(ARRAY_DELIMITER)) {
			/**
			 * array of n elements
			 * @example queries = ["ams", "gru", "fra"]
			 */
			const queries = query.split(ARRAY_DELIMITER);
			const rawValue = value.toLowerCase().replace(`${filter}:`, "");
			if (
				queries.some(
					(item, i) => item === rawValue && i !== queries.length - 1,
				)
			)
				return 0;
			if (queries.some((item) => rawValue.includes(item))) return 1;
		}
		if (query.includes(SLIDER_DELIMITER)) {
			/**
			 * range between 2 elements
			 * @example queries = ["0", "3000"]
			 */
			const queries = query.split(SLIDER_DELIMITER);
			const rawValue = value.toLowerCase().replace(`${filter}:`, "");

			const rawValueAsNumber = Number.parseInt(rawValue);
			const queryAsNumber = Number.parseInt(queries[0]);

			if (queryAsNumber < rawValueAsNumber) {
				if (rawValue.includes(queries[1])) return 1;
				return 0;
			}
			return 0;
		}
		const rawValue = value.toLowerCase().replace(`${filter}:`, "");
		if (rawValue.includes(query)) return 1;
	}
	return 0;
}

export function getFieldValueByType<TData>({
	field,
	value,
}: {
	field?: DataTableFilterField<TData>;
	value: unknown;
}) {
	if (!field) return null;

	switch (field.type) {
		case "slider": {
			if (Array.isArray(value)) {
				return value.join(SLIDER_DELIMITER);
			}
			return value;
		}
		case "checkbox": {
			if (Array.isArray(value)) {
				return value.join(ARRAY_DELIMITER);
			}
			// REMINER: inversed logic
			if (typeof value === "string") {
				return value.split(ARRAY_DELIMITER);
			}
			return value;
		}
		case "timerange": {
			if (Array.isArray(value)) {
				if (isArrayOfDates(value)) {
					return value
						.map((date) => date.getTime())
						.join(RANGE_DELIMITER);
				}
				return value.join(RANGE_DELIMITER);
			}
			if (value instanceof Date) {
				return value.getTime();
			}
			return value;
		}
		default: {
			return value;
		}
	}
}

export function notEmpty<TValue>(
	value: TValue | null | undefined,
): value is TValue {
	return value !== null && value !== undefined;
}

export function columnFiltersParser<TData>({
	searchParamsParser,
	filterFields,
}: {
	searchParamsParser: Record<string, ParserBuilder<any>>;
	filterFields: DataTableFilterField<TData>[];
}) {
	return {
		parse: (inputValue: string) => {
			// Split by spaces but be smart about checkbox fields that might contain spaces
			const tokens = [];
			const parts = inputValue.trim().split(" ");
			let i = 0;
			
			while (i < parts.length) {
				const part = parts[i];
				
				// If this part contains a colon, it's a field:value pair
				if (part.includes(":")) {
					const [fieldName, ...valueParts] = part.split(":");
					const field = filterFields?.find(f => f.value === fieldName);
					
					// For checkbox fields (like tags), collect all following parts until the next field
					if (field?.type === "checkbox") {
						let value = valueParts.join(":");
						let j = i + 1;
						
						// Collect subsequent parts until we hit another field:value pattern
						while (j < parts.length && !parts[j].includes(":")) {
							value += " " + parts[j];
							j++;
						}
						
						tokens.push(`${fieldName}:${value}`);
						i = j; // Skip the parts we consumed
					} else {
						tokens.push(part);
						i++;
					}
				} else {
					tokens.push(part);
					i++;
				}
			}

			const values = tokens.reduce(
				(prev, curr) => {
					const colonIndex = curr.indexOf(":");
					if (colonIndex === -1) return prev;
					
					const name = curr.substring(0, colonIndex);
					let value = curr.substring(colonIndex + 1);
					
					// Remove surrounding quotes if present
					if (value.startsWith('"') && value.endsWith('"')) {
						value = value.slice(1, -1);
					}
					
					if (!value || !name) return prev;
					prev[name] = value;
					return prev;
				},
				{} as Record<string, string>,
			);

			const searchParams = Object.entries(values).reduce(
				(prev, [key, value]) => {
					const parser = searchParamsParser[key];
					const field = filterFields?.find((f) => f.value === key);

					if (!parser) return prev;

					// For timerange fields with formatted date strings, convert back to proper format
					if (
						field?.type === "timerange" &&
						typeof value === "string" &&
						value.includes(RANGE_DELIMITER)
					) {
						const dateStrings = value.split(RANGE_DELIMITER);
						// Convert ISO date strings to timestamps for the parser
						const timestamps = dateStrings
							.map((dateStr) => new Date(dateStr).getTime())
							.join(RANGE_DELIMITER);
						prev[key] = parser.parse(timestamps);
						return prev;
					}

					prev[key] = parser.parse(value);
					return prev;
				},
				{} as Record<string, unknown>,
			);

			return searchParams;
		},
		serialize: (columnFilters: ColumnFiltersState) => {
			const values = columnFilters.reduce((prev, curr) => {
				const field = filterFields?.find(
					(field) => curr.id === field.value,
				);
				const { commandDisabled } = field || { commandDisabled: true };
				const parser = searchParamsParser[curr.id];

				if (commandDisabled || !parser) return prev;

				// For timerange fields, format dates properly for display
				if (
					field?.type === "timerange" &&
					Array.isArray(curr.value) &&
					isArrayOfDates(curr.value)
				) {
					const dates = curr.value as Date[];
					const formattedDates = dates
						.map((date) => date.toISOString().split("T")[0])
						.join(RANGE_DELIMITER);
					return `${prev}${curr.id}:${formattedDates} `;
				}

				// For checkbox fields, preserve spaces in values by wrapping in quotes if needed
				if (field?.type === "checkbox") {
					const serializedValue = parser.serialize(curr.value);
					const needsQuotes = serializedValue.includes(" ");
					const finalValue = needsQuotes ? `"${serializedValue}"` : serializedValue;
					return `${prev}${curr.id}:${finalValue} `;
				}

				return `${prev}${curr.id}:${parser.serialize(curr.value)} `;
			}, "");

			return values;
		},
	};
}
