"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { isArrayOfDates } from "@app/organizations/lib/is-array";
import { DatePickerWithRange } from "@ui/components/date-picker-with-range";
import { useMemo } from "react";
import type { DateRange } from "react-day-picker";
import type { DataTableTimerangeFilterField } from "./types";

export function DataTableFilterTimerange<TData>({
	value: _value,
	presets,
}: DataTableTimerangeFilterField<TData>) {
	const value = _value as string;
	const { table, columnFilters } = useDataTable();
	
	// Safe column lookup with fallback for server-side filtering
	let column = null;
	try {
		column = table.getColumn(value);
	} catch (error) {
		// Column doesn't exist, use server-side filtering fallback
		console.debug(`[Table] Column '${value}' not found, using server-side filtering`);
	}
	
	const filterValue = columnFilters.find((i) => i.id === value)?.value;

	const date: DateRange | undefined = useMemo(
		() =>
			filterValue instanceof Date
				? { from: filterValue, to: undefined }
				: Array.isArray(filterValue) && isArrayOfDates(filterValue)
					? { from: filterValue?.[0], to: filterValue?.[1] }
					: undefined,
		[filterValue],
	);

	const setDate = (date: DateRange | undefined) => {
		let filterValue: any = undefined;
		
		if (!date) {
			// Clear the filter when no date is selected
			filterValue = undefined;
		} else if (date.from && !date.to) {
			filterValue = [date.from];
		} else if (date.to && date.from) {
			filterValue = [date.from, date.to];
		}
		
		if (column) {
			column.setFilterValue(filterValue);
		} else {
			// For server-side filtering, manually set filter even if no column exists
			const currentFilters = table.getState().columnFilters;
			const newFilters = currentFilters.filter(f => f.id !== value);
			if (filterValue !== undefined) {
				newFilters.push({ id: value, value: filterValue });
			}
			table.setColumnFilters(newFilters);
		}
	};

	return <DatePickerWithRange {...{ date, setDate, presets }} />;
}
