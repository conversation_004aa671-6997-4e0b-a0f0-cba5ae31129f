import {
	IconAt,
	IconBrandFacebook,
	IconBrandInstagram,
	IconBrandLinkedin,
	IconBrandTwitter,
	IconBriefcase,
	IconBuilding,
	IconCake,
	IconCalendar,
	IconCalendarClock,
	IconChartColumn,
	IconCurrencyDollar,
	IconFileText,
	IconHash,
	IconHome,
	IconMapPin,
	IconNote,
	IconPhone,
	IconRuler,
	IconSignature,
	IconTag,
	IconUsers,
	IconWorld,
	IconUser,
	IconLocation,
	IconEye,
	IconTrash,
	IconClock,
	IconUserCheck,
	IconUsersGroup,
	IconLink,
	IconCheck,
	IconX,
	IconShield,
	IconFlag,
	IconCertificate,
	IconMoneybag,
	IconBuildingEstate,
	IconCar,
	IconFlame,
	IconDroplet,
	IconBolt,
	IconHeart,
	IconScale,
	IconGavel,
	IconTrendingUp,
	IconPercentage,
	IconMap,
	IconClipboardList,
	IconBed,
	IconBath,
	<PERSON>conDoor,
	IconTrees,
	IconHammer,
	IconRoad,
	IconBarrierBlock,
	IconNumbers,
	IconCamera,
	IconChartLine,
	IconHandStop,
	IconMoodHeart,
	IconAlertTriangle,
	IconDatabase,
	IconId,
	IconCashBanknote,
} from "@tabler/icons-react";
import { ADDRESS_FIELDS, ADDRESS_LABELS } from "../../../lib/constants";

export interface AttributeDefinition {
	field: string;
	label: string;
	icon: any;
	category: string;
	readonly?: boolean;
}

// Address field definitions for consistent reuse
const ADDRESS_ATTRIBUTES: AttributeDefinition[] = [
	{
		field: `address.location.${ADDRESS_FIELDS.STREET}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.STREET],
		icon: IconMapPin,
		category: "Location",
	},
	{
		field: `address.location.${ADDRESS_FIELDS.STREET2}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.STREET2],
		icon: IconMapPin,
		category: "Location",
	},
	{
		field: `address.location.${ADDRESS_FIELDS.CITY}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.CITY],
		icon: IconMapPin,
		category: "Location",
	},
	{
		field: `address.location.${ADDRESS_FIELDS.STATE}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.STATE],
		icon: IconMapPin,
		category: "Location",
	},
	{
		field: `address.location.${ADDRESS_FIELDS.ZIP}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.ZIP],
		icon: IconMapPin,
		category: "Location",
	},
	{
		field: `address.location.${ADDRESS_FIELDS.COUNTY}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.COUNTY],
		icon: IconMapPin,
		category: "Location",
	},
	{
		field: `address.location.${ADDRESS_FIELDS.COUNTRY}`,
		label: ADDRESS_LABELS[ADDRESS_FIELDS.COUNTRY],
		icon: IconMapPin,
		category: "Location",
	},
];

export const AVAILABLE_ATTRIBUTES: Record<string, AttributeDefinition[]> = {
	contact: [
		// Basic Info
		{
			field: "id",
			label: "Record ID",
			icon: IconHash,
			category: "Basic Info",
			readonly: true,
		},
		{
			field: "apolloId",
			label: "Apollo ID",
			icon: IconId,
			category: "System",
			readonly: true,
		},
		
		// Personal Information
		{
			field: "firstName",
			label: "First name",
			icon: IconSignature,
			category: "Name",
		},
		{
			field: "lastName",
			label: "Last name",
			icon: IconSignature,
			category: "Name",
		},
		{
			field: "image",
			label: "Profile Image",
			icon: IconCamera,
			category: "Personal",
		},
		{
			field: "title",
			label: "Job title",
			icon: IconBriefcase,
			category: "Professional",
		},
		{
			field: "persona",
			label: "Persona",
			icon: IconUser,
			category: "Personal",
		},
		{
			field: "status",
			label: "Status",
			icon: IconChartColumn,
			category: "Sales",
		},
		
		// Contact Information
		{
			field: "address",
			label: "Full Address",
			icon: IconMapPin,
			category: "Contact Info",
		},
		...ADDRESS_ATTRIBUTES,
		{
			field: "phone",
			label: "Phone numbers",
			icon: IconPhone,
			category: "Contact Info",
		},
		{
			field: "email",
			label: "Email addresses",
			icon: IconAt,
			category: "Contact Info",
		},
		{
			field: "website",
			label: "Website",
			icon: IconWorld,
			category: "Contact Info",
		},
		{
			field: "social",
			label: "Social Media",
			icon: IconBrandLinkedin,
			category: "Social",
		},
		{
			field: "social.linkedin",
			label: "LinkedIn",
			icon: IconBrandLinkedin,
			category: "Social",
		},
		{
			field: "social.facebook",
			label: "Facebook",
			icon: IconBrandFacebook,
			category: "Social",
		},
		{
			field: "social.twitter",
			label: "Twitter",
			icon: IconBrandTwitter,
			category: "Social",
		},
		{
			field: "social.instagram",
			label: "Instagram",
			icon: IconBrandInstagram,
			category: "Social",
		},
		
		// Additional Info
		{
			field: "source",
			label: "Source",
			icon: IconLink,
			category: "Sales",
		},
		{
			field: "stage",
			label: "Stage",
			icon: IconChartColumn,
			category: "Sales",
		},
		{
			field: "birthday",
			label: "Birthday",
			icon: IconCake,
			category: "Personal",
		},
		{
			field: "age",
			label: "Age",
			icon: IconCalendar,
			category: "Personal",
		},
		{
			field: "spouseName",
			label: "Spouse Name",
			icon: IconMoodHeart,
			category: "Personal",
		},
		{
			field: "summary",
			label: "Summary",
			icon: IconFileText,
			category: "Basic Info",
		},
		
		// Company Association
		{
			field: "companyId",
			label: "Company ID",
			icon: IconBuilding,
			category: "Company",
		},
		{
			field: "company.name",
			label: "Company Name",
			icon: IconBuilding,
			category: "Company",
		},
		
		// Buyer Information
		{
			field: "buyerNeeds",
			label: "Buyer Needs",
			icon: IconClipboardList,
			category: "Sales",
		},
		{
			field: "generatedSummary",
			label: "Generated Summary",
			icon: IconFileText,
			category: "AI Generated",
		},
		
		// Audit fields
		{
			field: "createdBy",
			label: "Created By",
			icon: IconUserCheck,
			category: "System",
			readonly: true,
		},
		{
			field: "updatedBy",
			label: "Updated By",
			icon: IconUserCheck,
			category: "System",
			readonly: true,
		},
		{
			field: "lastViewedAt",
			label: "Last Viewed",
			icon: IconEye,
			category: "System",
			readonly: true,
		},
		{
			field: "lastViewedBy",
			label: "Last Viewed By",
			icon: IconEye,
			category: "System",
			readonly: true,
		},
		{
			field: "isDeleted",
			label: "Is Deleted",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "deletedAt",
			label: "Deleted At",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "deletedBy",
			label: "Deleted By",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "createdAt",
			label: "Created",
			icon: IconCalendarClock,
			category: "System",
			readonly: true,
		},
		{
			field: "updatedAt",
			label: "Updated",
			icon: IconCalendarClock,
			category: "System",
			readonly: true,
		},
		{
			field: "tags",
			label: "Tags",
			icon: IconTag,
			category: "Basic Info",
		},
	],
	
	company: [
		// Basic Info
		{
			field: "id",
			label: "Record ID",
			icon: IconHash,
			category: "Basic Info",
			readonly: true,
		},
		{
			field: "name",
			label: "Company Name",
			icon: IconBuilding,
			category: "Basic Info",
		},
		{
			field: "website",
			label: "Website",
			icon: IconWorld,
			category: "Contact Info",
		},
		{
			field: "industry",
			label: "Industry",
			icon: IconBriefcase,
			category: "Company Details",
		},
		{
			field: "size",
			label: "Company Size",
			icon: IconUsers,
			category: "Company Details",
		},
		{
			field: "description",
			label: "Description",
			icon: IconFileText,
			category: "Basic Info",
		},
		{
			field: "logo",
			label: "Logo",
			icon: IconCamera,
			category: "Basic Info",
		},
		
		// Contact Information
		{
			field: "address",
			label: "Full Address",
			icon: IconMapPin,
			category: "Contact Info",
		},
		...ADDRESS_ATTRIBUTES,
		{
			field: "phone",
			label: "Phone",
			icon: IconPhone,
			category: "Contact Info",
		},
		{
			field: "email",
			label: "Email",
			icon: IconAt,
			category: "Contact Info",
		},
		
		// Audit fields
		{
			field: "createdBy",
			label: "Created By",
			icon: IconUserCheck,
			category: "System",
			readonly: true,
		},
		{
			field: "updatedBy",
			label: "Updated By",
			icon: IconUserCheck,
			category: "System",
			readonly: true,
		},
		{
			field: "isDeleted",
			label: "Is Deleted",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "deletedAt",
			label: "Deleted At",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "deletedBy",
			label: "Deleted By",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "createdAt",
			label: "Created",
			icon: IconCalendarClock,
			category: "System",
			readonly: true,
		},
		{
			field: "updatedAt",
			label: "Updated",
			icon: IconCalendarClock,
			category: "System",
			readonly: true,
		},
		{
			field: "tags",
			label: "Tags",
			icon: IconTag,
			category: "Basic Info",
		},
	],
	
	property: [
		// Basic Info
		{
			field: "id",
			label: "Record ID",
			icon: IconHash,
			category: "Basic Info",
			readonly: true,
		},
		{
			field: "apolloId",
			label: "Apollo ID",
			icon: IconId,
			category: "System",
			readonly: true,
		},
		{
			field: "name",
			label: "Property Name",
			icon: IconHome,
			category: "Basic Info",
		},
		{
			field: "recordType",
			label: "Record Type",
			icon: IconTag,
			category: "Basic Info",
		},
		{
			field: "image",
			label: "Property Image",
			icon: IconCamera,
			category: "Basic Info",
		},
		
		// Property Classification
		{
			field: "propertyType",
			label: "Property Type",
			icon: IconBuildingEstate,
			category: "Classification",
		},
		{
			field: "propertySubType",
			label: "Property Sub Type",
			icon: IconBuildingEstate,
			category: "Classification",
		},
		{
			field: "market",
			label: "Market",
			icon: IconMap,
			category: "Classification",
		},
		{
			field: "subMarket",
			label: "Sub Market",
			icon: IconMap,
			category: "Classification",
		},
		{
			field: "listingId",
			label: "Listing ID",
			icon: IconNumbers,
			category: "Classification",
		},
		{
			field: "status",
			label: "Status",
			icon: IconChartColumn,
			category: "Classification",
		},
		
		// Simple relationships
		{
			field: "tags",
			label: "Tags",
			icon: IconTag,
			category: "Relationships",
		},
		{
			field: "lists",
			label: "Lists",
			icon: IconClipboardList,
			category: "Relationships",
		},
		{
			field: "linkedContacts",
			label: "Linked Contacts",
			icon: IconUsers,
			category: "Relationships",
		},
		{
			field: "linkedCompanies",
			label: "Linked Companies",
			icon: IconBuilding,
			category: "Relationships",
		},
		{
			field: "tasks",
			label: "Tasks",
			icon: IconClipboardList,
			category: "Relationships",
		},
		
		// Address fields
		...ADDRESS_ATTRIBUTES,
		
		// Location fields (from PropertyLocation model)
		{
			field: "location.address",
			label: "Full Address",
			icon: IconMapPin,
			category: "Location",
		},
		// Note: Removed duplicate location.address.* fields to avoid conflicts
		// Use the standard ADDRESS_ATTRIBUTES (address.city, address.street, etc.) instead
		{
			field: "location.website",
			label: "Property Website",
			icon: IconWorld,
			category: "Location",
		},
		{
			field: "location.neighborhood",
			label: "Neighborhood",
			icon: IconMapPin,
			category: "Location",
		},
		{
			field: "location.county",
			label: "County",
			icon: IconMapPin,
			category: "Location",
		},
		{
			field: "location.subdivision",
			label: "Subdivision",
			icon: IconMapPin,
			category: "Location",
		},
		{
			field: "location.lotNumber",
			label: "Lot Number",
			icon: IconNumbers,
			category: "Location",
		},
		{
			field: "location.parcelNumber",
			label: "Parcel Number",
			icon: IconNumbers,
			category: "Location",
		},
		{
			field: "location.zoning",
			label: "Zoning",
			icon: IconBarrierBlock,
			category: "Location",
		},
		
		// Physical Details fields (from PropertyPhysicalDetails model)
		{
			field: "physicalDetails.yearBuilt",
			label: "Year Built",
			icon: IconCalendar,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.squareFootage",
			label: "Square Footage",
			icon: IconRuler,
			category: "Physical Details",
		},

		{
			field: "physicalDetails.floors",
			label: "Floors",
			icon: IconChartLine,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.structures",
			label: "Structures",
			icon: IconBuilding,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.bedrooms",
			label: "Bedrooms",
			icon: IconBed,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.bathrooms",
			label: "Bathrooms",
			icon: IconBath,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.roomsCount",
			label: "Rooms Count",
			icon: IconDoor,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.buildingSquareFeet",
			label: "Building Sq Ft",
			icon: IconRuler,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.garageSquareFeet",
			label: "Garage Sq Ft",
			icon: IconCar,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.livingSquareFeet",
			label: "Living Sq Ft",
			icon: IconRuler,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.lotSquareFeet",
			label: "Lot Sq Ft",
			icon: IconTrees,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.lotSize",
			label: "Lot Size",
			icon: IconTrees,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.lotType",
			label: "Lot Type",
			icon: IconTrees,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.lotAcres",
			label: "Lot Acres",
			icon: IconTrees,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.construction",
			label: "Construction",
			icon: IconHammer,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.primaryUse",
			label: "Primary Use",
			icon: IconHome,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.propertyUse",
			label: "Property Use",
			icon: IconHome,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.class",
			label: "Property Class",
			icon: IconCertificate,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.parking",
			label: "Parking",
			icon: IconCar,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.parkingSpaces",
			label: "Parking Spaces",
			icon: IconCar,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.garageType",
			label: "Garage Type",
			icon: IconCar,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.heatingType",
			label: "Heating Type",
			icon: IconFlame,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.meterType",
			label: "Meter Type",
			icon: IconBolt,
			category: "Physical Details",
		},
		{
			field: "physicalDetails.legalDescription",
			label: "Legal Description",
			icon: IconGavel,
			category: "Physical Details",
		},
		
		// Financial fields (from PropertyFinancials model)
		{
			field: "financials.price",
			label: "Price",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.estimatedValue",
			label: "Estimated Value",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.pricePerSquareFoot",
			label: "Price/Sq Ft",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.equity",
			label: "Equity",
			icon: IconMoneybag,
			category: "Financial",
		},
		{
			field: "financials.equityPercent",
			label: "Equity %",
			icon: IconPercentage,
			category: "Financial",
		},
		{
			field: "financials.estimatedEquity",
			label: "Estimated Equity",
			icon: IconMoneybag,
			category: "Financial",
		},
		{
			field: "financials.saleDate",
			label: "Sale Date",
			icon: IconCalendar,
			category: "Financial",
		},
		{
			field: "financials.salePrice",
			label: "Sale Price",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.lastSalePrice",
			label: "Last Sale Price",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.lastSaleDate",
			label: "Last Sale Date",
			icon: IconCalendar,
			category: "Financial",
		},
		{
			field: "financials.landValue",
			label: "Land Value",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.buildingValue",
			label: "Building Value",
			icon: IconCurrencyDollar,
			category: "Financial",
		},
		{
			field: "financials.cap",
			label: "Cap Rate",
			icon: IconPercentage,
			category: "Financial",
		},
		{
			field: "financials.exchange",
			label: "Exchange",
			icon: IconCheck,
			category: "Financial",
		},
		{
			field: "financials.exchangeId",
			label: "Exchange ID",
			icon: IconNumbers,
			category: "Financial",
		},
		{
			field: "financials.taxInfo",
			label: "Tax Info",
			icon: IconCashBanknote,
			category: "Financial",
		},
		
		// Flags fields (from PropertyFlags model)
		{
			field: "flags.absenteeOwner",
			label: "Absentee Owner",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.inStateAbsenteeOwner",
			label: "In-State Absentee",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.outOfStateAbsenteeOwner",
			label: "Out-State Absentee",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.ownerOccupied",
			label: "Owner Occupied",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.corporateOwned",
			label: "Corporate Owned",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.vacant",
			label: "Vacant",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.mobileHome",
			label: "Mobile Home",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.carport",
			label: "Carport",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.auction",
			label: "Auction",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.cashBuyer",
			label: "Cash Buyer",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.investorBuyer",
			label: "Investor Buyer",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.freeClear",
			label: "Free & Clear",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.highEquity",
			label: "High Equity",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.privateLender",
			label: "Private Lender",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.deedInLieu",
			label: "Deed in Lieu",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.quitClaim",
			label: "Quit Claim",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.sheriffsDeed",
			label: "Sheriff's Deed",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.warrantyDeed",
			label: "Warranty Deed",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.inherited",
			label: "Inherited",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.spousalDeath",
			label: "Spousal Death",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.lien",
			label: "Lien",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.taxLien",
			label: "Tax Lien",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.preForeclosure",
			label: "Pre-Foreclosure",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.trusteeSale",
			label: "Trustee Sale",
			icon: IconFlag,
			category: "Property Flags",
		},
		{
			field: "flags.floodZone",
			label: "Flood Zone",
			icon: IconDroplet,
			category: "Property Flags",
		},
		
		// MLS Data fields (from PropertyMLS model)
		{
			field: "mlsData.mlsActive",
			label: "MLS Active",
			icon: IconCheck,
			category: "MLS",
		},
		{
			field: "mlsData.mlsCancelled",
			label: "MLS Cancelled",
			icon: IconX,
			category: "MLS",
		},
		{
			field: "mlsData.mlsFailed",
			label: "MLS Failed",
			icon: IconX,
			category: "MLS",
		},
		{
			field: "mlsData.mlsHasPhotos",
			label: "MLS Has Photos",
			icon: IconCamera,
			category: "MLS",
		},
		{
			field: "mlsData.mlsPending",
			label: "MLS Pending",
			icon: IconClock,
			category: "MLS",
		},
		{
			field: "mlsData.mlsSold",
			label: "MLS Sold",
			icon: IconCheck,
			category: "MLS",
		},
		{
			field: "mlsData.mlsDaysOnMarket",
			label: "Days on Market",
			icon: IconCalendar,
			category: "MLS",
		},
		{
			field: "mlsData.mlsListingPrice",
			label: "MLS Listing Price",
			icon: IconCurrencyDollar,
			category: "MLS",
		},
		{
			field: "mlsData.mlsListingPricePerSquareFoot",
			label: "MLS Price/Sq Ft",
			icon: IconCurrencyDollar,
			category: "MLS",
		},
		{
			field: "mlsData.mlsSoldPrice",
			label: "MLS Sold Price",
			icon: IconCurrencyDollar,
			category: "MLS",
		},
		{
			field: "mlsData.mlsStatus",
			label: "MLS Status",
			icon: IconChartColumn,
			category: "MLS",
		},
		{
			field: "mlsData.mlsType",
			label: "MLS Type",
			icon: IconTag,
			category: "MLS",
		},
		{
			field: "mlsData.mlsListingDate",
			label: "MLS Listing Date",
			icon: IconCalendar,
			category: "MLS",
		},
		
		// Legal Info fields (from PropertyLegal model)
		{
			field: "legalInfo.floodZoneDescription",
			label: "Flood Zone Description",
			icon: IconDroplet,
			category: "Legal",
		},
		{
			field: "legalInfo.floodZoneType",
			label: "Flood Zone Type",
			icon: IconDroplet,
			category: "Legal",
		},
		{
			field: "legalInfo.noticeType",
			label: "Notice Type",
			icon: IconAlertTriangle,
			category: "Legal",
		},
		{
			field: "legalInfo.reaId",
			label: "REA ID",
			icon: IconNumbers,
			category: "Legal",
		},
		{
			field: "legalInfo.lastUpdateDate",
			label: "Last Update Date",
			icon: IconCalendar,
			category: "Legal",
		},
		
		// Audit fields
		{
			field: "createdBy",
			label: "Created By",
			icon: IconUserCheck,
			category: "System",
			readonly: true,
		},
		{
			field: "updatedBy",
			label: "Updated By",
			icon: IconUserCheck,
			category: "System",
			readonly: true,
		},
		{
			field: "lastViewedAt",
			label: "Last Viewed",
			icon: IconEye,
			category: "System",
			readonly: true,
		},
		{
			field: "lastViewedBy",
			label: "Last Viewed By",
			icon: IconEye,
			category: "System",
			readonly: true,
		},
		{
			field: "isDeleted",
			label: "Is Deleted",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "deletedAt",
			label: "Deleted At",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "deletedBy",
			label: "Deleted By",
			icon: IconTrash,
			category: "System",
			readonly: true,
		},
		{
			field: "createdAt",
			label: "Created",
			icon: IconCalendarClock,
			category: "System",
			readonly: true,
		},
		{
			field: "updatedAt",
			label: "Updated",
			icon: IconCalendarClock,
			category: "System",
			readonly: true,
		},
	],
	
	// Legacy mappings for backward compatibility
	contacts: [],
	companies: [],
	properties: [],
};

// Set up legacy mappings
AVAILABLE_ATTRIBUTES.contacts = AVAILABLE_ATTRIBUTES.contact;
AVAILABLE_ATTRIBUTES.companies = AVAILABLE_ATTRIBUTES.company;
AVAILABLE_ATTRIBUTES.properties = AVAILABLE_ATTRIBUTES.property;

/**
 * Get the icon for a given field based on the available attributes mapping
 * @param field - The field name to look up
 * @param objectType - The object type (contact, company, property)
 * @returns The icon component or a default icon if not found
 */
export function getFieldIcon(
	field: string,
	objectType: keyof typeof AVAILABLE_ATTRIBUTES,
) {
	const attributes = AVAILABLE_ATTRIBUTES[objectType] || [];

	// First try exact match
	const exactMatch = attributes.find((attr) => attr.field === field);
	if (exactMatch) {
		return exactMatch.icon;
	}

	// Then try partial matches for nested fields (e.g., "company.name" matches "company")
	const partialMatch = attributes.find((attr) => {
		// Handle nested fields like "company.name", "address.city"
		if (field.includes(".")) {
			const baseField = field.split(".")[0];
			return attr.field === baseField || attr.field.includes(baseField);
		}

		// Handle fields that contain the attribute field name
		return field.includes(attr.field) || attr.field.includes(field);
	});

	if (partialMatch) {
		return partialMatch.icon;
	}

	// Default fallback icon
	return IconTag;
}

