"use client";

// ⚡ PERFORMANCE OPTIMIZED VERSION AVAILABLE!
// For better performance, smooth scrolling, and skeleton loading, use:
// import { OptimizedDataTableInfinite } from "./shared/OptimizedDataTableInfinite";
// 
// The new version includes:
// ✨ Virtual scrolling with @tanstack/react-virtual
// ✨ Skeleton loading states for smooth UX
// ✨ Componentized architecture for better maintainability
// ✨ Optimized memoization and reduced re-renders
// ✨ Improved TanStack Table patterns
// 
// Usage: Simply replace DataTableInfinite with OptimizedDataTableInfinite
// All props are compatible, with additional performance options:
// - enableVirtualization?: boolean (default: true)
// - rowHeight?: number (default: 40)
// - overscan?: number (default: 5)
// - loadingRowCount?: number (default: 10)

import BottomBar from "@app/contacts/components/BottomBar";
import { arrSome, inDateRange } from "@app/organizations/lib/filterfns";
import { useCreatePin, useDeletePin, useIsPinned } from "@app/pins/lib/api";
import {
	DataTableCellContextMenu,
	DataTableContextMenu,
} from "@app/shared/components/data-table/DataTableContextMenu";
import { InlineCellEditor } from "@app/shared/components/data-table/InlineCellEditor";
import { STORAGE_KEYS } from "@app/shared/lib/constants";
import { useLocalStorage } from "@app/shared/lib/local-storage";
import { useRouter } from "next/navigation";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	MouseSensor,
	TouchSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import {
	arrayMove,
	horizontalListSortingStrategy,
	SortableContext,
	useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
	IconArrowDown,
	IconArrowLeft,
	IconArrowRight,
	IconArrowUp,
	IconBrandLine,
	IconEyeOff,
	IconMessage,
	IconPencilOff,
	IconPlus,
} from "@tabler/icons-react";
import {
	useMutation,
	useQueryClient,
} from "@tanstack/react-query";
import type {
	ColumnDef,
	ColumnFiltersState,
	Row,
	RowSelectionState,
	SortingState,
	TableOptions,
	Table as TTable,
	VisibilityState,
} from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getFacetedRowModel,
	getFilteredRowModel,
	getSortedRowModel,
	getFacetedMinMaxValues as getTTableFacetedMinMaxValues,
	getFacetedUniqueValues as getTTableFacetedUniqueValues,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Table,
	TableBody,
	TableCell,
	TableFooter,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { type ParserBuilder, useQueryStates } from "nuqs";
import * as React from "react";
import { toast } from "sonner";
import { AddColumnPopover } from "./AddColumnPopover";
import { DataTableFilterCommand } from "./shared/data-table/data-table-filter-command";
import { DataTableFilterControls } from "./shared/data-table/data-table-filter-controls";
import { DataTableProvider } from "./shared/data-table/data-table-provider";
import type {
	DataTableFilterField,
	SheetField,
} from "./shared/data-table/types";
import PropertySidebar from "@app/organizations/components/PropertySidebar";
import { PropertyAvatar } from "../../../../shared/components/PropertyAvatar";
import { ArrayFieldType, FieldType, ObjectType, TaggableObjectType } from "@repo/database";
import { ContactAvatar } from "@shared/components/ContactAvatar";

export interface DataTableInfiniteProps<TData, TValue, TMeta> {
	columns: ColumnDef<TData, TValue>[];
	getRowClassName?: (row: Row<TData>) => string;
	getRowId?: TableOptions<TData>["getRowId"];
	data: TData[];
	defaultColumnFilters?: ColumnFiltersState;
	defaultColumnSorting?: SortingState;
	defaultRowSelection?: RowSelectionState;
	defaultColumnVisibility?: VisibilityState;
	filterFields?: DataTableFilterField<TData>[];
	sheetFields?: SheetField<TData, TMeta>[];
	isFetching?: boolean;
	isLoading?: boolean;
	fetchNextPage: () => Promise<void>;
	hasNextPage?: boolean;
	fetchPreviousPage?: () => Promise<void>;
	refetch: () => Promise<void>;
	totalRows?: number;
	filterRows?: number;
	totalRowsFetched?: number;
	chartData?: any[];
	chartDataColumnId?: string;
	getFacetedUniqueValues?: (table: TTable<TData>, columnId: string) => Map<string, number>;
	getFacetedMinMaxValues?: (table: TTable<TData>, columnId: string) => [number, number] | undefined;
	meta?: TMeta;
	renderLiveRow?: (row: Row<TData>) => React.ReactNode;
	renderSheetTitle?: (row: Row<TData>) => string;
	renderHeader?: () => React.ReactNode;
	searchParamsParser: Record<string, ParserBuilder<any>>;
	objectType?: string;
	primaryColumn?: string;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		updatedAt?: string;
	};
	onUpdateView?: (updatedView: any) => void;
	onEdit?: (e: React.MouseEvent, recordId: string) => void;
	onFavorite?: (e: React.MouseEvent, recordId: string) => void;
	onPin?: (e: React.MouseEvent, recordId: string, record: any, isPinned: boolean) => void;
	onDelete?: (e: React.MouseEvent, recordId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
	onEditCell?: (
		e: React.MouseEvent,
		cell: { columnId: string; value: any; rowId: string },
	) => Promise<void>;
	onClearValue?: (
		e: React.MouseEvent,
		cell: { columnId: string; rowId: string },
	) => Promise<void>;
	customMenuItems?: (record: any) => any[];
	isFavorite?: (record: any) => boolean;
	organizationId?: string;
	fieldTypes?: Record<
		string,
		FieldType>;
	arrayFieldTypes?: Record<string, ArrayFieldType>;
	selectOptions?: Record<string, Array<{ label: string; value: string }>>;
	enableInlineEditing?: boolean;
	readonlyColumns?: string[];
	renderFilterBar?: boolean;
	renderFilterControls?: boolean;
	selectedProperty?: any;
	onPropertySelect?: (property: any) => void;
	onRowSelectionChange?: (selection: RowSelectionState) => void;
	// Pagination mode props
	isPaginationMode?: boolean;
	paginationInfo?: {
		currentPage: number;
		totalPages: number;
		pageSize: number;
		total: number;
		offset: number;
	};
	onPageChange?: (page: number) => void;
}

function formatCompactNumber(num: number): string {
	if (num >= 1000000) {
		return (num / 1000000).toFixed(1) + "M";
	}
	if (num >= 1000) {
		return (num / 1000).toFixed(1) + "K";
	}
	return num.toString();
}

function HeaderCell({
	header,
	onMoveColumn,
	primaryColumn,
	readonlyColumns = [],
	dragOverId,
	onHide,
}: {
	header: any;
	onMoveColumn?: (id: string, direction: "left" | "right") => void;
	primaryColumn?: string;
	readonlyColumns?: string[];
	dragOverId?: string | null;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
}) {
	if (header.isPlaceholder) return null;

	const canSort = header.column.getCanSort();
	const canHide = header.column.getCanHide();
	const sorted = header.column.getIsSorted();
	const width = header.column.getSize();
	const isPinned = header.column.getIsPinned();
	const isReadonly = readonlyColumns.includes(header.column.id);

	// Determine if this column can be dragged (not pinned)
	const isDraggable =
		!isPinned &&
		header.column.id !== "select" &&
		header.column.id !== primaryColumn;

	// Calculate left position for pinned columns
	const getLeftPosition = () => {
		if (header.column.id === "select") return "left-0";
		if (isPrimaryColumn) return "";
		return "";
	};

	// Get the style with proper left positioning
	const getColumnStyle = () => {
		// Always force select column to 40px regardless of what getSize() returns
		const actualWidth = header.column.id === "select" ? 40 : width;

		const baseStyle = {
			width: actualWidth,
			minWidth: actualWidth,
			maxWidth: actualWidth,
			// For select column, add extra CSS properties to force the width
			...(header.column.id === "select" && {
				flex: "0 0 40px" as any,
				boxSizing: "border-box" as any,
				overflow: "hidden" as any,
				textOverflow: "clip" as any,
			}),
		};

		if (isPrimaryColumn) {
			return {
				...baseStyle,
				left: "40px",
			};
		}

		return baseStyle;
	};

	const columnAccessorKey = (header.column.columnDef as any)?.accessorKey;

	// Check if this is the primary column (could match by id or accessorKey)
	const isPrimaryColumn =
		header.column.id === primaryColumn ||
		columnAccessorKey === primaryColumn ||
		header.column.columnDef.id === primaryColumn;

	// Handle pinned columns (select and primary) - not draggable
	if (header.column.id === "select" || isPrimaryColumn) {
		return (
			<TableHead
				key={header.id}
				colSpan={header.colSpan}
				style={getColumnStyle()}
				className={cn(
					"h-10 text-left align-middle font-medium text-muted-foreground select-none",
					"relative border-b border-t border-border group",
					header.column.id === "select"
						? "select-column-header !w-10 !min-w-10 !max-w-10 !px-0 !p-0"
						: "px-2",
					isPinned && [
						"sticky z-50 bg-sidebar",
						isPrimaryColumn &&
							"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
						getLeftPosition(),
					],
				)}
			>
				{header.column.id === "select" ? (
					<div className="flex items-center justify-center w-full h-full">
						{flexRender(
							header.column.columnDef.header,
							header.getContext(),
						)}
					</div>
				) : (
					<div className="flex items-center justify-between w-full gap-2 min-w-0 px-2">
						<div className="truncate flex-1 min-w-0">
							{flexRender(
								header.column.columnDef.header,
								header.getContext(),
							)}
						</div>
						<div className="flex items-center gap-1 flex-shrink-0">
							{isReadonly && (
								<IconPencilOff className="size-3.5 text-muted-foreground" />
							)}
							{sorted && (
								<span>
									{sorted === "desc" ? (
										<IconArrowDown className="size-3.5" />
									) : (
										<IconArrowUp className="size-3.5" />
									)}
								</span>
							)}
						</div>
					</div>
				)}
				{header.column.getCanResize() &&
					header.column.id !== "select" && (
						<div
							onDoubleClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								header.column.resetSize();
							}}
							onMouseDown={(e) => {
								e.preventDefault();
								e.stopPropagation();
								header.getResizeHandler()(e);
							}}
							onTouchStart={(e) => {
								e.preventDefault();
								e.stopPropagation();
								header.getResizeHandler()(e);
							}}
							className={cn(
								"absolute right-0 top-0 h-full w-0.5 cursor-col-resize select-none touch-none z-20",
								"bg-transparent hover:bg-blue-500 active:bg-blue-500",
								header.column.getIsResizing() &&
									"bg-blue-500 opacity-100",
							)}
						/>
					)}
			</TableHead>
		);
	}

	// Draggable header cell for non-pinned columns
	return (
		<DraggableHeaderCell
			header={header}
			onMoveColumn={onMoveColumn}
			primaryColumn={primaryColumn}
			readonlyColumns={readonlyColumns}
			dragOverId={dragOverId}
			onHide={onHide}
		/>
	);
}

function DraggableHeaderCell({
	header,
	onMoveColumn,
	primaryColumn,
	readonlyColumns = [],
	dragOverId,
	onHide,
}: {
	header: any;
	onMoveColumn?: (id: string, direction: "left" | "right") => void;
	primaryColumn?: string;
	readonlyColumns?: string[];
	dragOverId?: string | null;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
}) {
	const canSort = header.column.getCanSort();
	const canHide = header.column.getCanHide();
	const sorted = header.column.getIsSorted();
	const width = header.column.getSize();
	const isPinned = header.column.getIsPinned();
	const isReadonly = readonlyColumns.includes(header.column.id);

	const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);
	const dropdownTimerRef = React.useRef<NodeJS.Timeout | null>(null);

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: header.column.id,
	});

	const style = {
		transform: CSS.Translate.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
		position: "relative" as const,
		zIndex: isDragging ? 1 : 0,
	};

	const getColumnStyle = () => {
		const actualWidth = width;

		const baseStyle = {
			width: actualWidth,
			minWidth: actualWidth,
			maxWidth: actualWidth,
		};

		return baseStyle;
	};

	// Handle manual dropdown control to work with drag sensors
	const handleManualClick = React.useCallback(
		(e: React.MouseEvent) => {
			// Don't prevent default - let the dropdown handle the click naturally
			e.stopPropagation();

			// Clear any existing timer
			if (dropdownTimerRef.current) {
				clearTimeout(dropdownTimerRef.current);
				dropdownTimerRef.current = null;
			}

			// If already dragging, don't open dropdown
			if (isDragging) {
				return;
			}

			// Set a timer that allows drag to take priority
			// If no drag starts by then, open the dropdown
			dropdownTimerRef.current = setTimeout(() => {
				if (!isDragging) {
					setIsDropdownOpen(true);
				}
				dropdownTimerRef.current = null;
			}, 150); // Shorter delay for better responsiveness
		},
		[header.column.id, isDragging],
	);

	// Close dropdown when drag starts
	React.useEffect(() => {
		if (isDragging) {
			setIsDropdownOpen(false);
			// Clear the dropdown timer if drag starts
			if (dropdownTimerRef.current) {
				clearTimeout(dropdownTimerRef.current);
				dropdownTimerRef.current = null;
			}
		}
	}, [isDragging, header.column.id]);

	// Cleanup timer on unmount
	React.useEffect(() => {
		return () => {
			if (dropdownTimerRef.current) {
				clearTimeout(dropdownTimerRef.current);
			}
		};
	}, []);

	return (
		<TableHead
			ref={setNodeRef}
			key={header.id}
			colSpan={header.colSpan}
			style={{ ...getColumnStyle(), ...style }}
			className={cn(
				"h-10 text-left align-middle font-medium text-muted-foreground select-none",
				"relative border-b border-t border-border group",
				// "hover:bg-muted/50 transition-colors",
				"px-2",
				isDragging &&
					"opacity-50 bg-blue-500/50 border-blue-500 border-l",
				// dragOverId === header.column.id && [
				//   "before:absolute before:left-0 before:top-0 before:w-1 before:h-full before:bg-blue-500 before:z-30",
				//   "bg-blue-500/20 border-blue-500"
				// ],
				isPinned && [
					"sticky z-50 bg-sidebar",
					"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
				],
			)}
			{...attributes}
			{...listeners}
		>
			<DropdownMenu
				open={isDropdownOpen}
				onOpenChange={setIsDropdownOpen}
			>
				<DropdownMenuTrigger asChild>
					<div
						role="button"
						tabIndex={0}
						className={cn(
							"flex items-center justify-between w-full gap-2 pr-2 h-full min-w-0 cursor-pointer",
						)}
						style={{
							paddingRight: header.column.getCanResize()
								? "12px"
								: "8px",
						}}
						onClick={handleManualClick}
						onDoubleClick={(e) => {
							e.stopPropagation();
							// Clear any pending timer
							if (dropdownTimerRef.current) {
								clearTimeout(dropdownTimerRef.current);
								dropdownTimerRef.current = null;
							}
							// Immediately open dropdown on double-click
							setIsDropdownOpen(true);
						}}
						onKeyDown={(e) => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								setIsDropdownOpen(true);
							}
						}}
					>
						<div className="truncate flex-1 min-w-0">
							{flexRender(
								header.column.columnDef.header,
								header.getContext(),
							)}
						</div>
						<div className="flex items-center gap-1 flex-shrink-0">
							{isReadonly && (
								<Tooltip>
									<TooltipTrigger>
										<IconPencilOff className="size-3.5 text-muted-foreground" />
									</TooltipTrigger>
									<TooltipContent side="right">
										This column is read-only
									</TooltipContent>
								</Tooltip>
							)}
							{sorted && (
								<span>
									{sorted === "desc" ? (
										<IconArrowDown className="size-3.5" />
									) : (
										<IconArrowUp className="size-3.5" />
									)}
								</span>
							)}
						</div>
					</div>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					align="start"
					style={{ width: Math.max(width, 220) }}
					className="min-w-[220px]"
				>
					{canSort && (
						<>
							<DropdownMenuItem
								onClick={() =>
									header.column.toggleSorting(false)
								}
								icon2={<IconArrowUp className="size-3.5" />}
								className="w-full"
							>
								Sort ascending
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() =>
									header.column.toggleSorting(true)
								}
								icon2={<IconArrowDown className="size-3.5" />}
								className="w-full"
							>
								Sort descending
							</DropdownMenuItem>
						</>
					)}
					{(canSort || canHide) && <DropdownMenuSeparator />}
					{onMoveColumn && (
						<>
							<DropdownMenuItem
								onClick={() =>
									onMoveColumn(header.column.id, "left")
								}
								icon2={<IconArrowLeft className="size-4" />}
								className="w-full"
							>
								Move left
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() =>
									onMoveColumn(header.column.id, "right")
								}
								icon2={<IconArrowRight className="size-4" />}
								className="w-full"
							>
								Move right
							</DropdownMenuItem>
						</>
					)}
					<DropdownMenuItem
						onClick={() => {
							// Only update the view in database - let the view sync handle UI updates
							if (onHide) {
								onHide(
									{} as React.MouseEvent,
									header.column.id,
								);
							}
						}}
						icon2={<IconEyeOff className="size-4" />}
						className="w-full"
					>
						Hide column
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
			{header.column.getCanResize() && header.column.id !== "select" && (
				<div
					onDoubleClick={(e) => {
						e.preventDefault();
						e.stopPropagation();
						header.column.resetSize();
					}}
					onMouseDown={(e) => {
						e.preventDefault();
						e.stopPropagation();
						header.getResizeHandler()(e);
					}}
					onTouchStart={(e) => {
						e.preventDefault();
						e.stopPropagation();
						header.getResizeHandler()(e);
					}}
					className={cn(
						"absolute right-0 top-0 h-full w-0.5 cursor-col-resize select-none touch-none z-30",
						"bg-transparent hover:bg-blue-500 active:bg-blue-500",
						header.column.getIsResizing() &&
							"bg-blue-500 opacity-100",
					)}
				/>
			)}
		</TableHead>
	);
}

function FooterCell({
	header,
	primaryColumn,
	columnAggregations,
	calculateAggregation,
	getAggregationLabel,
	setAggregation,
	removeAggregation,
	hasNoRecords = false,
	isPaginationMode = false,
	paginationInfo,
	onPageChange,
	isLoading = false,
	isFetching = false,
	objectType,
	hasActiveFilters = false,
}: {
	header: any;
	primaryColumn?: string;
	columnAggregations: Record<string, string>;
	calculateAggregation: (columnId: string, aggregationType: string) => string;
	getAggregationLabel: (aggregationType: string) => string;
	setAggregation: (columnId: string, aggregationType: string) => void;
	removeAggregation: (columnId: string) => void;
	hasNoRecords?: boolean;
	isPaginationMode?: boolean;
	paginationInfo?: {
		currentPage: number;
		totalPages: number;
		pageSize: number;
		total: number;
		offset: number;
	};
	onPageChange?: (page: number) => void;
	isLoading?: boolean;
	isFetching?: boolean;
	objectType?: string;
	hasActiveFilters?: boolean;
}) {
	if (header.isPlaceholder) return null;

	const width = header.column.getSize();
	const isPinned = header.column.getIsPinned();
	const columnId = header.column.id;
	const columnAccessorKey = (header.column.columnDef as any)?.accessorKey;
	const currentAggregation = columnAggregations[columnId];

	const getColumnStyle = () => {
		const actualWidth = header.column.id === "select" ? 40 : width;

		const baseStyle = {
			width: actualWidth,
			minWidth: actualWidth,
			maxWidth: actualWidth,
			...(header.column.id === "select" && {
				flex: "0 0 40px" as any,
				boxSizing: "border-box" as any,
				overflow: "hidden" as any,
				textOverflow: "clip" as any,
			}),
		};

		if (header.column.id === primaryColumn) {
			return {
				...baseStyle,
				left: "40px",
			};
		}

		return baseStyle;
	};

	const getLeftPosition = () => {
		if (header.column.id === "select") return "left-0";
		if (header.column.id === primaryColumn) return "";
		return "";
	};

	if (header.column.id === "select") {
		return (
			<TableCell
				style={getColumnStyle()}
				className={cn(
					"border-r-0",
					"text-left align-top text-muted-foreground select-none bg-sidebar",
					"select-column-cell !w-10 !min-w-10 !max-w-10 !p-0 min-h-10",
					isPinned && [
						"sticky z-10 bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
						getLeftPosition(),
					],
				)}
			>
				{/* Empty select column footer */}
			</TableCell>
		);
	}

	if (isPinned) {
		// Show pagination controls in primary column when in pagination mode
		if (isPaginationMode && (columnId === primaryColumn || columnAccessorKey === primaryColumn) && paginationInfo) {
			return (
				<TableCell
					style={getColumnStyle()}
					className={cn(
						"text-left align-top text-muted-foreground select-none backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
						"px-2 relative group min-h-10 py-2",
						"bg-sidebar",
						isPinned && [
							"border-r-1",
							"sticky z-10 bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
							"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border",
							getLeftPosition(),
						],
					)}
				>
					<div className="flex items-center justify-between gap-2 min-w-0">
						<div className="text-xs text-muted-foreground truncate">
							{hasActiveFilters ? (
								// When filters are active, show only filtered count
								<>
									<span className="text-xs font-mono text-muted-foreground truncate">{paginationInfo.offset + 1}-{Math.min(paginationInfo.offset + paginationInfo.pageSize, paginationInfo.total)}</span>
									<span className="text-muted-foreground/50">{" "}of{" "}</span> 
									<span className="text-xs font-mono text-muted-foreground truncate">{paginationInfo.total.toLocaleString()}</span>
									<span className="text-muted-foreground/50"> filtered</span>
								</>
							) : (
								// When no filters, show normal display
								<>
									<span className="text-xs font-mono text-muted-foreground truncate">{paginationInfo.offset + 1}-{Math.min(paginationInfo.offset + paginationInfo.pageSize, paginationInfo.total)}</span>
									<span className="text-muted-foreground/50">{" "}of{" "}</span> 
									<span className="text-xs font-mono text-muted-foreground truncate"> {paginationInfo.total.toLocaleString()}</span>
								</>
							)}
						</div>
						
						<div className="flex items-center gap-1">
							<Button
								size="icon"
								className="px-1.5 py-0.5 h-6 w-6 text-xs bg-sidebar disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
								variant="relio"
								onClick={() => onPageChange?.(paginationInfo.currentPage - 1)}
								disabled={paginationInfo.currentPage === 1 || isLoading}
								title="Previous page"
							>
								←
							</Button>
							
							<div className="text-xs font-medium min-w-0 truncate">
								<span className="text-xs font-mono text-muted-foreground truncate">{paginationInfo.currentPage}</span>
								<span className="text-muted-foreground/50">{"/"}</span> 
								<span className="text-xs font-mono text-muted-foreground truncate">{paginationInfo.totalPages}</span>
							</div>
							
							<Button
								size="icon"
								className="px-1.5 py-0.5 h-6 w-6 text-xs bg-sidebar disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
								variant="relio"
								onClick={() => onPageChange?.(paginationInfo.currentPage + 1)}
								disabled={paginationInfo.currentPage >= paginationInfo.totalPages || isLoading}
								title="Next page"
							>
								→
							</Button>
						</div>
					</div>
				</TableCell>
			);
		}

		return (
			<TableCell
				style={getColumnStyle()}
				className={cn(
					"text-left align-top text-muted-foreground select-none bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
					"px-2 relative group min-h-10 py-2",
					isPinned && [
						"border-r-1",
						"sticky z-10 bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
						"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border",
						getLeftPosition(),
					],
				)}
			>
				<div className="flex items-center gap-1 justify-end min-w-0">
					<div className="text-foreground font-medium truncate">
						{calculateAggregation(columnId, "count")}
					</div>
					<div className="text-muted-foreground truncate">count</div>
				</div>
			</TableCell>
		);
	}

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<TableCell
					style={getColumnStyle()}
									className={cn(
					"h-10",
					"text-left align-top text-muted-foreground select-none bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
					"px-2 relative group min-h-10 py-2",
					// "hover:bg-muted/50 transition-colors",
					isPinned && [
						"sticky rounded-lg px-2 py-1drop-blur-3xl supports-[backdrop-filter]:bg-sidebar",
						"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border",
						getLeftPosition(),
					],
				)}
				>
					{currentAggregation ? (
						<div className="flex items-center justify-between min-h-5 min-w-0 gap-1 cursor-pointer hover:bg-muted/50 transition-colors w-fit rounded-lg px-2 py-1">
							<span className="text-foreground font-medium truncate flex-shrink-0">
								{calculateAggregation(
									columnId,
									currentAggregation,
								)}
							</span>
							<span className="text-muted-foreground truncate flex-1 min-w-0">
								{getAggregationLabel(currentAggregation)}
							</span>
						</div>
					) : (
						<div className="flex items-center justify-start text-muted-foreground gap-1 min-w-0 hover:bg-muted/50 transition-colors w-fit rounded-lg px-2 py-1 cursor-pointer">
							<IconPlus className="w-3 h-3 flex-shrink-0" />
							<span className="truncate">Add calculation</span>
						</div>
					)}
				</TableCell>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="start" className="w-48">
				<DropdownMenuItem
					onClick={() => setAggregation(columnId, "count")}
					disabled={currentAggregation === "count"}
				>
					Count
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => setAggregation(columnId, "count-empty")}
					disabled={currentAggregation === "count-empty"}
				>
					Count empty
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => setAggregation(columnId, "count-filled")}
					disabled={currentAggregation === "count-filled"}
				>
					Count filled
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => setAggregation(columnId, "percent-empty")}
					disabled={currentAggregation === "percent-empty"}
				>
					Percent empty
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => setAggregation(columnId, "percent-filled")}
					disabled={currentAggregation === "percent-filled"}
				>
					Percent filled
				</DropdownMenuItem>
				{currentAggregation && <DropdownMenuSeparator />}
				{currentAggregation && (
					<DropdownMenuItem onClick={() => removeAggregation(columnId)}>
						None
					</DropdownMenuItem>
				)}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function DataTableInfinite<TData, TValue, TMeta>({
	columns,
	getRowClassName,
	getRowId,
	data,
	defaultColumnFilters = [],
	defaultColumnSorting = [],
	defaultRowSelection = {},
	defaultColumnVisibility = {},
	filterFields = [],
	sheetFields = [],
	isFetching,
	isLoading,
	fetchNextPage,
	hasNextPage,
	fetchPreviousPage,
	refetch,
	totalRows = 0,
	filterRows = 0,
	totalRowsFetched = 0,
	chartData = [],
	chartDataColumnId,
	getFacetedUniqueValues,
	getFacetedMinMaxValues,
	meta,
	renderLiveRow,
	renderSheetTitle,
	renderHeader,
	searchParamsParser,
	objectType,
	primaryColumn = "name",
	view,
	onUpdateView,
	onEdit,
	onFavorite,
	onPin,
	onDelete,
	onCopy,
	onHide,
	onEditCell,
	onClearValue,
	customMenuItems,
	isFavorite,
	organizationId,
	fieldTypes = {},
	arrayFieldTypes = {},
	selectOptions = {},
	enableInlineEditing = true,
	readonlyColumns = [],
	renderFilterBar = true,
	renderFilterControls = true,
	selectedProperty,
	onPropertySelect,
	onRowSelectionChange,
	isPaginationMode = false,
	paginationInfo,
	onPageChange,
}: DataTableInfiniteProps<TData, TValue, TMeta>) {
	// Conditionally use URL state management based on pagination mode
	// In pagination mode, the parent component manages URL state
	const [search, setSearch] = isPaginationMode 
		? [{ sort: undefined, start: undefined, size: undefined, id: undefined, cursor: undefined, direction: undefined, live: undefined }, () => {}]
		: useQueryStates(searchParamsParser);

	// Extract filters from search params for display
	const { sort, start, size, id, cursor, direction, live, ...activeFilters } = search;

	// Convert URL search params to TanStack Table filter format for display only
	// In pagination mode, use the passed defaultColumnFilters instead
	const urlColumnFilters = React.useMemo(() => {
		if (isPaginationMode) {
			return defaultColumnFilters;
		}
		return Object.entries(activeFilters)
			.filter(([_, value]) => value !== null && value !== undefined && value !== "")
			.map(([key, value]) => ({
				id: key,
				value,
			}));
	}, [isPaginationMode, defaultColumnFilters, activeFilters]);

	// Local UI state (not persisted, only for table UI)
	const [sorting, setSorting] = React.useState<SortingState>(
		sort ? [{ id: sort.id, desc: sort.desc }] : defaultColumnSorting
	);
	const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(defaultRowSelection);
	const [columnOrder, setColumnOrder] = useLocalStorage<string[]>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_ORDER}-${objectType || "default"}-${view?.id || "default"}`,
		[],
	);
	const [columnVisibility, setColumnVisibility] = useLocalStorage<VisibilityState>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_VISIBILITY}-${objectType || "default"}-${view?.id || "default"}`,
		defaultColumnVisibility,
	);
	const [columnSizing, setColumnSizing] = useLocalStorage<Record<string, number>>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_SIZING}-${objectType || "default"}-${view?.id || "default"}`,
		{ select: 40 }, // Always initialize select column to 40px
	);

	// Column aggregations state - each column can have only one aggregation
	const [columnAggregations, setColumnAggregations] = useLocalStorage<Record<string, string>>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_AGGREGATIONS}-${objectType || "default"}-${view?.id || "default"}`,
		{},
	);

	// Ensure select column is always in the sizing state
	const ensureSelectColumnSizing = React.useCallback(
		(sizing: Record<string, number>) => {
			return { ...sizing, select: 40 };
		},
		[],
	);

	// Always ensure select column is present in column sizing
	const safeColumnSizing = React.useMemo(() => {
		return ensureSelectColumnSizing(columnSizing);
	}, [columnSizing, ensureSelectColumnSizing]);

	const topBarRef = React.useRef<HTMLDivElement>(null);
	const tableRef = React.useRef<HTMLTableElement>(null);
	const [topBarHeight, setTopBarHeight] = React.useState(0);
	const [showBottomBar, setShowBottomBar] = React.useState(false);
	const [editingCell, setEditingCell] = React.useState<{
		rowId: string;
		columnId: string;
	} | null>(null);
	const [selectedCell, setSelectedCell] = React.useState<{
		rowId: string;
		columnId: string;
	} | null>(null);

	// Query client for view updates
	const queryClient = useQueryClient();

	// Pin functionality
	const createPinMutation = useCreatePin(organizationId);
	const deletePinMutation = useDeletePin(organizationId);

	// Global click handler to unfocus cells when clicking outside
	React.useEffect(() => {
		const handleGlobalClick = (e: MouseEvent) => {
			// Check if the click is outside the table
			if (tableRef.current && !tableRef.current.contains(e.target as Node)) {
				// Clear editing and selected cell states when clicking outside
				if (editingCell) {
					setEditingCell(null);
				}
				if (selectedCell) {
					setSelectedCell(null);
				}
			}
		};

		// Add event listener to document
		document.addEventListener('mousedown', handleGlobalClick);

		// Cleanup event listener on unmount
		return () => {
			document.removeEventListener('mousedown', handleGlobalClick);
		};
	}, [editingCell, selectedCell]);

	// Mutation to update view column order
	const updateViewColumnOrderMutation = useMutation({
		mutationFn: async (updatedColumnDefs: any[]) => {
			if (!view?.id) return;

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					columnDefs: updatedColumnDefs,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update view");
			}

			return response.json();
		},
		onSuccess: (data) => {
			// Invalidate queries to ensure fresh data
			if (organizationId) {
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, objectType],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}

			// Call the onUpdateView callback if provided
			if (onUpdateView && data) {
				onUpdateView(data);
			}
		},
		onError: (error) => {
			console.error("Failed to update view column order:", error);
			toast.error("Failed to update view");
		},
	});

	// Mutation specifically for hiding columns
	const hideColumnMutation = useMutation({
		mutationFn: async ({
			updatedColumnDefs,
			columnName,
		}: {
			updatedColumnDefs: any[];
			columnName: string;
		}) => {
			if (!view?.id) return { data: null, columnName };

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					columnDefs: updatedColumnDefs,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to hide column");
			}

			const data = await response.json();
			return { data, columnName };
		},
		onSuccess: (result) => {
			if (!result) return;

			const { columnName, data } = result;

			// Invalidate queries to ensure fresh data
			if (organizationId) {
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, objectType],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}

			// Call the onUpdateView callback if provided
			if (onUpdateView && data) {
				onUpdateView(data);
			}
		},
		onError: (error) => {
			console.error("Failed to hide column:", error);
			toast.error("Failed to hide column");
		},
	});

	// Handle hiding columns from the header dropdown
	const handleHideColumn = React.useCallback(
		(e: React.MouseEvent, columnId: string) => {
			if (!view?.columnDefs) return;

			// Don't allow hiding the primary column
			if (columnId === primaryColumn) {
				console.warn("Cannot hide primary column:", primaryColumn);
				return;
			}

			// Find the column being hidden to get its display name
			const columnToHide = view.columnDefs.find(
				(col) => col.field === columnId,
			);
			const columnName = columnToHide?.headerName || columnId;

			// Remove the column from the view's columnDefs
			const updatedColumnDefs = view.columnDefs.filter(
				(col) => col.field !== columnId,
			);
			hideColumnMutation.mutate({ updatedColumnDefs, columnName });

			// Call the original onHide callback if it exists (for parent component)
			if (onHide) {
				onHide(e, columnId);
			}
		},
		[view, primaryColumn, hideColumnMutation, onHide],
	);

	const handlePin = React.useCallback(
		(
			e: React.MouseEvent,
			recordId: string,
			record: any,
			isPinned: boolean,
		) => {
			if (!organizationId || !objectType) return;

			if (isPinned) {
				// Unpin the record
				deletePinMutation.mutate({
					objectId: recordId,
					objectType: objectType as ObjectType,
					organizationId,
				});
			} else {
				// Pin the record - get the display name from the primary column
				const displayName =
					record[primaryColumn] ||
					record.name ||
					record.title ||
					`${objectType} ${recordId}`;

				createPinMutation.mutate({
					objectId: recordId,
					objectType: objectType as ObjectType,
					name: displayName,
					organizationId,
				});
			}
		},
		[
			organizationId,
			objectType,
			primaryColumn,
			createPinMutation,
			deletePinMutation,
		],
	);

	const onScroll = React.useCallback(
		(e: React.UIEvent<HTMLElement>) => {
			const { scrollTop, clientHeight, scrollHeight } = e.currentTarget;
			
			// Calculate if we're near the bottom (within 100px of bottom)
			const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;
			
			// More detailed logging for debugging
			if (isNearBottom && hasNextPage && !isFetching) {
				console.log('🔄 Loading more data:', {
					hasNextPage,
					isFetching,
					totalRowsFetched,
					filterRows,
					scrollTop,
					clientHeight,
					scrollHeight,
					distanceFromBottom: scrollHeight - (scrollTop + clientHeight)
				});
			}

			if (isNearBottom && hasNextPage && !isFetching) {
				fetchNextPage();
			}
		},
		[fetchNextPage, isFetching, hasNextPage, totalRowsFetched, filterRows],
	);

	React.useEffect(() => {
		const observer = new ResizeObserver(() => {
			const rect = topBarRef.current?.getBoundingClientRect();
			if (rect) {
				setTopBarHeight(rect.height);
			}
		});

		const topBar = topBarRef.current;
		if (!topBar) return;

		observer.observe(topBar);
		return () => observer.unobserve(topBar);
	}, [topBarRef]);

	// Calculate primary column width for proper pinning
	const primaryColumnWidth = React.useMemo(() => {
		const primaryCol = columns.find(
			(col) =>
				col.id === primaryColumn ||
				(col as any).accessorKey === primaryColumn,
		);
		return primaryCol?.size || 250;
	}, [columns, primaryColumn]);

	// Create table instance with disabled client-side filtering
	const table = useReactTable({
		data,
		columns,
		state: {
			columnFilters: urlColumnFilters, // Use URL filters for display only
			sorting,
			columnVisibility,
			rowSelection,
			columnOrder,
			columnSizing: safeColumnSizing,
			columnPinning: {
				left: ["select", primaryColumn],
			},
		},
		enableMultiRowSelection: true,
		columnResizeMode: "onChange",
		columnResizeDirection: "ltr",
		enableColumnResizing: true,
		getRowId,
		enableColumnPinning: true,
		// CRITICAL: Disable client-side filtering to rely on server-side only
		manualFiltering: true,
		onColumnVisibilityChange: setColumnVisibility,
		// Custom filter change handler that updates URL instead of local state
		onColumnFiltersChange: (updater) => {
			// In pagination mode, don't update URL state since parent component handles it
			if (isPaginationMode) {
				// Just update the internal table state, parent will handle URL synchronization
				return;
			}

			const newFilters = typeof updater === 'function' ? updater(urlColumnFilters) : updater;
			
			// Convert TanStack table filters back to URL parameters
			const newSearchParams: Record<string, any> = {};
			
			// Preserve non-filter parameters
			const { sort, start, size, id, cursor, direction, live } = search;
			if (sort) newSearchParams.sort = sort;
			if (size) newSearchParams.size = size;
			if (id) newSearchParams.id = id;
			if (cursor) newSearchParams.cursor = cursor;
			if (direction) newSearchParams.direction = direction;
			if (live) newSearchParams.live = live;
			
			// Apply new filter values
			newFilters.forEach((filter) => {
				if (filter.value !== null && filter.value !== undefined && filter.value !== "") {
					// Handle different filter value types
					if (Array.isArray(filter.value)) {
						// For array values (tags, propertyType, etc.), join with commas
						newSearchParams[filter.id] = filter.value;
					} else if (typeof filter.value === 'object' && filter.value !== null && 'from' in filter.value && 'to' in filter.value) {
						// Handle date range objects
						newSearchParams[filter.id] = [(filter.value as any).from, (filter.value as any).to];
					} else {
						// For simple values
						newSearchParams[filter.id] = filter.value;
					}
				}
			});

			// Reset pagination when filters change
			if (isPaginationMode) {
				newSearchParams.start = 0;
			}

			// Update URL search params (server-side filtering)
			setSearch(newSearchParams);
		},
		onRowSelectionChange: (updater) => {
			const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
			setRowSelection(newSelection);
			onRowSelectionChange?.(newSelection);
		},
		onSortingChange: (updater) => {
			const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
			setSorting(newSorting);
			
			// In pagination mode, don't update URL state since parent component handles it
			if (isPaginationMode) {
				return;
			}
			
			// Update URL search params for sorting
			const sortParam = newSorting[0] ? {
				id: newSorting[0].id,
				desc: newSorting[0].desc
			} : null;
			
			// Reset pagination when sorting changes
			const searchUpdate: Record<string, any> = { sort: sortParam };
			if (isPaginationMode) {
				searchUpdate.start = 0;
			}
			
			setSearch(searchUpdate);
		},
		onColumnOrderChange: setColumnOrder,
		onColumnSizingChange: (sizing) => {
			// Prevent select column from being resized
			const currentSizing = safeColumnSizing;
			const newSizing =
				typeof sizing === "function" ? sizing(currentSizing) : sizing;

			const finalSizing = ensureSelectColumnSizing(newSizing);
			setColumnSizing(finalSizing);
		},
		getSortedRowModel: getSortedRowModel(),
		getCoreRowModel: getCoreRowModel(),
		// Disable client-side filtering models
		// getFilteredRowModel: getFilteredRowModel(),
		// getFacetedRowModel: getFacetedRowModel(),
		// getFacetedUniqueValues: getTTableFacetedUniqueValues(),
		// getFacetedMinMaxValues: getTTableFacetedMinMaxValues(),
		filterFns: { inDateRange, arrSome },
		debugAll: process.env.NEXT_PUBLIC_TABLE_DEBUG === "true",
		meta: { 
			getRowClassName,
			// Pass server-provided counts for accurate display in filter controls
			totalRows,
			filterRows,
			totalRowsFetched,
		},
	});

	// Initialize and sync column order
	React.useEffect(() => {
		const allColumnIds = table.getAllColumns().map((col) => col.id);

		// If columnOrder is empty or doesn't match current columns, reset it
		if (
			columnOrder.length === 0 ||
			columnOrder.length !== allColumnIds.length ||
			!columnOrder.every((id) => allColumnIds.includes(id))
		) {
			setColumnOrder(allColumnIds);
		}
	}, [table, columnOrder, setColumnOrder]);

	// Sync column order and visibility with view
	React.useEffect(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const viewColumnOrder = view.columnDefs.map((col) => col.field);
			const allColumnIds = table.getAllColumns().map((col) => col.id);

			// Find the actual primary column in the table (could match by different properties)
			const actualPrimaryColumn =
				allColumnIds.find((id) => {
					const column = table.getColumn(id);
					return (
						id === primaryColumn ||
						column?.columnDef.id === primaryColumn ||
						(column?.columnDef as any)?.accessorKey ===
							primaryColumn
					);
				}) || primaryColumn;

			// Set column visibility based on view, but always keep primary column visible
			const newVisibility: VisibilityState = {};
			allColumnIds.forEach((columnId) => {
				const viewColumn = view.columnDefs.find(
					(col) => col.field === columnId,
				);
				if (
					columnId === "select" ||
					columnId === primaryColumn ||
					columnId === actualPrimaryColumn
				) {
					// Always keep primary and select columns visible
					newVisibility[columnId] = true;
				} else if (viewColumn) {
					// Column exists in view - show it
					newVisibility[columnId] = true;
				} else {
					// Hide columns not in view
					newVisibility[columnId] = false;
				}
			});

			// Update visibility if it changed
			if (
				JSON.stringify(newVisibility) !==
				JSON.stringify(columnVisibility)
			) {
				setColumnVisibility(newVisibility);
			}

			// Sync column order (keep pinned columns at start)
			const pinnedColumns = ["select", actualPrimaryColumn];
			const nonPinnedViewOrder = viewColumnOrder.filter(
				(id) => !pinnedColumns.includes(id),
			);
			const nonPinnedAllColumns = allColumnIds.filter(
				(id) => !pinnedColumns.includes(id),
			);

			const syncedOrder = [
				...pinnedColumns.filter((id) => allColumnIds.includes(id)),
				...nonPinnedViewOrder.filter((id) => allColumnIds.includes(id)),
				...nonPinnedAllColumns.filter(
					(id) => !viewColumnOrder.includes(id),
				),
			];

			if (JSON.stringify(syncedOrder) !== JSON.stringify(columnOrder)) {
				setColumnOrder(syncedOrder);
			}
		}
	}, [view?.columnDefs, view?.updatedAt, view?.id, table, primaryColumn]);

	// Sync sorting state with URL on mount
	React.useEffect(() => {
		if (sort && sorting.length === 0) {
			setSorting([{ id: sort.id, desc: sort.desc }]);
		}
	}, [sort]);

	const selectedRow = React.useMemo(() => {
		if ((isLoading || isFetching) && !data.length) return;
		const selectedRowKey = Object.keys(rowSelection)?.[0];
		return table
			.getCoreRowModel()
			.flatRows.find((row) => row.id === selectedRowKey);
	}, [rowSelection, table, isLoading, isFetching, data]);

	// Get selected rows for multi-selection
	const selectedRows = React.useMemo(() => {
		return table.getSelectedRowModel().rows;
	}, [table, rowSelection]);

	React.useEffect(() => {
		setShowBottomBar(selectedRows.length > 0);
	}, [selectedRows]);

	React.useEffect(() => {
		if (isLoading || isFetching) return;
		if (Object.keys(rowSelection)?.length && !selectedRow) {
			// setSearch({ id: null });
			setRowSelection({});
		} else if (Object.keys(rowSelection)?.length) {
			// Will do when we include quick view
			// setSearch({ id: Object.keys(rowSelection)?.[0] || null });
		}
	}, [rowSelection, selectedRow, isLoading, isFetching]);

	const columnSizeVars = React.useMemo(() => {
		const headers = table.getFlatHeaders();
		const colSizes: { [key: string]: string } = {};
		for (let i = 0; i < headers.length; i++) {
			const header = headers[i]!;
			// Force select column to always be 40px
			const size =
				header.id === "select" || header.column.id === "select"
					? 40
					: header.getSize();
			const columnSize =
				header.id === "select" || header.column.id === "select"
					? 40
					: header.column.getSize();

			colSizes[`--header-${header.id.replace(".", "-")}-size`] =
				`${size}px`;
			colSizes[`--col-${header.column.id.replace(".", "-")}-size`] =
				`${columnSize}px`;
		}
		return colSizes;
	}, [
		table.getState().columnSizingInfo,
		table.getState().columnSizing,
		table.getState().columnVisibility,
	]);

	// Get column order outside of render to ensure consistency
	const currentColumnOrder = table.getState().columnOrder;

	// Check if only pinned columns are visible (empty state condition)
	const visibleColumns = table.getVisibleLeafColumns();
	const onlyPinnedColumnsVisible = React.useMemo(() => {
		const visibleColumnIds = visibleColumns.map((col) => col.id);

		// Find the actual primary column among visible columns
		const actualPrimaryColumn = visibleColumnIds.find((id) => {
			const column = table.getColumn(id);
			return (
				id === primaryColumn ||
				column?.columnDef.id === primaryColumn ||
				(column?.columnDef as any)?.accessorKey === primaryColumn
			);
		});

		// System/pinned columns are select and the actual primary column
		const systemColumns = ["select", actualPrimaryColumn].filter(Boolean);

		// Check if all visible columns are system columns
		const nonSystemColumns = visibleColumnIds.filter(
			(id) => !systemColumns.includes(id),
		);

		// Show empty state only if there are no non-system columns
		// This means only select and/or primary columns are visible
		return nonSystemColumns.length === 0 && visibleColumnIds.length > 0;
	}, [visibleColumns, primaryColumn, table]);

	// Set up sensors for drag and drop
	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
		useSensor(KeyboardSensor),
	);

	// Removed excessive logging for cleaner console

	// State for drag over indicator
	const [dragOverId, setDragOverId] = React.useState<string | null>(null);

	// Handle drag start
	const handleDragStart = React.useCallback((event: any) => {
		// Optional: Add any drag start logic here
	}, []);

	// Handle drag over to show drop indicator
	const handleDragOver = React.useCallback((event: any) => {
		const { over } = event;
		setDragOverId(over?.id || null);
	}, []);

	// Handle drag end
	const handleDragEnd = React.useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;

			// Clear drag over indicator
			setDragOverId(null);

			if (active.id !== over?.id && over) {
				// Update local column order state
				setColumnOrder((columnOrder) => {
					const oldIndex = columnOrder.indexOf(active.id as string);
					const newIndex = columnOrder.indexOf(over.id as string);
					const newColumnOrder = arrayMove(
						columnOrder,
						oldIndex,
						newIndex,
					);

					// Update the view's columnDefs if we have a view
					if (view?.columnDefs && view.columnDefs.length > 0) {
						const columnDefsMap = new Map(
							view.columnDefs.map((col) => [col.field, col]),
						);

						// Reorder ALL columnDefs according to the new column order
						const reorderedColumnDefs = newColumnOrder
							.map((fieldId) => columnDefsMap.get(fieldId))
							.filter(
								(col): col is NonNullable<typeof col> =>
									col !== undefined,
							);

						// Add any remaining columnDefs that weren't in the reorder
						const orderedFields = new Set(newColumnOrder);
						const remainingColumnDefs = view.columnDefs.filter(
							(col) => !orderedFields.has(col.field),
						);

						const finalColumnDefs = [
							...reorderedColumnDefs,
							...remainingColumnDefs,
						];
						updateViewColumnOrderMutation.mutate(finalColumnDefs);
					}

					return newColumnOrder;
				});
			}
		},
		[setColumnOrder, columnOrder, view, updateViewColumnOrderMutation],
	);

	// Helper functions for aggregations (using server-provided data)
	const calculateAggregation = React.useCallback(
		(columnId: string, aggregationType: string) => {
			// Use server-provided totals instead of client-side calculations for accuracy
			switch (aggregationType) {
				case "count":
					return filterRows.toString(); // Use server-provided filtered count
				case "count-empty": {
					// For more accurate empty/filled counts, we'd need server-side support
					// For now, fall back to client-side calculation as a temporary measure
					const rows = table.getCoreRowModel().rows; // Use unfiltered core data
					const emptyCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value === null ||
							value === undefined ||
							value === "" ||
							(Array.isArray(value) && value.length === 0)
						);
					}).length;
					return emptyCount.toString();
				}
				case "count-filled": {
					const rows = table.getCoreRowModel().rows;
					const filledCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value !== null &&
							value !== undefined &&
							value !== "" &&
							(!Array.isArray(value) || value.length > 0)
						);
					}).length;
					return filledCount.toString();
				}
				case "percent-empty": {
					if (filterRows === 0) return "0%";
					const rows = table.getCoreRowModel().rows;
					const emptyCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value === null ||
							value === undefined ||
							value === "" ||
							(Array.isArray(value) && value.length === 0)
						);
					}).length;
					return `${Math.round((emptyCount / filterRows) * 100)}%`;
				}
				case "percent-filled": {
					if (filterRows === 0) return "0%";
					const rows = table.getCoreRowModel().rows;
					const filledCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value !== null &&
							value !== undefined &&
							value !== "" &&
							(!Array.isArray(value) || value.length > 0)
						);
					}).length;
					return `${Math.round((filledCount / filterRows) * 100)}%`;
				}
				default:
					return "0";
			}
		},
		[table, filterRows], // Use server-provided filterRows for accurate counts
	);

	const getAggregationLabel = (aggregationType: string) => {
		switch (aggregationType) {
			case "count":
				return "count";
			case "count-empty":
				return "count empty";
			case "count-filled":
				return "count filled";
			case "percent-empty":
				return "percent empty";
			case "percent-filled":
				return "percent filled";
			default:
				return aggregationType;
		}
	};

	const setAggregation = (columnId: string, aggregationType: string) => {
		setColumnAggregations((prev) => ({
			...prev,
			[columnId]: aggregationType,
		}));
	};

	const removeAggregation = (columnId: string) => {
		setColumnAggregations((prev) => {
			const newAggregations = { ...prev };
			delete newAggregations[columnId];
			return newAggregations;
		});
	};

	return (
		<DataTableProvider
			table={table}
			columns={columns}
			filterFields={filterFields}
			columnFilters={urlColumnFilters}
			sorting={sorting}
			rowSelection={rowSelection}
			columnOrder={columnOrder}
			columnVisibility={columnVisibility}
			enableColumnOrdering={true}
			isLoading={isFetching || isLoading}
			getFacetedUniqueValues={getFacetedUniqueValues}
			getFacetedMinMaxValues={getFacetedMinMaxValues}
		>
			{renderHeader?.()}
			<div
				className="flex h-[calc(100vh-100px)] w-full flex-col sm:flex-row overflow-hidden"
				style={
					{
						"--top-bar-height": `${topBarHeight}px`,
						...columnSizeVars,
					} as React.CSSProperties
				}
			>
				{renderFilterControls && (
				<div
					className={cn(
						"flex w-full flex-col sm:sticky sm:top-0 sm:h-screen sm:min-w-52 sm:max-w-52 sm:self-start md:min-w-72 md:max-w-72",
						"group-data-[expanded=false]/controls:hidden",
						"hidden sm:flex",
					)}
				>
					<DataTableFilterControls />
				</div>
				)}
				<div
					className={cn(
						"flex max-w-full flex-1 flex-col border-border sm:border-l h-full overflow-hidden",
						"group-data-[expanded=true]/controls:sm:max-w-[calc(100vw_-_208px)]",
						renderFilterBar ? "group-data-[expanded=true]/controls:md:max-w-[calc(100vw_-_288px)]" : "",
					)}
				>
					{renderFilterBar && (
						<div
							ref={topBarRef}
							className={cn(
								"flex flex-col gap-4 bg-sidebar p-2 flex-shrink-0",
								"sticky top-0 z-50",
							)}
						>
							<DataTableFilterCommand
								searchParamsParser={searchParamsParser}
							/>
						</div>
					)}
					<div className="flex-1 overflow-hidden relative flex flex-col">
						<div className="flex-1 relative">
							<div 
								className="absolute inset-0 overflow-auto"
								onScroll={onScroll}
							>
								<DndContext
									sensors={sensors}
									collisionDetection={closestCenter}
									onDragStart={handleDragStart}
									onDragOver={handleDragOver}
									onDragEnd={handleDragEnd}
									modifiers={[restrictToHorizontalAxis]}
								>
									<Table
										ref={tableRef}
										className="border-separate border-spacing-0 w-full [&_th:first-child]:!w-10 [&_th:first-child]:!min-w-10 [&_th:first-child]:!max-w-10 [&_td:first-child]:!w-10 [&_td:first-child]:!min-w-10 [&_td:first-child]:!max-w-10"
										style={{
											tableLayout: "fixed",
										}}
									>
											<style
												dangerouslySetInnerHTML={{
													__html: `
		                      table .select-column-header,
		                      table .select-column-cell,
		                      table th:first-child,
		                      table td:first-child {
		                        width: 40px !important;
		                        min-width: 40px !important;
		                        max-width: 40px !important;
		                        flex: 0 0 40px !important;
		                        box-sizing: border-box !important;
		                        overflow: hidden !important;
		                      }
		                    `,
												}}
											/>
											<TableHeader
												className={cn(
													"sticky top-0 bg-sidebar",
													"shadow-sm border-b border-border",
												)}
											>
												{table
													.getHeaderGroups()
													.map((headerGroup) => (
														<TableRow
															key={headerGroup.id}
															className={cn(
																"h-8",
																"bg-sidebar",
																"[&>:not(:last-child)]:border-r",
															)}
														>
															<SortableContext
																items={columnOrder}
																strategy={
																	horizontalListSortingStrategy
																}
															>
																{headerGroup.headers.map(
																	(header) => (
																		<HeaderCell
																			key={
																				header.id
																			}
																			header={
																				header
																			}
																			primaryColumn={
																				primaryColumn
																			}
																			readonlyColumns={
																				readonlyColumns
																			}
																			dragOverId={
																				dragOverId
																			}
																			onHide={
																				handleHideColumn
																			}
																			onMoveColumn={(
																				id,
																				direction,
																			) => {
																				const currentOrder =
																					[
																						...table.getState()
																							.columnOrder,
																					];
																				const columnIndex =
																					currentOrder.indexOf(
																						id,
																					);

																				if (
																					columnIndex >
																						0 &&
																					direction ===
																						"left"
																				) {
																					const newOrder =
																						[
																							...currentOrder,
																						];
																					[
																						newOrder[
																							columnIndex -
																								1
																						],
																						newOrder[
																							columnIndex
																						],
																					] =
																						[
																							newOrder[
																								columnIndex
																							],
																							newOrder[
																								columnIndex -
																									1
																							],
																						];
																					table.setColumnOrder(
																						newOrder,
																					);
																				} else if (
																					columnIndex <
																						currentOrder.length -
																							1 &&
																					direction ===
																						"right"
																				) {
																					const newOrder =
																						[
																							...currentOrder,
																						];
																					[
																						newOrder[
																							columnIndex
																						],
																						newOrder[
																							columnIndex +
																								1
																						],
																					] =
																						[
																							newOrder[
																								columnIndex +
																									1
																							],
																							newOrder[
																								columnIndex
																							],
																						];
																					table.setColumnOrder(
																						newOrder,
																					);
																				}
																			}}
																		/>
																	),
																)}
															</SortableContext>
															{/* Add column header - always visible */}
															<TableHead className="border-t border-b border-border p-0 min-w-[300px] w-full">
																<div className="flex items-center justify-start h-10 px-2 min-w-[300px] w-full">
																	<AddColumnPopover
																		view={view}
																		objectType={objectType as ObjectType}
																		primaryColumn={
																			primaryColumn
																		}
																		onColumnAdded={() => {
																			refetch();
																		}}
																	>
																		<Button
																			variant="ghost"
																			className="hover:bg-muted/50 text-muted-foreground rounded-lg h-8 px-3"
																		>
																			<div className="flex items-center justify-center gap-2">
																				<IconPlus className="w-4 h-4" />
																				<span>
																					Add
																					column
																				</span>
																			</div>
																		</Button>
																	</AddColumnPopover>
																</div>
															</TableHead>
														</TableRow>
													))}
											</TableHeader>
											<TableBody id="content" tabIndex={-1}>
												{table.getRowModel().rows
													?.length ? (
													table
														.getRowModel()
														.rows.map((row) => {
															if (
																onlyPinnedColumnsVisible
															) {
																// Custom row rendering for empty state
																return (
																	<TableRow
																		key={row.id}
																		className="h-10 group [&>:not(:last-child)]:border-r"
																	>
																		{/* Render existing row cells */}
																		{row
																			.getVisibleCells()
																			.map(
																				(
																					cell,
																				) => {
																					const columnAccessorKey =
																						(
																							cell
																								.column
																								.columnDef as any
																						)
																							?.accessorKey;
																					const isPrimaryOrSelectColumn =
																						cell
																							.column
																							.id ===
																							"select" ||
																						cell
																							.column
																							.id ===
																							primaryColumn ||
																						columnAccessorKey ===
																							primaryColumn;

																					return (
																						<TableCell
																							key={
																								cell.id
																							}
																							style={{
																								width:
																									cell
																										.column
																										.id ===
																									"select"
																										? 40
																										: cell.column.getSize(),
																								minWidth:
																									cell
																										.column
																										.id ===
																									"select"
																										? 40
																										: cell.column.getSize(),
																								maxWidth:
																									cell
																										.column
																										.id ===
																									"select"
																										? 40
																										: cell.column.getSize(),
																								...(cell
																									.column
																									.id ===
																									primaryColumn && {
																									left: "40px",
																								}),
																							}}
																							className={cn(
																								"border-b border-border relative align-middle min-w-0",
																								// Handle padding and width for select column
																								cell
																									.column
																									.id ===
																									"select"
																									? "select-column-cell !p-0 !w-10 !min-w-10 !max-w-10"
																									: "p-2",
																								cell.column.getIsPinned() && [
																									"sticky z-10",
																									"bg-background/55 backdrop-blur-3xl supports-[backdrop-filter]:bg-background/35",
																									cell
																										.column
																										.id ===
																										primaryColumn &&
																										"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
																									cell
																										.column
																										.id ===
																									"select"
																										? "left-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
																										: "",
																								],
																							)}
																						>
																							{cell
																								.column
																								.id ===
																							"select" ? (
																								<div className="flex items-center justify-center w-full h-full">
																									{flexRender(
																										cell
																											.column
																											.columnDef
																											.cell,
																										cell.getContext(),
																									)}
																								</div>
																							) : (
																								<div className="w-full min-w-0 truncate">
																									{flexRender(
																										cell
																											.column
																											.columnDef
																											.cell,
																										cell.getContext(),
																									)}
																								</div>
																							)}
																						</TableCell>
																					);
																				},
																			)}
																		<TableCell className="w-full min-w-0 truncate">
																			{row.index ===
																				0 && (
																				<div className="flex items-center justify-center h-full">
																					<span className="text-xs text-muted-foreground">
																						No
																						columns
																						to
																						display
																					</span>
																				</div>
																			)}
																		</TableCell>
																	</TableRow>
																);
															}
															// Normal row rendering when not in empty state
															return (
																<MemoizedRow
																	key={row.id}
																	row={row}
																	table={table}
																	selected={row.getIsSelected()}
																	columnOrder={
																		columnOrder
																	}
																	primaryColumn={
																		primaryColumn
																	}
																	onEdit={onEdit}
																	onFavorite={
																		onFavorite
																	}
																	onPin={
																		handlePin
																	}
																	onDelete={
																		onDelete
																	}
																	onCopy={onCopy}
																	onHide={
																		handleHideColumn
																	}
																	onEditCell={
																		onEditCell
																	}
																	onClearValue={
																		onClearValue
																	}
																	customMenuItems={
																		customMenuItems
																	}
																	isFavorite={
																		isFavorite
																	}
																	organizationId={
																		organizationId
																	}
																	objectType={
																		objectType
																	}
																	fieldTypes={
																		fieldTypes
																	}
																	arrayFieldTypes={
																		arrayFieldTypes
																	}
																	selectOptions={
																		selectOptions
																	}
																	enableInlineEditing={
																		enableInlineEditing
																	}
																	editingCell={
																		editingCell
																	}
																	setEditingCell={
																		setEditingCell
																	}
																	selectedCell={
																		selectedCell
																	}
																	setSelectedCell={
																		setSelectedCell
																	}
																	view={view}
																	readonlyColumns={
																		readonlyColumns
																	}
																	onPropertySelect={
																		onPropertySelect
																	}
																/>
															);
														})
												) : (
													<TableRow>
														{onlyPinnedColumnsVisible ? (
															<>
																{/* Show select and primary column cells even when no data */}
																<TableCell
																	className="select-column-cell h-24 border-b border-border bg-background/55 sticky left-0 z-10 !w-10 !min-w-10 !max-w-10 !p-0 !px-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
																	style={{
																		width: 40,
																		minWidth: 40,
																		maxWidth: 40,
																	}}
																>
																	{/* Empty select column */}
																</TableCell>
																<TableCell
																	className="h-10 border-b border-border bg-background/55 sticky z-10 min-w-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
																	style={{
																		left: "40px",
																		minWidth:
																			"250px",
																	}}
																>
																	<div className="text-muted-foreground text-sm truncate">
																		No results.
																	</div>
																</TableCell>
																{/* Add column cell for no data scenario */}
																{/* <TableCell className="border-b border-border min-w-48">
																	<div className="flex items-center justify-center h-full py-8">
																		<div className="text-center">
																			<div className="mb-2">
																				<div className="w-8 h-8 mx-auto mb-2 rounded-full bg-blue-50 flex items-center justify-center">
																					<svg
																						className="w-4 h-4 text-blue-500"
																						fill="none"
																						stroke="currentColor"
																						viewBox="0 0 24 24"
																					>
																						<path
																							strokeLinecap="round"
																							strokeLinejoin="round"
																							strokeWidth={
																								2
																							}
																							d="M12 6v6m0 0v6m0-6h6m-6 0H6"
																						/>
																					</svg>
																				</div>
																			</div>
																			<h4 className="text-xs font-medium text-foreground mb-1">
																				Add
																				column
																			</h4>
																			<p className="text-xs text-muted-foreground mb-3">
																				No
																				columns
																				to
																				display
																			</p>
																			<AddColumnPopover
																				view={
																					view
																				}
																				objectType={
																					objectType as
																						| "contacts"
																						| "companies"
																						| "properties"
																				}
																				primaryColumn={
																					primaryColumn
																				}
																				onColumnAdded={() => {
																					// Force re-render to update filter fields
																					refetch();
																				}}
																			>
																				<Button
																					size="sm"
																					className="bg-blue-600 hover:bg-blue-700 text-white text-xs h-7"
																				>
																					<IconPlus className="w-3 h-3 mr-1" />
																					Add
																					Column
																				</Button>
																			</AddColumnPopover>
																		</div>
																	</div>
																</TableCell> */}
															</>
														) : (
															<>
																{/* TODO: Add column cell for normal no results state */}
															</>
														)}
													</TableRow>
												)}
											</TableBody>
									</Table>
								</DndContext>
							</div>
						</div>

						{/* Footer moved outside scrollable area and pinned to bottom */}
						{renderFilterBar && (
							<div className="border-t border-border bg-background/98 backdrop-blur-xl shadow-xl ring-1 ring-border/50 z-30">
								<div className="overflow-x-auto">
									<Table
										className="border-separate border-spacing-0 relative w-full [&_th:first-child]:!w-10 [&_th:first-child]:!min-w-10 [&_th:first-child]:!max-w-10 [&_td:first-child]:!w-10 [&_td:first-child]:!min-w-10 [&_td:first-child]:!max-w-10"
										style={{
											tableLayout: "fixed",
										}}
									>
										<TableFooter>
											{table
												.getFooterGroups()
												.map((footerGroup) => (
													<TableRow
														key={footerGroup.id}
														className="hover:bg-transparent"
													>
														{footerGroup.headers.map(
															(header) => (
																<FooterCell
																	key={header.id}
																	header={header}
																	primaryColumn={primaryColumn}
																	columnAggregations={columnAggregations}
																	calculateAggregation={calculateAggregation}
																	getAggregationLabel={getAggregationLabel}
																	setAggregation={setAggregation}
																	removeAggregation={removeAggregation}
																	hasNoRecords={data.length === 0}
																	isPaginationMode={
																		isPaginationMode
																	}
																	paginationInfo={
																		paginationInfo
																	}
																	onPageChange={
																		onPageChange
																	}
																	isLoading={
																		isLoading
																	}
																	isFetching={
																		isFetching
																	}
																	objectType={
																		objectType
																	}
																	hasActiveFilters={
																		urlColumnFilters.length > 0
																	}
																/>
															),
														)}
														{/* Add column footer cell */}
														<TableCell
															className={cn(
																"bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar min-w-48 min-h-10 py-2",
																data.length ===
																	0 &&
																	"border-t",
															)}
														/>
													</TableRow>
												))}
										</TableFooter>
									</Table>
								</div>
							</div>
						)}
					</div>
					{selectedProperty && (
						<PropertySidebar
							selectedProperty={selectedProperty}
							onClose={() => onPropertySelect?.(null)}
							isTableOpen={true}
						/>
					)}
				</div>
			</div>



			{showBottomBar && (
				<BottomBar
					selectedRecords={selectedRows.map((row) => row.original)}
					objectType={objectType}
				/>
			)}
		</DataTableProvider>
	);
}

// Add this type near the top of the file, after imports
type ExtendedColumnDef<TData> = ColumnDef<TData, unknown> & {
  accessorKey?: string;
};

function Row<TData>({
	row,
	table,
	selected,
	columnOrder,
	primaryColumn,
	onEdit,
	onFavorite,
	onPin,
	onDelete,
	onCopy,
	onHide,
	onEditCell,
	onClearValue,
	customMenuItems,
	isFavorite,
	organizationId,
	objectType,
	fieldTypes = {},
	arrayFieldTypes = {},
	selectOptions = {},
	enableInlineEditing = true,
	editingCell,
	setEditingCell,
	selectedCell,
	setSelectedCell,
	view,
	readonlyColumns = [],
	onPropertySelect,
}: {
	row: Row<TData>;
	table: TTable<TData>;
	selected?: boolean;
	columnOrder: string[];
	primaryColumn: string;
	onEdit?: (e: React.MouseEvent, recordId: string) => void;
	onFavorite?: (e: React.MouseEvent, recordId: string) => void;
	onPin?: (
		e: React.MouseEvent,
		recordId: string,
		record: any,
		isPinned: boolean,
	) => void;
	onDelete?: (e: React.MouseEvent, recordId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
	onEditCell?: (
		e: React.MouseEvent,
		cell: { columnId: string; value: any; rowId: string },
	) => Promise<void>;
	onClearValue?: (
		e: React.MouseEvent,
		cell: { columnId: string; rowId: string },
	) => Promise<void>;
	customMenuItems?: (record: any) => any[];
	isFavorite?: (record: any) => boolean;
	organizationId?: string;
	objectType?: string;
	fieldTypes?: Record<
		string,
		FieldType
	>;
	arrayFieldTypes?: Record<string, ArrayFieldType>;
	selectOptions?: Record<string, Array<{ label: string; value: string }>>;
	enableInlineEditing?: boolean;
	editingCell?: { rowId: string; columnId: string } | null;
	setEditingCell?: React.Dispatch<
		React.SetStateAction<{ rowId: string; columnId: string } | null>
	>;
	selectedCell?: { rowId: string; columnId: string } | null;
	setSelectedCell?: React.Dispatch<
		React.SetStateAction<{ rowId: string; columnId: string } | null>
	>;
	readonlyColumns?: string[];
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		updatedAt?: string;
	};
	onPropertySelect?: (property: any) => void;
}) {
	const router = useRouter();
	const visibleCells = row.getVisibleCells();

	// Sort cells according to column order
	const orderedCells = React.useMemo(() => {
		if (!columnOrder.length) return visibleCells;

		return visibleCells.sort((a, b) => {
			const aIndex = columnOrder.indexOf(a.column.id);
			const bIndex = columnOrder.indexOf(b.column.id);

			// If column is not in columnOrder, put it at the end
			if (aIndex === -1) return 1;
			if (bIndex === -1) return -1;

			return aIndex - bIndex;
		});
	}, [visibleCells, columnOrder]);

	const handleCellClick = (e: React.MouseEvent, cell: any) => {
		if (e.button === 2) return;

		if (cell.column.id === 'select') {
			row.toggleSelected();
			return;
		}

		// For primary column, we don't want the entire cell to be clickable
		// The navigation will be handled by the value click
	};

	const handleValueClick = (e: React.MouseEvent) => {
		e.stopPropagation();

		if (objectType) {
			const orgSlug = window.location.pathname.split('/')[2];
			
			const allRows = table.getFilteredRowModel().rows;
			const currentIndex = allRows.findIndex(r => r.id === row.id);
			const totalRecords = allRows.length;
			
			const url = `/app/${orgSlug}/${objectType}/${row.id}`;
			const searchParams = new URLSearchParams();
			searchParams.set('index', currentIndex.toString());
			searchParams.set('total', totalRecords.toString());
			
			if (view?.id) {
				searchParams.set('viewId', view.id);
				searchParams.set('viewName', view.name);
			}
			
			const finalUrl = `${url}?${searchParams.toString()}`;
			router.push(finalUrl);
		}
	};


	const handleStartEdit = (columnId: string) => {
		if (enableInlineEditing && setEditingCell) {
			setEditingCell({ rowId: row.id, columnId });
		}
	};

	const handleStopEdit = () => {
		if (setEditingCell) {
			setEditingCell(null);
		}
	};

	const handleSelectCell = (columnId: string) => {
		if (setSelectedCell) {
			setSelectedCell({ rowId: row.id, columnId });
		}
	};

	const handleSaveEdit = async (columnId: string, newValue: any) => {
		if (onEditCell) {
			await onEditCell({} as React.MouseEvent, {
				columnId,
				value: newValue,
				rowId: row.id,
			});
		}
	};

	const getFieldType = (columnId: string) => {
		return fieldTypes[columnId] || "text";
	};

	const getArrayFieldType = (columnId: string) => {
		return arrayFieldTypes[columnId] || "text";
	};

	const getSelectOptions = (columnId: string) => {
		return selectOptions[columnId] || [];
	};

	// Check if this record is pinned
	const isPinned = useIsPinned(organizationId, objectType as ObjectType, row.id);

	return (
		<DataTableContextMenu
			record={row.original}
			isFavorite={isFavorite?.(row.original)}
			isPinned={isPinned}
			organizationId={organizationId}
			objectType={objectType as ObjectType}
			onEdit={onEdit}
			onFavorite={onFavorite}
			onPin={
				onPin
					? (e) => onPin(e, row.id, row.original, isPinned)
					: undefined
			}
			onDelete={onDelete}
			onCopy={onCopy}
			onHide={onHide}
			customMenuItems={customMenuItems}
		>
			<TableRow
				id={row.id}
				tabIndex={0}
				data-state={selected && "selected"}
				className={cn(
					"h-10 group",
					"[&>:not(:last-child)]:border-r",
					// "hover:bg-muted/50",
					"cursor-default",
					"focus-visible:bg-muted/50 focus-visible:outline",
				)}
			>
				{orderedCells.map((cell) => {
					const fieldType = getFieldType(cell.column.id);
					const arrayFieldType = getArrayFieldType(cell.column.id);



					// For array fields, get the full array from original row data
					// For other fields, use the cell value (which might be processed by accessorFn)
					const cellValue =
						fieldType === "array"
							? (cell.row.original as any)[cell.column.id]
							: cell.getValue();

					// Check if this cell is editable
					const columnDef = cell.column.columnDef as ExtendedColumnDef<TData>;
					const columnAccessorKey = columnDef.accessorKey;
					const isPrimaryColumn = cell.column.id === primaryColumn || columnAccessorKey === primaryColumn;
					const isPrimaryOrSelectColumn =
						cell.column.id === "select" ||
						isPrimaryColumn;

					const isReadonly = readonlyColumns.includes(cell.column.id);
					const isEditable =
						enableInlineEditing &&
						!isPrimaryOrSelectColumn &&
						!isReadonly &&
						onEditCell;

					const cellContent = (
						<TableCell
							onClick={isPrimaryColumn ? undefined : (e) => handleCellClick(e, cell)}
							style={{
								width:
									cell.column.id === "select"
										? 40
										: cell.column.getSize(),
								minWidth:
									cell.column.id === "select"
										? 40
										: cell.column.getSize(),
								maxWidth:
									cell.column.id === "select"
										? 40
										: cell.column.getSize(),
								...(cell.column.id === primaryColumn && {
									left: "40px",
								}),
							}}
							className={cn(
								"border-b border-border relative min-w-0 cursor-default",
								// Handle padding based on column type and editability
								isEditable
									? "p-0 h-10"
									: cell.column.id === "select"
										? "!p-0 align-middle"
										: isPrimaryColumn
										? "p-0 h-10 align-middle"
										: "p-2 align-middle",
								// Force select column to 40px with CSS
								cell.column.id === "select" &&
									"select-column-cell !w-10 !min-w-10 !max-w-10",
								cell.column.getIsPinned() && [
									"sticky z-10",
									"bg-background/55 backdrop-blur-3xl supports-[backdrop-filter]:bg-background/35",
									cell.column.id === primaryColumn &&
										"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
									cell.column.id === "select"
										? "left-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
										: "",
								],
							)}
						>
							{cell.column.id === "select" ? (
								<div className="flex items-center justify-center w-full h-full">
									{flexRender(
										cell.column.columnDef.cell,
										cell.getContext(),
									)}
								</div>
							) : isPrimaryColumn ? (
								<div className="relative group flex items-center w-full h-full px-2">
									<div className="flex items-center gap-2 rounded-lg hover:bg-muted/50 px-1">
										{objectType === "property" && (
											<PropertyAvatar
												name={cellValue as string || "Property"}
												className="h-5 w-5 flex-shrink-0"
											/>
										)}
										{objectType === "contact" && (
											<ContactAvatar
												name={cellValue as string || "Contact"}
												className="h-5 w-5 flex-shrink-0"
											/>
										)}
										<div
											onClick={handleValueClick}
											className="cursor-pointer text-foreground font-medium truncate flex-1 min-w-0 py-1 rounded-sm px-1 -mx-1 truncate"
										>
											{cellValue || flexRender(
												cell.column.columnDef.cell,
												cell.getContext(),
											)}
										</div>
									</div>
									<div className="absolute right-2 opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-x-2 group-hover:translate-x-0 hover:bg-muted/50 rounded-md p-1">
										<IconBrandLine className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors cursor-pointer" />
									</div>
								</div>
							) : isEditable ? (
								<InlineCellEditor
									value={cellValue}
									fieldType={fieldType}
									arrayFieldType={arrayFieldType}
									selectOptions={getSelectOptions(
										cell.column.id,
									)}
									isEditing={
										editingCell?.rowId === row.id &&
										editingCell?.columnId === cell.column.id
									}
									isSelected={
										selectedCell?.rowId === row.id &&
										selectedCell?.columnId ===
											cell.column.id
									}
									onStartEdit={() =>
										handleStartEdit(cell.column.id)
									}
									onStopEdit={handleStopEdit}
									onSelect={() =>
										handleSelectCell(cell.column.id)
									}
									onSave={async (newValue: any) => {
										await handleSaveEdit(
											cell.column.id,
											newValue,
										);
										handleStopEdit();
									}}
									width={cell.column.getSize().toString()}
									organizationId={organizationId}
									objectType={objectType as TaggableObjectType}
									objectId={row.id}
								>
									{flexRender(
										cell.column.columnDef.cell,
										cell.getContext(),
									)}
								</InlineCellEditor>
							) : (
								<div className="w-full min-w-0 truncate">
									{flexRender(
										cell.column.columnDef.cell,
										cell.getContext(),
									)}
								</div>
							)}
						</TableCell>
					);

					if (isPrimaryOrSelectColumn) {
						return (
							<React.Fragment key={cell.id}>
								{cellContent}
							</React.Fragment>
						);
					}

					// Only show context menu for editable cells
					if (isEditable) {
						return (
							<DataTableCellContextMenu
								key={cell.id}
								record={row.original}
								cell={{
									columnId: cell.column.id,
									value: cell.getValue(),
									rowId: row.id,
								}}
								organizationId={organizationId}
								objectType={objectType || "record"}
								onEditCell={
									setEditingCell
										? (e, cellData) => {
												// Trigger inline editing instead of the old prompt approach
												setEditingCell({
													rowId: cellData.rowId,
													columnId: cellData.columnId,
												});
											}
										: undefined
								}
								onFavorite={onFavorite}
								onPin={
									onPin
										? (e) =>
												onPin(
													e,
													row.id,
													row.original,
													isPinned,
												)
										: undefined
								}
								onDelete={onDelete}
								onCopy={onCopy}
								onHide={onHide}
								onClearValue={onClearValue}
								isFavorite={isFavorite?.(row.original)}
								isPinned={isPinned}
								customMenuItems={customMenuItems}
							>
								{cellContent}
							</DataTableCellContextMenu>
						);
					}

					// Non-editable cells (read-only) don't get context menu
					return (
						<React.Fragment key={cell.id}>
							{cellContent}
						</React.Fragment>
					);
				})}
				{/* Add column cell for normal rows */}
				<TableCell className="border-b border-border min-w-48 p-2">
					<div className="flex items-center justify-center cursor-default" />
				</TableCell>
			</TableRow>
		</DataTableContextMenu>
	);
}

const MemoizedRow = React.memo(
	Row,
	(prev, next) =>
		prev.row.id === next.row.id &&
		prev.selected === next.selected &&
		JSON.stringify(prev.columnOrder) === JSON.stringify(next.columnOrder) &&
		prev.primaryColumn === next.primaryColumn &&
		prev.onEdit === next.onEdit &&
		prev.onFavorite === next.onFavorite &&
		prev.onPin === next.onPin &&
		prev.onDelete === next.onDelete &&
		prev.onCopy === next.onCopy &&
		prev.onHide === next.onHide &&
		prev.onEditCell === next.onEditCell &&
		prev.onClearValue === next.onClearValue &&
		JSON.stringify(prev.customMenuItems) ===
			JSON.stringify(next.customMenuItems) &&
		prev.isFavorite === next.isFavorite &&
		prev.organizationId === next.organizationId &&
		prev.objectType === next.objectType &&
		JSON.stringify(prev.fieldTypes) === JSON.stringify(next.fieldTypes) &&
		JSON.stringify(prev.arrayFieldTypes) ===
			JSON.stringify(next.arrayFieldTypes) &&
		JSON.stringify(prev.selectOptions) ===
			JSON.stringify(next.selectOptions) &&
		prev.enableInlineEditing === next.enableInlineEditing &&
		JSON.stringify(prev.editingCell) === JSON.stringify(next.editingCell) &&
		prev.setEditingCell === next.setEditingCell &&
		JSON.stringify(prev.selectedCell) ===
			JSON.stringify(next.selectedCell) &&
		prev.setSelectedCell === next.setSelectedCell &&
		JSON.stringify(prev.view) === JSON.stringify(next.view) &&
		JSON.stringify(prev.readonlyColumns) ===
			JSON.stringify(next.readonlyColumns),
) as typeof Row;
