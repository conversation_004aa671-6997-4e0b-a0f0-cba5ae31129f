"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	type DragStartEvent,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { EllipsisDropdown } from "@shared/components/EllipsisDropdown";
import {
	IconPlus,
	IconChevronRight,
	IconGripVertical,
	IconEyeOff,
	IconTag,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { cn } from "@ui/lib";
import { useState } from "react";
import { toast } from "sonner";
import {
	getFieldIcon,
} from "./constants/available-attributes";
import { ObjectType } from "@repo/database";

interface TableViewSettingsProps {
	view: {
		id: string;
		name: string;
		viewType?: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
	};
	objectType: ObjectType;
	primaryColumn?: string;
	onUpdateView?: (updatedView: any) => void;
	setAddColumnOpen: (open: boolean) => void;
}

// Sortable column item component
function SortableColumnItem({
	column,
	index,
	onRemoveColumn,
	objectType,
	isPrimary = false,
}: {
	column: any;
	index: number;
	onRemoveColumn: (field: string) => void;
	objectType: ObjectType;
	isPrimary?: boolean;
}) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: column.field,
		data: {
			type: "column",
			column,
			index,
		},
		disabled: isPrimary, // Disable dragging for primary column
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={cn(
				"group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border",
				isDragging && "opacity-50",
				isPrimary && "opacity-75", // Make primary column visually distinct
			)}
		>
			<div
				{...attributes}
				{...listeners}
				className={cn(
					"cursor-grab active:cursor-grabbing",
					isPrimary && "cursor-not-allowed opacity-50"
				)}
			>
				<IconGripVertical className="h-3 w-3 text-muted-foreground" />
			</div>
			<div className="flex items-center gap-2 flex-1 min-w-0">
				{/* Column icon based on field type */}
				{(() => {
					const FieldIcon = getFieldIcon(column.field, objectType);
					return (
						<FieldIcon className="h-4 w-4 text-muted-foreground" />
					);
				})()}

				<span className="text-sm truncate cursor-default">
					{column.headerName}
					{isPrimary && (
						<span className="text-xs text-muted-foreground ml-1">
							(Primary)
						</span>
					)}
				</span>
			</div>
			{!isPrimary && (
				<EllipsisDropdown className="p-1">
					<EllipsisDropdown.Item className="gap-1">
						<IconTag className="h-3 w-3" />
						Change attribute title
					</EllipsisDropdown.Item>
					<EllipsisDropdown.Item
						className="gap-1"
						onClick={() => onRemoveColumn(column.field)}
					>
						<IconEyeOff className="h-3 w-3" />
						Hide column
					</EllipsisDropdown.Item>
				</EllipsisDropdown>
			)}
		</div>
	);
}

export const TableViewSettings = ({
	view,
	objectType,
	primaryColumn = "name",
	onUpdateView,
	setAddColumnOpen,
}: TableViewSettingsProps) => {
	const [activeId, setActiveId] = useState<string | null>(null);

	const queryClient = useQueryClient();
	const { activeOrganization } = useActiveOrganization();

	// DnD sensors
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
	);

	// Mutation to update view columns
	const updateViewMutation = useMutation({
		mutationFn: async (updates: {
			columnDefs?: any[];
		}) => {
			if (!view?.id) return;

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify(updates),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update view");
			}

			return response.json();
		},
		onMutate: async (updates) => {
			if (!view?.id || !activeOrganization?.id) return;

			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["objectView", view.id],
			});
			await queryClient.cancelQueries({
				queryKey: ["objectViews", activeOrganization.id, objectType],
			});

			// Snapshot the previous values
			const previousView = queryClient.getQueryData([
				"objectView",
				view.id,
			]);
			const previousViews = queryClient.getQueryData([
				"objectViews",
				activeOrganization.id,
				objectType,
			]);

			// Optimistically update the single view
			queryClient.setQueryData(["objectView", view.id], (old: any) => {
				if (!old) return old;
				return {
					...old,
					...updates,
					updatedAt: new Date().toISOString(),
				};
			});

			// Optimistically update the views list
			queryClient.setQueryData(
				["objectViews", activeOrganization.id, objectType],
				(old: any[]) => {
					if (!old || !Array.isArray(old)) return old;
					return old.map((v: any) =>
						v.id === view.id
							? {
									...v,
									...(updates.columnDefs && {
										columnDefs: [...updates.columnDefs],
									}),
									updatedAt: new Date().toISOString(),
									_cacheKey: Date.now(),
								}
							: v,
					);
				},
			);

			return { previousView, previousViews };
		},
		onSuccess: (data, variables, context) => {
			if (onUpdateView && data) {
				onUpdateView(data);
			}

			// Invalidate queries to ensure fresh data
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: [
						"objectViews",
						activeOrganization.id,
						objectType,
					],
				});
				queryClient.invalidateQueries({
					queryKey: ["objectViews", activeOrganization.id],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}
		},
		onError: (error, variables, context) => {
			toast.error(`Failed to update view: ${error.message}`);

			// Rollback optimistic updates
			if (context?.previousView && view?.id) {
				queryClient.setQueryData(
					["objectView", view.id],
					context.previousView,
				);
			}
			if (context?.previousViews && activeOrganization?.id) {
				queryClient.setQueryData(
					["objectViews", activeOrganization.id, objectType],
					context.previousViews,
				);
			}
		},
	});

	const handleRemoveColumn = (field: string) => {
		if (!view?.columnDefs) return;

		if (field === primaryColumn) {
			console.warn("Cannot remove primary column:", primaryColumn);
			return;
		}

		const updatedColumnDefs = view.columnDefs.filter(
			(col) => col.field !== field,
		);
		updateViewMutation.mutate({ columnDefs: updatedColumnDefs });
		toast.success("Column hidden");
	};

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		setActiveId(null);

		if (!over) return;

		// Handle column reordering
		if (!view?.columnDefs) return;

		// Separate primary and non-primary columns
		const reorderableColumns = view.columnDefs.filter(
			(col) => col.field !== primaryColumn,
		);
		const primaryColumns = view.columnDefs.filter(
			(col) => col.field === primaryColumn,
		);

		const oldIndex = reorderableColumns.findIndex(
			(col) => col.field === active.id,
		);
		const newIndex = reorderableColumns.findIndex(
			(col) => col.field === over.id,
		);

		if (oldIndex !== newIndex) {
			// Reorder the columns
			const reorderedColumns = arrayMove(
				reorderableColumns,
				oldIndex,
				newIndex,
			);
			// Keep primary column at the beginning
			const finalColumns = [...primaryColumns, ...reorderedColumns];
			updateViewMutation.mutate({ columnDefs: finalColumns });
		}
	};

	// Get reorderable columns (all columns except primary)
	const reorderableColumns = view?.columnDefs?.filter(
		(col) => col.field !== primaryColumn,
	) || [];
	
	// Get primary column
	const primaryColumnDef = view?.columnDefs?.find(
		(col) => col.field === primaryColumn,
	);

	return (
		<div className="p-1">
			<div className="text-xs text-muted-foreground my-2 mx-2">
				View settings
			</div>

			{/* Current columns */}
			<div className="space-y-1">
				{/* Show primary column first (not draggable) */}
				{primaryColumnDef && (
					<SortableColumnItem
						key={primaryColumnDef.field}
						column={primaryColumnDef}
						index={0}
						onRemoveColumn={handleRemoveColumn}
						objectType={objectType}
						isPrimary={true}
					/>
				)}

				{/* Show reorderable columns with drag and drop */}
				{reorderableColumns.length > 0 && (
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragStart={handleDragStart}
						onDragEnd={handleDragEnd}
					>
						<SortableContext
							items={reorderableColumns.map((col) => col.field)}
							strategy={verticalListSortingStrategy}
						>
							{reorderableColumns.map((column, index) => (
								<SortableColumnItem
									key={column.field}
									column={column}
									index={index + 1} // +1 because primary column is at index 0
									onRemoveColumn={handleRemoveColumn}
									objectType={objectType}
									isPrimary={false}
								/>
							))}
						</SortableContext>
					</DndContext>
				)}
			</div>

			{/* Add column option */}
			<div
				className={cn(
					"group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer mt-2",
				)}
				onClick={() => setAddColumnOpen(true)}
				data-testid="add-column-button"
			>
				<div>
					<IconPlus className="h-4 w-4 text-muted-foreground" />
				</div>
				<div className="flex items-center gap-2 flex-1 min-w-0">
					<span className="text-sm truncate">
						Add column
					</span>
				</div>
				<div className="flex items-center gap-2 mr-1">
					<IconChevronRight className="h-4 w-4 text-muted-foreground" />
				</div>
			</div>
		</div>
	);
}; 