"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { type ObjectView, useObjectViews } from "@app/object-views/lib/api";
import { ObjectType, ObjectViewType, singularToPluralMap } from "@repo/database";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconLayoutKanban, IconMap, IconTable } from "@tabler/icons-react";
import { Card, CardContent } from "@ui/components/card";
import { cn } from "@ui/lib";
import { useRouter } from "next/navigation";
import React, { useMemo, useState } from "react";
import { CreateViewModal } from "./CreateViewModal";
import { KanbanView } from "./views/KanbanView";
import { MapView } from "./views/MapView";
import { TableView } from "./views/TableView";

interface ObjectsViewProps {
	objectType: ObjectType;
	organizationSlug: string;
	viewId?: string;
	showCreateViewOnly?: boolean;
}

export function ObjectsView({
	objectType,
	organizationSlug,
	viewId,
	showCreateViewOnly,
}: ObjectsViewProps) {
	const [selectedViewId, setSelectedViewId] = useState<string | undefined>(
		viewId,
	);
	const [currentView, setCurrentView] = useState<ObjectView | undefined>();
	const [isCreatingView, setIsCreatingView] = useState(false);
	const [creatingViewType, setCreatingViewType] =
		useState<ObjectViewType | null>(null);
	const router = useRouter();

	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;

	// Get plural form for URL construction
	const pluralObjectType = singularToPluralMap[objectType] || objectType;

	const {
		data: views = [],
		isLoading: isLoadingViews,
		error,
	} = useObjectViews(organizationId, objectType, !showCreateViewOnly);

	const foundView = useMemo(() => {
		return (
			views.find((v: ObjectView) => v.id === selectedViewId) ||
			views.find((v: ObjectView) => v.isDefault) ||
			views[0]
		);
	}, [views, selectedViewId]);

	React.useEffect(() => {
		if (foundView) {
			// Ensure the view always has a primary column
			const ensureHasPrimaryColumn = (view: ObjectView): ObjectView => {
				const hasPrimaryColumn = view.columnDefs?.some(col => col.field === "name");
				
				console.debug("[ObjectsView] Checking primary column", {
					viewId: view.id,
					hasPrimaryColumn,
					columnDefsLength: view.columnDefs?.length || 0,
					columnDefs: view.columnDefs
				});
				
				if (!hasPrimaryColumn || !view.columnDefs || view.columnDefs.length === 0) {
					console.debug("[ObjectsView] Adding primary column to view", view.id);
					// Add the primary column if it's missing
					const getPrimaryColumnDef = (objectType: string) => {
						const headerNames = {
							contact: "Name",
							company: "Company Name", 
							property: "Property Name",
							custom_object: "Name"
						};
						
						return {
							field: "name",
							headerName: headerNames[objectType as keyof typeof headerNames] || "Name",
							width: 200,
							type: "text",
							visible: true
						};
					};

					const primaryColumn = getPrimaryColumnDef(objectType);
					const updatedView = {
						...view,
						columnDefs: [primaryColumn, ...(view.columnDefs || [])]
					};
					
					console.debug("[ObjectsView] Updated view with primary column", {
						viewId: updatedView.id,
						columnDefs: updatedView.columnDefs
					});
					
					return updatedView;
				}
				
				return view;
			};

			const viewWithPrimaryColumn = ensureHasPrimaryColumn(foundView);
			setCurrentView(viewWithPrimaryColumn);
		}
	}, [foundView, objectType]);

	const handleCreateView = (viewType: ObjectViewType) => {
		setCreatingViewType(viewType);
		setIsCreatingView(true);
	};

	const handleViewCreated = (newViewId: string) => {
		router.push(`/app/${organizationSlug}/${pluralObjectType}/view/${newViewId}`);
	};

	const handleViewChange = (newViewId: string) => {
		router.push(`/app/${organizationSlug}/${pluralObjectType}/view/${newViewId}`);
	};

	const handleViewRenamed = (updatedView: any) => {
		if (currentView && currentView.id === updatedView.id) {
			setCurrentView(updatedView);
		}
	};

	if (isLoadingViews && !showCreateViewOnly) {
		return (
			<div className="space-y-4">
				<div className="h-8 w-48 bg-muted animate-pulse rounded" />
				<div className="h-64 bg-muted animate-pulse rounded" />
			</div>
		);
	}

	if (views.length === 0 || showCreateViewOnly) {
		return (
			<div className="flex-1 flex items-center justify-center min-h-[600px]">
				<div className="max-w-2xl mx-auto text-center space-y-8">
					<div className="">
						<h2 className="text-2xl font-semibold">
							Start with a view
						</h2>
						<p className="text-muted-foreground text-lg">
							Organize and visualize your data to highlight what's
							important
						</p>
					</div>

					<div
						className={cn(
							"grid gap-6 max-w-4xl mx-auto",
							objectType === "property"
								? "grid-cols-3"
								: "grid-cols-2",
						)}
					>
						<Card
							className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/20"
							onClick={() => handleCreateView("table")}
						>
							<CardContent className="p-8 text-center space-y-4">
								<div className="mx-auto w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
									<IconTable className="h-8 w-8 text-muted-foreground" />
								</div>
								<div className="space-y-2">
									<h3 className="text-xl font-semibold">
										Table
									</h3>
									<p className="text-sm text-muted-foreground">
										Organize your {objectType} in a table
										view and create a unique filter set to
										show only relevant {objectType}.
									</p>
								</div>
							</CardContent>
						</Card>

						<Card
							className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/20"
							onClick={() => handleCreateView("kanban")}
						>
							<CardContent className="p-8 text-center space-y-4">
								<div className="mx-auto w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
									<IconLayoutKanban className="h-8 w-8 text-muted-foreground" />
								</div>
								<div className="space-y-2">
									<h3 className="text-xl font-semibold">
										Kanban
									</h3>
									<p className="text-sm text-muted-foreground">
										Visualize your {objectType} workflow as
										cards in columns to track progress and
										status.
									</p>
								</div>
							</CardContent>
						</Card>

						{objectType === "property" && (
							<Card
								className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/20"
								onClick={() => handleCreateView("map")}
							>
								<CardContent className="p-8 text-center space-y-4">
									<div className="mx-auto w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
										<IconMap className="h-8 w-8 text-muted-foreground" />
									</div>
									<div className="space-y-2">
										<h3 className="text-xl font-semibold">
											Map
										</h3>
										<p className="text-sm text-muted-foreground">
											Visualize your properties on an
											interactive map with location-based
											filtering and clustering.
										</p>
									</div>
								</CardContent>
							</Card>
						)}
					</div>
					{organizationId && (
						<CreateViewModal
							open={isCreatingView}
							onOpenChange={(open) => {
								setIsCreatingView(open);
								if (!open) setCreatingViewType(null);
							}}
							objectType={objectType as ObjectType}
							organizationId={organizationId}
							initialViewType={creatingViewType}
							onViewCreated={(viewId) => {
								handleViewCreated(viewId);
								setIsCreatingView(false);
								setCreatingViewType(null);
							}}
						/>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full">
			{currentView && (
				<div className="flex-1 overflow-hidden">
					{(() => {
						console.debug("[ObjectsView] Rendering view", {
							id: currentView.id,
							name: currentView.name,
							type: currentView.viewType,
							columnDefs: currentView.columnDefs,
						});

						if (!currentView.columnDefs || currentView.columnDefs.length === 0) {
							return (
								<div className="flex items-center justify-center h-64">
									<p className="text-red-500">No columns defined for this view. Please configure columns in the view settings.</p>
								</div>
							);
						}

						if (currentView.viewType === "kanban") {
							return (
								<KanbanView
									objectType={objectType as ObjectType}
									organizationId={organizationId ?? ""}
									view={currentView as ObjectView}
									user={user ? { name: user.name, avatarUrl: user.image } : undefined}
									views={views}
									onViewChange={handleViewChange}
									onRenameView={() => {}}
									onDuplicateView={() => {}}
									onDeleteView={() => {}}
									onToggleViewFavorite={() => {}}
									onToggleViewPublic={() => {}}
									onSetViewDefault={() => {}}
									onViewRenamed={handleViewRenamed}
								/>
							);
						} else if (currentView.viewType === "map") {
							return (
								<MapView
									objectType={objectType as ObjectType}
									organizationId={organizationId ?? ""}
									view={currentView as ObjectView}
									user={user ? { name: user.name, avatarUrl: user.image } : undefined}
									views={views}
									onViewChange={handleViewChange}
									onRenameView={() => {}}
									onDuplicateView={() => {}}
									onDeleteView={() => {}}
									onToggleViewFavorite={() => {}}
									onToggleViewPublic={() => {}}
									onSetViewDefault={() => {}}
									onViewRenamed={handleViewRenamed}
								/>
							);
						} else {
							return (
								<TableView
									objectType={objectType as ObjectType}
									organizationId={organizationId ?? ""}
									view={currentView as ObjectView}
									user={user ? { name: user.name, avatarUrl: user.image } : undefined}
									views={views}
									onViewChange={handleViewChange}
									onRenameView={() => {}}
									onDuplicateView={() => {}}
									onDeleteView={() => {}}
									onToggleViewFavorite={() => {}}
									onToggleViewPublic={() => {}}
									onSetViewDefault={() => {}}
									onViewRenamed={handleViewRenamed}
								/>
							);
						}
					})()}
				</div>
			)}
			{organizationId && (
				<CreateViewModal
					open={isCreatingView}
					onOpenChange={(open) => {
						setIsCreatingView(open);
						if (!open) setCreatingViewType(null);
					}}
					objectType={objectType as ObjectType}
					organizationId={organizationId}
					initialViewType={creatingViewType}
					onViewCreated={(viewId) => {
						handleViewCreated(viewId);
						setIsCreatingView(false);
						setCreatingViewType(null);
					}}
				/>
			)}
		</div>
	);
}
