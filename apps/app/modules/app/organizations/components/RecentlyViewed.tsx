"use client";

import { ToggleStar } from "@app/favorites/components/ToggleStar";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { ObjectType } from "@repo/database";
import { UserAvatar } from "@shared/components/UserAvatar";
import { ScrollArea } from "@ui/components/scroll-area";
import { cn } from "@ui/lib";
import { formatRelative } from "date-fns";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { type Key, useMemo } from "react";

const RecentlyViewed = ({ recordType }: any) => {
	const path = usePathname();
	const { activeOrganization } = useActiveOrganization();

	const recentlyViewedContacts: any[] = [];
	const recentlyViewedCompanies: any[] = [];
	const recentlyViewedProperties: any[] = [];

	const renderRecords = (
		recordType: string,
		recentlyViewedRecords: any,
		message: string,
	) => {
		return recentlyViewedRecords && recentlyViewedRecords.length > 0 ? (
			recentlyViewedRecords.map(
				(
					record: {
						id: string;
						profileImg: string;
						name: string;
						firstName: string;
						lastName: string;
						image: string;
						phone: any;
						domains: any;
						email: any;
						industry: string;
						address: { street: string };
						lastViewedTime: number;
						recordType: ObjectType
					},
					index: Key | null | undefined,
				) => {
					const recordId: string = record.id;
					const isFavoriteRecord = false;

					return (
						<div key={index}>
							<div
								className={cn(
									"px-3 flex h-8 items-center justify-between overflow-hidden rounded-lg py-1.5 text-sm font-medium hover:bg-accent/50 hover:text-accent-foreground max-h-[450px]",
									path ===
										`/app/${activeOrganization?.slug}/${record.recordType}/${record.id}`
										? "bg-accent/50"
										: "transparent",
								)}
							>
								<div
									className={cn("flex flex-row items-center")}
								>
									<Link
										href={`/app/${activeOrganization?.slug}/${record.recordType}/${record.id}`}
										className={
											"flex flex-row items-center space-x-2 w-full mr-1"
										}
									>
										<UserAvatar
											name={record.name}
											avatarUrl={record.image}
											className="h-6 w-6"
										/>

										<div
											className={
												"flex justify-between items-center"
											}
										>
											<span className="truncate">
												{record.name}
											</span>
										</div>

										<p className="text-sm text-muted-foreground w-full">
											{recordType === "contact"
												? record?.phone &&
													record?.phone.length > 0
													? record?.phone[0]?.number
													: ""
												: recordType === "contact"
													? !record?.phone &&
														record.email
														? record.email[0]
																?.address
														: ""
													: ""}
										</p>
									</Link>

									<ToggleStar
										id={recordId}
										isFavorite={isFavoriteRecord}
										objectType={record.recordType}
									/>
								</div>

								<div className="ml-auto text-sm">
									{record?.lastViewedTime && (
										<p className="text-muted-foreground">
											{formatRelative(
												new Date(record.lastViewedTime),
												new Date(),
											)}
										</p>
									)}
								</div>
							</div>
						</div>
					);
				},
			)
		) : (
			<div
				className={
					"h-full w-full m-auto flex justify-center items-center text-sm text-muted-foreground"
				}
			>
				{message}
			</div>
		);
	};

	return (
		<ScrollArea className="flex flex-col">
			{recordType === "contact" &&
				renderRecords(
					"contact",
					recentlyViewedContacts,
					"No recently viewed contacts",
				)}
			{recordType === "company" &&
				renderRecords(
					"company",
					recentlyViewedCompanies,
					"No recently viewed companies",
				)}
			{recordType === "property" &&
				renderRecords(
					"property",
					recentlyViewedProperties,
					"No recently viewed properties",
				)}
		</ScrollArea>
	);
};

export default RecentlyViewed;
