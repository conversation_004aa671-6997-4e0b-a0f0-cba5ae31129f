"use client";

import { IconStar, IconStarFilled, IconX, IconMapPin, IconChevronLeft, IconChevronRight, IconCopy } from "@tabler/icons-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { AutoSizingInput } from "@ui/components/input";
import { useToggleFavorite } from "@app/favorites/lib/api";
import { useUpdateProperty } from "@app/properties/lib/api";
import { useStreetView } from "./objects/views/hooks/useStreetView";
import { ToggleStar } from "@app/favorites/components/ToggleStar";
import { fetchFavorites } from "@app/favorites/lib/api";
import { useQuery } from "@tanstack/react-query";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import PropertyDetails from "@app/properties/components/PropertyDetails";

interface PropertySidebarProps {
	selectedProperty: any;
	onClose: () => void;
	isTableOpen: boolean;
	onPinToggle?: (property: any, isPinned: boolean) => void;
}

interface PropertyInfoSectionProps {
	title: string;
	data: any;
	renderCustomItem?: (
		key: string,
		value: any,
		index?: number,
	) => React.ReactNode;
}

const PropertyInfoSection = ({
	title,
	data,
	renderCustomItem,
}: PropertyInfoSectionProps) => {
	const copyToClipboard = (data: any, label: string) => {
		const dataString = Object.entries(data)
			.map(([key, value]) => `${key}: ${value}`)
			.join("\n");

		navigator.clipboard.writeText(dataString).then(
			() => {
				toast.success(`${label} copied to clipboard`);
			},
			(err) => {
				console.error("Could not copy text: ", err);
			},
		);
	};

	const renderDefaultItem = (key: string, value: any) => {
		if (value === null || value === undefined) return null;

		return (
			<div key={key} className="px-2">
				<div className="flex flex-row items-center">
					<Tooltip>
						<TooltipTrigger className="w-[30%] text-left">
							<div className="text-[10px] uppercase font-normal text-gray-500 truncate">
								{key.replace(/([A-Z])/g, " $1").trim()}
							</div>
						</TooltipTrigger>
						<TooltipContent side="right">
							{key
								.replace(/([A-Z])/g, " $1")
								.trim()
								.replace(/\b\w/g, (c) => c.toUpperCase())}
						</TooltipContent>
					</Tooltip>
					<div className="w-[70%]">
						<Input
							type="text"
							value={String(value)}
							disabled={true}
							className="cursor-default bg-transparent border-none text-xs h-6"
						/>
					</div>
				</div>
			</div>
		);
	};

	return (
		<div style={{ overflowY: "auto", height: "320px" }}>
			<div className="flex items-center justify-between border-b border-border bg-background z-50 sticky top-0 p-2">
				<Label className="uppercase text-[10px]">{title}</Label>
				<Button
					variant="ghost"
					size="sm"
					onClick={() => copyToClipboard(data, title)}
					className="h-6 w-6 p-0"
				>
					📋
				</Button>
			</div>
			{data &&
				Object.entries(data)
					.map(([key, value], index) =>
						renderCustomItem
							? renderCustomItem(key, value, index)
							: renderDefaultItem(key, value),
					)
					.filter(Boolean)}
		</div>
	);
};

export default function PropertySidebar({
	selectedProperty,
	onClose,
	isTableOpen,
	onPinToggle,
}: PropertySidebarProps) {
	const [showLeftArrow, setShowLeftArrow] = useState(false);
	const [showRightArrow, setShowRightArrow] = useState(false);
	const [isEditing, setIsEditing] = useState(false);
	const [propertyName, setPropertyName] = useState(selectedProperty?.name || "");
	const [isPinned, setIsPinned] = useState(false);
	const [editingField, setEditingField] = useState<string | null>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);
	
	const { streetViewRef, isLoading, hasError, isAvailable, errorMessage } =
		useStreetView({ selectedProperty });
	
	const toggleFavorite = useToggleFavorite(selectedProperty?.organizationId);
	const updatePropertyMutation = useUpdateProperty();

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", selectedProperty.organizationId],
		queryFn: () =>
			selectedProperty.organizationId
				? fetchFavorites(selectedProperty.organizationId)
				: Promise.resolve([]),
		enabled: !!selectedProperty.organizationId,
	});

	const isFavorite = favorites.some(
		(fav) => fav.objectType === "property" && fav.objectId === selectedProperty.id,
	);

	const isFromDatabase = selectedProperty?.source === "database";
	const tabs = isFromDatabase
		? ["details", "linked records", "sale history", "mortgage history", "tax info"]
		: [
				"details",
				"owner info",
				"sale history",
				"mortgage history",
				"tax info",
				"raw data",
		];

	const checkScrollPosition = () => {
		if (scrollContainerRef.current) {
			const { scrollLeft, scrollWidth, clientWidth } =
				scrollContainerRef.current;
			setShowLeftArrow(scrollLeft > 0);
			setShowRightArrow(scrollLeft < scrollWidth - clientWidth);
		}
	};

	const scroll = (direction: "left" | "right") => {
		if (scrollContainerRef.current) {
			const scrollAmount = scrollContainerRef.current.clientWidth / 2;
			scrollContainerRef.current.scrollBy({
				left: direction === "left" ? -scrollAmount : scrollAmount,
				behavior: "smooth",
			});
		}
	};

	useEffect(() => {
		checkScrollPosition();
		window.addEventListener("resize", checkScrollPosition);
		return () => window.removeEventListener("resize", checkScrollPosition);
	}, [tabs]);

	useEffect(() => {
		setPropertyName(selectedProperty?.name || "");
	}, [selectedProperty?.name]);

	useEffect(() => {
		if (selectedProperty?.isPinned !== undefined) {
			setIsPinned(selectedProperty.isPinned);
		}
	}, [selectedProperty]);

	const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setPropertyName(e.target.value);
	};

	const handleNameBlur = async () => {
		if (propertyName !== selectedProperty?.name) {
			try {
				await updatePropertyMutation.mutateAsync({
					id: selectedProperty.id,
					name: propertyName,
					organizationId: selectedProperty.organizationId,
				});
				toast.success("Property name updated");
			} catch (error) {
				toast.error("Failed to update property name");
				setPropertyName(selectedProperty?.name || "");
			}
		}
		setIsEditing(false);
	};

	const handleNameKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			e.currentTarget.blur();
		} else if (e.key === "Escape") {
			setPropertyName(selectedProperty?.name || "");
			setIsEditing(false);
		}
	};

	const renderPropertyDetails = () => {
		return <PropertyDetails selectedProperty={selectedProperty} />;
	};

	const renderOwnerInfo = () => {
		const ownerInfo = selectedProperty?.ownerInfo;
		if (!ownerInfo)
			return <div className="p-4">No owner information available</div>;

		return (
			<PropertyInfoSection title="Owner Information" data={ownerInfo} />
		);
	};

	const renderSaleHistory = () => {
		const saleHistory = selectedProperty?.saleHistory;
		if (!saleHistory)
			return <div className="p-4">No sale history available</div>;

		const renderSaleItem = (key: string, value: any, index?: number) => {
			if (value === null || value === undefined) return null;

			if (typeof value === "object" && value !== null) {
				return (
					<div
						key={`${key}-${index ?? 0}`}
						className="border-b border-border p-2"
					>
						<Label className="text-sm font-medium">
							Sale {(index ?? 0) + 1}
						</Label>
						{Object.entries(value).map(([subKey, subValue]) => (
							<div
								key={subKey}
								className="flex justify-between py-1"
							>
								<span className="text-xs text-muted-foreground">
									{subKey}:
								</span>
								<span className="text-xs">
									{String(subValue)}
								</span>
							</div>
						))}
					</div>
				);
			}
			return null;
		};

		return (
			<PropertyInfoSection
				title="Sale History"
				data={saleHistory}
				renderCustomItem={renderSaleItem}
			/>
		);
	};

	const renderMortgageHistory = () => {
		const mortgageHistory = selectedProperty?.mortgageHistory;
		if (!mortgageHistory)
			return <div className="p-4">No mortgage history available</div>;

		return (
			<PropertyInfoSection
				title="Mortgage History"
				data={mortgageHistory}
			/>
		);
	};

	const renderTaxInfo = () => {
		const taxInfo = selectedProperty?.taxInfo;
		if (!taxInfo)
			return <div className="p-4">No tax information available</div>;

		return <PropertyInfoSection title="Tax Information" data={taxInfo} />;
	};

	const renderRawData = () => {
		const copyToClipboard = (data: any) => {
			const dataString = JSON.stringify(data, null, 2);
			navigator.clipboard.writeText(dataString).then(() => {
				toast.success("Raw data copied to clipboard");
			});
		};

		return (
			<div className="p-4">
				<div className="flex items-center justify-between mb-4">
					<Label className="text-base font-semibold">
						Raw Property Data
					</Label>
					<Button
						variant="ghost"
						size="sm"
						onClick={() => copyToClipboard(selectedProperty)}
						className="h-6 w-6 p-0"
					>
						📋
					</Button>
				</div>
				<div className="bg-muted p-4 rounded-md text-xs font-mono max-h-64 overflow-auto">
					<pre>{JSON.stringify(selectedProperty, null, 2)}</pre>
				</div>
			</div>
		);
	};

	const handlePinToggle = () => {
		const newPinnedState = !isPinned;
		setIsPinned(newPinnedState);
		onPinToggle?.(selectedProperty, newPinnedState);
	};

	return (
		<div className={cn(
			"absolute left-1 top-1 bottom-1 w-full max-w-[400px] flex flex-col rounded-xl overflow-hidden bg-sidebar border border-border",
			isTableOpen && "bottom-[calc(25vh+4px)]"
		)}>
			<div className="flex items-center justify-between p-4 border-b">
				<div className="flex items-center gap-2 flex-1 min-w-0">
					{onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0"
              >
                <IconX className="h-4 w-4" />
              </Button>
					)}
					<div className="flex-shrink-0">
						<PropertyAvatar name={selectedProperty?.name} className="h-6 w-6" />
					</div>
					<div className="flex-1 min-w-0" onClick={() => setIsEditing(true)}>
						{isEditing ? (
							<AutoSizingInput
								value={propertyName}
								onChange={handleNameChange}
								onBlur={handleNameBlur}
								onKeyDown={handleNameKeyDown}
								className="font-semibold text-lg"
								autoFocus
							/>
						) : (
							<h3 className="font-semibold text-lg truncate cursor-pointer hover:text-primary transition-colors">
								{selectedProperty?.name}
							</h3>
						)}
					</div>
				</div>
				<div className="flex items-center gap-2">
					<ToggleStar
						id={selectedProperty?.id}
						isFavorite={isFavorite}
						objectType="property"
					/>
				</div>
			</div>

			{/* Street View */}
			<div className="w-full h-[250px] min-h-[250px] border-b border-border relative">
				<div ref={streetViewRef} className="w-full h-full" />

				{/* Loading state */}
				{isLoading && (
					<div className="absolute inset-0 bg-muted/80 flex items-center justify-center">
						<div className="flex items-center gap-2 text-sm text-muted-foreground">
							<div className="animate-spin h-4 w-4 border-2 border-muted-foreground border-t-transparent rounded-full" />
							Loading Street View...
						</div>
					</div>
				)}

				{/* Error state */}
				{hasError && !isLoading && (
					<div className="absolute inset-0 bg-muted flex items-center justify-center">
						<div className="text-center text-sm text-muted-foreground">
							<div className="mb-2">📍</div>
							<div>
								{errorMessage || "Street View not available"}
							</div>
						</div>
					</div>
				)}
			</div>

			<div className="p-2 border-b border-t border-border bg-muted/30 w-full">
				<div className="flex items-center justify-between gap-1">
					<Button variant="primary" className="text-xs truncate h-auto px-1">
						Skiptrace
					</Button>

					{(() => {
						const propertyType = isFromDatabase 
							? selectedProperty?.propertyType 
							: selectedProperty?.propertyInfo?.propertyType;
						
						if (propertyType && propertyType !== "none") {
							return (
								<Badge variant="views" className="text-xs">
									{propertyType === "OTHER" || propertyType === "other"
										? "Commercial"
										: propertyType.charAt(0).toUpperCase() + propertyType.slice(1).toLowerCase()}
								</Badge>
							);
						}
						return null;
					})()}
				</div>
			</div>

			<Tabs
				defaultValue="details"
				className="flex-grow flex flex-col overflow-hidden"
			>
				<TabsList className="flex flex-row justify-start w-full rounded-none bg-transparent p-0 py-2 border-b border-border relative h-[30px]">
					{showLeftArrow && (
						<div className="absolute left-0 z-10 p-0.5">
							<Button
								variant="primary"
								size="sm"
								onClick={() => scroll("left")}
								className="h-6 w-6 p-0"
							>
								<IconChevronLeft className="h-4 w-4" />
							</Button>
						</div>
					)}
					<div
						ref={scrollContainerRef}
						className="flex flex-row items-center overflow-x-scroll no-scrollbar"
						onScroll={checkScrollPosition}
					>
						{tabs.map((tab) => (
							<TabsTrigger
								key={tab}
								value={tab}
								className="uppercase text-[10px]"
							>
								{tab}
							</TabsTrigger>
						))}
					</div>
					{showRightArrow && (
						<div className="absolute right-0 z-10 p-0.5">
							<Button
								variant="primary"
								size="sm"
								onClick={() => scroll("right")}
								className="h-6 w-6 p-0"
							>
								<IconChevronRight className="h-4 w-4" />
							</Button>
						</div>
					)}
				</TabsList>

				<div className="flex-1 overflow-hidden">
					<TabsContent
						value="details"
						className="h-full overflow-y-auto mt-0"
					>
						{renderPropertyDetails()}
					</TabsContent>

					{!isFromDatabase && (
						<>
							<TabsContent
								value="owner info"
								className="h-full overflow-y-auto mt-0"
							>
								{renderOwnerInfo()}
							</TabsContent>
							<TabsContent
								value="sale history"
								className="h-full overflow-y-auto mt-0"
							>
								{renderSaleHistory()}
							</TabsContent>
							<TabsContent
								value="mortgage history"
								className="h-full overflow-y-auto mt-0"
							>
								{renderMortgageHistory()}
							</TabsContent>
							<TabsContent
								value="tax info"
								className="h-full overflow-y-auto mt-0"
							>
								{renderTaxInfo()}
							</TabsContent>
							<TabsContent
								value="raw data"
								className="h-full overflow-y-auto mt-0"
							>
								{renderRawData()}
							</TabsContent>
						</>
					)}

					{isFromDatabase && (
						<>
							<TabsContent
								value="linked records"
								className="h-full overflow-y-auto mt-0"
							>
								<div className="p-4">
									Linked records functionality
								</div>
							</TabsContent>
							<TabsContent
								value="sale history"
								className="h-full overflow-y-auto mt-0"
							>
								<div className="p-4">
									Sale history functionality
								</div>
							</TabsContent>
						</>
					)}
				</div>
			</Tabs>
		</div>
	);
}
