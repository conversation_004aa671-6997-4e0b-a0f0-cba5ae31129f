import {
	IconMinus,
	IconTrendingDown,
	IconTrendingUp,
} from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import {
	Card,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { cn } from "@ui/lib";
import React from "react";

interface TasksWidgetProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
}

export function TasksWidget({
	selectedFilter,
	data,
	isLoading,
}: TasksWidgetProps) {
	const tasksNotDone = data?.tasksNotDoneCount ?? 0;
	const tasksCompleted = data?.tasksCompletedCount ?? 0;
	const totalTasks = tasksNotDone + tasksCompleted;
	const completionRate =
		totalTasks > 0 ? Math.round((tasksCompleted / totalTasks) * 100) : 0;

	const getTrendIcon = () => {
		if (completionRate > 70) return <IconTrendingUp className="size-4" />;
		if (completionRate < 30) return <IconTrendingDown className="size-4" />;
		return <IconMinus className="size-4" />;
	};

	const getTrendText = () => {
		if (completionRate > 70) return "Excellent progress";
		if (completionRate > 50) return "Good progress";
		if (completionRate > 25) return "Needs attention";
		return "Requires focus";
	};

	const getBadgeStatus = () => {
		if (completionRate > 70) return "success";
		if (completionRate > 50) return "info";
		if (completionRate > 25) return "warning";
		return "error";
	};

	return (
		<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
			<CardHeader className="pb-2">
				<div className="flex items-center justify-between">
					<CardDescription>Task Progress</CardDescription>
					<Badge status={getBadgeStatus()}>
						{getTrendIcon()}
						{isLoading ? "..." : `${completionRate}%`}
					</Badge>
				</div>
				<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
					{isLoading ? "..." : `${tasksCompleted}/${totalTasks}`}
				</CardTitle>
				<div className="pt-2">
					<div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
						<div
							className={cn(
								"h-2 rounded-full transition-all duration-300",
								completionRate > 75
									? "bg-green-500"
									: completionRate > 50
										? "bg-yellow-500"
										: completionRate > 25
											? "bg-orange-500"
											: "bg-red-500",
							)}
							style={{ width: `${completionRate}%` }}
						/>
					</div>
				</div>
			</CardHeader>
			<CardFooter className="flex-col items-start gap-1.5 text-sm pt-0">
				<div className="line-clamp-1 flex gap-2 font-medium">
					{getTrendText()} {getTrendIcon()}
				</div>
				<div className="text-muted-foreground">
					Task completion for {selectedFilter.toLowerCase()}
				</div>
			</CardFooter>
		</Card>
	);
}
