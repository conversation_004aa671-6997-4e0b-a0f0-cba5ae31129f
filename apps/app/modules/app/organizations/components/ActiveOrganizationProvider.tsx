"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { sessionQuery<PERSON>ey } from "@app/auth/lib/api";
import {
	activeOrganizationQueryKey,
	useActiveOrganizationQuery,
} from "@app/organizations/lib/api";
import { purchasesQuery<PERSON>ey } from "@app/payments/lib/api";
import { authClient } from "@repo/auth/client";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { config } from "@repo/config";
import { useRouter } from "@shared/hooks/router";
import { apiClient } from "@shared/lib/api-client";
import { useQueryClient } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import nProgress from "nprogress";
import { type ReactNode, useEffect, useState } from "react";
import { ActiveOrganizationContext } from "../lib/active-organization-context";

export function ActiveOrganizationProvider({
	children,
}: {
	children: ReactNode;
}) {
	const router = useRouter();
	const queryClient = useQueryClient();
	const { session, user } = useSession();
	const params = useParams();

	const activeOrganizationSlug = params.organizationSlug as string;

	const { data: activeOrganization } = useActiveOrganizationQuery(
		activeOrganizationSlug,
		{
			enabled: !!activeOrganizationSlug,
		},
	);

	const refetchActiveOrganization = async () => {
		await queryClient.refetchQueries({
			queryKey: activeOrganizationQueryKey(activeOrganizationSlug),
		});
	};

	const setActiveOrganization = async (
		organizationIdOrSlug: string | null,
	) => {
		nProgress.start();

		// Determine if the parameter is an ID (ObjectId format) or slug
		const isObjectId =
			organizationIdOrSlug &&
			/^[0-9a-fA-F]{24}$/.test(organizationIdOrSlug);

		const { data: newActiveOrganization } =
			await authClient.organization.setActive(
				organizationIdOrSlug
					? isObjectId
						? { organizationId: organizationIdOrSlug }
						: { organizationSlug: organizationIdOrSlug }
					: {
							organizationId: null,
						},
			);

		if (!newActiveOrganization) {
			nProgress.done();
			return;
		}

		await queryClient.setQueryData(
			activeOrganizationQueryKey(newActiveOrganization.slug),
			newActiveOrganization,
		);

		if (config.organizations.enableBilling) {
			await queryClient.prefetchQuery({
				queryKey: purchasesQueryKey(newActiveOrganization.id),
				queryFn: async () => {
					const response = await apiClient.payments.purchases.$get({
						query: {
							organizationId: newActiveOrganization.id,
						},
					});

					if (!response.ok) {
						throw new Error("Failed to fetch purchases");
					}

					return response.json();
				},
			});
		}

		await queryClient.setQueryData(sessionQueryKey, (data: any) => {
			return {
				...data,
				session: {
					...data?.session,
					activeOrganizationId: newActiveOrganization.id,
				},
			};
		});

		router.push(`/app/${newActiveOrganization.slug}`);
	};

	const [loaded, setLoaded] = useState(false);
	const [isSettingActive, setIsSettingActive] = useState(false);

	// Auto-set organization as active when navigating to organization pages
	useEffect(() => {
		if (
			activeOrganization &&
			session?.activeOrganizationId !== activeOrganization.id &&
			!isSettingActive
		) {
			setIsSettingActive(true);
			// Set the organization as active without redirecting
			authClient.organization
				.setActive({
					organizationId: activeOrganization.id,
				})
				.then(({ data: newActiveOrganization }) => {
					if (newActiveOrganization) {
						// Update the session query cache
						queryClient.setQueryData(
							sessionQueryKey,
							(data: any) => {
								return {
									...data,
									session: {
										...data?.session,
										activeOrganizationId:
											newActiveOrganization.id,
									},
								};
							},
						);
					}
				})
				.catch((error) => {
					console.error(
						"Failed to auto-set organization as active:",
						error,
					);
				})
				.finally(() => {
					setIsSettingActive(false);
				});
		}
	}, [activeOrganization, session?.activeOrganizationId, queryClient, isSettingActive]);

	// Set loaded to true when we have both activeOrganization and matching session activeOrganizationId
	useEffect(() => {
		if (
			activeOrganization &&
			session?.activeOrganizationId === activeOrganization.id &&
			!isSettingActive
		) {
			setLoaded(true);
		} else if (!activeOrganization && session && !isSettingActive) {
			// If there's no active organization but we have a session, we're loaded
			setLoaded(true);
		}
	}, [activeOrganization, session?.activeOrganizationId, isSettingActive]);

	const activeOrganizationUserRole = activeOrganization?.members.find(
		(member) => member.userId === session?.userId,
	)?.role;

	return (
		<ActiveOrganizationContext.Provider
			value={{
				loaded,
				activeOrganization: activeOrganization ?? null,
				activeOrganizationUserRole: activeOrganizationUserRole ?? null,
				isOrganizationAdmin:
					!!activeOrganization &&
					!!user &&
					isOrganizationAdmin(activeOrganization, user),
				setActiveOrganization,
				refetchActiveOrganization,
			}}
		>
			{children}
		</ActiveOrganizationContext.Provider>
	);
}
