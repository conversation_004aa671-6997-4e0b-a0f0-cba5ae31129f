"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	rectSortingStrategy,
	SortableContext,
	sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";
import { cn } from "@ui/lib";
import React, { useMemo, useState } from "react";
import {
	useDashboardLayout,
	type WidgetConfig,
} from "../hooks/use-dashboard-layout";
import { DashboardWidget } from "./DashboardWidget";
import { AiAnalyticsWidget } from "./widgets/AiAnalyticsWidget";
import { CallResultsWidget } from "./widgets/CallResultsWidget";
import { OverviewWidget } from "./widgets/OverviewWidget";
import { RecentlyViewedWidget } from "./widgets/RecentlyViewedWidget";
import { TasksSummaryWidget } from "./widgets/TasksSummaryWidget";
import { TasksWidget } from "./widgets/TasksWidget";

const defaultWidgets: WidgetConfig[] = [
	{
		id: "tasks-summary",
		type: "tasks-summary",
		title: "Tasks Not Completed",
		position: { x: 0, y: 0 },
		size: { width: 1, height: 1 },
		minSize: { width: 1, height: 1 },
		maxSize: { width: 3, height: 2 },
		visible: true,
	},
	{
		id: "call-results",
		type: "call-results",
		title: "Call Results",
		position: { x: 1, y: 0 },
		size: { width: 1, height: 1 },
		minSize: { width: 1, height: 1 },
		maxSize: { width: 3, height: 2 },
		visible: true,
	},
	{
		id: "tasks-progress",
		type: "tasks-progress",
		title: "Task Progress",
		position: { x: 2, y: 0 },
		size: { width: 1, height: 1 },
		minSize: { width: 1, height: 1 },
		maxSize: { width: 3, height: 2 },
		visible: true,
	},
	{
		id: "ai-analytics",
		type: "ai-analytics",
		title: "AI Analytics",
		position: { x: 3, y: 0 },
		size: { width: 1, height: 1 },
		minSize: { width: 1, height: 1 },
		maxSize: { width: 3, height: 2 },
		visible: true,
	},
	{
		id: "overview",
		type: "overview",
		title: "Overview",
		position: { x: 0, y: 1 },
		size: { width: 2, height: 2 },
		minSize: { width: 2, height: 2 },
		maxSize: { width: 4, height: 3 },
		visible: true,
	},
	{
		id: "recently-viewed",
		type: "recently-viewed",
		title: "Recently Viewed",
		position: { x: 2, y: 1 },
		size: { width: 2, height: 2 },
		minSize: { width: 2, height: 2 },
		maxSize: { width: 4, height: 3 },
		visible: true,
	},
];

interface DashboardGridProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
	editable?: boolean;
	resetLayoutRef?: React.MutableRefObject<(() => void) | null>;
}

export function DashboardGrid({
	selectedFilter,
	data,
	isLoading,
	editable = false,
	resetLayoutRef,
}: DashboardGridProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const { layout, updateLayout, resetLayout } =
		useDashboardLayout(defaultWidgets);

	// Effect to expose resetLayout to parent
	React.useEffect(() => {
		if (resetLayoutRef) {
			resetLayoutRef.current = resetLayout;
		}
	}, [resetLayout, resetLayoutRef]);

	const [activeWidget, setActiveWidget] = useState<WidgetConfig | null>(null);

	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	const handleDragStart = (event: DragStartEvent) => {
		const widget = layout.find((w) => w.id === event.active.id);
		setActiveWidget(widget || null);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (active.id !== over?.id) {
			const oldIndex = layout.findIndex((w) => w.id === active.id);
			const newIndex = layout.findIndex((w) => w.id === over?.id);

			if (oldIndex !== -1 && newIndex !== -1) {
				const newLayout = arrayMove(layout, oldIndex, newIndex);
				updateLayout(newLayout);
			}
		}

		setActiveWidget(null);
	};

	const renderWidget = (widget: WidgetConfig) => {
		const baseProps = {
			selectedFilter,
			data,
			isLoading,
		};

		switch (widget.type) {
			case "tasks-summary":
				return <TasksSummaryWidget {...baseProps} />;
			case "call-results":
				return <CallResultsWidget {...baseProps} />;
			case "tasks-progress":
				return <TasksWidget {...baseProps} />;
			case "overview":
				return <OverviewWidget {...baseProps} />;
			case "recently-viewed":
				return (
					<RecentlyViewedWidget
						{...baseProps}
						user={user}
						activeOrganization={activeOrganization}
					/>
				);
			case "ai-analytics":
				return <AiAnalyticsWidget {...baseProps} />;
			default:
				return <div>Unknown widget type</div>;
		}
	};

	const gridStyle = useMemo(() => {
		const maxX = Math.max(
			...layout.map((w) => w.position.x + w.size.width),
		);
		const maxY = Math.max(
			...layout.map((w) => w.position.y + w.size.height),
		);

		return {
			display: "grid",
			gridTemplateColumns: `repeat(${Math.max(maxX, 4)}, 1fr)`,
			gridTemplateRows: `repeat(${Math.max(maxY, 4)}, minmax(180px, auto))`,
			gap: "1rem",
			padding: "1rem",
			width: "100%",
		};
	}, [layout]);

	return (
		<div className="space-y-4">
			<div
				className={cn(
					"relative min-h-[calc(100vh-9.75rem)]",
					editable && [
						"before:absolute before:inset-0 before:opacity-10",
						"before:bg-[radial-gradient(circle_at_1px_1px,rgb(148_163_184)_1px,transparent_0)]",
						"before:bg-[length:20px_20px]",
						"before:mask-[radial-gradient(ellipse_80%_80%_at_50%_50%,black_40%,transparent_100%)]",
						"before:pointer-events-none before:z-0",
					],
				)}
			>
				<DndContext
					sensors={sensors}
					collisionDetection={closestCenter}
					onDragStart={handleDragStart}
					onDragEnd={handleDragEnd}
				>
					<div style={gridStyle}>
						<SortableContext
							items={layout.map((w) => w.id)}
							strategy={rectSortingStrategy}
						>
							{layout
								.filter((widget) => widget.visible)
								.map((widget) => (
									<DashboardWidget
										key={widget.id}
										widget={widget}
										editable={editable}
										onResize={(newSize) => {
											const newLayout = layout.map((w) =>
												w.id === widget.id
													? { ...w, size: newSize }
													: w,
											);
											updateLayout(newLayout);
										}}
									>
										{renderWidget(widget)}
									</DashboardWidget>
								))}
						</SortableContext>
					</div>

					<DragOverlay>
						{activeWidget ? (
							<div className="bg-white dark:bg-gray-900 border-2 border-blue-500 rounded-lg shadow-lg opacity-90">
								{renderWidget(activeWidget)}
							</div>
						) : null}
					</DragOverlay>
				</DndContext>
			</div>
		</div>
	);
}
