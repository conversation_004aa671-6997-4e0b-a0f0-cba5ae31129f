import { useInfiniteQuery } from "@tanstack/react-query";

interface UseObjectsDataParams {
  organizationId?: string;
  objectType?: string;
  sort?: { id: string; desc: boolean };
  filters?: Record<string, any>;
}

export function useObjectsData({
  organizationId,
  objectType,
  sort,
  filters = {},
}: UseObjectsDataParams) {
  const fetchObjects = async ({ pageParam = 0 }) => {
    if (!organizationId || !objectType) return null;

    const queryParams = new URLSearchParams();
    
    // Add required params
    queryParams.append("organizationId", organizationId);
    queryParams.append("limit", "100");
    queryParams.append("direction", "next");
    
    // Add cursor if we have one
    if (pageParam !== 0) {
      queryParams.append("cursor", pageParam.toString());
    }

    // Add sorting
    if (sort) {
      queryParams.append("sort", JSON.stringify(sort));
    }

    // Add search if present
    if (filters.search) {
      queryParams.append("search", filters.search);
    }

    // Add createdAt if present
    if (filters.createdAt) {
      queryParams.append("createdAt", filters.createdAt);
    }

    const response = await fetch(
      `/api/objects/${objectType}/infinite?${queryParams.toString()}`,
      {
        credentials: "include",
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch objects");
    }

    const data = await response.json();
    return {
      ...data,
      nextCursor: data.nextCursor || undefined
    };
  };

  const query = useInfiniteQuery({
    queryKey: ["objects", organizationId, objectType, sort, filters],
    queryFn: fetchObjects,
    initialPageParam: 0,
    getNextPageParam: (lastPage) => lastPage?.nextCursor,
    enabled: !!organizationId && !!objectType,
  });

  // Flatten pages into a single array of items
  const items = query.data?.pages.flatMap((page) => page?.items ?? []) ?? [];

  return {
    ...query,
    data: {
      ...query.data,
      items,
    },
  };
} 