import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "./use-active-organization";

export interface AnalyticsData {
	totalTasks: number;
	tasksCompleted: number;
	tasksNotDoneCount: number;
	tasksInProgress: number;
	tasksBacklog: number;
	tasksTodo: number;
	tasksReview: number;
	tasksPercentage: number;
	callPercentage: number;
	callResultsCount: number;
	notesCount: number;
	taskStatusBreakdown: {
		backlog: number;
		todo: number;
		in_progress: number;
		review: number;
		done: number;
	};
	taskPriorityBreakdown: {
		urgent: number;
		high: number;
		medium: number;
		low: number;
		no_priority: number;
	};
	filter: string;
	dateRange: {
		startDate: string;
		endDate: string;
	};
	recentTasks: Array<{
		id: string;
		title: string;
		status: string;
		priority: string;
		assignee?: {
			id: string;
			name: string;
			image?: string;
		};
		createdBy: {
			id: string;
			name: string;
			image?: string;
		};
		createdAt: string;
		updatedAt: string;
	}>;
	timeSeriesData: Array<{
		date: string;
		calls: number;
		notes: number;
		tasks: number;
	}>;
}

async function fetchAnalytics(
	organizationId: string,
	filter: string,
): Promise<AnalyticsData> {
	const res = await fetch(
		`/api/start/analytics?organizationId=${organizationId}&filter=${encodeURIComponent(filter)}`,
	);
	if (!res.ok) {
		throw new Error("Failed to fetch analytics");
	}
	return res.json();
}

export function useGetFilteredData({ filter }: { filter: string }) {
	const { activeOrganization } = useActiveOrganization();

	return useQuery({
		queryKey: ["analytics", activeOrganization?.id, filter],
		queryFn: () => fetchAnalytics(activeOrganization!.id, filter),
		enabled: !!activeOrganization?.id,
		staleTime: 2 * 60 * 1000, // 2 minutes - data can be slightly stale for analytics
		gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
		refetchOnWindowFocus: false, // Disable automatic refetch on window focus
		refetchOnMount: false, // Don't refetch on component mount if data exists
		refetchOnReconnect: "always", // Refetch when connection is restored
		retry: (failureCount, error) => {
			// Retry up to 3 times, but not for 4xx errors
			if (error instanceof Error && error.message.includes('4')) {
				return false;
			}
			return failureCount < 3;
		},
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
	});
}
