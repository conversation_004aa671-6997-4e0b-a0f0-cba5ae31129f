import { z } from "zod";
import {
	ARRAY_DELIMITER,
	RANGE_DELIMITER,
} from "./search-params";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

// Contact schema based on your existing data structure
export const contactSchema = z.object({
	id: z.string(),
	name: z.string(),
	firstName: z.string().optional(),
	lastName: z.string().optional(),
	email: z
		.array(
			z.object({
				address: z.string(),
				label: z.string().optional(),
				isPrimary: z.boolean().optional(),
				isBad: z.boolean().optional(),
			}),
		)
		.default([]),
	phone: z
		.array(
			z.object({
				number: z.string(),
				label: z.string().optional(),
				isPrimary: z.boolean().optional(),
				isBad: z.boolean().optional(),
			}),
		)
		.default([]),
	status: z.enum(CONTACT_STATUS.map((status) => status.value) as [string, ...string[]]).optional(),
	persona: z.enum(CONTACT_PERSONA.map((persona) => persona.value) as [string, ...string[]]).optional(),
	stage: z.enum(CONTACT_STAGE.map((stage) => stage.value) as [string, ...string[]]).optional(),
	title: z.string().optional(),
	company: z
		.object({
			name: z.string(),
			id: z.string().optional(),
		})
		.optional(),
	website: z.string().optional(),
	linkedin: z.string().optional(),
	facebook: z.string().optional(),
	twitter: z.string().optional(),
	instagram: z.string().optional(),
	address: z
		.object({
			street: z.string().optional(),
			city: z.string().optional(),
			state: z.string().optional(),
			zip: z.string().optional(),
			country: z.string().optional(),
		})
		.optional(),
	birthday: z.date().optional(),
	spouseName: z.string().optional(),
	summary: z.string().optional(),
	tags: z.array(z.string()).optional(),
	source: z.string().optional(),
	createdAt: z.date(),
	updatedAt: z.date(),
	buyerNeeds: z
		.array(
			z.object({
				propertyType: z.string(),
				priceRange: z.object({
					min: z.number(),
					max: z.number(),
				}),
				location: z.string(),
				timeline: z.string(),
			}),
		)
		.optional(),
});

export type ContactSchema = z.infer<typeof contactSchema>;

// Filter schema for URL parsing
export const contactFilterSchema = z.object({
	name: z.string().optional(),
	email: z.string().optional(),
	phone: z.string().optional(),
	status: z
		.string()
		.transform((val) => val.split(ARRAY_DELIMITER))
		.pipe(z.enum(CONTACT_STATUS.map((status) => status.value) as [string, ...string[]]).array())
		.optional(),
	persona: z
		.string()
		.transform((val) => val.split(ARRAY_DELIMITER))
		.pipe(z.enum(CONTACT_PERSONA.map((persona) => persona.value) as [string, ...string[]]).array())
		.optional(),
	stage: z
		.string()
		.transform((val) => val.split(ARRAY_DELIMITER))
		.pipe(z.enum(CONTACT_STAGE.map((stage) => stage.value) as [string, ...string[]]).array())
		.optional(),
	title: z.string().optional(),
	company: z.string().optional(),
	createdAt: z
		.string()
		.transform((val) => val.split(RANGE_DELIMITER).map(Number))
		.pipe(z.coerce.date().array())
		.optional(),
});

export type ContactFilterSchema = z.infer<typeof contactFilterSchema>;

// Facet metadata for filters
export const facetMetadataSchema = z.object({
	rows: z.array(z.object({ value: z.any(), total: z.number() })),
	total: z.number(),
	min: z.number().optional(),
	max: z.number().optional(),
});

export type FacetMetadataSchema = z.infer<typeof facetMetadataSchema>;

// Base chart schema for timeline charts
export type BaseChartSchema = { timestamp: number; [key: string]: number };

export const timelineChartSchema = z.object({
	timestamp: z.number(), // UNIX timestamp
	contacts: z.number().default(0),
	companies: z.number().default(0),
	properties: z.number().default(0),
}) satisfies z.ZodType<BaseChartSchema>;

export type TimelineChartSchema = z.infer<typeof timelineChartSchema>;
