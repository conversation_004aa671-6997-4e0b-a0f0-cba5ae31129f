import { useMutation, useQueryClient } from "@tanstack/react-query";
import type {
	ApiHandlers,
	BaseObjectSchema,
} from "../../../../object-views/lib/types";
import { ObjectType } from "@repo/database";

interface GenericApiFactoryOptions {
	objectType: ObjectType;
	apiEndpoint: string; // e.g., "/api/contacts" or "/api/companies"
}

export function createGenericApiHandlers<TData extends BaseObjectSchema>(
	options: GenericApiFactoryOptions,
): ApiHandlers<TData> {
	const { objectType, apiEndpoint } = options;

	return {
		updateField: {
			mutate: async (params: {
				id: string;
				field: string;
				value: any;
				organizationId: string;
			}) => {
				const response = await fetch(
					`${apiEndpoint}/${params.id}/field`,
					{
						method: "PATCH",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							field: params.field,
							value: params.value,
							organizationId: params.organizationId,
						}),
					},
				);

				if (!response.ok) {
					throw new Error(`Failed to update ${objectType} field`);
				}

				return response.json();
			},
			hook: () => {
				const queryClient = useQueryClient();
				return useMutation({
					mutationFn: async (params: {
						id: string;
						field: string;
						value: any;
						organizationId: string;
					}) => {
						const response = await fetch(
							`${apiEndpoint}/${params.id}/field`,
							{
								method: "PATCH",
								headers: {
									"Content-Type": "application/json",
								},
								body: JSON.stringify({
									field: params.field,
									value: params.value,
									organizationId: params.organizationId,
								}),
							},
						);

						if (!response.ok) {
							throw new Error(
								`Failed to update ${objectType} field`,
							);
						}

						return response.json();
					},
					onMutate: async (variables) => {
						// Cancel any outgoing refetches (so they don't overwrite our optimistic update)
						await queryClient.cancelQueries({
							queryKey: [objectType, variables.organizationId],
						});
						await queryClient.cancelQueries({
							queryKey: [`${objectType}-infinite`, variables.organizationId],
						});

						// Snapshot the previous values
						const previousQueries = queryClient.getQueriesData({
							queryKey: [objectType],
							exact: false,
						});
						const previousInfiniteQueries = queryClient.getQueriesData({
							queryKey: [`${objectType}-infinite`],
							exact: false,
						});

						// Optimistically update regular queries
						queryClient.setQueriesData(
							{ queryKey: [objectType], exact: false },
							(oldData: any[] | undefined) => {
								if (!oldData || !Array.isArray(oldData)) {
									return oldData;
								}

								return oldData.map((item) => {
									if (item.id === variables.id) {
										return {
											...item,
											[variables.field]: variables.value,
										};
									}
									return item;
								});
							},
						);

						// Optimistically update infinite queries
						queryClient.setQueriesData(
							{ queryKey: [`${objectType}-infinite`], exact: false },
							(oldData: any) => {
								if (!oldData?.pages?.length) return oldData;

								const updatedPages = oldData.pages.map((page: any) => {
									if (page?.data) {
										return {
											...page,
											data: page.data.map((item: any) => {
												if (item.id === variables.id) {
													return {
														...item,
														[variables.field]: variables.value,
													};
												}
												return item;
											}),
										};
									}
									return page;
								});

								return {
									...oldData,
									pages: updatedPages,
								};
							},
						);

						// Return context for rollback
						return { previousQueries, previousInfiniteQueries };
					},
					onError: (err, variables, context) => {
						// Rollback optimistic updates on error
						if (context?.previousQueries) {
							context.previousQueries.forEach(([queryKey, data]) => {
								queryClient.setQueryData(queryKey, data);
							});
						}
						if (context?.previousInfiniteQueries) {
							context.previousInfiniteQueries.forEach(([queryKey, data]) => {
								queryClient.setQueryData(queryKey, data);
							});
						}
					},
					onSettled: (data, error, variables) => {
						if (error) {
							// Only invalidate on error to ensure we get fresh data
							queryClient.invalidateQueries({
								queryKey: [objectType, variables.organizationId],
								exact: false,
								refetchType: "active",
							});
							queryClient.invalidateQueries({
								queryKey: [`${objectType}-infinite`, variables.organizationId],
								exact: false,
								refetchType: "active",
							});
						} else if (data) {
							// On success, update cache with server response instead of invalidating
							// Update regular queries
							queryClient.setQueriesData(
								{ queryKey: [objectType], exact: false },
								(oldData: any[] | undefined) => {
									if (!oldData || !Array.isArray(oldData)) {
										return oldData;
									}

									return oldData.map((item) => {
										if (item.id === variables.id) {
											// Use server response data if available, otherwise keep optimistic update
											return data.contact || data.company || data.property || data || item;
										}
										return item;
									});
								},
							);

							// Update infinite queries with server response
							queryClient.setQueriesData(
								{ queryKey: [`${objectType}-infinite`], exact: false },
								(oldData: any) => {
									if (!oldData?.pages?.length) return oldData;

									const updatedPages = oldData.pages.map((page: any) => {
										if (page?.data) {
											return {
												...page,
												data: page.data.map((item: any) => {
													if (item.id === variables.id) {
														// Use server response data
														return data.contact || data.company || data.property || data || item;
													}
													return item;
												}),
											};
										}
										return page;
									});

									return {
										...oldData,
										pages: updatedPages,
									};
								},
							);
						}
					},
				});
			},
		},
		clearField: {
			mutate: async (params: {
				id: string;
				field: string;
				organizationId: string;
			}) => {
				const response = await fetch(
					`${apiEndpoint}/${params.id}/field/${params.field}?organizationId=${params.organizationId}`,
					{
						method: "DELETE",
						headers: {
							"Content-Type": "application/json",
						},
					},
				);

				if (!response.ok) {
					throw new Error(`Failed to clear ${objectType} field`);
				}

				return response.json();
			},
			hook: () => {
				const queryClient = useQueryClient();
				return useMutation({
					mutationFn: async (params: {
						id: string;
						field: string;
						organizationId: string;
					}) => {
						const response = await fetch(
							`${apiEndpoint}/${params.id}/field/${params.field}?organizationId=${params.organizationId}`,
							{
								method: "DELETE",
								headers: {
									"Content-Type": "application/json",
								},
							},
						);

						if (!response.ok) {
							throw new Error(
								`Failed to clear ${objectType} field`,
							);
						}

						return response.json();
					},
					onSuccess: () => {
						// Invalidate all queries for this object type
						queryClient.invalidateQueries({
							queryKey: [objectType],
						});
						// Also invalidate infinite queries
						queryClient.invalidateQueries({
							queryKey: [`${objectType}-infinite`],
							exact: false,
							refetchType: "none",
						});
					},
				});
			},
		},
		deleteRecord: {
			mutate: async (params: { id: string; organizationId: string }) => {
				const response = await fetch(
					`${apiEndpoint}/${params.id}?organizationId=${params.organizationId}`,
					{
						method: "DELETE",
						headers: {
							"Content-Type": "application/json",
						},
					},
				);

				if (!response.ok) {
					throw new Error(`Failed to delete ${objectType}`);
				}

				return response.json();
			},
			hook: () => {
				const queryClient = useQueryClient();
				return useMutation({
					mutationFn: async (params: {
						id: string;
						organizationId: string;
					}) => {
						const response = await fetch(
							`${apiEndpoint}/${params.id}?organizationId=${params.organizationId}`,
							{
								method: "DELETE",
								headers: {
									"Content-Type": "application/json",
								},
							},
						);

						if (!response.ok) {
							throw new Error(`Failed to delete ${objectType}`);
						}

						return response.json();
					},
					onSuccess: () => {
						// Invalidate all queries for this object type
						queryClient.invalidateQueries({
							queryKey: [objectType],
						});
						// Also invalidate infinite queries
						queryClient.invalidateQueries({
							queryKey: [`${objectType}-infinite`],
							exact: false,
							refetchType: "none",
						});
					},
				});
			},
		},
	};
}

// Helper function to create API hooks for a specific object type
export function createGenericApiHooks<TData extends BaseObjectSchema>(
	options: GenericApiFactoryOptions,
) {
	const handlers = createGenericApiHandlers<TData>(options);

	return {
		useUpdateField: handlers.updateField.hook,
		useClearField: handlers.clearField.hook,
		useDeleteRecord: handlers.deleteRecord.hook,
		...handlers,
	};
}

// Get status history for an object
export function createGetStatusHistoryFunction(
	apiEndpoint: string,
	objectType: ObjectType,
) {
	return async (
		objectId: string,
		organizationId: string,
	): Promise<{
		statusHistory: Array<{
			id: string;
			statusField: string;
			fromStatus: string | null;
			toStatus: string;
			createdAt: string;
			user: {
				id: string;
				name: string;
				image?: string;
			};
		}>;
	}> => {
		const response = await fetch(
			`${apiEndpoint}/${objectId}/status-history?organizationId=${organizationId}`,
			{
				method: "GET",
				headers: {
					"Content-Type": "application/json",
				},
			},
		);

		if (!response.ok) {
			const errorData = await response.json().catch(() => null);
			throw new Error(
				errorData?.error ||
					`Failed to fetch ${objectType} status history`,
			);
		}

		return response.json();
	};
}
