import {
	IconAlertTriangle,
	IconBrandAsana,
	IconBuilding,
	IconCircle,
	IconCircleDotted,
	IconCircleHalf2,
	IconClock,
	IconEyeOff,
	IconHome,
	IconHomeCheck,
	IconHomeDollar,
	IconPlus,
	IconSquareRounded<PERSON>heck,
	Icon<PERSON>ser,
	IconUser<PERSON><PERSON>ck,
	IconUsers,
} from "@tabler/icons-react";
import { ObjectType } from "@repo/database";
import { PROPERTY_STATUSES } from "@app/organizations/components/objects/properties/schema";

export interface KanbanStatusConfig {
	label: string;
	value: string;
	icon: React.ComponentType<any>;
	color: string;
	order?: number;
	trackTime?: boolean;
	showConfetti?: boolean;
	targetTime?: number;
	targetTimeUnit?: string;
}

export interface KanbanConfig {
	statusAttribute: string;
	statuses: KanbanStatusConfig[];
	allowCustomStatuses?: boolean;
}

// Extended interface for view-based kanban configuration
export interface ViewKanbanConfig extends KanbanConfig {
	customStatuses?: KanbanStatusConfig[];
}

// Default icon mapping for auto-generated statuses
const DEFAULT_STATUS_ICONS = [
	{ icon: IconCircleDotted, color: "#6b7280" }, // zinc-500
	{ icon: IconCircle, color: "#3b82f6" }, // blue-500
	{ icon: IconCircleHalf2, color: "#eab308" }, // yellow-500
	{ icon: IconBrandAsana, color: "#f97316" }, // orange-500
	{ icon: IconSquareRoundedCheck, color: "#22c55e" }, // green-500
	{ icon: IconUser, color: "#8b5cf6" }, // purple-500
	{ icon: IconUsers, color: "#6366f1" }, // indigo-500
	{ icon: IconClock, color: "#f59e0b" }, // amber-500
	{ icon: IconEyeOff, color: "#6b7280" }, // gray-500
];

// Special value for items without a status
export const NO_STATUS_VALUE = "__no_status__";

// Helper function to create status config from a value
function createStatusConfig(value: string, index = 0): KanbanStatusConfig {
	const iconConfig =
		DEFAULT_STATUS_ICONS[index % DEFAULT_STATUS_ICONS.length];
	return {
		label: value
			.split("_")
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(" "),
		value,
		icon: iconConfig.icon,
		color: iconConfig.color,
		order: index,
	};
}

// Helper function to create a "No {field}" status for items without a status value
export function createNoStatusConfig(fieldName: string): KanbanStatusConfig {
	return {
		label: `No ${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`,
		value: NO_STATUS_VALUE,
		icon: IconAlertTriangle,
		color: "#94a3b8", // slate-400
		order: -1, // Always appears first
	};
}

// Helper function to merge custom statuses with defaults and ensure "No {field}" status is included
export function mergeStatuses(
	defaultStatuses: KanbanStatusConfig[],
	customStatuses?: KanbanStatusConfig[],
	fieldName?: string,
): KanbanStatusConfig[] {
	// Create a map of existing statuses
	const statusMap = new Map<string, KanbanStatusConfig>();

	// Add "No {field}" status first if fieldName is provided
	if (fieldName) {
		const noStatusConfig = createNoStatusConfig(fieldName);
		statusMap.set(noStatusConfig.value, noStatusConfig);
	}

	// Add default statuses
	defaultStatuses.forEach((status) => {
		statusMap.set(status.value, status);
	});

	// Override with custom statuses and add new ones
	if (customStatuses && customStatuses.length > 0) {
		customStatuses.forEach((status) => {
			statusMap.set(status.value, status);
		});
	}

	// Sort by order if specified, otherwise maintain insertion order
	return Array.from(statusMap.values()).sort((a, b) => {
		if (a.order !== undefined && b.order !== undefined) {
			return a.order - b.order;
		}
		return 0;
	});
}

// Default configurations (as fallbacks)
export const CONTACT_STATUS_CONFIG: KanbanConfig = {
	statusAttribute: "status",
	allowCustomStatuses: true,
	statuses: [
		{
			label: "Lead",
			value: "lead",
			icon: IconUser,
			color: "#eab308",
			order: 0,
		},
		{
			label: "Active",
			value: "active",
			icon: IconUserCheck,
			color: "#22c55e",
			order: 1,
		},
		{
			label: "Client",
			value: "client",
			icon: IconUsers,
			color: "#3b82f6",
			order: 2,
		},
		{
			label: "Past Client",
			value: "past_client",
			icon: IconClock,
			color: "#6b7280",
			order: 3,
		},
		{
			label: "Inactive",
			value: "inactive",
			icon: IconEyeOff,
			color: "#a1a1aa",
			order: 4,
		},
	],
};

export const CONTACT_STAGE_CONFIG: KanbanConfig = {
	statusAttribute: "stage",
	allowCustomStatuses: true,
	statuses: [
		{
			label: "New",
			value: "new",
			icon: IconCircleDotted,
			color: "#6b7280",
			order: 0,
		},
		{
			label: "Contacted",
			value: "contacted",
			icon: IconCircle,
			color: "#3b82f6",
			order: 1,
		},
		{
			label: "Qualified",
			value: "qualified",
			icon: IconCircleHalf2,
			color: "#eab308",
			order: 2,
		},
		{
			label: "Proposal",
			value: "proposal",
			icon: IconBrandAsana,
			color: "#f97316",
			order: 3,
		},
		{
			label: "Closed",
			value: "closed",
			icon: IconSquareRoundedCheck,
			color: "#22c55e",
			order: 4,
		},
	],
};

export const PROPERTY_STATUS_CONFIG: KanbanConfig = {
	statusAttribute: "status",
	allowCustomStatuses: true,
	statuses: PROPERTY_STATUSES.map((status) => ({
		label: status.label,
		value: status.value,
		icon: IconCircleDotted,
		color: "#6b7280",
		order: 0,
	})),
};

export const COMPANY_STATUS_CONFIG: KanbanConfig = {
	statusAttribute: "status",
	allowCustomStatuses: true,
	statuses: [
		{
			label: "Prospect",
			value: "prospect",
			icon: IconCircleDotted,
			color: "#eab308",
			order: 0,
		},
		{
			label: "Active",
			value: "active",
			icon: IconBuilding,
			color: "#22c55e",
			order: 1,
		},
		{
			label: "Client",
			value: "client",
			icon: IconSquareRoundedCheck,
			color: "#3b82f6",
			order: 2,
		},
		{
			label: "Inactive",
			value: "inactive",
			icon: IconEyeOff,
			color: "#a1a1aa",
			order: 3,
		},
	],
};

export const CUSTOM_OBJECT_STATUS_CONFIG: KanbanConfig = {
	statusAttribute: "status",
	allowCustomStatuses: true,
	statuses: [],
};

// Default kanban configurations by object type
export const DEFAULT_KANBAN_CONFIGS: Record<ObjectType, KanbanConfig[]> = {
	contact: [CONTACT_STATUS_CONFIG, CONTACT_STAGE_CONFIG],
	company: [COMPANY_STATUS_CONFIG],
	property: [PROPERTY_STATUS_CONFIG],
	custom_object: [CUSTOM_OBJECT_STATUS_CONFIG],
	task: [],
	note: [],
	view: [],
};

// Get kanban config by object type and status attribute, with support for custom statuses
export function getKanbanConfig(
	objectType: ObjectType,
	statusAttribute?: string,
	customStatuses?: KanbanStatusConfig[],
	dynamicStatuses?: string[],
): KanbanConfig | null {
	const configs = DEFAULT_KANBAN_CONFIGS[objectType];

	if (!statusAttribute) {
		const defaultConfig = configs[0];
		if (!defaultConfig) return null;

		return {
			...defaultConfig,
			statuses: mergeStatuses(
				defaultConfig.statuses,
				customStatuses,
				defaultConfig.statusAttribute,
			),
		};
	}

	// Try to find exact match first
	const exactMatch = configs.find(
		(config) => config.statusAttribute === statusAttribute,
	);

	if (exactMatch) {
		return {
			...exactMatch,
			statuses: mergeStatuses(
				exactMatch.statuses,
				customStatuses,
				statusAttribute,
			),
		};
	}

	// If no exact match found but we have dynamic statuses, create a config
	if (dynamicStatuses && dynamicStatuses.length > 0) {
		const dynamicConfig: KanbanConfig = {
			statusAttribute,
			allowCustomStatuses: true,
			statuses: dynamicStatuses.map((status, index) =>
				createStatusConfig(status, index),
			),
		};

		return {
			...dynamicConfig,
			statuses: mergeStatuses(
				dynamicConfig.statuses,
				customStatuses,
				statusAttribute,
			),
		};
	}

	// Fall back to the first available config
	const fallbackConfig = configs[0];
	if (!fallbackConfig) return null;

	console.warn(
		`No kanban config found for statusAttribute "${statusAttribute}" on ${objectType}. Falling back to default.`,
	);
	return {
		...fallbackConfig,
		statuses: mergeStatuses(
			fallbackConfig.statuses,
			customStatuses,
			statusAttribute,
		),
	};
}

// Create a kanban config from unique values found in data
export function createDynamicKanbanConfig(
	objectType: ObjectType,
	statusAttribute: string,
	uniqueValues: string[],
	customStatuses?: KanbanStatusConfig[],
): KanbanConfig {
	const dynamicStatuses = uniqueValues.map((value, index) =>
		createStatusConfig(value, index),
	);

	return {
		statusAttribute,
		allowCustomStatuses: true,
		statuses: mergeStatuses(
			dynamicStatuses,
			customStatuses,
			statusAttribute,
		),
	};
}

// Validate and sanitize status value
export function sanitizeStatusValue(value: string): string {
	return value
		.toLowerCase()
		.replace(/[^a-z0-9]/g, "_")
		.replace(/_+/g, "_")
		.replace(/^_|_$/g, "");
}

// Generate a new status configuration
export function createNewStatus(
	label: string,
	existingStatuses: KanbanStatusConfig[],
): KanbanStatusConfig {
	const value = sanitizeStatusValue(label);
	const order = Math.max(...existingStatuses.map((s) => s.order || 0), 0) + 1;
	const iconIndex = existingStatuses.length % DEFAULT_STATUS_ICONS.length;
	const iconConfig = DEFAULT_STATUS_ICONS[iconIndex];

	return {
		label,
		value,
		icon: iconConfig.icon,
		color: iconConfig.color,
		order,
	};
}
