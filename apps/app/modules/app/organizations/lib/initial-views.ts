import { upsertCustomFieldDefinition } from "@app/custom-field-definitions/lib/api";
import { createObjectView } from "@app/object-views/lib/api";
import { PROPERTY_STATUSES } from "@app/organizations/components/objects/properties/schema";
import { COMPANY_STAGE, COMPANY_STATUS, CONTACT_STAGE, CONTACT_STATUS, CUSTOM_OBJECT_STAGE, CUSTOM_OBJECT_STATUS, PROPERTY_STAGE } from "@app/shared/lib/constants";
import { objectTypes } from "@repo/database";
import { getRandomColors } from "@ui/components/color-picker";

// Default column definitions for initial views
// const defaultColumnDefs = {
// 	contacts: [
// 		{ field: "firstName", headerName: "First Name", width: 150 },
// 		{ field: "lastName", headerName: "Last Name", width: 150 },
// 		{ field: "title", headerName: "Title", width: 150 },
// 		{ field: "email", headerName: "Email", width: 200 },
// 		{ field: "phone", headerName: "Phone", width: 150 },
// 		{ field: "company.name", headerName: "Company", width: 150 },
// 		{ field: "status", headerName: "Status", width: 120 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// 	companies: [
// 		{ field: "name", headerName: "Company Name", width: 200 },
// 		{ field: "industry", headerName: "Industry", width: 150 },
// 		{ field: "size", headerName: "Size", width: 120 },
// 		{ field: "website", headerName: "Website", width: 200 },
// 		{ field: "phone", headerName: "Phone", width: 150 },
// 		{ field: "email", headerName: "Email", width: 200 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// 	properties: [
// 		{ field: "name", headerName: "Property Name", width: 200 },
// 		{ field: "propertyType", headerName: "Type", width: 150 },
// 		{ field: "address.street", headerName: "Street", width: 200 },
// 		{ field: "address.city", headerName: "City", width: 150 },
// 		{ field: "address.state", headerName: "State", width: 100 },
// 		{ field: "price", headerName: "Price", width: 150 },
// 		{ field: "units", headerName: "Units", width: 100 },
// 	],
// };

/**
 * Creates initial custom field definitions for a new organization
 * @param organizationId - The ID of the organization to create custom fields for
 * @returns Promise that resolves when all custom fields are created (or fails gracefully)
 */
async function createInitialCustomFieldDefinitions(
	organizationId: string,
): Promise<void> {
	try {
		const customFieldPromises = objectTypes.flatMap((objectType) => [
			// Create Status field
			upsertCustomFieldDefinition({
				name: "status",
				label: "Status",
				type: "select",
				objectType,
				organizationId,
				options: {
					choices: getDefaultStatusOptions(objectType),
				},
			}),
			// Create Stage field
			upsertCustomFieldDefinition({
				name: "stage",
				label: "Stage",
				type: "select",
				objectType,
				organizationId,
				options: {
					choices: getDefaultStageOptions(objectType),
				},
			}),
		]);

		await Promise.allSettled(customFieldPromises);
	} catch (error) {
		// Don't fail organization creation if custom field creation fails
		console.warn(
			"Failed to create initial custom fields for organization:",
			error,
		);
	}
}

/**
 * Get default status options for each object type with random colors
 */
function getDefaultStatusOptions(objectType: string) {
	let options: Array<{ label: string; value: string }> = [];

	switch (objectType) {
		case "contact":
			options = CONTACT_STATUS.map((status) => ({
				label: status.label,
				value: status.value,
			}));
			break;
		case "company":
			options = COMPANY_STATUS.map((status) => ({
				label: status.label,
				value: status.value,
			}));
			break;
		case "property":
			options = PROPERTY_STATUSES.map((status) => ({
				label: status.label,
				value: status.value,
			}));
			break;
		default:
			options = CUSTOM_OBJECT_STATUS.map((status) => ({
				label: status.label,
				value: status.value,
			}));
	}

	// Add random colors to each option
	const randomColors = getRandomColors(options.length);
	return options.map((option, index) => ({
		...option,
		color: randomColors[index],
	}));
}

/**
 * Get default stage options for each object type with random colors
 */
function getDefaultStageOptions(objectType: string) {
	let options: Array<{ label: string; value: string }> = [];

	switch (objectType) {
		case "contact":
			options = CONTACT_STAGE.map((stage) => ({
				label: stage.label,
				value: stage.value,
			}));
			break;
		case "company":
			options = COMPANY_STAGE.map((stage) => ({
				label: stage.label,
				value: stage.value,
			}));
			break;
		case "property":
			options = PROPERTY_STAGE.map((stage) => ({
				label: stage.label,
				value: stage.value,
			}));
			break;
		default:
			options = CUSTOM_OBJECT_STAGE.map((stage) => ({
				label: stage.label,
				value: stage.value,
			}));
	}

	// Add random colors to each option
	const randomColors = getRandomColors(options.length);
	return options.map((option, index) => ({
		...option,
		color: randomColors[index],
	}));
}

/**
 * Creates initial default views for a new organization
 * @param organizationId - The ID of the organization to create views for
 * @returns Promise that resolves when all views are created (or fails gracefully)
 */
export async function createInitialOrganizationViews(
	organizationId: string,
): Promise<void> {
	try {
		// First create custom field definitions
		await createInitialCustomFieldDefinitions(organizationId);

		const viewPromises = objectTypes.map(async (objectType) => {
			const viewName = `${objectType.charAt(0).toUpperCase() + objectType.slice(1)}`;
			return createObjectView({
				name: viewName,
				objectType,
				organizationId,
				viewType: "table",
				columnDefs: [],
				filters: [],
				filterCondition: "and",
				isDefault: true,
				isPublic: true,
			});
		});

		await Promise.allSettled(viewPromises);
	} catch (error) {
		// Don't fail organization creation if view creation fails
		console.warn("Failed to create initial views for organization:", error);
	}
}
