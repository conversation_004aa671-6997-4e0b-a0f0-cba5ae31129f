import { useState } from "react";
import { toast } from "sonner";

type SharingLevel = "metadata" | "subject" | "full";

interface ForwardingEmailConfig {
  id: string;
  organizationId: string;
  address: string;
  isActive: boolean;
  defaultSharingLevel: SharingLevel;
  individualSharing?: Array<{ userId: string; level: SharingLevel }>;
  blockedEmails: string[];
  blockedDomains: string[];
  autoCreateContacts: boolean;
  autoCreateCompanies: boolean;
  createdAt: string;
  updatedAt: string;
}

export function useForwardingEmailConfig(
  organizationId: string | undefined,
  initialConfig?: ForwardingEmailConfig
) {
  const [loading, setLoading] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [isActive, setIsActive] = useState(initialConfig?.isActive ?? false);
  const [sharingLevel, setSharingLevel] = useState<SharingLevel>(
    initialConfig?.defaultSharingLevel ?? "full"
  );
  const [blocklist, setBlocklist] = useState<string[]>(
    initialConfig ? [...initialConfig.blockedEmails, ...initialConfig.blockedDomains] : []
  );

  const updateSharingLevel = async (newLevel: SharingLevel) => {
    if (!organizationId) return false;

    setLoading(true);
    try {
      const response = await fetch(`/api/forwarding-email-config/${organizationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          defaultSharingLevel: newLevel,
        }),
      });

      if (response.ok) {
        setSharingLevel(newLevel);
        toast.success("Sharing settings updated");
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to update sharing settings";
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      console.error("Error updating sharing level:", error);
      toast.error("Failed to update sharing settings");
      return false;
    } finally {
      setLoading(false);
    }
  };

  const addToBlocklist = async (entries: string[]) => {
    if (!organizationId) return false;

    // Separate emails and domains
    const emails = entries.filter(entry => entry.includes("@"));
    const domains = entries.filter(entry => !entry.includes("@"));

    setLoading(true);
    try {
      const response = await fetch(`/api/forwarding-email-config/${organizationId}/blocklist`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          emails,
          domains,
        }),
      });

      if (response.ok) {
        setBlocklist(prev => [...prev, ...entries]);
        toast.success("Added to blocklist");
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to add to blocklist";
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      console.error("Error adding to blocklist:", error);
      toast.error("Failed to add to blocklist");
      return false;
    } finally {
      setLoading(false);
    }
  };

  const removeFromBlocklist = async (entry: string) => {
    if (!organizationId) return false;

    const isEmail = entry.includes("@");
    const queryParam = isEmail ? `email=${encodeURIComponent(entry)}` : `domain=${encodeURIComponent(entry)}`;

    setLoading(true);
    try {
      const response = await fetch(
        `/api/forwarding-email-config/${organizationId}/blocklist?${queryParam}`,
        {
          method: "DELETE",
          credentials: "include",
        }
      );

      if (response.ok) {
        setBlocklist(prev => prev.filter(item => item !== entry));
        toast.success("Removed from blocklist");
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to remove from blocklist";
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      console.error("Error removing from blocklist:", error);
      toast.error("Failed to remove from blocklist");
      return false;
    } finally {
      setLoading(false);
    }
  };

  const connect = async () => {
    if (!organizationId) return false;

    setConnecting(true);
    try {
      const response = await fetch(`/api/forwarding-email-config/${organizationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          isActive: true,
        }),
      });

      if (response.ok) {
        setIsActive(true);
        toast.success("Forwarding email connected");
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to connect forwarding email";
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      console.error("Error connecting forwarding email:", error);
      toast.error("Failed to connect forwarding email");
      return false;
    } finally {
      setConnecting(false);
    }
  };

  const disconnect = async () => {
    if (!organizationId) return false;

    setConnecting(true);
    try {
      const response = await fetch(`/api/forwarding-email-config/${organizationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          isActive: false,
        }),
      });

      if (response.ok) {
        setIsActive(false);
        toast.success("Forwarding email disconnected");
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to disconnect forwarding email";
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      console.error("Error disconnecting forwarding email:", error);
      toast.error("Failed to disconnect forwarding email");
      return false;
    } finally {
      setConnecting(false);
    }
  };

  return {
    // State
    loading,
    connecting,
    isActive,
    sharingLevel,
    blocklist,
    
    // Actions
    updateSharingLevel,
    addToBlocklist,
    removeFromBlocklist,
    connect,
    disconnect,
    
    // Setters for external updates
    setIsActive,
    setSharingLevel,
    setBlocklist,
  };
} 