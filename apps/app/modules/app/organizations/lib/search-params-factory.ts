import {
	createParser,
	createSearchParamsCache,
	createSerializer,
	type inferParserType,
	parseAsArrayOf,
	parseAsBoolean,
	parseAsInteger,
	parseAsString,
	parseAsStringLiteral,
	parseAsTimestamp,
} from "nuqs/server";

const ARRAY_DELIMITER = ",";
const RANGE_DELIMITER = ":";
const SLIDER_DELIMITER = ":";
const SORT_DELIMITER = ".";

export const parseAsSort = createParser({
	parse(queryValue) {
		const [id, desc] = queryValue.split(SORT_DELIMITER);
		if (!id && !desc) return null;
		return { id, desc: desc === "desc" };
	},
	serialize(value) {
		return `${value.id}.${value.desc ? "desc" : "asc"}`;
	},
});

// Custom timestamp parser that handles different date formats intelligently
export const parseAsFlexibleTimestamp = createParser({
	parse(queryValue) {
		if (!queryValue) return null;

		const num = Number(queryValue);

		// If it's a 4-digit number, treat it as a year
		if (/^\d{4}$/.test(queryValue)) {
			return new Date(num, 0, 1); // January 1st of that year
		}

		// If it's a 10-digit number, treat it as seconds since epoch
		if (/^\d{10}$/.test(queryValue)) {
			return new Date(num * 1000);
		}

		// If it's a 13-digit number, treat it as milliseconds since epoch
		if (/^\d{13}$/.test(queryValue)) {
			return new Date(num);
		}

		// If it's an ISO date string format (YYYY-MM-DD), parse it
		if (/^\d{4}-\d{2}-\d{2}$/.test(queryValue)) {
			return new Date(queryValue + "T00:00:00.000Z");
		}

		// If it's a full ISO date string, parse it
		if (/^\d{4}-\d{2}-\d{2}T/.test(queryValue)) {
			return new Date(queryValue);
		}

		// Fallback to parseAsTimestamp behavior
		const date = new Date(queryValue);
		return isNaN(date.getTime()) ? null : date;
	},
	serialize(value) {
		return value.getTime().toString();
	},
});

// Base search params that all objects share
export const baseSearchParamsParser = {
	// REQUIRED FOR SORTING & PAGINATION
	sort: parseAsSort,
	size: parseAsInteger.withDefault(100),
	start: parseAsInteger.withDefault(0),

	// REQUIRED FOR INFINITE SCROLLING
	direction: parseAsStringLiteral(["prev", "next"]).withDefault("next"),
	cursor: parseAsTimestamp.withDefault(new Date()),
	live: parseAsBoolean.withDefault(false),

	// REQUIRED FOR SELECTION
	id: parseAsString,

	// COMMON FILTERS
	createdAt: parseAsArrayOf(parseAsFlexibleTimestamp, RANGE_DELIMITER),
};

// Factory function to create search params parser for any object type
export function createSearchParamsParser<T extends Record<string, any>>(
	objectSpecificParams: T,
) {
	const parser = {
		...baseSearchParamsParser,
		...objectSpecificParams,
	};

	const cache = createSearchParamsCache(parser);
	const serializer = createSerializer(parser);

	return {
		parser,
		cache,
		serializer,
		type: null as inferParserType<typeof parser>,
	};
}

// Helper functions for creating common parameter types
export const createStringArrayParam = (values: readonly string[]) =>
	parseAsArrayOf(parseAsStringLiteral(values), ARRAY_DELIMITER);

export const createDynamicStringArrayParam = () => {
	// Custom parser that properly handles URL encoding for spaces
	return createParser({
		parse(queryValue) {
			if (!queryValue) return null;
			// Split by comma and decode each item
			return queryValue.split(ARRAY_DELIMITER).map(item => decodeURIComponent(item.trim()));
		},
		serialize(value) {
			if (!value || !Array.isArray(value) || value.length === 0) return "";
			// Encode each item and join with comma
			return value.map(item => encodeURIComponent(String(item))).join(ARRAY_DELIMITER);
		},
	});
};

export const createStringParam = () => {
	// Custom parser that properly handles URL encoding for spaces and special characters
	return createParser({
		parse(queryValue) {
			if (!queryValue) return null;
			// Decode the URL-encoded value
			return decodeURIComponent(queryValue);
		},
		serialize(value) {
			if (!value) return "";
			// Encode the value for URL
			return encodeURIComponent(String(value));
		},
	});
};

export const createDateRangeParam = () =>
	parseAsArrayOf(parseAsFlexibleTimestamp, RANGE_DELIMITER);

export const createNumberRangeParam = () =>
	parseAsArrayOf(parseAsInteger, SLIDER_DELIMITER);

export { ARRAY_DELIMITER, RANGE_DELIMITER, SLIDER_DELIMITER, SORT_DELIMITER };
