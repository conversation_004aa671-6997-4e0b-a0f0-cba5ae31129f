"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { useTranslations } from "next-intl";

export function LoginModeSwitch({
	activeMode,
	onChange,
	className,
}: {
	activeMode: "password" | "magic-link";
	onChange: (mode: string) => void;
	className?: string;
}) {
	const t = useTranslations();
	return (
		<Tabs value={activeMode} onValueChange={onChange} className={className}>
			<TabsList className="w-full border-b-0">
				<TabsTrigger value="password" className="h-8 flex-1 border border-transparent data-[state=active]:border-border data-[state=active]:rounded-lg data-[state=active]:bg-muted/50">
					{t("auth.login.modes.password")}
				</TabsTrigger>
				<TabsTrigger value="magic-link" className="h-8 flex-1 border border-transparent data-[state=active]:border-border data-[state=active]:rounded-lg data-[state=active]:bg-muted/50">
					{t("auth.login.modes.magicLink")}
				</TabsTrigger>
			</TabsList>
		</Tabs>
	);
}
