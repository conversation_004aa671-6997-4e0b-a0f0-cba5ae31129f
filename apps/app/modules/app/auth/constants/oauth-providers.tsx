import type { JSXElementConstructor } from "react";

type IconProps = {
	className?: string;
};

export const oAuthProviders = {
	google: {
		name: "Google",
		icon: ({ ...props }: IconProps) => (
			<svg viewBox="0 0 488 512" {...props}>
				<title>Google</title>
				<path
					d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"
					fill="currentColor"
				/>
			</svg>
		),
	},
	// apple: {
	// 	name: "<PERSON>",
	// 	icon: ({ ...props }: IconProps) => (
	// 		<svg viewBox="0 0 180 180" {...props}>
	// 			<title>Apple</title>
	// 			<path
	// 				fill="currentColor"
	// 				d="m108,35
	//         c5.587379,-6.7633 9.348007,-16.178439 8.322067,-25.546439
	//         c-8.053787,0.32369 -17.792625,5.36682 -23.569427,12.126399
	//         c-5.177124,5.985922 -9.711121,15.566772 -8.48777,24.749359
	//         c8.976891,0.69453 18.147476,-4.561718 23.73513,-11.329308"
	// 			/>
	// 			<path
	// 				fill="currentColor"
	// 				d="M88,162.415214
	//         c-12.24469,0 -16.072174,6.151901 -26.213551,6.550446
	//         c-10.52422,0.398254 -18.538303,-10.539917 -25.26247,-20.251053
	//         c-13.740021,-19.864456 -24.24024,-56.132286 -10.1411,-80.613663
	//         c7.004152,-12.157551 19.521101,-19.85622 33.10713,-20.053638
	//         c10.334515,-0.197132 20.089069,6.952717 26.406689,6.952717"
	// 			/>
	// 			<path
	// 				fill="currentColor"
	// 				d="M85,55
	//         c6.313614,0 18.167473,-8.59832 30.628998,-7.335548
	//         c5.21682,0.217129 19.860519,2.1073 29.263641,15.871029
	//         c-0.75766,0.469692 -17.472931,10.200527 -17.291229,30.443592
	//         c0.224838,24.213104 21.241287,32.270615 21.474121,32.373459
	//         c-0.177704,0.56826 -3.358078,11.482742 -11.072464,22.756622
	//         c-6.668747,9.746841 -13.590027,19.457977 -24.493088,19.659103
	//         c-10.713348,0.197403 -14.158287,-6.353043 -26.406677,-6.353043"
	// 			/>
	// 		</svg>
	// 	),
	// },
} as const satisfies Record<
	string,
	{
		name: string;
		icon: JSXElementConstructor<React.SVGProps<SVGSVGElement>>;
	}
>;

export type OAuthProvider = keyof typeof oAuthProviders;
