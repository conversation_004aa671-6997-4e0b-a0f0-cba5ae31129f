import type { ObjectType, ObjectViewType } from "./types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// INTERFACES
export interface ObjectView {
	id: string;
	name: string;
	objectType: ObjectType;
	organizationId: string;
	viewType: ObjectViewType;
	columnDefs: Array<{
		field: string;
		headerName: string;
		width: number;
		type?: string;
		visible?: boolean;
	}>;
	cardRowFields?: Array<{
		field: string;
		headerName: string;
		type?: string;
	}>;
	filters?: Array<{
		field: string;
		logic: string;
		text?: string;
		number?: number;
	}>;
	filterCondition?: "and" | "or";
	statusAttribute?: string; // For kanban views
	mapConfig?: {
		displayType: 'table' | 'grid';
		rowDensity: 'compact' | 'normal' | 'comfortable';
		showExportOptions: boolean;
		allowColumnReorder: boolean;
		showSearchBar: boolean;
	};
	isDefault: boolean;
	isPublic: boolean;
	creator: {
		id: string;
		name: string;
		email: string;
	};
	createdAt: string;
	updatedAt: string;
}

export interface CreateViewPayload {
	name: string;
	objectType: ObjectType;
	organizationId: string;
	viewType?: ObjectViewType;
	statusAttribute?: string;
	columnDefs: Array<{
		field: string;
		headerName: string;
		width: number;
		type?: string;
		visible?: boolean;
	}>;
	cardRowFields?: Array<{
		field: string;
		headerName: string;
		type?: string;
	}>;
	filters?: Array<{
		field: string;
		logic: string;
		text?: string;
		number?: number;
	}>;
	filterCondition?: "and" | "or";
	mapConfig?: {
		displayType: 'table' | 'grid';
		rowDensity: 'compact' | 'normal' | 'comfortable';
		showExportOptions: boolean;
		allowColumnReorder: boolean;
		showSearchBar: boolean;
	};
	isDefault?: boolean;
	isPublic?: boolean;
}

// FETCH ALL VIEWS FOR AN ORGANIZATION AND OBJECT TYPE
export async function fetchObjectViews(
	organizationId: string,
	objectType: ObjectType,
): Promise<ObjectView[]> {
	const res = await fetch(
		`/api/object-views/views?organizationId=${organizationId}&objectType=${objectType}`,
		{
			method: "GET",
			credentials: "include",
		},
	);

	if (!res.ok) {
		throw new Error("Failed to fetch views");
	}

	return res.json();
}

// FETCH A SPECIFIC VIEW
export async function fetchObjectView(viewId: string): Promise<ObjectView> {
	const res = await fetch(`/api/object-views/views/${viewId}`, {
		method: "GET",
		credentials: "include",
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch view");
	}

	return res.json();
}

// CREATE A NEW VIEW
export async function createObjectView(
	payload: CreateViewPayload,
): Promise<ObjectView> {
	const res = await fetch("/api/object-views/views", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		credentials: "include",
		body: JSON.stringify(payload),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create view");
	}

	return res.json();
}

// DUPLICATE A VIEW
export async function duplicateObjectView(
	viewId: string,
	currentFilters?: Array<{ id: string; value: any }>,
): Promise<ObjectView> {
	const res = await fetch(`/api/object-views/views/${viewId}/duplicate`, {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		credentials: "include",
		body: JSON.stringify({ currentFilters }),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to duplicate view");
	}

	return res.json();
}

// DELETE A VIEW
export async function deleteObjectView(viewId: string): Promise<void> {
	const res = await fetch(`/api/object-views/views/${viewId}`, {
		method: "DELETE",
		credentials: "include",
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete view");
	}
}

// SERVER-SIDE FUNCTIONS (for use in Server Components)
export async function getDefaultView(
	objectType: ObjectType,
): Promise<ObjectView | null> {
	try {
		// This function is meant to be used on the server-side
		// We'll need to implement a server-side version that can access session data
		// For now, return null and implement proper server-side logic later
		return null;
	} catch (error) {
		console.error("Error fetching default view:", error);
		return null;
	}
}

// REACT QUERY HOOKS
export function useObjectViews(
	organizationId: string | undefined,
	objectType: ObjectType,
	enabled = true,
) {
	return useQuery({
		queryKey: ["objectViews", organizationId, objectType],
		queryFn: () => fetchObjectViews(organizationId!, objectType),
		enabled: !!organizationId && enabled,
		staleTime: 1000 * 30, // 30 seconds - more reactive to changes
	});
}

export function useObjectView(viewId: string | undefined) {
	return useQuery({
		queryKey: ["objectView", viewId],
		queryFn: () => fetchObjectView(viewId!),
		enabled: !!viewId,
		staleTime: 1000 * 30, // 30 seconds - more reactive to changes
	});
}

// USER VIEW PREFERENCES

// Get user's default view for an object type
export async function fetchUserDefaultView(
	objectType: ObjectType,
	organizationId?: string,
): Promise<ObjectView | null> {
	const params = new URLSearchParams({ objectType });
	if (organizationId) {
		params.append("organizationId", organizationId);
	}

	const res = await fetch(
		`/api/object-views/user-preferences/default?${params.toString()}`,
		{
			method: "GET",
			credentials: "include",
		},
	);

	if (!res.ok) {
		throw new Error("Failed to fetch user default view");
	}

	const data = await res.json();
	return data.defaultView;
}

// Set user's default view for an object type
export async function setUserDefaultView(
	objectType: ObjectType,
	viewId: string,
	organizationId?: string,
): Promise<void> {
	const body: any = { objectType, viewId };
	if (organizationId) {
		body.organizationId = organizationId;
	}

	const res = await fetch("/api/object-views/user-preferences/default", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		credentials: "include",
		body: JSON.stringify(body),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to set user default view");
	}
}

// Remove user's default view preference for an object type
export async function removeUserDefaultView(
	objectType: ObjectType,
	organizationId?: string,
): Promise<void> {
	const params = new URLSearchParams({ objectType });
	if (organizationId) {
		params.append("organizationId", organizationId);
	}

	const res = await fetch(
		`/api/object-views/user-preferences/default?${params.toString()}`,
		{
			method: "DELETE",
			credentials: "include",
		},
	);

	if (!res.ok) {
		throw new Error("Failed to remove user default view");
	}
}

// React Query hooks for user preferences
export function useUserDefaultView(
	objectType: ObjectType,
	organizationId?: string,
) {
	return useQuery({
		queryKey: ["userDefaultView", objectType, organizationId],
		queryFn: () => fetchUserDefaultView(objectType, organizationId),
		enabled: !!organizationId, // Only fetch if we have an organizationId
		staleTime: 1000 * 60 * 5, // 5 minutes
	});
}

export function useSetUserDefaultView() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			objectType,
			viewId,
			organizationId,
		}: {
			objectType: ObjectType;
			viewId: string;
			organizationId?: string;
		}) => setUserDefaultView(objectType, viewId, organizationId),
		onSuccess: (_, { objectType, organizationId }) => {
			queryClient.invalidateQueries({
				queryKey: ["userDefaultView", objectType, organizationId],
			});
		},
	});
}

export function useRemoveUserDefaultView() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			objectType,
			organizationId,
		}: {
			objectType: ObjectType;
			organizationId?: string;
		}) => removeUserDefaultView(objectType, organizationId),
		onSuccess: (_, { objectType, organizationId }) => {
			queryClient.invalidateQueries({
				queryKey: ["userDefaultView", objectType, organizationId],
			});
		},
	});
}

export function useCreateObjectView() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createObjectView,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["objectViews", data.organizationId, data.objectType],
			});
		},
	});
}

export function useDuplicateObjectView() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			viewId,
			currentFilters,
		}: {
			viewId: string;
			currentFilters?: Array<{ id: string; value: any }>;
		}) => duplicateObjectView(viewId, currentFilters),
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["objectViews", data.organizationId, data.objectType],
			});
		},
	});
}

export function useDeleteObjectView() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteObjectView,
		onSuccess: (_, viewId) => {
			// Invalidate all object views queries since we don't have organizationId/objectType in the response
			queryClient.invalidateQueries({
				queryKey: ["objectViews"],
			});
			// Also invalidate the specific view
			queryClient.invalidateQueries({
				queryKey: ["objectView", viewId],
			});
		},
	});
}

// QUERY KEYS (for consistency)
export const objectViewsQueryKey = (
	organizationId: string,
	objectType: ObjectType,
) => ["objectViews", organizationId, objectType];

export const objectViewQueryKey = (viewId: string) => ["objectView", viewId];
