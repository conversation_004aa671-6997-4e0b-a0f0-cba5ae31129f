import type { PropertyCreateInput } from "@repo/api/src/routes/properties/types";
import type { Property } from "@repo/database/src/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// PAYLOAD INTERFACES
export interface UpdatePropertyPayload {
	id: string;
	name?: string;
	image?: string;
	propertyType?: string;
	propertySubType?: string;
	market?: string;
	subMarket?: string;
	listingId?: string;
	status?: string;
	organizationId?: string;
	address?: {
		street?: string;
		street2?: string;
		city?: string;
		state?: string;
		zip?: string;
		county?: string;
		country?: string;
	};
	location?: {
		type: "Point";
		coordinates: [number, number]; // [longitude, latitude]
	};
	website?: string;
	neighborhood?: any;
	subdivision?: string;
	lotNumber?: string;
	parcelNumber?: string;
	zoning?: string;
	yearBuilt?: number;
	squareFootage?: number;
	units?: number;
	floors?: number;
	structures?: number;
	bedrooms?: number;
	bathrooms?: number;
	roomsCount?: number;
	buildingSquareFeet?: number;
	garageSquareFeet?: number;
	livingSquareFeet?: number;
	lotSquareFeet?: number;
	lotSize?: number;
	lotType?: string;
	lotAcres?: number;
	construction?: string;
	primaryUse?: string;
	propertyUse?: string;
	class?: string;
	parking?: string;
	parkingSpaces?: number;
	garageType?: string;
	heatingType?: string;
	meterType?: string;
	legalDescription?: string;
	price?: number;
	estimatedValue?: number;
	pricePerSquareFoot?: number;
	equity?: number;
	equityPercent?: number;
	estimatedEquity?: number;
	saleDate?: Date;
	salePrice?: number;
	lastSalePrice?: number;
	lastSaleDate?: Date;
	landValue?: number;
	buildingValue?: number;
	cap?: number;
	exchange?: boolean;
	exchangeId?: string;
	taxInfo?: any;
	// Boolean flags
	absenteeOwner?: boolean;
	inStateAbsenteeOwner?: boolean;
	outOfStateAbsenteeOwner?: boolean;
	ownerOccupied?: boolean;
	corporateOwned?: boolean;
	vacant?: boolean;
	mobileHome?: boolean;
	carport?: boolean;
	auction?: boolean;
	cashBuyer?: boolean;
	investorBuyer?: boolean;
	freeClear?: boolean;
	highEquity?: boolean;
	privateLender?: boolean;
	deedInLieu?: boolean;
	quitClaim?: boolean;
	sheriffsDeed?: boolean;
	warrantyDeed?: boolean;
	inherited?: boolean;
	spousalDeath?: boolean;
	lien?: boolean;
	taxLien?: boolean;
	preForeclosure?: boolean;
	trusteeSale?: boolean;
	floodZone?: boolean;
	// MLS Data
	mlsActive?: boolean;
	mlsCancelled?: boolean;
	mlsFailed?: boolean;
	mlsHasPhotos?: boolean;
	mlsPending?: boolean;
	mlsSold?: boolean;
	mlsDaysOnMarket?: number;
	mlsListingPrice?: number;
	mlsListingPricePerSquareFoot?: number;
	mlsSoldPrice?: number;
	mlsStatus?: string;
	mlsType?: string;
	mlsListingDate?: string;
	// Legal & Environmental
	floodZoneDescription?: string;
	floodZoneType?: string;
	noticeType?: string;
	reaId?: string;
	lastUpdateDate?: string;
	// Demographics
	fmrEfficiency?: number;
	fmrFourBedroom?: number;
	fmrOneBedroom?: number;
	fmrThreeBedroom?: number;
	fmrTwoBedroom?: number;
	fmrYear?: number;
	hudAreaCode?: string;
	hudAreaName?: string;
	medianIncome?: number;
	suggestedRent?: number;
	// REA API data
	reaApiData?: any;
	// Legacy/Optional fields
	description?: string;
	customFields?: any;
}

// Types for related entities
export interface PropertyUnitMixPayload {
	id?: string;
	propertyId: string;
	organizationId: string;
	name: string;
	units?: number;
	minSquareFootage?: number;
	maxSquareFootage?: number;
	minPrice?: number;
	maxPrice?: number;
	minRent?: number;
	maxRent?: number;
}

export interface PropertySaleHistoryPayload {
	id?: string;
	propertyId: string;
	organizationId: string;
	seller?: string;
	buyer?: string;
	saleDate?: Date;
	salePrice?: number;
	askingPrice?: number;
	transactionType?: string;
	pricePerSquareFoot?: number;
	pricePerUnit?: number;
	transferredOwnershipPercentage?: number;
	capRate?: number;
	grmRate?: number;
}

export interface PropertyMortgagePayload {
	id?: string;
	propertyId: string;
	organizationId: string;
	amount?: number;
	assumable?: boolean;
	deedType?: any;
	documentDate?: string;
	documentNumber?: any;
	granteeName?: string;
	interestRate?: number;
	interestRateType?: string;
	lenderCode?: string;
	lenderName?: string;
	lenderType?: string;
	loanType?: string;
	loanTypeCode?: string;
	maturityDate?: string;
	mortgageId?: number;
	open?: boolean;
	position?: string;
	recordingDate?: string;
	seqNo?: number;
	term?: number;
	termType?: string;
	transactionType?: string;
}

// CREATE PROPERTY
export async function createProperty(
	payload: PropertyCreateInput,
): Promise<Property> {
	const response = await fetch("/api/objects/properties", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(payload),
	});

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({}));
		throw new Error(errorData.error || "Failed to create property");
	}

	const result = await response.json();
	return result.property;
}

// FETCH ALL PROPERTIES
export async function fetchProperties(
	organizationId?: string,
): Promise<Property[]> {
	const url = organizationId
		? `/api/objects/properties?organizationId=${organizationId}&limit=100&offset=0`
		: "/api/objects/properties";

	const res = await fetch(url);
	if (!res.ok) {
		const errorText = await res.text();
		console.error("Properties API error response:", errorText);
		throw new Error("Failed to fetch properties");
	}
	const result = await res.json();
	return result.properties || [];
}

// FETCH SINGLE PROPERTY
export async function fetchProperty(id: string, organizationId: string): Promise<Property> {
	const res = await fetch(`/api/objects/properties/${id}?organizationId=${organizationId}`);
	if (!res.ok) {
		const error = await res.json().catch(() => ({}));
		
		// Enhance error with more context for 404s
		if (res.status === 404) {
			// Check if the error message suggests access issues vs not found
			const errorMessage = error.error || "Not found";
			if (errorMessage.toLowerCase().includes("organization") || errorMessage.toLowerCase().includes("access") || errorMessage.toLowerCase().includes("permission")) {
				const accessError = new Error("ACCESS_DENIED");
				(accessError as any).status = 403;
				(accessError as any).originalMessage = errorMessage;
				throw accessError;
			} else {
				const notFoundError = new Error("PROPERTY_NOT_FOUND");
				(notFoundError as any).status = 404;
				(notFoundError as any).originalMessage = errorMessage;
				throw notFoundError;
			}
		}
		
		throw new Error(error.error || "Failed to fetch property");
	}
	const result = await res.json();
	return result.property;
}

// UPDATE PROPERTY
export async function updateProperty(
	payload: UpdatePropertyPayload,
): Promise<Property> {
	const { id, ...data } = payload;
	const res = await fetch(`/api/objects/properties/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update property");
	}
	return res.json();
}

// DELETE PROPERTY
export async function deleteProperty(id: string): Promise<void> {
	const res = await fetch(`/api/objects/properties/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete property");
	}
}

// BATCH DELETE PROPERTIES
export async function batchDeleteProperties(payload: {
	ids: string[];
	organizationId: string;
}): Promise<{ deletedCount: number; message: string }> {
	const res = await fetch("/api/objects/properties/delete-batch", {
		method: "DELETE",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(payload),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete properties");
	}
	return res.json();
}

export async function updatePropertyField(payload: {
	id: string;
	field: string;
	value: any;
	organizationId: string;
}): Promise<Property> {
	const res = await fetch(`/api/objects/properties/${payload.id}/field`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify({
			field: payload.field,
			value: payload.value,
			organizationId: payload.organizationId,
		}),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update property field");
	}
	const result = await res.json();
	
	// Handle different response formats and ensure we have a valid property
	const property = result.property || result;
	
	// Check if we got an empty response or invalid property data
	if (!property || typeof property !== 'object' || !property.id) {
		console.error("Invalid API response for updatePropertyField:", {
			status: res.status,
			url: res.url,
			result,
			property,
			payload,
		});
		
		// If the API returned success but empty data, still throw an error
		// but don't crash the UI - let the mutation handle the error gracefully
		if (Object.keys(result).length === 0) {
			throw new Error("Server returned empty response. The update may have succeeded but we couldn't retrieve the updated property data.");
		} else {
			throw new Error("Invalid response from server - property data is missing or malformed");
		}
	}
	
	return property;
}

export async function clearPropertyField(payload: {
	id: string;
	field: string;
	organizationId: string;
}): Promise<Property> {
	const res = await fetch(
		`/api/objects/properties/${payload.id}/field/${payload.field}?organizationId=${payload.organizationId}`,
		{
			method: "DELETE",
		},
	);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to clear property field");
	}
	const result = await res.json();
	
	// Handle different response formats and ensure we have a valid property
	const property = result.property || result;
	
	// Check if we got an empty response or invalid property data
	if (!property || typeof property !== 'object' || !property.id) {
		console.error("Invalid API response for clearPropertyField:", {
			status: res.status,
			url: res.url,
			result,
			property,
			payload,
		});
		
		// If the API returned success but empty data, still throw an error
		// but don't crash the UI - let the mutation handle the error gracefully
		if (Object.keys(result).length === 0) {
			throw new Error("Server returned empty response. The update may have succeeded but we couldn't retrieve the updated property data.");
		} else {
			throw new Error("Invalid response from server - property data is missing or malformed");
		}
	}
	
	return property;
}

export async function searchProperties(
	query: string,
	organizationId: string,
): Promise<Property[]> {
	if (!query.trim()) return [];

	const response = await fetch(
		`/api/objects/properties?search=${encodeURIComponent(query)}&organizationId=${organizationId}`,
	);

	if (!response.ok) {
		throw new Error("Failed to search properties");
	}

	const result = await response.json();
	return result.properties || [];
}

// REA API FUNCTIONS
export async function fetchPropertyInfo(address: string): Promise<any> {
	const response = await fetch("/api/properties/rea/property-info", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ address }),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to fetch property info from REA");
	}

	return response.json();
}

export async function fetchPropertyBoundary(payload: {
	address?: string;
	id?: number;
	lat?: number;
	lng?: number;
}): Promise<any> {
	const response = await fetch("/api/properties/rea/property-boundary", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(payload),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to fetch property boundary");
	}

	return response.json();
}

export async function fetchPropertyAutocomplete(query: string): Promise<any> {
	const response = await fetch("/api/properties/rea/autocomplete", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ query }),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to fetch property autocomplete");
	}

	return response.json();
}

// RELATED ENTITIES FUNCTIONS
export async function fetchPropertyUnitMixes(propertyId: string, organizationId: string): Promise<PropertyUnitMixPayload[]> {
	const res = await fetch(`/api/objects/properties/${propertyId}/unit-mixes?organizationId=${organizationId}`);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch property unit mixes");
	}
	const result = await res.json();
	return result.unitMixes || [];
}

export async function fetchPropertySaleHistory(propertyId: string, organizationId: string): Promise<PropertySaleHistoryPayload[]> {
	const res = await fetch(`/api/objects/properties/${propertyId}/sale-history?organizationId=${organizationId}`);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch property sale history");
	}
	const result = await res.json();
	return result.saleHistory || [];
}

export async function fetchPropertyMortgages(propertyId: string, organizationId: string): Promise<PropertyMortgagePayload[]> {
	const res = await fetch(`/api/objects/properties/${propertyId}/mortgages?organizationId=${organizationId}`);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch property mortgages");
	}
	const result = await res.json();
	return result.mortgages || [];
}

// REACT QUERY HOOKS
export function useCreateProperty() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createProperty,
		onSuccess: (newProperty: Property, variables) => {
			// Update all infinite queries optimistically
			queryClient.setQueriesData(
				{
					queryKey: ["properties-infinite", variables.organizationId],
					exact: false,
				},
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const firstPage = oldData.pages[0];
					if (firstPage?.data) {
						const updatedFirstPage = {
							...firstPage,
							data: [newProperty, ...firstPage.data],
							meta: {
								...firstPage.meta,
								totalRowCount:
									(firstPage.meta?.totalRowCount || 0) + 1,
								filterRowCount:
									(firstPage.meta?.filterRowCount || 0) + 1,
							},
						};

						return {
							...oldData,
							pages: [
								updatedFirstPage,
								...oldData.pages.slice(1),
							],
						};
					}
					return oldData;
				},
			);

			// Update regular properties query
			queryClient.setQueryData(
				["properties", variables.organizationId],
				(old: Property[] = []) => [newProperty, ...old],
			);

			// Reset queries completely to avoid cursor timing issues
			queryClient.removeQueries({
				queryKey: ["properties-infinite", variables.organizationId],
				exact: false,
			});

			queryClient.invalidateQueries({
				queryKey: ["properties", variables.organizationId],
				exact: false,
			});

			queryClient.invalidateQueries({
				queryKey: ["objects", "properties"],
				exact: false,
			});
		},
	});
}

export function useUpdateProperty(organizationId?: string) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (payload: UpdatePropertyPayload) => updateProperty({ ...payload, organizationId }),
		onSuccess: (updatedProperty: Property) => {
			queryClient.setQueryData(
				["property", updatedProperty.id],
				updatedProperty,
			);

			queryClient.setQueriesData(
				{ queryKey: ["properties"], exact: false },
				(oldProperties: Property[] | undefined) => {
					if (!oldProperties || !Array.isArray(oldProperties)) {
						return oldProperties;
					}

					return oldProperties.map((property) => {
						if (property.id === updatedProperty.id) {
							return { ...property, ...updatedProperty };
						}
						return property;
					});
				},
			);
		},
		onMutate: async (variables: UpdatePropertyPayload) => {
			await queryClient.cancelQueries({
				queryKey: ["property", variables.id],
			});
			await queryClient.cancelQueries({
				queryKey: ["properties"],
				exact: false,
			});

			const previousProperty = queryClient.getQueryData([
				"property",
				variables.id,
			]);

			const allPropertiesQueries = queryClient.getQueriesData({
				queryKey: ["properties"],
				exact: false,
			});

			if (previousProperty) {
				const optimisticProperty = {
					...(previousProperty as Property),
					...variables,
				};
				queryClient.setQueryData(
					["property", variables.id],
					optimisticProperty,
				);

				queryClient.setQueriesData(
					{ queryKey: ["properties"], exact: false },
					(oldProperties: Property[] | undefined) => {
						if (!oldProperties || !Array.isArray(oldProperties)) {
							return oldProperties;
						}

						return oldProperties.map((property) => {
							if (property.id === variables.id) {
								return { ...property, ...variables };
							}
							return property;
						});
					},
				);
			}

			return { previousProperty, allPropertiesQueries };
		},
		onError: (err, variables, context) => {
			if (context?.previousProperty) {
				queryClient.setQueryData(
					["property", variables.id],
					context.previousProperty,
				);
			}
			if (context?.allPropertiesQueries) {
				context.allPropertiesQueries.forEach(([queryKey, data]) => {
					queryClient.setQueryData(queryKey, data);
				});
			}
		},
	});
}

export function useDeleteProperty() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: deleteProperty,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["properties-infinite"],
				exact: false,
			});
			queryClient.invalidateQueries({
				queryKey: ["properties"],
				exact: false,
			});
		},
	});
}

export function useBatchDeleteProperties() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: batchDeleteProperties,
		onSuccess: (data, variables) => {
			queryClient.setQueriesData(
				{
					queryKey: ["properties-infinite", variables.organizationId],
					exact: false,
				},
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.filter(
									(property: Property) =>
										!variables.ids.includes(property.id),
								),
								meta: {
									...page.meta,
									totalRowCount: Math.max(
										0,
										(page.meta?.totalRowCount || 0) -
											data.deletedCount,
									),
									filterRowCount: Math.max(
										0,
										(page.meta?.filterRowCount || 0) -
											data.deletedCount,
									),
								},
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				},
			);

			// Update all infinite query instances
			const allPropertyQueries = queryClient.getQueryCache().findAll({
				queryKey: ["properties-infinite", variables.organizationId],
				exact: false,
			});

			allPropertyQueries.forEach((query) => {
				queryClient.setQueryData(query.queryKey, (oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.filter(
									(property: Property) =>
										!variables.ids.includes(property.id),
								),
								meta: {
									...page.meta,
									totalRowCount: Math.max(
										0,
										(page.meta?.totalRowCount || 0) -
											data.deletedCount,
									),
									filterRowCount: Math.max(
										0,
										(page.meta?.filterRowCount || 0) -
											data.deletedCount,
									),
								},
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				});
			});

			// Update regular properties queries
			queryClient.setQueryData(
				["properties", variables.organizationId],
				(old: Property[] = []) =>
					old.filter(
						(property) => !variables.ids.includes(property.id),
					),
			);

			// Invalidate all related queries
			setTimeout(() => {
				queryClient.invalidateQueries({
					queryKey: ["properties-infinite", variables.organizationId],
					exact: false,
					refetchType: "all",
				});

				queryClient.invalidateQueries({
					queryKey: ["properties", variables.organizationId],
					exact: false,
				});

				queryClient.invalidateQueries({
					queryKey: ["objects", "properties"],
					exact: false,
				});
			}, 500);
		},
	});
}

export function useUpdatePropertyField() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: updatePropertyField,
		onSuccess: (updatedProperty: Property, variables) => {
			// Defensive check to ensure updatedProperty exists and has an id
			if (!updatedProperty || !updatedProperty.id) {
				console.error("Invalid property data in onSuccess:", updatedProperty);
				return;
			}
			
			queryClient.setQueryData(
				["property", updatedProperty.id],
				updatedProperty,
			);

			// Update regular properties queries
			queryClient.setQueriesData(
				{ queryKey: ["properties"], exact: false },
				(oldProperties: Property[] | undefined) => {
					if (!oldProperties || !Array.isArray(oldProperties)) {
						return oldProperties;
					}

					return oldProperties.map((property) => {
						if (property.id === updatedProperty.id) {
							return { ...property, ...updatedProperty };
						}
						return property;
					});
				},
			);

			// Update infinite property queries
			queryClient.setQueriesData(
				{ queryKey: ["properties-infinite"], exact: false },
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.map((property: Property) => {
									if (property.id === updatedProperty.id) {
										return { ...property, ...updatedProperty };
									}
									return property;
								}),
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				}
			);
		},
	});
}

export function useClearPropertyField() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: clearPropertyField,
		onSuccess: (updatedProperty: Property, variables) => {
			// Defensive check to ensure updatedProperty exists and has an id
			if (!updatedProperty || !updatedProperty.id) {
				console.error("Invalid property data in onSuccess:", updatedProperty);
				return;
			}
			
			queryClient.setQueryData(
				["property", updatedProperty.id],
				updatedProperty,
			);

			queryClient.setQueriesData(
				{ queryKey: ["properties"], exact: false },
				(oldProperties: Property[] | undefined) => {
					if (!oldProperties || !Array.isArray(oldProperties)) {
						return oldProperties;
					}

					return oldProperties.map((property) => {
						if (property.id === updatedProperty.id) {
							return { ...property, ...updatedProperty };
						}
						return property;
					});
				},
			);

			// Update infinite property queries
			queryClient.setQueriesData(
				{ queryKey: ["properties-infinite"], exact: false },
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.map((property: Property) => {
									if (property.id === updatedProperty.id) {
										return { ...property, ...updatedProperty };
									}
									return property;
								}),
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				}
			);

			// NO QUERY INVALIDATION - let the optimistic updates handle cache sync
			// The above cache updates ensure data consistency without triggering refetches
		},
	});
}

export function useProperties(organizationId?: string) {
	return useQuery({
		queryKey: ["properties", organizationId],
		queryFn: () => fetchProperties(organizationId),
		staleTime: 1000 * 60,
		enabled: !!organizationId,
	});
}

export function useProperty(id: string | undefined, organizationId?: string) {
	return useQuery({
		queryKey: ["property", id],
		queryFn: () => (id && organizationId ? fetchProperty(id, organizationId) : Promise.resolve(null)),
		enabled: !!id && !!organizationId,
		staleTime: 1000 * 30, // 30 seconds
	});
}

export function useSearchProperties(query: string, organizationId: string) {
	return useQuery({
		queryKey: ["properties", "search", query, organizationId],
		queryFn: () => searchProperties(query, organizationId),
		enabled: query.length > 0 && !!organizationId,
		staleTime: 30000, // Cache for 30 seconds
	});
}

// REA API HOOKS
export function usePropertyInfo() {
	return useMutation({
		mutationFn: fetchPropertyInfo,
	});
}

export function usePropertyBoundary() {
	return useMutation({
		mutationFn: fetchPropertyBoundary,
	});
}

export function usePropertyAutocomplete() {
	return useMutation({
		mutationFn: fetchPropertyAutocomplete,
	});
}

// RELATED ENTITIES HOOKS
export function usePropertyUnitMixes(propertyId: string | undefined, organizationId?: string) {
	return useQuery<PropertyUnitMixPayload[]>({
		queryKey: ["property-unit-mixes", propertyId, organizationId],
		queryFn: () => (propertyId && organizationId ? fetchPropertyUnitMixes(propertyId, organizationId) : Promise.resolve([])),
		enabled: !!propertyId && !!organizationId,
		staleTime: 1000 * 30, // 30 seconds
	});
}

export function usePropertySaleHistory(propertyId: string | undefined, organizationId?: string) {
	return useQuery<PropertySaleHistoryPayload[]>({
		queryKey: ["property-sale-history", propertyId, organizationId],
		queryFn: () => (propertyId && organizationId ? fetchPropertySaleHistory(propertyId, organizationId) : Promise.resolve([])),
		enabled: !!propertyId && !!organizationId,
		staleTime: 1000 * 30, // 30 seconds
	});
}

export function usePropertyMortgages(propertyId: string | undefined, organizationId?: string) {
	return useQuery<PropertyMortgagePayload[]>({
		queryKey: ["property-mortgages", propertyId, organizationId],
		queryFn: () => (propertyId && organizationId ? fetchPropertyMortgages(propertyId, organizationId) : Promise.resolve([])),
		enabled: !!propertyId && !!organizationId,
		staleTime: 1000 * 30, // 30 seconds
	});
}
