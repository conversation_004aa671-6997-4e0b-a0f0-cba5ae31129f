export interface GeocodeResult {
	success: boolean;
	coordinates?: [number, number]; // [lng, lat]
	address?: string;
	confidence?: number;
	error?: string;
}

export async function geocodeAddress(address: string): Promise<GeocodeResult> {
	if (!address.trim()) {
		return { success: false, error: "Address is required" };
	}

	try {
		const response = await fetch(
			`/api/geocode?address=${encodeURIComponent(address)}`,
		);

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "Geocoding failed");
		}

		const result = await response.json();
		return result;
	} catch (error) {
		console.error("Geocoding error:", error);
		return {
			success: false,
			error:
				error instanceof Error
					? error.message
					: "Failed to geocode address",
		};
	}
}

export function formatAddressForGeocoding(address: {
	street?: string;
	street2?: string;
	city?: string;
	state?: string;
	zip?: string;
	country?: string;
}): string {
	const parts = [
		address.street,
		address.street2,
		address.city,
		address.state,
		address.zip,
		address.country,
	].filter(Boolean);

	return parts.join(", ");
}
