"use client";

import { CreatePropertyModal } from "@app/properties/components/CreatePropertyModal";
import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import * as React from "react";

interface PropertiesContextType {
	openCreateProperty: (initialValues?: Record<string, any>) => void;
}

const PropertiesContext = React.createContext<
	PropertiesContextType | undefined
>(undefined);

export function useProperties() {
	const context = React.useContext(PropertiesContext);
	if (!context) {
		throw new Error(
			"useProperties must be used within a PropertiesProvider",
		);
	}
	return context;
}

export function PropertiesProvider({
	children,
}: {
	children: React.ReactNode;
}) {
	const [isCreateModalOpen, setIsCreateModalOpen] = React.useState(false);
	const [initialValues, setInitialValues] = React.useState<
		Record<string, any> | undefined
	>(undefined);

	useHotkeys([
		{
			key: "p",
			callback: () => {
				if (!isCreateModalOpen) {
					setIsCreateModalOpen(true);
				}
			},
			ignoreInputs: true,
			preventModifiers: {
				ctrlKey: true,
				altKey: true,
				metaKey: true,
			},
		},
	]);

	const openCreateProperty = React.useCallback(
		(values?: Record<string, any>) => {
			setInitialValues(values);
			setIsCreateModalOpen(true);
		},
		[],
	);

	const handleModalOpenChange = (open: boolean) => {
		setIsCreateModalOpen(open);
		if (!open) {
			setInitialValues(undefined);
		}
	};

	return (
		<PropertiesContext.Provider value={{ openCreateProperty }}>
			{children}
			<CreatePropertyModal
				isOpen={isCreateModalOpen}
				onClose={() => handleModalOpenChange(false)}
			/>
		</PropertiesContext.Provider>
	);
}
