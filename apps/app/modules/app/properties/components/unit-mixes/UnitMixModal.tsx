"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { formatNumberWithCommas, removeCommasFromNumber } from "@shared/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { IconSquareRoundedCheck, IconTrash } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import React, { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useCreateUnitMix, useUpdateUnitMix, useDeleteUnitMix, type UnitMix } from "@app/unit-mixes/lib/api";
import { useParams } from "next/navigation";

interface UnitMixModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	editData?: UnitMix | null;
	propertyId?: string;
}

const unitMixSchema = z.object({
	name: z.string().min(1, "Name is required"),
	units: z.number().optional(),
	minSquareFootage: z.number().optional(),
	maxSquareFootage: z.number().optional(),
	minPrice: z.number().optional(),
	maxPrice: z.number().optional(),
	minRent: z.number().optional(),
	maxRent: z.number().optional(),
});

type UnitMixFormData = z.infer<typeof unitMixSchema>;

export function UnitMixModal({
	open,
	onOpenChange,
	editData,
	propertyId: propPropertyId,
}: UnitMixModalProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const params = useParams();
	
	// Get propertyId from props or params
	const propertyId = propPropertyId || (params?.id as string);
	
	const createUnitMix = useCreateUnitMix();
	const updateUnitMix = useUpdateUnitMix();
	const deleteUnitMix = useDeleteUnitMix();

	const [isSubmitting, setIsSubmitting] = useState(false);
	const isEdit = !!editData;

	const form = useForm<UnitMixFormData>({
		resolver: zodResolver(unitMixSchema),
		mode: "onSubmit",
		reValidateMode: "onSubmit",
		defaultValues: {
			name: "",
			units: undefined,
			minSquareFootage: undefined,
			maxSquareFootage: undefined,
			minPrice: undefined,
			maxPrice: undefined,
			minRent: undefined,
			maxRent: undefined,
		},
	});

	// Apply edit data when modal opens or editData changes
	React.useEffect(() => {
		if (open && editData) {
			form.reset({
				name: editData.name || "",
				units: editData.units || undefined,
				minSquareFootage: editData.minSquareFootage || undefined,
				maxSquareFootage: editData.maxSquareFootage || undefined,
				minPrice: editData.minPrice || undefined,
				maxPrice: editData.maxPrice || undefined,
				minRent: editData.minRent || undefined,
				maxRent: editData.maxRent || undefined,
			});
		}
	}, [open, editData, form]);

	// Reset form when modal opens/closes
	React.useEffect(() => {
		if (!open) {
			form.reset({
				name: "",
				units: undefined,
				minSquareFootage: undefined,
				maxSquareFootage: undefined,
				minPrice: undefined,
				maxPrice: undefined,
				minRent: undefined,
				maxRent: undefined,
			});
		}
	}, [open, form]);

	async function onSubmit(formData: UnitMixFormData) {
		// Check for form validation errors
		if (Object.keys(form.formState.errors).length > 0) {
			const firstError = Object.values(form.formState.errors)[0];
			toast.error(
				firstError?.message ||
					"Please fix the form errors before submitting",
			);
			return;
		}

		if (!activeOrganization?.id || !user?.id || !propertyId) {
			toast.error("Missing organization, user, or property data");
			return;
		}

		setIsSubmitting(true);

		try {
			if (isEdit && editData?.id) {
				// Update existing unit mix
				await updateUnitMix.mutateAsync({
					id: editData.id,
					data: {
						name: formData.name,
						units: formData.units,
						minSquareFootage: formData.minSquareFootage,
						maxSquareFootage: formData.maxSquareFootage,
						minPrice: formData.minPrice,
						maxPrice: formData.maxPrice,
						minRent: formData.minRent,
						maxRent: formData.maxRent,
					},
				});
				toast.success("Unit mix updated successfully");
			} else {
				// Create new unit mix
				await createUnitMix.mutateAsync({
					organizationId: activeOrganization.id,
					propertyId: propertyId,
					name: formData.name,
					units: formData.units,
					minSquareFootage: formData.minSquareFootage,
					maxSquareFootage: formData.maxSquareFootage,
					minPrice: formData.minPrice,
					maxPrice: formData.maxPrice,
					minRent: formData.minRent,
					maxRent: formData.maxRent,
				});
				toast.success("Unit mix created successfully");
			}

			setIsSubmitting(false);
			onOpenChange(false);
		} catch (error: any) {
			console.error("Unit mix submission error:", error);
			toast.error(error?.message || "Failed to save unit mix");
			setIsSubmitting(false);
		}
	}

	async function handleDelete() {
		if (!editData?.id || !activeOrganization?.id) {
			toast.error("Missing data for deletion");
			return;
		}

		try {
			await deleteUnitMix.mutateAsync({
				id: editData.id,
				organizationId: activeOrganization.id,
			});
			toast.success("Unit mix deleted successfully");
			onOpenChange(false);
		} catch (error: any) {
			console.error("Unit mix deletion error:", error);
			toast.error(error?.message || "Failed to delete unit mix");
		}
	}

	// Show form errors as toast messages
	React.useEffect(() => {
		const errors = form.formState.errors;
		if (Object.keys(errors).length > 0) {
			const firstError = Object.values(errors)[0];
			if (firstError?.message) {
				toast.error(firstError.message as string);
			}
		}
	}, [form.formState.errors]);

	const isLoading = createUnitMix.isPending || updateUnitMix.isPending || deleteUnitMix.isPending || isSubmitting;

	// Custom number input component with comma formatting
	const NumberInput = ({ value, onChange, placeholder }: { value?: number; onChange: (value: number | undefined) => void; placeholder: string }) => (
		<Input
			placeholder={placeholder}
			value={value !== undefined ? formatNumberWithCommas(value) : ""}
			onChange={(e) => {
				const numericValue = removeCommasFromNumber(e.target.value);
				// Handle null case by converting to undefined
				onChange(numericValue === null ? undefined : numericValue);
			}}
			className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
		/>
	);

	return (
		<StandardizedModal
			open={open}
			onOpenChange={onOpenChange}
			title={isEdit ? "Edit Unit Mix" : "Add Unit Mix"}
			description={isEdit ? "Edit unit mix details" : "Create a new unit mix"}
			icon={<IconSquareRoundedCheck className="h-5 w-5" />}
			maxWidth="lg"
			preventOutsideClick={true}
			footer={
				<StandardizedModalFooter
					leftContent={
						isEdit && (
							<Button
								type="button"
								variant="error"
								size="sm"
								onClick={handleDelete}
								disabled={isLoading}
								className="flex items-center gap-2"
							>
								<IconTrash className="h-4 w-4" />
								Delete
							</Button>
						)
					}
				>
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={() => onOpenChange(false)}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						type="button"
						variant="action"
						size="sm"
						disabled={isLoading}
						onClick={form.handleSubmit(onSubmit)}
					>
						{isLoading ? (
							<div className="flex items-center gap-2">
								<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
								<span>{isEdit ? "Saving..." : "Creating..."}</span>
							</div>
						) : (
							isEdit ? "Save Unit Mix" : "Add Unit Mix"
						)}
					</Button>
				</StandardizedModalFooter>
			}
		>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
				{/* Name and Units Row */}
				<div className="flex gap-4">
					<div className="flex-[3] space-y-1">
						<Label htmlFor="name" className="text-xs text-muted-foreground">
							Name
							<span className="text-red-500 ml-1">*</span>
						</Label>
						<Input
							id="name"
							{...form.register("name")}
							placeholder="Unit mix name"
							className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
						/>
						{form.formState.errors.name && (
							<span className="text-red-500 text-xs">
								{form.formState.errors.name.message}
							</span>
						)}
					</div>
					<div className="flex-1 space-y-1">
						<Label htmlFor="units" className="text-xs text-muted-foreground">
							Units
						</Label>
						<Controller
							name="units"
							control={form.control}
							render={({ field }) => (
								<NumberInput
									value={field.value}
									onChange={field.onChange}
									placeholder="Units"
								/>
							)}
						/>
					</div>
				</div>

				{/* Square Footage Row */}
				<div className="space-y-1">
					<Label className="text-xs text-muted-foreground">Square Footage</Label>
					<div className="flex gap-4">
						<div className="flex-1">
							<Controller
								name="minSquareFootage"
								control={form.control}
								render={({ field }) => (
									<NumberInput
										value={field.value}
										onChange={field.onChange}
										placeholder="Min Sq Ft"
									/>
								)}
							/>
						</div>
						<div className="flex-1">
							<Controller
								name="maxSquareFootage"
								control={form.control}
								render={({ field }) => (
									<NumberInput
										value={field.value}
										onChange={field.onChange}
										placeholder="Max Sq Ft"
									/>
								)}
							/>
						</div>
					</div>
				</div>

				{/* Price Row */}
				<div className="space-y-1">
					<Label className="text-xs text-muted-foreground">Price</Label>
					<div className="flex gap-4">
						<div className="flex-1">
							<Controller
								name="minPrice"
								control={form.control}
								render={({ field }) => (
									<NumberInput
										value={field.value}
										onChange={field.onChange}
										placeholder="Min Price"
									/>
								)}
							/>
						</div>
						<div className="flex-1">
							<Controller
								name="maxPrice"
								control={form.control}
								render={({ field }) => (
									<NumberInput
										value={field.value}
										onChange={field.onChange}
										placeholder="Max Price"
									/>
								)}
							/>
						</div>
					</div>
				</div>

				{/* Rent Row */}
				<div className="space-y-1">
					<Label className="text-xs text-muted-foreground">Rent</Label>
					<div className="flex gap-4">
						<div className="flex-1">
							<Controller
								name="minRent"
								control={form.control}
								render={({ field }) => (
									<NumberInput
										value={field.value}
										onChange={field.onChange}
										placeholder="Min Rent"
									/>
								)}
							/>
						</div>
						<div className="flex-1">
							<Controller
								name="maxRent"
								control={form.control}
								render={({ field }) => (
									<NumberInput
										value={field.value}
										onChange={field.onChange}
										placeholder="Max Rent"
									/>
								)}
							/>
						</div>
					</div>
				</div>
			</form>
		</StandardizedModal>
	);
} 