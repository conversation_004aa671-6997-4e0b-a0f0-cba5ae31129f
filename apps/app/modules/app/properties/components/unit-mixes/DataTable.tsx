"use client";

import React from "react";
import {
	type ColumnDef,
	flexRender,
	type HeaderGroup,
	type Row,
	type Table,
} from "@tanstack/react-table";
import { Skeleton } from "@ui/components/skeleton";
import {
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	Table as UITable,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import EmptyContainer from "@app/shared/components/EmptyContainer";
import { IconListNumbers } from "@tabler/icons-react";
import type { UnitMix } from "@app/unit-mixes/lib/api";

interface UnitMixTableProps {
	columns: ColumnDef<UnitMix>[];
	table: Table<UnitMix>;
	isLoading: boolean;
	onEditUnitMix?: (unitMix?: UnitMix) => void;
}

export default function UnitMixTable({
	columns,
	table,
	isLoading,
	onEditUnitMix,
}: UnitMixTableProps) {
	return (
		<DataTable
			columns={columns}
			table={table}
			isLoading={isLoading}
			onEditUnitMix={onEditUnitMix}
		/>
	);
}

interface DataTableProps {
	columns: ColumnDef<UnitMix>[];
	table: Table<UnitMix>;
	isLoading: boolean;
	onEditUnitMix?: (unitMix?: UnitMix) => void;
}

export function DataTable({
	columns,
	table,
	isLoading,
	onEditUnitMix,
}: DataTableProps) {
	const [selectedUnitMix, setSelectedUnitMix] = React.useState<UnitMix | null>(null);
	const [isModalOpen, setIsModalOpen] = React.useState(false);

	const TableSkeleton = ({ columns }: { columns: ColumnDef<UnitMix>[] }) => (
		<>
			{[...Array(5)].map((_, index) => (
				<TableRow key={index}>
					{columns.map((column, cellIndex) => (
						<TableCell key={cellIndex} className="px-4 py-2">
							<Skeleton className="h-6 w-full" />
						</TableCell>
					))}
				</TableRow>
			))}
		</>
	);

	const handleRowClick = (row: Row<UnitMix>) => {
		onEditUnitMix?.(row.original);
	};

	const handleModalOpenChange = (open: boolean) => {
		setIsModalOpen(open);
		if (!open) {
			setSelectedUnitMix(null);
		}
	};

	const rows = table.getRowModel().rows;
	const hasData = rows && rows.length > 0;

	return (
		<div className="space-y-4">
			{/* TODO: Add unit mix modal when ready */}
			{/* {user && (
				<UnitMixModal
					open={isModalOpen}
					onOpenChange={handleModalOpenChange}
					unitMixToEdit={selectedUnitMix}
				/>
			)} */}
			
			<div className="overflow-auto">
				<UITable>
					<TableHeader>
						{table.getHeaderGroups().map((headerGroup: HeaderGroup<UnitMix>) => (
							<TableRow
								key={headerGroup.id}
								className="border-b hover:bg-transparent"
							>
								{headerGroup.headers.map((header) => (
									<TableHead
										key={header.id}
										colSpan={header.colSpan}
										className="px-4 text-xs font-medium text-zinc-500 !h-8"
									>
										<div className="flex items-center h-full w-full">
											{header.isPlaceholder
												? null
												: flexRender(
														header.column.columnDef.header,
														header.getContext(),
													)}
										</div>
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{isLoading ? (
							<TableSkeleton columns={columns} />
						) : hasData ? (
							rows.map((row: Row<UnitMix>) => (
								<TableRow
									key={row.id}
									onClick={() => handleRowClick(row)}
									data-state={row.getIsSelected() && "selected"}
									className={cn(
										"cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
										"bg-white dark:bg-transparent"
									)}
								>
									{row.getVisibleCells().map((cell) => (
										<TableCell 
											key={cell.id}
											className="px-4 py-2"
										>
											{flexRender(
												cell.column.columnDef.cell,
												cell.getContext()
											)}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell
									colSpan={columns.length}
									className="h-auto text-center rounded-b-lg"
								>
									<EmptyContainer
										title="No unit mixes found"
										subtitle="You have no unit mixes for this property."
										button="Add unit mix"
										onClick={() => {
											onEditUnitMix?.();
										}}
										icon={IconListNumbers}
									/>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</UITable>
			</div>
		</div>
	);
}
