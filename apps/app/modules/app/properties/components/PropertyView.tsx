"use client";

import React from "react";
import { useStreetView } from "@app/organizations/components/objects/views/hooks/useStreetView";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import { 
	IconMapPin, 
	IconHome, 
	IconCalendar, 
	IconUser, 
	IconBuilding,
	IconLoader2,
	IconExclamationCircle
} from "@tabler/icons-react";
import { formatDistanceToNow } from "date-fns";

interface PropertyViewProps {
	property: any;
}

export const PropertyView = ({ property }: PropertyViewProps) => {
	const { streetViewRef, isLoading, hasError, isAvailable, errorMessage } = useStreetView({
		selectedProperty: property,
	});

  console.log("property", property);

	if (!property) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center text-muted-foreground">
					<IconHome className="w-8 h-8 mx-auto mb-2" />
					<p>No property selected</p>
				</div>
			</div>
		);
	}

	const getPropertyCoordinates = (property: any) => {
		// Try different possible coordinate locations in the property data
		const coords = 
			property?.location?.location?.coordinates ||  // Nested GeoJSON structure
			property?.location?.coordinates ||            // Direct coordinates on location
			property?.coordinates;                        // Direct coordinates on property
		
		if (Array.isArray(coords) && coords.length === 2) {
			return {
				lat: coords[1] as number,
				lng: coords[0] as number,
			};
		}
		return { lat: undefined, lng: undefined };
	};

	const { lat, lng } = getPropertyCoordinates(property);

	return (
		<div className="space-y-4 mt-2">
      <div className="relative bg-muted rounded-lg overflow-hidden" style={{ height: "300px" }}>
        {/* Street View Container */}
        <div
          ref={streetViewRef}
          className="absolute inset-0 w-full h-full"
        />
        
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Loading street view...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="text-center">
              <IconExclamationCircle className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                {errorMessage || "Street view not available"}
              </p>
            </div>
          </div>
        )}

        {/* No Coordinates State */}
        {!lat || !lng ? (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="text-center">
              <IconMapPin className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                No location coordinates available
              </p>
            </div>
          </div>
        ) : null}
      </div>
		</div>
	);
}; 