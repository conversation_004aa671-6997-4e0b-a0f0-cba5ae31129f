import { ArrowDown, ArrowUp, Chev<PERSON>UpDown, EyeOff } from "lucide-react";
import { Column } from "@tanstack/react-table";
import { cn } from "@ui/lib";
import { ColumnHeader } from "@app/shared/components/ColumnHeader";
import { IconLetterTSmall } from "@tabler/icons-react";

const iconMapping = {
	Title: IconLetterTSmall,
} as const;

interface DataTableColumnHeaderProps<TData, TValue>
    extends React.HTMLAttributes<HTMLDivElement> {
    column: Column<TData, TValue>;
    title: string;
}

export function DataTableColumnHeader<TData, TValue>({
     column,
     title,
     className,
 }: DataTableColumnHeaderProps<TData, TValue>) {
    if (!column.getCanSort()) {
        return <div className={cn(className)}>{title}</div>;
    }

    const Icon = iconMapping[title as keyof typeof iconMapping];

    return (
        <div className={cn("flex items-center space-x-2", className)}>
            <ColumnHeader title={title} icon={Icon} />
        </div>
    );
}
