"use client";

import React, { useState } from "react";
import {
	type ColumnDef,
	flexRender,
	type HeaderGroup,
	type Row,
	type Table,
} from "@tanstack/react-table";
import { Skeleton } from "@ui/components/skeleton";
import {
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	Table as UITable,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import EmptyContainer from "@app/shared/components/EmptyContainer";
import { IconCalendarDollar, IconChartBar } from "@tabler/icons-react";
import { type SaleHistory } from "@app/sale-history/lib/api";
import { formatCurrency } from "@app/organizations/lib/format";
import { Label } from "@ui/components/label";

interface SaleHistoryTableProps {
	columns: ColumnDef<SaleHistory>[];
	table: Table<SaleHistory>;
	isLoading: boolean;
	onEditSaleHistory?: (saleHistory?: SaleHistory) => void;
}

export default function SaleHistoryTable({
	columns,
	table,
	isLoading,
	onEditSaleHistory,
}: SaleHistoryTableProps) {
	return (
		<DataTable
			columns={columns}
			table={table}
			isLoading={isLoading}
			onEditSaleHistory={onEditSaleHistory}
		/>
	);
}

interface DataTableProps {
	columns: ColumnDef<SaleHistory>[];
	table: Table<SaleHistory>;
	isLoading: boolean;
	onEditSaleHistory?: (saleHistory?: SaleHistory) => void;
}

export function DataTable({
	columns,
	table,
	isLoading,
	onEditSaleHistory,
}: DataTableProps) {
	const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

	const toggleRowExpansion = (rowId: string) => {
		setExpandedRows(prev => ({
			...prev,
			[rowId]: !prev[rowId]
		}));
	};

	const TableSkeleton = ({ columns }: { columns: ColumnDef<SaleHistory>[] }) => (
		<>
			{[...Array(5)].map((_, index) => (
				<TableRow key={index}>
					{columns.map((column, cellIndex) => (
						<TableCell key={cellIndex} className="px-4 py-2">
							<Skeleton className="h-6 w-full" />
						</TableCell>
					))}
				</TableRow>
			))}
		</>
	);

	const rows = table.getRowModel().rows;
	const hasData = rows && rows.length > 0;

	return (
		<div className="space-y-4">			
			<div className="overflow-auto">
				<UITable>
					<TableHeader>
						{table.getHeaderGroups().map((headerGroup: HeaderGroup<SaleHistory>) => (
							<TableRow
								key={headerGroup.id}
								className="border-b hover:bg-transparent"
							>
								{headerGroup.headers.map((header) => (
									<TableHead
										key={header.id}
										colSpan={header.colSpan}
										className="px-4 text-xs font-medium text-zinc-500 !h-8"
									>
										<div className="flex items-center h-full w-full">
											{header.isPlaceholder
												? null
												: flexRender(
														header.column.columnDef.header,
														header.getContext(),
													)}
										</div>
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{isLoading ? (
							<TableSkeleton columns={columns} />
						) : hasData ? (
							rows.map((row: Row<SaleHistory>) => (
								<React.Fragment key={row.id}>
									<TableRow
										onClick={() => toggleRowExpansion(row.id)}
										className={cn(
											"cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
											"bg-white dark:bg-transparent"
										)}
										data-state={row.getIsSelected() ? "selected" : undefined}
									>
										{row.getVisibleCells().map((cell) => (
											<TableCell 
												key={cell.id}
												className="px-4 py-2"
											>
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext()
												)}
											</TableCell>
										))}
									</TableRow>
									{expandedRows[row.id] && (
										<TableRow>
											<TableCell colSpan={columns.length + 1} className='!p-0 !m-0'>
												<div className="p-4 bg-muted/50 w-full flex justify-between">
													<div className='flex flex-col'>
														<Label>Asking Price</Label>
														<span>{formatCurrency(row.original.askingPrice || 0) || "N/A"}</span>
													</div>

													<div className='flex flex-col'>
														<Label>Price Per Square Foot</Label>
														<span>{formatCurrency(row.original.pricePerSquareFoot || 0) || "N/A"}</span>
													</div>

													<div className='flex flex-col'>
														<Label>Price Per Unit</Label>
														<span>{formatCurrency(row.original.pricePerUnit || 0) || "N/A"}</span>
													</div>

													<div className='flex flex-col'>
														<Label>CAP Rate</Label>
														<span>{row.original.capRate ? `${row.original.capRate}%` : "N/A"}</span>
													</div>

													<div className='flex flex-col'>
														<Label>GRM Rate</Label>
														<span>{row.original.grmRate ? `${row.original.grmRate}%` : "N/A"}</span>
													</div>
													
													<div className='flex flex-col'>
														<Label>Transaction Type</Label>
														<span>{row.original.transactionType || "N/A"}</span>
													</div>

													<div className='flex flex-col'>
														<Label>Ownership %</Label>
														<span>{row.original.transferredOwnershipPercentage ? `${row.original.transferredOwnershipPercentage}%` : "N/A"}</span>
													</div>
												</div>
											</TableCell>
										</TableRow>
									)}
								</React.Fragment>
							))
						) : (
							<TableRow>
								<TableCell
									colSpan={columns.length}
									className="h-auto text-center rounded-b-lg"
								>
									<EmptyContainer
										title="No sale history found"
										subtitle="You have no sale history for this property."
										button="Add sale history"
										onClick={() => {
											onEditSaleHistory?.();
										}}
										icon={IconCalendarDollar}
									/>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</UITable>
			</div>
		</div>
	);
}
