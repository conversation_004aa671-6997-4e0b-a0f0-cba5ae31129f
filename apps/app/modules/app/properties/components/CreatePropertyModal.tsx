"use client";

import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Form } from "@ui/components/form";
import { IconSquareRoundedCheck, IconMapPin, IconX, IconArrowRight, IconArrowLeft } from "@tabler/icons-react";
import { ReaAddressField } from "@ui/components/rea-address-field";
import { getReaPropertyDetails } from "@shared/actions/rea";
import { propertySchema, type PropertyFormData } from "./form/property-form-schema";
import { getPropertyFormDefaults } from "./form/property-defaults";
import { geocodeAddress, type GeocodeResult } from "../lib/geocoding";
import { useCreateProperty } from "../lib/api";
import type { PropertyCreateInput } from "@repo/api/src/routes/properties/types";
import { toast } from "sonner";
import { 
	PROPERTY_TYPES, 
	PROPERTY_STATUSES,
	CONSTRUCTION_TYPES,
	PROPERTY_USES,
	HEATING_TYPES,
	PARKING_TYPES,
	GARAGE_TYPES,
	LOT_TYPES,
	MLS_STATUSES,
	MLS_TYPES,
	FLOOD_ZONE_TYPES,
	NOTICE_TYPES,
} from "@app/organizations/components/objects/properties/schema";
import { cn } from "@ui/lib";

interface CreatePropertyModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export function CreatePropertyModal({ isOpen, onClose }: CreatePropertyModalProps) {
	const { session } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const router = useRouter();

	// Create property mutation
	const createPropertyMutation = useCreateProperty();

	// Form state - comprehensive schema with 140+ fields for complete property data
	// Currently displays essential fields in UI, but all fields are available for programmatic use
	const form = useForm<PropertyFormData>({
		resolver: zodResolver(propertySchema),
		defaultValues: getPropertyFormDefaults(),
	});

	// State
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [keepOpen, setKeepOpen] = useState(false);
	const [isGeocoding, setIsGeocoding] = useState(false);
	const [geocodeResult, setGeocodeResult] = useState<GeocodeResult | null>(null);
	const [coordinates, setCoordinates] = useState<[number, number] | null>(null);

	// REA property data state
	const [reaPropertyData, setReaPropertyData] = useState<any>(null);
	const [isFetchingReaData, setIsFetchingReaData] = useState(false);
	
	// Address entry mode toggle
	const [useAutocomplete, setUseAutocomplete] = useState(true);

	// Function to fetch comprehensive REA property details
	const fetchReaPropertyDetails = useCallback(async (address: string) => {
		if (!address.trim()) return null;
		
		setIsFetchingReaData(true);
		try {
			const data = await getReaPropertyDetails(address);
			return data;
		} catch (error) {
			console.error("❌ Error fetching REA property details:", error);
			return null;
		} finally {
			setIsFetchingReaData(false);
		}
	}, []);

	// Watch for address changes to fetch REA property details
	const watchedAddress = form.watch("address");
	const [lastFetchedAddress, setLastFetchedAddress] = useState<string>("");

	// Handle address changes and fetch REA property details
	useEffect(() => {
		const fullAddress = watchedAddress?.fullAddress;
		if (fullAddress && fullAddress !== lastFetchedAddress && fullAddress.length > 10) {
			setLastFetchedAddress(fullAddress);
			
			// Fetch comprehensive REA property details
			fetchReaPropertyDetails(fullAddress).then((reaData) => {
				if (reaData?.data) {
					setReaPropertyData(reaData);
					const data = reaData.data;
					
					// Set REA ID for easy lookup
					if (data.id) {
						form.setValue("reaId", data.id.toString());
					}
					
					// Auto-populate financial data
					if (data.estimatedValue) {
						form.setValue("price", data.estimatedValue);
					}
					
											// Auto-populate property type
					if (data.propertyType) {
						const reaPropertyType = data.propertyType.toString();
						
						// Create mapping from our PROPERTY_TYPES to REA format
						const reaToFormTypeMap: Record<string, string> = {};
						
						// Build mapping dynamically from PROPERTY_TYPES
						PROPERTY_TYPES.forEach(type => {
							// Convert to uppercase and replace spaces with underscores for REA format
							const reaKey = type.value.toUpperCase().replace(/_/g, '_');
							reaToFormTypeMap[reaKey] = type.value;
						});
						
						// Special case mappings for any inconsistencies
						reaToFormTypeMap['SINGLE_FAMILY'] = 'single_family';
						reaToFormTypeMap['MULTI_FAMILY'] = 'multi_family';
						
						// Find matching property type from our constants or use 'other' as fallback
						const mappedType = reaToFormTypeMap[reaPropertyType] || 'other';
						form.setValue("propertyType", mappedType);
					}
					
					// Auto-populate property info data
					if (data.propertyInfo) {
						const propInfo = data.propertyInfo;
						if (propInfo.yearBuilt) form.setValue("yearBuilt", propInfo.yearBuilt);
						if (propInfo.buildingSquareFeet && propInfo.buildingSquareFeet > 0) {
							form.setValue("squareFootage", propInfo.buildingSquareFeet);
						}
						if (propInfo.bedrooms) form.setValue("bedrooms", propInfo.bedrooms);
						if (propInfo.bathrooms) form.setValue("bathrooms", propInfo.bathrooms);
						if (propInfo.unitsCount && propInfo.unitsCount > 0) {
							form.setValue("units", propInfo.unitsCount);
						}
						if (propInfo.lotSquareFeet && propInfo.lotSquareFeet > 0) {
							form.setValue("lotSize", propInfo.lotSquareFeet);
						}
						
						// Auto-populate coordinates if available
						if (propInfo.latitude && propInfo.longitude) {
							setCoordinates([propInfo.longitude, propInfo.latitude]);
						}
					}
					
					// Also populate zip from postalCode if available
					if (watchedAddress.postalCode) {
						form.setValue("address.zip", watchedAddress.postalCode);
					}
					
				} else {
					// Clear any previous REA data
					setReaPropertyData(null);
					toast.info("ℹ️ Property details not found in REA database");
				}
			});
		}
	}, [watchedAddress?.fullAddress, form, lastFetchedAddress, fetchReaPropertyDetails]);

	// Clear all address and REA data
	const clearAddressData = useCallback(() => {
		setReaPropertyData(null);
		setLastFetchedAddress("");
		setCoordinates(null);
		setUseAutocomplete(true); // Reset to autocomplete mode
		form.setValue("address.street", "");
		form.setValue("address.street2", "");
		form.setValue("address.city", "");
		form.setValue("address.state", "");
		form.setValue("address.postalCode", "");
		form.setValue("address.zip", "");
		form.setValue("address.county", "");
		form.setValue("address.country", "United States");
		form.setValue("address.fullAddress", "");
		form.setValue("address.reaId", "");
		form.setValue("reaId", "");
	}, [form]);

	// Form submission
	const onSubmit = useCallback(async (data: PropertyFormData) => {
		if (!session?.userId || !activeOrganization?.id) return;

		setIsSubmitting(true);

		try {
			// Auto-populate property name with street address if no name provided
			let propertyName = data.name;
			if (!propertyName || propertyName.trim() === '') {
				if (data.address.street) {
					propertyName = data.address.street;
				} else if (data.address.fullAddress) {
					// Extract street portion from full address if available
					const addressParts = data.address.fullAddress.split(',');
					propertyName = addressParts[0]?.trim() || data.address.fullAddress;
				}
			}

			// Geocode address if not already done and address is provided
			let finalCoordinates = coordinates;
			if (!finalCoordinates && (data.address.fullAddress || (data.address.street && data.address.city))) {
				setIsGeocoding(true);
				const addressToGeocode = data.address.fullAddress || [
					data.address.street,
					data.address.city,
					data.address.state,
					data.address.postalCode || data.address.zip,
					data.address.country,
				].filter(Boolean).join(", ");
				
				const result = await geocodeAddress(addressToGeocode);
				setGeocodeResult(result);
				if (result.success && result.coordinates) {
					finalCoordinates = result.coordinates;
					setCoordinates(finalCoordinates);
				}
				setIsGeocoding(false);
			}

			// Create property with comprehensive REA data
			const propertyData: PropertyCreateInput = {
				...data,
				name: propertyName, // Use the auto-populated name if needed
				organizationId: activeOrganization.id,
				recordType: "property", // Set default record type
				location: finalCoordinates ? {
					type: "Point",
					coordinates: finalCoordinates,
				} : undefined,
				// Include comprehensive REA API data for full database population
				reaApiData: reaPropertyData || undefined,
			};

			await createPropertyMutation.mutateAsync(propertyData);

			// Show success message
			toast.success("Property created successfully");

			setIsSubmitting(false);

			if (keepOpen) {
				// Reset form but keep modal open
				form.reset(getPropertyFormDefaults());
				setCoordinates(null);
				setGeocodeResult(null);
				setReaPropertyData(null);
				clearAddressData();
			} else {
				onClose();
				// Note: No need for router.refresh() as the mutation handles cache invalidation
			}
		} catch (error) {
			console.error("Error creating property:", error);
			toast.error("Failed to create property. Please try again.");
			setIsSubmitting(false);
		}
	}, [session?.userId, activeOrganization?.id, coordinates, onClose, router, keepOpen, form]);

	// Reset form when modal closes
	React.useEffect(() => {
		if (!isOpen) {
			form.reset();
			setCoordinates(null);
			setGeocodeResult(null);
			setReaPropertyData(null);
			clearAddressData();
		}
	}, [isOpen, form, clearAddressData]);

	const isLoading = isSubmitting || isGeocoding || isFetchingReaData || createPropertyMutation.isPending;

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent
				className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0"
				onPointerDownOutside={(e) => e.preventDefault()}
			>
				<DialogTitle className="sr-only">
					Create Property
				</DialogTitle>
				<DialogDescription className="sr-only">
					Create a new property
				</DialogDescription>

				{/* Fixed Header */}
				<div className="flex items-center justify-between px-4 pt-2 pb-1">
					<div className="flex flex-row items-center gap-x-2">
						<IconSquareRoundedCheck className="h-5 w-5" />
						<span className="text-md font-light">
							Create Property
						</span>
					</div>
				</div>

				<FormProvider {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="flex flex-col flex-1 min-h-0 overflow-y-auto max-h-[600px]"
					>
					{/* Scrollable Content */}
					<div className="flex flex-col px-2 pt-2 pb-4 space-y-4 overflow-y-auto flex-1">
						{/* Name Section */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Property Name
								<span className="text-red-500 ml-1">*</span>
								{form.formState.errors.name && (
									<span className="text-red-500 text-xs ml-2">
										{form.formState.errors.name.message}
									</span>
								)}
							</h3>
							<Input
								{...form.register("name")}
								placeholder="Set Property Name..."
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						</div>

						{/* Address Section with REA Integration */}
						<div className="flex flex-col">
							<div className="flex items-center justify-between">
								<h3 className="text-xs text-muted-foreground">
									Property Address
								</h3>
								{useAutocomplete && (
									<span 
									onClick={() => setUseAutocomplete(false)} 
										className="text-xs cursor-pointer hover:underline text-blue-500 flex items-center gap-1">
										Enter Manually
									</span>
								)}
								{!useAutocomplete && (
									<span 
										onClick={() => setUseAutocomplete(true)} 
										className="text-xs cursor-pointer hover:underline text-blue-500 flex items-center gap-1">
										Use Autocomplete
									</span>
								)}
							</div>
							<div className="space-y-2">
								{useAutocomplete && (
									<ReaAddressField
										register={form.register("address")}
										label=""
										placeholder="Start typing property address..."
									/>
								)}
								{(!useAutocomplete || watchedAddress?.fullAddress) && (
									<div className={cn("grid grid-cols-1 space-y-2", !useAutocomplete && "mt-1.5")}>
										<Input
											{...form.register("address.street")}
											placeholder="Street Address..."
											className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
										/>
										<Input
											{...form.register("address.street2")}
											placeholder="Unit, Suite, Apt (optional)..."
											className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
										/>
										<div className="grid grid-cols-2 gap-3">
											<Input
												{...form.register("address.city")}
												placeholder="City..."
												className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
											/>
											<Input
												{...form.register("address.state")}
												placeholder="State..."
												className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
											/>
										</div>
										<div className="grid grid-cols-2 gap-3">
											<Input
												{...form.register("address.postalCode")}
												placeholder="ZIP Code..."
												className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
											/>
											<Input
												{...form.register("address.country")}
												placeholder="Country..."
												defaultValue="United States"
												className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
											/>
										</div>
									</div>
								)}

								{/* Coordinates display */}
								{coordinates && (
									<div className="text-xs text-muted-foreground bg-muted p-2 rounded-md">
										<div className="flex items-center gap-1">
											<IconMapPin className="h-3 w-3" />
											<span>
												Coordinates: {coordinates[1].toFixed(6)}, {coordinates[0].toFixed(6)}
											</span>
										</div>
									</div>
								)}
							</div>
						</div>

						{/* Property Type */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Property Type
							</h3>
							<Select
								value={form.watch("propertyType") || ""}
								onValueChange={(value) => form.setValue("propertyType", value)}
							>
								<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
									<SelectValue placeholder="Select property type..." />
								</SelectTrigger>
								<SelectContent>
									{PROPERTY_TYPES.map((type) => (
										<SelectItem key={type.value} value={type.value}>
											{type.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Status */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Status
							</h3>
							<Select
								value={form.watch("status") || ""}
								onValueChange={(value) => form.setValue("status", value)}
							>
								<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
									<SelectValue placeholder="Select status..." />
								</SelectTrigger>
								<SelectContent>
									{PROPERTY_STATUSES.map((status) => (
										<SelectItem key={status.value} value={status.value}>
											{status.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Price */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Price
							</h3>
							<Input
								{...form.register("price", { valueAsNumber: true })}
								type="number"
								placeholder="Set Price..."
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						</div>

						{/* Property Details */}
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-1">
								<h3 className="text-xs text-muted-foreground">
									Year Built
								</h3>
								<Input
									{...form.register("yearBuilt", { valueAsNumber: true })}
									type="number"
									placeholder="Year Built..."
									className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
								/>
							</div>

							<div className="space-y-1">
								<h3 className="text-xs text-muted-foreground">
									Square Footage
								</h3>
								<Input
									{...form.register("squareFootage", { valueAsNumber: true })}
									type="number"
									placeholder="Square Feet..."
									className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
								/>
							</div>
						</div>

						<div className="grid grid-cols-3 gap-4">
							<div className="space-y-1">
								<h3 className="text-xs text-muted-foreground">
									Bedrooms
								</h3>
								<Input
									{...form.register("bedrooms", { valueAsNumber: true })}
									type="number"
									placeholder="Bedrooms..."
									className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
								/>
							</div>

							<div className="space-y-1">
								<h3 className="text-xs text-muted-foreground">
									Bathrooms
								</h3>
								<Input
									{...form.register("bathrooms", { valueAsNumber: true })}
									type="number"
									placeholder="Bathrooms..."
									className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
								/>
							</div>

							<div className="space-y-1">
								<h3 className="text-xs text-muted-foreground">
									Units
								</h3>
								<Input
									{...form.register("units", { valueAsNumber: true })}
									type="number"
									placeholder="Units..."
									className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
								/>
							</div>
						</div>

						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Lot Size
							</h3>
							<Input
								{...form.register("lotSize", { valueAsNumber: true })}
								type="number"
								placeholder="Lot Size..."
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						</div>


					</div>

					{/* Fixed Footer */}
					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<div className="flex items-center gap-2 mr-auto">
							<Switch
								id="keep-open"
								checked={keepOpen}
								onCheckedChange={setKeepOpen}
							/>
							<Label
								htmlFor="keep-open"
								className="text-sm text-muted-foreground"
							>
								Create more
							</Label>
						</div>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onClose()}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={isLoading}
						>
							{isLoading ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>Creating...</span>
								</div>
							) : (
								"Create Property"
							)}
						</Button>
					</div>
				</form>
				</FormProvider>
			</DialogContent>
		</Dialog>
	);
}
