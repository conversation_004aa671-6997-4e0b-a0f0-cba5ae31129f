"use client";

import React, { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useForm } from "react-hook-form";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Skeleton } from "@ui/components/skeleton";
import type { ActiveOrganization } from "@repo/auth";
import { useUpdateProperty } from "@app/properties/lib/api";

// Custom input component with label (same pattern as MoreInfo.tsx)
const DetailInput = React.forwardRef<
	HTMLInputElement,
	React.InputHTMLAttributes<HTMLInputElement> & {
		label: string;
		check?: React.ReactNode;
	}
>(({ className, label, check, ...props }, ref) => (
	<div className="flex flex-row items-center">
		<div className="ml-3 w-[16%] text-[10px] uppercase font-normal text-muted-foreground">
			{label}
		</div>
		<div className="flex-1 relative w-full">
			<Input
				ref={ref}
				className={cn(
					"w-full h-8 border-transparent rounded-lg",
					className
				)}
				{...props}
			/>
			{check && (
				<div className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer">
					{check}
				</div>
			)}
		</div>
	</div>
));
DetailInput.displayName = "DetailInput";

// Skeleton component for loading state
const DetailInputSkeleton = ({ label }: { label: string }) => (
	<div className="flex flex-row items-center">
		<div className="ml-3 w-[16%] text-[10px] uppercase font-normal text-muted-foreground">
			{label}
		</div>
		<div className="flex-1 relative w-full">
			<Skeleton className="w-full h-6 rounded-lg" />
		</div>
	</div>
);

// Form schema for property address fields
const propertyAddressSchema = z.object({
	street: z.string().optional(),
	street2: z.string().optional(),
	city: z.string().optional(),
	state: z.string().optional(),
	zip: z.string().optional(),
	county: z.string().optional(),
	country: z.string().optional(),
	market: z.string().optional(),
	subMarket: z.string().optional(),
});

type PropertyAddressFormData = z.infer<typeof propertyAddressSchema>;

interface PropertyDetailsProps {
	selectedProperty: any;
	organization?: ActiveOrganization;
	loading?: boolean;
}

const PropertyDetails = ({ selectedProperty, organization, loading = false }: PropertyDetailsProps) => {
	const { mutateAsync: updateProperty } = useUpdateProperty(organization?.id);

	const form = useForm<PropertyAddressFormData>({
		resolver: zodResolver(propertyAddressSchema),
		defaultValues: {},
	});

	useEffect(() => {
		if (selectedProperty) {
			// Extract address data from the nested structure and flatten it
			const address = selectedProperty?.location?.address || {};
			const formData = {
				street: address.street || "",
				street2: address.street2 || "",
				city: address.city || "",
				state: address.state || "",
				zip: address.zip || "",
				county: address.county || "",
				country: address.country || "",
				market: selectedProperty?.market || "",
				subMarket: selectedProperty?.subMarket || "",
			};
			form.reset(formData);
		}
	}, [selectedProperty, form]);

	// Handle field updates
	const handleFieldUpdate = async (field: string, value: any) => {
		if (!selectedProperty?.id || !organization?.id) return;
		
		try {
			// For address fields, we need to update the nested structure
			if (['street', 'street2', 'city', 'state', 'zip', 'county', 'country'].includes(field)) {
				const currentAddress = selectedProperty?.location?.address || {};
				const updatedAddress = {
					...currentAddress,
					[field]: value,
				};
				
				await updateProperty({
					id: selectedProperty.id,
					organizationId: organization.id,
					address: updatedAddress,
				});
			} else {
				// For top-level fields like market, subMarket
				await updateProperty({
					id: selectedProperty.id,
					organizationId: organization.id,
					[field]: value,
				});
			}
		} catch (error) {
			console.error(`Failed to update ${field}:`, error);
		}
	};

	if (!selectedProperty) {
		return (
			<div className="flex items-center justify-center h-32">
				<div className="text-center text-muted-foreground">
					<p>No property selected</p>
				</div>
			</div>
		);
	}

	if (loading) {
		return (
			<div className="space-y-1">
				<DetailInputSkeleton label="Street" />
				<DetailInputSkeleton label="Street2" />
				<DetailInputSkeleton label="City" />
				<DetailInputSkeleton label="State" />
				<DetailInputSkeleton label="ZIP" />
				<DetailInputSkeleton label="County" />
				<DetailInputSkeleton label="Country" />
				<DetailInputSkeleton label="Market" />
				<DetailInputSkeleton label="SubMarket" />
			</div>
		);
	}

	return (
		<Form {...form}>
			<form className="space-y-1">
				<FormField
					control={form.control}
					name="street"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="Street"
									placeholder="Add street address"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.street || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("street", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="street2"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="Street2"
									placeholder="Add street address line 2"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.street2 || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("street2", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="city"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="City"
									placeholder="Add city"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.city || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("city", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="state"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="State"
									placeholder="Add state"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.state || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("state", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="zip"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="ZIP"
									placeholder="Add ZIP code"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.zip || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("zip", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="county"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="County"
									placeholder="Add county"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.county || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("county", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="country"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="Country"
									placeholder="Add country"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.location?.address?.country || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("country", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="market"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="Market"
									placeholder="Add market"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.market || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("market", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="subMarket"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<DetailInput
									label="SubMarket"
									placeholder="Add sub-market"
									{...field}
									value={field.value || ""}
									onBlur={() => {
										const currentValue = selectedProperty?.subMarket || "";
										if (field.value !== currentValue) {
											handleFieldUpdate("subMarket", field.value);
										}
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</form>
		</Form>
	);
};

export default PropertyDetails; 