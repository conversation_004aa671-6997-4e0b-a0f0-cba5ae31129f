"use client";

import { useProperties } from "@app/properties/lib/properties-provider";
import { IconPlus } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

interface CreatePropertyButtonProps {
	icon?: boolean;
	className?: string;
}

export function CreatePropertyButton({
	icon = true,
	className,
}: CreatePropertyButtonProps) {
	const { openCreateProperty } = useProperties();

	const handleClick = () => {
		openCreateProperty();
	};

	return (
		<>
			{icon ? (
				<Button
					size="icon"
					className={cn(
						"transition group h-7 w-7 text-xs rounded-sm flex gap-1.5 items-center border !border-border !bg-muted/50 hover:!border hover:!border-border hover:!bg-muted/70",
						className,
					)}
					onClick={handleClick}
				>
					<IconPlus className="size-3 shrink-0 transition-all text-muted-foreground group-hover:text-primary" />
				</Button>
			) : (
				<Button
					variant="outline"
					onClick={handleClick}
					className={cn(
						"bg-blue-500 hover:bg-blue-600 !border !border-blue-500 text-white !py-1 px-2 rounded-lg w-fit !h-7",
						className,
					)}
				>
					Create Property
				</Button>
			)}
		</>
	);
}
