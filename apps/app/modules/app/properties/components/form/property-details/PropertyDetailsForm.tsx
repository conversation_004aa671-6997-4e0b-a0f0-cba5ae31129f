"use client";

import { useState, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { IconChevronDown, IconCircleFilled } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@ui/components/command";
import type { PropertyFormData } from "../property-form-schema";
import { useCustomFieldDefinitions } from "@app/custom-field-definitions/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { cn } from "@ui/lib";

interface PropertyDetailsFormProps {
	form: UseFormReturn<PropertyFormData>;
}

const propertyTypes = [
	{ label: "Single Family", value: "single-family" },
	{ label: "Multi Family", value: "multi-family" },
	{ label: "Condo", value: "condo" },
	{ label: "Townhouse", value: "townhouse" },
	{ label: "Land", value: "land" },
	{ label: "Commercial", value: "commercial" },
	{ label: "Other", value: "other" },
] as const;

const propertyStatuses = [
	{ label: "Active", value: "active" },
	{ label: "Pending", value: "pending" },
	{ label: "Sold", value: "sold" },
	{ label: "Off Market", value: "off-market" },
	{ label: "Coming Soon", value: "coming-soon" },
] as const;

export function PropertyDetailsForm({ form }: PropertyDetailsFormProps) {
	const { activeOrganization } = useActiveOrganization();
	const { data: customFields = [] } = useCustomFieldDefinitions(activeOrganization?.id || "");

	// Property type selection state
	const [openPropertyType, setOpenPropertyType] = useState(false);
	const [openPropertyStatus, setOpenPropertyStatus] = useState(false);

	// Selected values
	const selectedPropertyType = useMemo(() => {
		const currentType = form.watch("propertyType");
		return propertyTypes.find((type) => type.value === currentType);
	}, [form]);

	const selectedPropertyStatus = useMemo(() => {
		const currentStatus = form.watch("status");
		return propertyStatuses.find((status) => status.value === currentStatus);
	}, [form]);

	return (
		<div className="space-y-4">
			{/* Basic Details */}
			<div className="space-y-2">
				<Input
					{...form.register("name", { required: "Property name is required" })}
					placeholder="Property name"
					className={cn(
						"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
						form.formState.errors.name && "border-red-500"
					)}
				/>
				{form.formState.errors.name && (
					<div className="text-xs text-red-500">{form.formState.errors.name.message}</div>
				)}

				<Textarea
					{...form.register("description")}
					placeholder="Property description (optional)"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50 min-h-[100px]"
				/>
			</div>

			{/* Property Type and Status */}
			<div className="grid grid-cols-2 gap-2">
				<Popover open={openPropertyType} onOpenChange={setOpenPropertyType}>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							role="combobox"
							aria-expanded={openPropertyType}
							className="w-full justify-between"
						>
							{selectedPropertyType?.label ?? "Select type..."}
							<IconChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-[200px] p-0">
						<Command>
							<CommandInput placeholder="Search type..." className="h-9" />
							<CommandEmpty>No property type found.</CommandEmpty>
							<CommandGroup>
								{propertyTypes.map((type) => (
									<CommandItem
										key={type.value}
										onSelect={() => {
											form.setValue("propertyType", type.value);
											setOpenPropertyType(false);
										}}
									>
										{type.label}
										{type.value === selectedPropertyType?.value && (
											<IconCircleFilled className="ml-auto h-4 w-4" />
										)}
									</CommandItem>
								))}
							</CommandGroup>
						</Command>
					</PopoverContent>
				</Popover>

				<Popover open={openPropertyStatus} onOpenChange={setOpenPropertyStatus}>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							role="combobox"
							aria-expanded={openPropertyStatus}
							className="w-full justify-between"
						>
							{selectedPropertyStatus?.label ?? "Select status..."}
							<IconChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-[200px] p-0">
						<Command>
							<CommandInput placeholder="Search status..." className="h-9" />
							<CommandEmpty>No property status found.</CommandEmpty>
							<CommandGroup>
								{propertyStatuses.map((status) => (
									<CommandItem
										key={status.value}
										onSelect={() => {
											form.setValue("status", status.value);
											setOpenPropertyStatus(false);
										}}
									>
										{status.label}
										{status.value === selectedPropertyStatus?.value && (
											<IconCircleFilled className="ml-auto h-4 w-4" />
										)}
									</CommandItem>
								))}
							</CommandGroup>
						</Command>
					</PopoverContent>
				</Popover>
			</div>

			{/* Property Details */}
			<div className="grid grid-cols-2 gap-2">
				<Input
					{...form.register("price")}
					type="number"
					placeholder="Price"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
				<Input
					{...form.register("yearBuilt")}
					type="number"
					placeholder="Year built"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
			</div>

			<div className="grid grid-cols-2 gap-2">
				<Input
					{...form.register("squareFootage")}
					type="number"
					placeholder="Square footage"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
				<Input
					{...form.register("lotSize")}
					type="number"
					placeholder="Lot size"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
			</div>

			<div className="grid grid-cols-3 gap-2">
				<Input
					{...form.register("bedrooms")}
					type="number"
					placeholder="Bedrooms"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
				<Input
					{...form.register("bathrooms")}
					type="number"
					placeholder="Bathrooms"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
				<Input
					{...form.register("units")}
					type="number"
					placeholder="Units"
					className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
				/>
			</div>

			{/* Custom Fields */}
			{customFields.length > 0 && (
				<div className="space-y-2">
					<h3 className="text-sm font-medium">Custom Fields</h3>
					<div className="grid grid-cols-2 gap-2">
						{customFields.map((field) => (
							<Input
								key={field.id}
								{...form.register(`customFields.${field.name}`)}
								placeholder={field.label}
								type={field.type === "number" ? "number" : "text"}
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						))}
					</div>
				</div>
			)}
		</div>
	);
} 