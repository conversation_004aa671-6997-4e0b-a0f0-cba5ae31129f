export const getPropertyFormDefaults = () => ({
	// Basic Info
	name: "",
	image: undefined,
	
	// Property Classification
	propertyType: "",
	propertySubType: undefined,
	market: undefined,
	subMarket: undefined,
	listingId: undefined,
	status: "",
	
	// REA (Real Estate API) Integration
	reaId: undefined,
	
	// Address
	address: {
		street: "",
		street2: "",
		city: "",
		state: "",
		postalCode: "",
		zip: "",
		county: "",
		country: "United States",
		fullAddress: "",
		reaId: "",
	},
	
	// Location & Geographic
	website: undefined,
	subdivision: undefined,
	lotNumber: undefined,
	parcelNumber: undefined,
	zoning: undefined,
	
	// Physical Details
	yearBuilt: undefined,
	squareFootage: undefined,
	units: undefined,
	floors: undefined,
	structures: undefined,
	bedrooms: undefined,
	bathrooms: undefined,
	roomsCount: undefined,
	buildingSquareFeet: undefined,
	garageSquareFeet: undefined,
	livingSquareFeet: undefined,
	lotSquareFeet: undefined,
	lotSize: undefined,
	lotType: undefined,
	lotAcres: undefined,
	construction: undefined,
	primaryUse: undefined,
	propertyUse: undefined,
	class: undefined,
	parking: undefined,
	parkingSpaces: undefined,
	garageType: undefined,
	heatingType: undefined,
	meterType: undefined,
	legalDescription: undefined,
	
	// Financial Information
	price: undefined,
	estimatedValue: undefined,
	pricePerSquareFoot: undefined,
	equity: undefined,
	equityPercent: undefined,
	estimatedEquity: undefined,
	saleDate: undefined,
	salePrice: undefined,
	lastSalePrice: undefined,
	lastSaleDate: undefined,
	landValue: undefined,
	buildingValue: undefined,
	cap: undefined,
	exchange: undefined,
	exchangeId: undefined,
	
	// Boolean Flags (all default to false)
	absenteeOwner: false,
	inStateAbsenteeOwner: false,
	outOfStateAbsenteeOwner: false,
	ownerOccupied: false,
	corporateOwned: false,
	vacant: false,
	mobileHome: false,
	carport: false,
	auction: false,
	cashBuyer: false,
	investorBuyer: false,
	freeClear: false,
	highEquity: false,
	privateLender: false,
	deedInLieu: false,
	quitClaim: false,
	sheriffsDeed: false,
	warrantyDeed: false,
	inherited: false,
	spousalDeath: false,
	lien: false,
	taxLien: false,
	preForeclosure: false,
	trusteeSale: false,
	floodZone: false,
	
	// MLS Data
	mlsActive: false,
	mlsCancelled: false,
	mlsFailed: false,
	mlsHasPhotos: false,
	mlsPending: false,
	mlsSold: false,
	mlsDaysOnMarket: undefined,
	mlsListingPrice: undefined,
	mlsListingPricePerSquareFoot: undefined,
	mlsSoldPrice: undefined,
	mlsStatus: undefined,
	mlsType: undefined,
	mlsListingDate: undefined,
	
	// Legal & Environmental
	floodZoneDescription: undefined,
	floodZoneType: undefined,
	noticeType: undefined,
	lastUpdateDate: undefined,
	
	// Demographics
	fmrEfficiency: undefined,
	fmrFourBedroom: undefined,
	fmrOneBedroom: undefined,
	fmrThreeBedroom: undefined,
	fmrTwoBedroom: undefined,
	fmrYear: undefined,
	hudAreaCode: undefined,
	hudAreaName: undefined,
	medianIncome: undefined,
	suggestedRent: undefined,
	
	// Complex nested data from REA API
	reaApiData: undefined,
	
	// Legacy/Optional fields
	description: undefined,
	customFields: undefined,
}); 