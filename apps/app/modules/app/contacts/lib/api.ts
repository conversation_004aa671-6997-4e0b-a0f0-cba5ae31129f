import type { ContactCreateInput } from "@repo/api/src/routes/contacts/types";
import type { Contact } from "@repo/database/src/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// PAYLOAD INTERFACES
export interface UpdateContactPayload {
	id: string;
	firstName?: string;
	lastName?: string;
	image?: string;
	persona?: string;
	status?: string;
	title?: string;
	summary?: string;
	website?: string;
	companyId?: string;
	email?: Array<{
		label: string;
		address: string;
		isPrimary: boolean;
		isBad: boolean;
	}>;
	phone?: Array<{
		label: string;
		number: string;
		isPrimary: boolean;
		isBad: boolean;
	}>;
	address?: Array<{
		label: string;
		street?: string;
		street2?: string;
		city?: string;
		state?: string;
		zip?: string;
		country?: string;
	}>;
	social?: {
		linkedin?: string;
		facebook?: string;
		twitter?: string;
		instagram?: string;
	};
	organizationId?: string;
	relatedContacts?: Array<{
		firstName: string;
		lastName: string;
		label: string;
		phone?: Array<{
			label: string;
			number: string;
			isPrimary: boolean;
			isBad: boolean;
		}>;
		email?: Array<{
			label: string;
			address: string;
			isPrimary: boolean;
			isBad: boolean;
		}>;
		address?: Array<{
			label: string;
			street?: string;
			street2?: string;
			city?: string;
			state?: string;
			zip?: string;
			country?: string;
		}>;
	}>;
}

// Types for related contacts
export interface RelatedContactPayload {
	_id?: { $oid: string };
	firstName: string;
	lastName: string;
	label: string;
	phone?: Array<{
		number: string;
		label: string;
		isPrimary: boolean;
		isBad: boolean;
	}>;
	email?: Array<{
		address: string;
		label: string;
		isPrimary: boolean;
		isBad: boolean;
	}>;
	address?: Array<{
		street: string;
		street2?: string;
		city: string;
		state: string;
		zip: string;
		country: string;
		label: string;
	}>;
}

// CREATE CONTACT
export async function createContact(
	payload: ContactCreateInput,
): Promise<Contact> {
	const response = await fetch("/api/objects/contacts", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(payload),
	});

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({}));
		throw new Error(errorData.error || "Failed to create contact");
	}

	const result = await response.json();
	return result.contact;
}

export async function fetchRelatedContact(id: string, organizationId: string): Promise<RelatedContactPayload[]> {
	const res = await fetch(`/api/objects/contacts/${id}/related-contacts?organizationId=${organizationId}`);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch related contact");
	}
	const result = await res.json();
	return result.relatedContacts || [];
}

// FETCH ALL CONTACTS
export async function fetchContacts(
	organizationId?: string,
): Promise<Contact[]> {
	const url = organizationId
		? `/api/objects/contacts?organizationId=${organizationId}&limit=100&offset=0`
		: "/api/objects/contacts";

	const res = await fetch(url);
	if (!res.ok) {
		const errorText = await res.text();
		console.error("Contacts API error response:", errorText);
		throw new Error("Failed to fetch contacts");
	}
	const result = await res.json();
	return result.contact || [];
}

// FETCH SINGLE CONTACT
export async function fetchContact(id: string, organizationId: string): Promise<Contact> {
	const res = await fetch(`/api/objects/contacts/${id}?organizationId=${organizationId}`);
	if (!res.ok) {
		const error = await res.json().catch(() => ({}));
		
		// Enhance error with more context for 404s
		if (res.status === 404) {
			// Check if the error message suggests access issues vs not found
			const errorMessage = error.error || "Not found";
			if (errorMessage.toLowerCase().includes("organization") || errorMessage.toLowerCase().includes("access") || errorMessage.toLowerCase().includes("permission")) {
				const accessError = new Error("ACCESS_DENIED");
				(accessError as any).status = 403;
				(accessError as any).originalMessage = errorMessage;
				throw accessError;
			} else {
				const notFoundError = new Error("CONTACT_NOT_FOUND");
				(notFoundError as any).status = 404;
				(notFoundError as any).originalMessage = errorMessage;
				throw notFoundError;
			}
		}
		
		throw new Error(error.error || "Failed to fetch contact");
	}
	const result = await res.json();
	return result.contact;
}

// UPDATE CONTACT
export async function updateContact(
	payload: UpdateContactPayload,
): Promise<Contact> {
	const { id, ...data } = payload;
	const res = await fetch(`/api/objects/contacts/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update contact");
	}
	return res.json();
}

// DELETE CONTACT
export async function deleteContact(id: string): Promise<void> {
	const res = await fetch(`/api/objects/contacts/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete contact");
	}
}

// BATCH DELETE CONTACTS
export async function batchDeleteContacts(payload: {
	ids: string[];
	organizationId: string;
}): Promise<{ deletedCount: number; message: string }> {
	const res = await fetch("/api/objects/contacts/delete-batch", {
		method: "DELETE",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(payload),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete contacts");
	}
	return res.json();
}

export async function updateContactField(payload: {
	id: string;
	field: string;
	value: any;
	organizationId: string;
}): Promise<Contact> {
	const res = await fetch(`/api/objects/contacts/${payload.id}/field`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify({
			field: payload.field,
			value: payload.value,
			organizationId: payload.organizationId,
		}),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update contact field");
	}
	const result = await res.json();
	
	// Handle different response formats and ensure we have a valid contact
	const contact = result.contact || result;
	
	// Check if we got an empty response or invalid contact data
	if (!contact || typeof contact !== 'object' || !contact.id) {
		console.error("Invalid API response for updateContactField:", {
			status: res.status,
			url: res.url,
			result,
			contact,
			payload,
		});
		
		// If the API returned success but empty data, still throw an error
		// but don't crash the UI - let the mutation handle the error gracefully
		if (Object.keys(result).length === 0) {
			throw new Error("Server returned empty response. The update may have succeeded but we couldn't retrieve the updated contact data.");
		} else {
			throw new Error("Invalid response from server - contact data is missing or malformed");
		}
	}
	
	return contact;
}

export async function clearContactField(payload: {
	id: string;
	field: string;
	organizationId: string;
}): Promise<Contact> {
	const res = await fetch(
		`/api/objects/contacts/${payload.id}/field/${payload.field}?organizationId=${payload.organizationId}`,
		{
			method: "DELETE",
		},
	);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to clear contact field");
	}
	const result = await res.json();
	
	// Handle different response formats and ensure we have a valid contact
	const contact = result.contact || result;
	
	// Check if we got an empty response or invalid contact data
	if (!contact || typeof contact !== 'object' || !contact.id) {
		console.error("Invalid API response for clearContactField:", {
			status: res.status,
			url: res.url,
			result,
			contact,
			payload,
		});
		
		// If the API returned success but empty data, still throw an error
		// but don't crash the UI - let the mutation handle the error gracefully
		if (Object.keys(result).length === 0) {
			throw new Error("Server returned empty response. The update may have succeeded but we couldn't retrieve the updated contact data.");
		} else {
			throw new Error("Invalid response from server - contact data is missing or malformed");
		}
	}
	
	return contact;
}

export async function searchContacts(
	query: string,
	organizationId: string,
): Promise<Contact[]> {
	if (!query.trim()) return [];

	const response = await fetch(
		`/api/objects/contacts?search=${encodeURIComponent(query)}&organizationId=${organizationId}`,
	);

	if (!response.ok) {
		throw new Error("Failed to search contacts");
	}

	const result = await response.json();
	return result.contacts || [];
}

// Function to update related contacts for a contact
export async function updateRelatedContacts({
	contactId,
	organizationId,
	relatedContacts,
}: {
	contactId: string;
	organizationId?: string;
	relatedContacts: RelatedContactPayload[];
}) {
	const response = await fetch(`/api/objects/contacts/${contactId}/related-contacts`, {
		method: "PUT",
		headers: {
			"Content-Type": "application/json",
			...(organizationId && { "X-Organization-ID": organizationId }),
		},
		body: JSON.stringify({ relatedContacts }),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.message || "Failed to update related contacts");
	}

	return response.json();
}

// REACT QUERY HOOKS
export function useCreateContact() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createContact,
		onSuccess: (newContact: Contact, variables) => {
			// Update all infinite queries optimistically
			queryClient.setQueriesData(
				{
					queryKey: ["contacts-infinite", variables.organizationId],
					exact: false,
				},
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const firstPage = oldData.pages[0];
					if (firstPage?.data) {
						const updatedFirstPage = {
							...firstPage,
							data: [newContact, ...firstPage.data],
							meta: {
								...firstPage.meta,
								totalRowCount:
									(firstPage.meta?.totalRowCount || 0) + 1,
								filterRowCount:
									(firstPage.meta?.filterRowCount || 0) + 1,
							},
						};

						return {
							...oldData,
							pages: [
								updatedFirstPage,
								...oldData.pages.slice(1),
							],
						};
					}
					return oldData;
				},
			);

			// Update regular contacts query
			queryClient.setQueryData(
				["contacts", variables.organizationId],
				(old: Contact[] = []) => [newContact, ...old],
			);

			// Reset queries completely to avoid cursor timing issues
			queryClient.removeQueries({
				queryKey: ["contacts-infinite", variables.organizationId],
				exact: false,
			});

			queryClient.invalidateQueries({
				queryKey: ["contacts", variables.organizationId],
				exact: false,
			});

			queryClient.invalidateQueries({
				queryKey: ["objects", "contacts"],
				exact: false,
			});
		},
	});
}

export function useUpdateContact(organizationId?: string) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (payload: UpdateContactPayload) => updateContact({ ...payload, organizationId }),
		onSuccess: (updatedContact: Contact) => {
			queryClient.setQueryData(
				["contact", updatedContact.id],
				updatedContact,
			);

			queryClient.setQueriesData(
				{ queryKey: ["contacts"], exact: false },
				(oldContacts: Contact[] | undefined) => {
					if (!oldContacts || !Array.isArray(oldContacts)) {
						return oldContacts;
					}

					return oldContacts.map((contact) => {
						if (contact.id === updatedContact.id) {
							return { ...contact, ...updatedContact };
						}
						return contact;
					});
				},
			);
		},
		onMutate: async (variables: UpdateContactPayload) => {
			await queryClient.cancelQueries({
				queryKey: ["contact", variables.id],
			});
			await queryClient.cancelQueries({
				queryKey: ["contacts"],
				exact: false,
			});

			const previousContact = queryClient.getQueryData([
				"contact",
				variables.id,
			]);

			const allContactsQueries = queryClient.getQueriesData({
				queryKey: ["contacts"],
				exact: false,
			});

			if (previousContact) {
				const optimisticContact = {
					...(previousContact as Contact),
					...variables,
				};
				queryClient.setQueryData(
					["contact", variables.id],
					optimisticContact,
				);

				queryClient.setQueriesData(
					{ queryKey: ["contacts"], exact: false },
					(oldContacts: Contact[] | undefined) => {
						if (!oldContacts || !Array.isArray(oldContacts)) {
							return oldContacts;
						}

						return oldContacts.map((contact) => {
							if (contact.id === variables.id) {
								return { ...contact, ...variables };
							}
							return contact;
						});
					},
				);
			}

			return { previousContact, allContactsQueries };
		},
		onError: (err, variables, context) => {
			if (context?.previousContact) {
				queryClient.setQueryData(
					["contact", variables.id],
					context.previousContact,
				);
			}
			if (context?.allContactsQueries) {
				context.allContactsQueries.forEach(([queryKey, data]) => {
					queryClient.setQueryData(queryKey, data);
				});
			}
		},
	});
}

export function useDeleteContact() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: deleteContact,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["contacts-infinite"],
				exact: false,
			});
			queryClient.invalidateQueries({
				queryKey: ["contacts"],
				exact: false,
			});
		},
	});
}

export function useBatchDeleteContacts() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: batchDeleteContacts,
		onSuccess: (data, variables) => {
			queryClient.setQueriesData(
				{
					queryKey: ["contacts-infinite", variables.organizationId],
					exact: false,
				},
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.filter(
									(contact: Contact) =>
										!variables.ids.includes(contact.id),
								),
								meta: {
									...page.meta,
									totalRowCount: Math.max(
										0,
										(page.meta?.totalRowCount || 0) -
											data.deletedCount,
									),
									filterRowCount: Math.max(
										0,
										(page.meta?.filterRowCount || 0) -
											data.deletedCount,
									),
								},
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				},
			);

			// Update all infinite query instances
			const allContactQueries = queryClient.getQueryCache().findAll({
				queryKey: ["contacts-infinite", variables.organizationId],
				exact: false,
			});

			allContactQueries.forEach((query) => {
				queryClient.setQueryData(query.queryKey, (oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.filter(
									(contact: Contact) =>
										!variables.ids.includes(contact.id),
								),
								meta: {
									...page.meta,
									totalRowCount: Math.max(
										0,
										(page.meta?.totalRowCount || 0) -
											data.deletedCount,
									),
									filterRowCount: Math.max(
										0,
										(page.meta?.filterRowCount || 0) -
											data.deletedCount,
									),
								},
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				});
			});

			// Update regular contacts queries
			queryClient.setQueryData(
				["contacts", variables.organizationId],
				(old: Contact[] = []) =>
					old.filter(
						(contact) => !variables.ids.includes(contact.id),
					),
			);

			// Invalidate all related queries
			setTimeout(() => {
				queryClient.invalidateQueries({
					queryKey: ["contacts-infinite", variables.organizationId],
					exact: false,
					refetchType: "all",
				});

				queryClient.invalidateQueries({
					queryKey: ["contacts", variables.organizationId],
					exact: false,
				});

				queryClient.invalidateQueries({
					queryKey: ["objects", "contacts"],
					exact: false,
				});
			}, 500);
		},
	});
}

export function useUpdateContactField() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: updateContactField,
		onSuccess: (updatedContact: Contact, variables) => {
			// Defensive check to ensure updatedContact exists and has an id
			if (!updatedContact || !updatedContact.id) {
				console.error("Invalid contact data in onSuccess:", updatedContact);
				return;
			}
			
			queryClient.setQueryData(
				["contact", updatedContact.id],
				updatedContact,
			);

			// Update regular contacts queries
			queryClient.setQueriesData(
				{ queryKey: ["contacts"], exact: false },
				(oldContacts: Contact[] | undefined) => {
					if (!oldContacts || !Array.isArray(oldContacts)) {
						return oldContacts;
					}

					return oldContacts.map((contact) => {
						if (contact.id === updatedContact.id) {
							return { ...contact, ...updatedContact };
						}
						return contact;
					});
				},
			);

			// Update infinite contact queries
			queryClient.setQueriesData(
				{ queryKey: ["contacts-infinite"], exact: false },
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.map((contact: Contact) => {
									if (contact.id === updatedContact.id) {
										return { ...contact, ...updatedContact };
									}
									return contact;
								}),
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				}
			);
		},
	});
}

export function useClearContactField() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: clearContactField,
		onSuccess: (updatedContact: Contact, variables) => {
			// Defensive check to ensure updatedContact exists and has an id
			if (!updatedContact || !updatedContact.id) {
				console.error("Invalid contact data in onSuccess:", updatedContact);
				return;
			}
			
			queryClient.setQueryData(
				["contact", updatedContact.id],
				updatedContact,
			);

			queryClient.setQueriesData(
				{ queryKey: ["contacts"], exact: false },
				(oldContacts: Contact[] | undefined) => {
					if (!oldContacts || !Array.isArray(oldContacts)) {
						return oldContacts;
					}

					return oldContacts.map((contact) => {
						if (contact.id === updatedContact.id) {
							return { ...contact, ...updatedContact };
						}
						return contact;
					});
				},
			);

			// Update infinite contact queries
			queryClient.setQueriesData(
				{ queryKey: ["contacts-infinite"], exact: false },
				(oldData: any) => {
					if (!oldData?.pages?.length) return oldData;

					const updatedPages = oldData.pages.map((page: any) => {
						if (page?.data) {
							return {
								...page,
								data: page.data.map((contact: Contact) => {
									if (contact.id === updatedContact.id) {
										return { ...contact, ...updatedContact };
									}
									return contact;
								}),
							};
						}
						return page;
					});

					return {
						...oldData,
						pages: updatedPages,
					};
				}
			);

			// NO QUERY INVALIDATION - let the optimistic updates handle cache sync
			// The above cache updates ensure data consistency without triggering refetches
		},
	});
}

export function useContacts(organizationId?: string) {
	return useQuery({
		queryKey: ["contacts", organizationId],
		queryFn: () => fetchContacts(organizationId),
		staleTime: 1000 * 60,
		enabled: !!organizationId,
	});
}

export function useContact(id: string | undefined, organizationId?: string) {
	return useQuery({
		queryKey: ["contact", id],
		queryFn: () => (id && organizationId ? fetchContact(id, organizationId) : Promise.resolve(null)),
		enabled: !!id && !!organizationId,
		staleTime: 1000 * 30, // 30 seconds
	});
}

export function useSearchContacts(query: string, organizationId: string) {
	return useQuery({
		queryKey: ["contacts", "search", query, organizationId],
		queryFn: () => searchContacts(query, organizationId),
		enabled: query.length > 0 && !!organizationId,
		staleTime: 30000, // Cache for 30 seconds
	});
}

export function useRelatedContacts(id: string | undefined, organizationId?: string) {
	return useQuery<RelatedContactPayload[]>({
		queryKey: ["related-contacts", id, organizationId],
		queryFn: () => (id && organizationId ? fetchRelatedContact(id, organizationId) : Promise.resolve([])),
		enabled: !!id && !!organizationId,
		staleTime: 1000 * 30, // 30 seconds
	});
}

// Hook for managing related contacts
export function useUpdateRelatedContacts(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: { contactId: string; relatedContacts: RelatedContactPayload[] }) =>
			updateRelatedContacts({ ...payload, organizationId }),
		onSuccess: (updatedContact: Contact) => {
			// Update the contact query cache
			queryClient.setQueryData(["contact", updatedContact.id], updatedContact);

			// Update the contacts list cache
			queryClient.setQueriesData(
				{ queryKey: ["contacts"], exact: false },
				(oldContacts: Contact[] | undefined) => {
					if (!oldContacts || !Array.isArray(oldContacts)) {
						return oldContacts;
					}

					return oldContacts.map((contact) => {
						if (contact.id === updatedContact.id) {
							return { ...contact, ...updatedContact };
						}
						return contact;
					});
				}
			);


		},
		onMutate: async (variables) => {
			await queryClient.cancelQueries({
				queryKey: ["contact", variables.contactId],
			});
			await queryClient.cancelQueries({
				queryKey: ["contacts"],
				exact: false,
			});

			const previousContact = queryClient.getQueryData([
				"contact",
				variables.contactId,
			]);

			const allContactsQueries = queryClient.getQueriesData({
				queryKey: ["contacts"],
				exact: false,
			});

			if (previousContact) {
				const optimisticContact = {
					...(previousContact as Contact),
					relatedContacts: variables.relatedContacts,
				};
				queryClient.setQueryData(
					["contact", variables.contactId],
					optimisticContact
				);

				queryClient.setQueriesData(
					{ queryKey: ["contacts"], exact: false },
					(oldContacts: Contact[] | undefined) => {
						if (!oldContacts || !Array.isArray(oldContacts)) {
							return oldContacts;
						}

						return oldContacts.map((contact) => {
							if (contact.id === variables.contactId) {
								return JSON.parse(JSON.stringify({
									...contact,
									relatedContacts: variables.relatedContacts,
								}));
							}
							return contact;
						});
					}
				);
			}

			return { previousContact, allContactsQueries };
		},
		onError: (err, variables, context) => {
			if (context?.previousContact) {
				queryClient.setQueryData(
					["contact", variables.contactId],
					context.previousContact
				);
			}
			if (context?.allContactsQueries) {
				context.allContactsQueries.forEach(([queryKey, data]) => {
					queryClient.setQueryData(queryKey, data);
				});
			}
		},
	});
}
