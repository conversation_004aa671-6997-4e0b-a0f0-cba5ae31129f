"use client";

import React, { useState } from "react";
import GridItem from "@ui/components/grid-item";
import { IconEdit, IconPlus, IconHome, IconMapPin, IconCurrencyDollar } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";
import { cn } from "@ui/lib";
import { Skeleton } from "@ui/components/skeleton";
import { LinkPropertyModal } from "../LinkPropertyModal";
import { PROPERTY_TYPES } from "@app/organizations/components/objects/properties/schema";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import Link from "next/link";

interface PropertyData {
  id: string;
  name: string;
  propertyType?: string | null;
  status?: string | null;
  relation?: string;
  linkedAt?: Date;
  image?: string | null;
  location?: {
    address?: {
      street?: string;
      city?: string;
      state?: string;
      zip?: string;
    };
  };
  financials?: {
    price?: number;
    salePrice?: number;
    estimatedValue?: number;
  };
}

interface ContactPropertiesProps {
  data: {
    id: string;
    firstName?: string;
    lastName?: string;
    name?: string;
    email?: string[];
    image?: string;
  };
  organization: ActiveOrganization;
  loading?: boolean;
  properties?: PropertyData[];
  onAddProperty?: () => void;
  onEditProperty?: (property: PropertyData) => void;
  onRemoveProperty?: (propertyId: string) => void;
}

const PropertyCardSkeleton = () => (
  <div className="w-full">
    <div className="flex items-center space-x-2">
      <div className="flex-1 space-y-1">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </div>
      <Skeleton className="h-8 w-8 rounded-full" />
    </div>
  </div>
);

const Portfolio = ({ 
  data, 
  organization, 
  loading = false, 
  properties = [], 
  onAddProperty,
  onEditProperty,
  onRemoveProperty 
}: ContactPropertiesProps) => {
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");
  const [selectedProperty, setSelectedProperty] = useState<PropertyData | null>(null);
  const contact = data;

  const handleEditProperty = (property: PropertyData) => {
    setSelectedProperty(property);
    setModalMode("edit");
    setIsLinkModalOpen(true);
    
    if (onEditProperty) {
      onEditProperty(property);
    }
  };

  const handleAddProperty = () => {
    setSelectedProperty(null);
    setModalMode("add");
    setIsLinkModalOpen(true);
    
    if (onAddProperty) {
      onAddProperty();
    }
  };

  const handleModalClose = (open: boolean) => {
    setIsLinkModalOpen(open);
    if (!open) {
      setSelectedProperty(null);
      setModalMode("add");
    }
  };

  const formatAddress = (location?: PropertyData["location"]) => {
    if (!location?.address) return "No address";
    
    const { address } = location;
    const parts = [
      address.street,
      [address.city, address.state, address.zip].filter(Boolean).join(", ")
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(", ") : "No address";
  };

  const formatPrice = (financials?: PropertyData["financials"]) => {
    if (!financials) return null;
    
    const price = financials.price || financials.salePrice || financials.estimatedValue;
    if (!price) return null;
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  if (!data) {
    return (
      <div className="space-y-2">
        <PropertyCardSkeleton />
      </div>
    );
  }

  return (
    <>
      <div className="space-y-1">
        {loading ? (
          <>
            <PropertyCardSkeleton />
          </>
        ) : properties && properties.length > 0 ? (
          <>
            {properties.map((property, index) => {
              const formattedPrice = formatPrice(property.financials);
              
              return (
                <GridItem
                  key={property.id}
                  value={
                    <Link href={`/app/${organization?.slug}/property/${property.id}`}>
                      <div className="flex items-center gap-2">
                        <div className="border rounded-xl p-1">
                          <PropertyAvatar
                            name={property.name}
                            avatarUrl={property.image}
                            className="h-8 w-8"
                          />
                        </div>
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {property.name}
                            </span>
                            {property.status && (
                              <span className={cn(
                                "text-xs px-1.5 py-0.5 rounded-full",
                                property.status.toLowerCase() === "active" && "bg-green-100 text-green-700",
                                property.status.toLowerCase() === "pending" && "bg-yellow-100 text-yellow-700",
                                property.status.toLowerCase() === "sold" && "bg-blue-100 text-blue-700",
                                property.status.toLowerCase() === "off market" && "bg-gray-100 text-gray-700"
                              )}>
                                {property.status}
                              </span>
                            )}
                            {property.propertyType && (
                              <span className="text-xs px-1.5 py-0.5 bg-primary/10 text-primary rounded-full">
                                {PROPERTY_TYPES.find((type: { value: string; label: string }) => type.value === property.propertyType)?.label || 
                                property.propertyType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                              </span>
                            )}
                          </div>
                          <div className="flex flex-col gap-0.5 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1.5">
                              <IconMapPin className="h-3 w-3 shrink-0" />
                              <span className="truncate">
                                {formatAddress(property.location)}
                              </span>
                            </div>
                            {formattedPrice && (
                              <div className="flex items-center gap-1.5">
                                <IconCurrencyDollar className="h-3 w-3 shrink-0" />
                                <span className="font-medium text-foreground">
                                  {formattedPrice}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </Link>
                  }
                  edit={<IconEdit className="h-3 w-3" />}
                  add={<IconPlus className="h-3 w-3" />}
                  onEdit={() => handleEditProperty(property)}
                  onAdd={handleAddProperty}
                  badge={property.relation}
                  badgeClassName="bg-muted rounded-full text-xs px-1.5 py-0.5"
                />
              );
            })}
          </>
        ) : (
          <GridItem
            value="Link Property"
            onClick={handleAddProperty}
            icon={<IconHome className="h-4 w-4 text-muted-foreground" />}
          />
        )}
      </div>
      
      {/* Link Property Modal */}
      <LinkPropertyModal
        open={isLinkModalOpen}
        onOpenChange={handleModalClose}
        contact={contact}
        organization={organization}
        mode={modalMode}
        propertyToEdit={selectedProperty as any}
      />
    </>
  );
};

export default Portfolio;
