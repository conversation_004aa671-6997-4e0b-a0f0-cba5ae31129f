"use client";

import React from "react";
import { IconCalendar, IconEye, IconHash } from "@tabler/icons-react";
import { UserAvatar } from "@shared/components/UserAvatar";
import type { ActiveOrganization } from "@repo/auth";

interface ContactMetadataProps {
	data?: any;
	organization?: ActiveOrganization;
}

const HeadingText = ({ text }: { text: string }) => {
	return <h4 className="text-xs text-muted-foreground mb-1 cursor-default">{text}</h4>;
};

const formatDate = (dateString: string | Date) => {
	return new Date(dateString)
		.toLocaleString("en-US", {
			month: "long",
			day: "numeric",
			year: "numeric",
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		})
		.replace(/(\d+),/, (match, day) => {
			// Add ordinal suffix to day
			const n = Number.parseInt(day, 10);
			const s = ["th", "st", "nd", "rd"],
				v = n % 100;
			const ordinal =
				s[(v - 20) % 10] ||
				s[v] ||
				s[0];
			return ` ${n}${ordinal},`;
		});
};

const ContactMetadata = ({ data }: ContactMetadataProps) => {
	if (!data) {
		return (
			<div className="p-4 space-y-3">
				{/* Loading skeletons */}
				{[...Array(4)].map((_, i) => (
					<div key={i} className="space-y-1">
						<div className="h-3 w-16 bg-muted rounded animate-pulse" />
						<div className="h-4 w-32 bg-muted rounded animate-pulse" />
					</div>
				))}
			</div>
		);
	}

	return (
		<div className="p-4 space-y-4">
			{/* Created By */}
			{data.creator && (
				<div>
					<HeadingText text="Created by" />
					<div className="flex items-center gap-2">
						<UserAvatar
							className="h-6 w-6"
							name={data.creator.name}
							avatarUrl={data.creator.image}
						/>
						<span className="text-sm">
							{data.creator.name}
						</span>
					</div>
				</div>
			)}

			{/* Created At */}
			{data.createdAt && (
				<div>
					<HeadingText text="Created" />
					<div className="flex items-center gap-2">
						<IconCalendar className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm">
							{formatDate(data.createdAt)}
						</span>
					</div>
				</div>
			)}

			{/* Updated By */}
			{data.updater && (
				<div>
					<HeadingText text="Last updated by" />
					<div className="flex items-center gap-2">
						<UserAvatar
							className="h-6 w-6"
							name={data.updater.name}
							avatarUrl={data.updater.image}
						/>
						<span className="text-sm">
							{data.updater.name}
						</span>
					</div>
				</div>
			)}

			{/* Updated At */}
			{data.updatedAt && (
				<div>
					<HeadingText text="Last updated" />
					<div className="flex items-center gap-2">
						<IconCalendar className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm">
							{formatDate(data.updatedAt)}
						</span>
					</div>
				</div>
			)}

			{/* Last Viewed By */}
			{data.lastViewer && (
				<div>
					<HeadingText text="Last viewed by" />
					<div className="flex items-center gap-2">
						<UserAvatar
							className="h-6 w-6"
							name={data.lastViewer.name}
							avatarUrl={data.lastViewer.image}
						/>
						<span className="text-sm">
							{data.lastViewer.name}
						</span>
					</div>
				</div>
			)}

			{/* Last Viewed At */}
			{data.lastViewedAt && (
				<div>
					<HeadingText text="Last viewed" />
					<div className="flex items-center gap-2">
						<IconEye className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm">
							{formatDate(data.lastViewedAt)}
						</span>
					</div>
				</div>
			)}

			{/* Record ID for reference */}
			{data.id && (
				<div>
					<HeadingText text="Record ID" />
					<div className="flex items-center gap-2">
						<IconHash className="h-4 w-4 text-muted-foreground" />
						<span className="text-xs font-mono bg-muted px-2 py-1 rounded">
							{data.id}
						</span>
					</div>
				</div>
			)}
		</div>
	);
};

export default ContactMetadata; 