"use client";

import React, { useState, useEffect } from "react";
import { Textarea } from "@ui/components/textarea";
import { IconUser } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";
import { useUpdateContact } from "../../lib/api";

interface ContactSummaryProps {
	data: any;
	organization: ActiveOrganization;
}

const ContactSummary = ({ data, organization }: ContactSummaryProps) => {
	const [summary, setSummary] = useState(data?.summary || '');
	const { mutateAsync: updateContact } = useUpdateContact(organization?.id);

	useEffect(() => {
		if (data?.summary !== undefined) {
			setSummary(data.summary || '');
		}
	}, [data?.summary]);

	if (!data) {
		return (
			<div className="animate-pulse">
				<div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
				<div className="h-3 bg-muted rounded w-full mb-1"></div>
				<div className="h-3 bg-muted rounded w-2/3"></div>
			</div>
		);
	}

	const handleSummaryChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setSummary(e.target.value);
	};

	const handleUpdateSummary = async () => {
		if (summary !== data?.summary) {
			try {
				await updateContact({
					id: data.id,
					summary: summary,
					organizationId: organization?.id
				});
			} catch (error) {
				console.error('Error updating contact summary:', error);
			}
		}
	};

	return (
		<Textarea
			className="w-full min-h-[120px] border-none shadow-none focus-visible:ring-0 focus-visible:outline-none transition-all duration-200 rounded-lg"
			placeholder="Add a summary about this contact..."
			value={summary}
			onChange={handleSummaryChange}
			onBlur={handleUpdateSummary}
			style={{ resize: 'none' }}
		/>
	);
};

export default ContactSummary;