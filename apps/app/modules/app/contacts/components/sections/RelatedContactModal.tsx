"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@ui/components/dialog";
import { useUpdateRelatedContacts } from "../../lib/api";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { IconUser, IconPlus, IconTrash } from "@tabler/icons-react";
import { formatPhoneNumber } from "@shared/lib/utils";
import { Skeleton } from "@ui/components/skeleton";

interface RelatedContactModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  relatedContacts: any[];
  editingIndex: number | null;
  onRelatedContactsChange: (relatedContacts: any[]) => void;
  contactId?: string;
  organizationId?: string;
  loading?: boolean;
}

const relatedContactSchema = z.object({
  relatedContact: z
    .array(
      z.object({
        label: z.string().default("Family"),
        firstName: z.string().min(1, "First name is required"),
        lastName: z.string().min(1, "Last name is required"),
        phone: z.array(
          z.object({
            label: z.string().default("Work"),
            number: z.string(),
            isPrimary: z.boolean().default(false),
            isBad: z.boolean().default(false),
          })
        ).optional(),
        email: z.array(
          z.object({
            label: z.string().default("Work"),
            address: z.string().email("Invalid email address"),
            isPrimary: z.boolean().default(false),
            isBad: z.boolean().default(false),
          })
        ).optional(),
        address: z.array(
          z.object({
            label: z.string().default("Work"),
            street: z.string(),
            street2: z.string().optional(),
            city: z.string(),
            state: z.string(),
            zip: z.string(),
            country: z.string().default("United States"),
          })
        ).optional(),
      })
    )
    .min(1, "At least one related contact is required"),
});

type RelatedContactFormData = z.infer<typeof relatedContactSchema>;

const SkeletonInput = () => (
  <Skeleton className="h-9 w-full rounded-md" />
);

const SkeletonSelect = () => (
  <Skeleton className="h-9 w-full rounded-md" />
);

const LOADING_PLACEHOLDER = {
  label: "",
  firstName: "",
  lastName: "",
  phone: [{ number: "", label: "Work", isPrimary: true, isBad: false }],
  email: [{ address: "", label: "Work", isPrimary: true, isBad: false }],
  address: [{ street: "", city: "", state: "", zip: "", country: "United States", label: "Work" }],
};

export function RelatedContactModal({
  open,
  onOpenChange,
  relatedContacts,
  editingIndex,
  onRelatedContactsChange,
  contactId,
  organizationId,
  loading = true,
}: RelatedContactModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customLabelIndices, setCustomLabelIndices] = useState<Set<number>>(new Set());
  const updateRelatedContacts = useUpdateRelatedContacts(organizationId);

  const form = useForm<RelatedContactFormData>({
    resolver: zodResolver(relatedContactSchema),
    mode: "onSubmit",
    reValidateMode: "onSubmit",
    defaultValues: {
      relatedContact: [],
    },
  });

  // Initialize form data when modal opens
  React.useEffect(() => {
    if (open) {
      let contactsToSet: any[] = [];
      
      if (editingIndex !== null && relatedContacts[editingIndex]) {
        // Editing a specific contact
        contactsToSet = [relatedContacts[editingIndex]];
      } else {
        // Adding a new contact
        contactsToSet = [{
          label: "Family",
          firstName: "",
          lastName: "",
          phone: [],
          email: [],
          address: [],
        }];
      }
      
      form.reset({
        relatedContact: contactsToSet,
      });
      
      // Initialize custom label indices for existing custom labels
      const predefinedLabels = ["Family", "Spouse", "Child", "Parent", "Sibling", "Other"];
      const customIndices = new Set<number>();
      contactsToSet.forEach((contact, index) => {
        if (contact.label && !predefinedLabels.includes(contact.label)) {
          customIndices.add(index);
        }
      });
      setCustomLabelIndices(customIndices);
    }
  }, [open, relatedContacts, editingIndex, form]);

  // Reset form when modal closes
  React.useEffect(() => {
    if (!open) {
      form.reset({
        relatedContact: [],
      });
      setCustomLabelIndices(new Set());
    }
  }, [open, form]);

  async function onSubmit(formData: RelatedContactFormData) {
    setIsSubmitting(true);

    try {
      // Filter out empty contacts
      const validContacts = formData.relatedContact.filter(
        (contact) => contact.firstName.trim() !== "" && contact.lastName.trim() !== ""
      );

      if (validContacts.length === 0) {
        toast.error("Please enter at least one related contact");
        setIsSubmitting(false);
        return;
      }

      let finalContacts: any[];

      // If editing a specific contact, replace it in the original array
      if (editingIndex !== null) {
        finalContacts = [...relatedContacts];
        finalContacts[editingIndex] = validContacts[0];
      } else {
        // Add new contact
        finalContacts = [...relatedContacts, ...validContacts];
      }

      // Update in database if contactId is provided
      if (contactId) {
        await updateRelatedContacts.mutateAsync({
          contactId,
          relatedContacts: finalContacts.map((contact) => ({
            firstName: contact.firstName,
            lastName: contact.lastName,
            label: contact.label,
            address: contact.address,
            phone: contact.phone,
            email: contact.email,
            organizationId,
          })),
        });
        toast.success(
          editingIndex !== null
            ? "Related contact updated successfully"
            : "Related contact added successfully"
        );
      } else {
        // Fallback to callback for non-database scenarios
        onRelatedContactsChange(finalContacts);
        toast.success("Related contacts updated");
      }

      onOpenChange(false);
    } catch (error: any) {
      toast.error(error?.message || "Failed to save related contact");
    } finally {
      setIsSubmitting(false);
    }
  }

  // Show form errors as toast messages
  React.useEffect(() => {
    const errors = form.formState.errors;
    if (Object.keys(errors).length > 0) {
      const firstError = Object.values(errors)[0];
      if (firstError?.message) {
        toast.error(firstError.message as string);
      }
    }
  }, [form.formState.errors]);

  const title = editingIndex !== null ? "Edit Related Contact" : "Add Related Contact";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogTitle className="sr-only">{title}</DialogTitle>
        <DialogDescription className="sr-only">
          {editingIndex !== null
            ? "Edit an existing related contact"
            : "Add a new related contact"}
        </DialogDescription>

        <div className="flex items-center justify-between px-4 pt-2 pb-1">
          <div className="flex flex-row items-center gap-x-2">
            <IconUser className="h-5 w-5" />
            <span className="text-md font-light">{title}</span>
          </div>
        </div>

        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col flex-1 min-h-0 overflow-y-auto max-h-[600px]"
        >
          <div className="flex flex-col px-2 pt-2 pb-4 space-y-4 overflow-y-auto flex-1">
            <div className="space-y-1">
              <div className="space-y-3">
                {(loading ? [LOADING_PLACEHOLDER] : form.watch("relatedContact"))?.map((contact, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3 bg-muted/50">
                    {/* Label Select/Input */}
                    <div className="space-y-1">
                      <Label className="text-xs text-muted-foreground">
                        Relationship
                      </Label>
                      <div>
                        {loading ? (
                          <SkeletonSelect />
                        ) : (
                          <>
                            <Select
                              value={contact.label || "Family"}
                              onValueChange={(value) => {
                                const currentContacts = form.watch("relatedContact") || [];
                                const newContacts = [...currentContacts];
                                newContacts[index] = {
                                  ...newContacts[index],
                                  label: value,
                                };
                                form.setValue("relatedContact", newContacts);
                              }}
                            >
                              <SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
                                <SelectValue placeholder="Select relationship..." />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Family">Family</SelectItem>
                                <SelectItem value="Spouse">Spouse</SelectItem>
                                <SelectItem value="Child">Child</SelectItem>
                                <SelectItem value="Parent">Parent</SelectItem>
                                <SelectItem value="Sibling">Sibling</SelectItem>
                                <SelectItem value="Other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Name Fields */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs text-muted-foreground">
                          First Name
                        </Label>
                        {loading ? (
                          <SkeletonInput />
                        ) : (
                          <Input
                            value={contact.firstName}
                            onChange={(e) => {
                              const currentContacts = form.watch("relatedContact") || [];
                              const newContacts = [...currentContacts];
                              newContacts[index] = {
                                ...newContacts[index],
                                firstName: e.target.value,
                              };
                              form.setValue("relatedContact", newContacts);
                            }}
                            placeholder="First name"
                            className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                            autoFocus={editingIndex === null && index === 0}
                          />
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs text-muted-foreground">
                          Last Name
                        </Label>
                        {loading ? (
                          <SkeletonInput />
                        ) : (
                          <Input
                            value={contact.lastName}
                            onChange={(e) => {
                              const currentContacts = form.watch("relatedContact") || [];
                              const newContacts = [...currentContacts];
                              newContacts[index] = {
                                ...newContacts[index],
                                lastName: e.target.value,
                              };
                              form.setValue("relatedContact", newContacts);
                            }}
                            placeholder="Last name"
                            className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                          />
                        )}
                      </div>
                    </div>

                    {/* Phone Number */}
                    <div className="space-y-1">
                      <Label className="text-xs text-muted-foreground">
                        Phone Number
                      </Label>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="col-span-2">
                          {loading ? (
                            <SkeletonInput />
                          ) : (
                            <Input
                              value={formatPhoneNumber(contact.phone?.[0]?.number || "")}
                              onChange={(e) => {
                                const currentContacts = form.watch("relatedContact") || [];
                                const newContacts = [...currentContacts];
                                newContacts[index] = {
                                  ...newContacts[index],
                                  phone: [{
                                    number: e.target.value.replace(/[^\d]/g, ""),
                                    label: contact.phone?.[0]?.label || "Work",
                                    isPrimary: true,
                                    isBad: false,
                                  }],
                                };
                                form.setValue("relatedContact", newContacts);
                              }}
                              placeholder="(*************"
                              className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                            />
                          )}
                        </div>
                        {loading ? (
                          <SkeletonSelect />
                        ) : (
                          <Select
                            value={contact.phone?.[0]?.label || "Work"}
                            onValueChange={(value) => {
                              const currentContacts = form.watch("relatedContact") || [];
                              const newContacts = [...currentContacts];
                              const currentPhone = newContacts[index].phone?.[0] || { number: "", isPrimary: true, isBad: false };
                              newContacts[index] = {
                                ...newContacts[index],
                                phone: [{
                                  ...currentPhone,
                                  label: value,
                                }],
                              };
                              form.setValue("relatedContact", newContacts);
                            }}
                          >
                            <SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
                              <SelectValue placeholder="Label" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Work">Work</SelectItem>
                              <SelectItem value="Home">Home</SelectItem>
                              <SelectItem value="Mobile">Mobile</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                    </div>

                    {/* Email */}
                    <div className="space-y-1">
                      <Label className="text-xs text-muted-foreground">
                        Email Address
                      </Label>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="col-span-2">
                          {loading ? (
                            <SkeletonInput />
                          ) : (
                            <Input
                              value={contact.email?.[0]?.address || ""}
                              onChange={(e) => {
                                const currentContacts = form.watch("relatedContact") || [];
                                const newContacts = [...currentContacts];
                                newContacts[index] = {
                                  ...newContacts[index],
                                  email: [{
                                    address: e.target.value,
                                    label: contact.email?.[0]?.label || "Work",
                                    isPrimary: true,
                                    isBad: false,
                                  }],
                                };
                                form.setValue("relatedContact", newContacts);
                              }}
                              placeholder="<EMAIL>"
                              className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                            />
                          )}
                        </div>
                        {loading ? (
                          <SkeletonSelect />
                        ) : (
                          <Select
                            value={contact.email?.[0]?.label || "Work"}
                            onValueChange={(value) => {
                              const currentContacts = form.watch("relatedContact") || [];
                              const newContacts = [...currentContacts];
                              const currentEmail = newContacts[index].email?.[0] || { address: "", isPrimary: true, isBad: false };
                              newContacts[index] = {
                                ...newContacts[index],
                                email: [{
                                  ...currentEmail,
                                  label: value,
                                }],
                              };
                              form.setValue("relatedContact", newContacts);
                            }}
                          >
                            <SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
                              <SelectValue placeholder="Label" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Work">Work</SelectItem>
                              <SelectItem value="Home">Home</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                    </div>

                    {/* Address */}
                    <div className="space-y-1">
                      <Label className="text-xs text-muted-foreground">
                        Address
                      </Label>
                      <div className="space-y-2">
                        {loading ? (
                          <SkeletonInput />
                        ) : (
                          <Input
                            value={contact.address?.[0]?.street || ""}
                            onChange={(e) => {
                              const currentContacts = form.watch("relatedContact") || [];
                              const newContacts = [...currentContacts];
                              const currentAddress = newContacts[index].address?.[0] || {
                                label: "Work",
                                street: "",
                                city: "",
                                state: "",
                                zip: "",
                                country: "United States",
                              };
                              newContacts[index] = {
                                ...newContacts[index],
                                address: [{
                                  ...currentAddress,
                                  street: e.target.value,
                                }],
                              };
                              form.setValue("relatedContact", newContacts);
                            }}
                            placeholder="Street Address"
                            className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                          />
                        )}
                        <div className="grid grid-cols-3 gap-2">
                          {loading ? (
                            <>
                              <SkeletonInput />
                              <SkeletonInput />
                              <SkeletonInput />
                            </>
                          ) : (
                            <>
                              <Input
                                value={contact.address?.[0]?.city || ""}
                                onChange={(e) => {
                                  const currentContacts = form.watch("relatedContact") || [];
                                  const newContacts = [...currentContacts];
                                  const currentAddress = newContacts[index].address?.[0] || {
                                    label: "Work",
                                    street: "",
                                    city: "",
                                    state: "",
                                    zip: "",
                                    country: "United States",
                                  };
                                  newContacts[index] = {
                                    ...newContacts[index],
                                    address: [{
                                      ...currentAddress,
                                      city: e.target.value,
                                    }],
                                  };
                                  form.setValue("relatedContact", newContacts);
                                }}
                                placeholder="City"
                                className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                              />
                              <Input
                                value={contact.address?.[0]?.state || ""}
                                onChange={(e) => {
                                  const currentContacts = form.watch("relatedContact") || [];
                                  const newContacts = [...currentContacts];
                                  const currentAddress = newContacts[index].address?.[0] || {
                                    label: "Work",
                                    street: "",
                                    city: "",
                                    state: "",
                                    zip: "",
                                    country: "United States",
                                  };
                                  newContacts[index] = {
                                    ...newContacts[index],
                                    address: [{
                                      ...currentAddress,
                                      state: e.target.value,
                                    }],
                                  };
                                  form.setValue("relatedContact", newContacts);
                                }}
                                placeholder="State"
                                className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                              />
                              <Input
                                value={contact.address?.[0]?.zip || ""}
                                onChange={(e) => {
                                  const currentContacts = form.watch("relatedContact") || [];
                                  const newContacts = [...currentContacts];
                                  const currentAddress = newContacts[index].address?.[0] || {
                                    label: "Work",
                                    street: "",
                                    city: "",
                                    state: "",
                                    zip: "",
                                    country: "United States",
                                  };
                                  newContacts[index] = {
                                    ...newContacts[index],
                                    address: [{
                                      ...currentAddress,
                                      zip: e.target.value,
                                    }],
                                  };
                                  form.setValue("relatedContact", newContacts);
                                }}
                                placeholder="ZIP"
                                className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
                              />
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Delete Button */}
                    {!loading && form.watch("relatedContact").length > 1 && (
                      <div className="flex items-center justify-end">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const currentContacts = form.watch("relatedContact") || [];
                            const newContacts = currentContacts.filter((_, i) => i !== index);
                            form.setValue("relatedContact", newContacts);
                            
                            // Update custom label indices when removing a contact
                            setCustomLabelIndices(prev => {
                              const newSet = new Set<number>();
                              Array.from(prev).forEach(i => {
                                if (i < index) {
                                  newSet.add(i);
                                } else if (i > index) {
                                  newSet.add(i - 1);
                                }
                                // Skip i === index as it's being removed
                              });
                              return newSet;
                            });
                          }}
                          className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                        >
                          <IconTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                ))}

                {/* Add Contact Button */}
                {!loading && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const currentContacts = form.watch("relatedContact") || [];
                      const newContacts = [
                        ...currentContacts,
                        {
                          label: "Family",
                          firstName: "",
                          lastName: "",
                          phone: [],
                          email: [],
                          address: [],
                        },
                      ];
                      form.setValue("relatedContact", newContacts);
                    }}
                    className="w-full border-dashed !bg-transparent hover:!bg-muted/30 items-center gap-1"
                  >
                    <IconPlus className="h-4 w-4" />
                    Add Another Contact
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Fixed Footer */}
          <div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              disabled={loading || isSubmitting || updateRelatedContacts.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              size="sm"
              disabled={loading || isSubmitting || updateRelatedContacts.isPending}
            >
              {(loading || isSubmitting || updateRelatedContacts.isPending) ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  <span>Saving...</span>
                </div>
              ) : (
                "Save"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 