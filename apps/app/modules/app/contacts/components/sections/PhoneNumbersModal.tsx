"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { useUpdateContact } from "../../lib/api";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { IconPhone, IconPlus, IconTrash, IconX } from "@tabler/icons-react";
import { formatPhoneNumber, extractPhoneNumber } from "@shared/lib/utils";
import { cn } from "@ui/lib";

interface PhoneNumbersModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	phoneNumbers: any[];
	editingIndex: number | null;
	onPhoneNumbersChange: (phoneNumbers: any[]) => void;
	contactId?: string;
	organizationId?: string;
}

const phoneNumbersSchema = z.object({
	phone: z
		.array(
			z.object({
				label: z.string().default("Work"),
				number: z.string().min(1, "Phone number is required"),
				isPrimary: z.boolean().default(false),
				isBad: z.boolean().default(false),
			}),
		)
		.min(1, "At least one phone number is required"),
});

type PhoneNumbersFormData = z.infer<typeof phoneNumbersSchema>;

export function PhoneNumbersModal({
	open,
	onOpenChange,
	phoneNumbers,
	editingIndex,
	onPhoneNumbersChange,
	contactId,
	organizationId,
}: PhoneNumbersModalProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [customLabelIndices, setCustomLabelIndices] = useState<Set<number>>(new Set());
	const updateContact = useUpdateContact(organizationId);

	const form = useForm<PhoneNumbersFormData>({
		resolver: zodResolver(phoneNumbersSchema),
		mode: "onSubmit",
		reValidateMode: "onSubmit",
		defaultValues: {
			phone: [],
		},
	});

	// Initialize form data when modal opens
	React.useEffect(() => {
		if (open) {
			let phonesToSet: any[] = [];
			
			if (editingIndex !== null && phoneNumbers[editingIndex]) {
				// Editing a specific phone number
				phonesToSet = [phoneNumbers[editingIndex]];
			} else if (phoneNumbers.length > 0) {
				// Editing all phone numbers
				phonesToSet = phoneNumbers;
			} else {
				// Adding first phone number
				phonesToSet = [
					{
						label: "Work",
						number: "",
						isPrimary: true,
						isBad: false,
					},
				];
			}
			
			form.reset({
				phone: phonesToSet,
			});
			
			// Initialize custom label indices for existing custom labels
			const predefinedLabels = ["Work", "Home", "Mobile", "Fax"];
			const customIndices = new Set<number>();
			phonesToSet.forEach((phone, index) => {
				if (phone.label && !predefinedLabels.includes(phone.label)) {
					customIndices.add(index);
				}
			});
			setCustomLabelIndices(customIndices);
		}
	}, [open, phoneNumbers, editingIndex, form]);

	// Reset form when modal closes
	React.useEffect(() => {
		if (!open) {
			form.reset({
				phone: [],
			});
			setCustomLabelIndices(new Set());
		}
	}, [open, form]);

	async function onSubmit(formData: PhoneNumbersFormData) {
		setIsSubmitting(true);

		try {
			// Filter out empty phone numbers
			const validPhoneNumbers = formData.phone.filter(
				(phone) => phone.number.trim() !== ""
			);

			if (validPhoneNumbers.length === 0) {
				toast.error("Please enter at least one phone number");
				setIsSubmitting(false);
				return;
			}

			let finalPhoneNumbers: any[];

			// If editing a specific phone number, replace it in the original array
			if (editingIndex !== null) {
				finalPhoneNumbers = [...phoneNumbers];
				finalPhoneNumbers[editingIndex] = validPhoneNumbers[0];
			} else {
				// Replace all phone numbers or add new ones
				finalPhoneNumbers = validPhoneNumbers;
			}

			// Update in database if contactId is provided
			if (contactId) {
				await updateContact.mutateAsync({
					id: contactId,
					phone: finalPhoneNumbers.map((phone) => ({
						number: phone.number,
						label: phone.label,
						isPrimary: phone.isPrimary,
						isBad: phone.isBad,
					})),
				});
				toast.success(
					editingIndex !== null
						? "Phone number updated successfully"
						: "Phone numbers saved successfully"
				);
			} else {
				// Fallback to callback for non-database scenarios (like during contact creation)
				onPhoneNumbersChange(finalPhoneNumbers);
				toast.success("Phone numbers updated");
			}

			onOpenChange(false);
		} catch (error: any) {
			toast.error(error?.message || "Failed to save phone numbers");
		} finally {
			setIsSubmitting(false);
		}
	}

	// Show form errors as toast messages
	React.useEffect(() => {
		const errors = form.formState.errors;
		if (Object.keys(errors).length > 0) {
			const firstError = Object.values(errors)[0];
			if (firstError?.message) {
				toast.error(firstError.message as string);
			}
		}
	}, [form.formState.errors]);

	const title = editingIndex !== null ? "Edit Phone Number" : "Manage Phone Numbers";

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0"
				onPointerDownOutside={(e) => e.preventDefault()}
			>
				<DialogTitle className="sr-only">{title}</DialogTitle>
				<DialogDescription className="sr-only">
					{editingIndex !== null
						? "Edit an existing phone number"
						: "Add or edit phone numbers"}
				</DialogDescription>

				<div className="flex items-center justify-between px-4 pt-2 pb-1">
					<div className="flex flex-row items-center gap-x-2">
						<IconPhone className="h-5 w-5" />
						<span className="text-md font-light">{title}</span>
					</div>
				</div>

				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="flex flex-col flex-1 min-h-0 overflow-y-auto max-h-[600px]"
				>
					<div className="flex flex-col px-2 pt-2 pb-4 space-y-4 overflow-y-auto flex-1">
						<div className="space-y-1">
							<div className="space-y-3">
								{form.watch("phone")?.map((phone, index) => (
									<div key={index} className="border rounded-lg p-4 space-y-3 bg-muted/50">

										{/* Phone Number and Label Row */}
										<div className="grid grid-cols-2 gap-3">
											{/* Phone Number Input */}
											<div className="space-y-1">
												<Label className="text-xs text-muted-foreground">
													Phone Number
												</Label>
												<Input
													value={formatPhoneNumber(phone.number || "")}
													onChange={(e) => {
														const currentPhones = form.watch("phone") || [];
														const newPhones = [...currentPhones];
														const rawNumber = extractPhoneNumber(e.target.value);
														newPhones[index] = {
															...newPhones[index],
															number: rawNumber,
														};
														form.setValue("phone", newPhones);
													}}
													placeholder="(*************"
													className={cn(
														"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
														phone.isBad && "text-red-500 line-through"
													)}
													autoFocus={editingIndex === null && index === 0}
												/>
											</div>

											{/* Label Select/Input */}
											<div className="space-y-1">
												<Label className="text-xs text-muted-foreground">
													Label
												</Label>
												<div>
													{(() => {
														const predefinedLabels = ["Work", "Home", "Mobile", "Fax"];
														const isCustomMode = customLabelIndices.has(index);
														const currentLabel = phone.label || "";
														
														// Determine select value based on custom mode state, not label content
														const selectValue = isCustomMode ? "Other" : (currentLabel || "Work");

														return (
															<>
																<Select
																	value={selectValue}
																	onValueChange={(value) => {
																		const currentPhones = form.watch("phone") || [];
																		const newPhones = [...currentPhones];
																		
																		if (value === "Other") {
																			// Switch to custom mode
																			setCustomLabelIndices(prev => new Set(Array.from(prev).concat(index)));
																			// Clear the label when switching to custom mode
																			newPhones[index] = {
																				...newPhones[index],
																				label: "",
																			};
																		} else {
																			// Switch to predefined mode
																			setCustomLabelIndices(prev => {
																				const newSet = new Set(prev);
																				newSet.delete(index);
																				return newSet;
																			});
																			newPhones[index] = {
																				...newPhones[index],
																				label: value,
																			};
																		}
																		form.setValue("phone", newPhones);
																	}}
																>
																	<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
																		<SelectValue placeholder="Select label..." />
																	</SelectTrigger>
																	<SelectContent>
																		<SelectItem value="Work">Work</SelectItem>
																		<SelectItem value="Home">Home</SelectItem>
																		<SelectItem value="Mobile">Mobile</SelectItem>
																		<SelectItem value="Fax">Fax</SelectItem>
																		<SelectItem value="Other">Other</SelectItem>
																	</SelectContent>
																</Select>
																
																{isCustomMode && (
																	<Input
																		value={currentLabel}
																		onChange={(e) => {
																			const currentPhones = form.watch("phone") || [];
																			const newPhones = [...currentPhones];
																			newPhones[index] = {
																				...newPhones[index],
																				label: e.target.value,
																			};
																			form.setValue("phone", newPhones);
																		}}
																		placeholder="Custom label..."
																		className="mt-1 cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
																		autoFocus
																	/>
																)}
															</>
														);
													})()}
												</div>
											</div>
										</div>

										{/* Checkboxes */}
										<div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`primary-${index}`}
                            checked={phone.isPrimary || false}
                            onCheckedChange={(checked) => {
                              const currentPhones = form.watch("phone") || [];
                              const newPhones = [...currentPhones];
                              
                              // If setting this as primary, unset all others
                              if (checked) {
                                newPhones.forEach((p, i) => {
                                  p.isPrimary = i === index;
                                });
                              } else {
                                newPhones[index] = {
                                  ...newPhones[index],
                                  isPrimary: false,
                                };
                              }
                              form.setValue("phone", newPhones);
                            }}
                          />
                          <Label 
                            htmlFor={`primary-${index}`}
                            className="text-xs text-muted-foreground cursor-pointer"
                          >
                            Primary
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`bad-${index}`}
                            checked={phone.isBad || false}
                            onCheckedChange={(checked) => {
                              const currentPhones = form.watch("phone") || [];
                              const newPhones = [...currentPhones];
                              newPhones[index] = {
                                ...newPhones[index],
                                isBad: checked as boolean,
                              };
                              form.setValue("phone", newPhones);
                            }}
                          />
                          <Label 
                            htmlFor={`bad-${index}`}
                            className="text-xs text-muted-foreground cursor-pointer"
                          >
                            Bad Number
                          </Label>
                        </div>
                      </div>

                      <div className="flex items-center justify-end">
                                                 {form.watch("phone").length > 1 && (
                           <Button
                             type="button"
                             variant="ghost"
                             size="sm"
                             onClick={() => {
                               const currentPhones = form.watch("phone") || [];
                               const newPhones = currentPhones.filter((_, i) => i !== index);
                               form.setValue("phone", newPhones);
                               
                               // Update custom label indices when removing a phone
                               setCustomLabelIndices(prev => {
                                 const newSet = new Set<number>();
                                 Array.from(prev).forEach(i => {
                                   if (i < index) {
                                     newSet.add(i);
                                   } else if (i > index) {
                                     newSet.add(i - 1);
                                   }
                                   // Skip i === index as it's being removed
                                 });
                                 return newSet;
                               });
                             }}
                             className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                           >
                             <IconTrash className="h-4 w-4" />
                           </Button>
                         )}
                      </div>
										</div>
									</div>
								))}

								{/* Add Phone Button */}
								<Button
									type="button"
									variant="outline"
									onClick={() => {
										const currentPhones = form.watch("phone") || [];
										const newPhones = [
											...currentPhones,
											{
												label: "Work",
												number: "",
												isPrimary: currentPhones.length === 0,
												isBad: false,
											},
										];
										form.setValue("phone", newPhones);
									}}
									className="w-full border-dashed !bg-transparent hover:!bg-muted/30 items-center gap-1"
								>
									<IconPlus className="h-4 w-4" />
									Add Phone Number
								</Button>
							</div>
						</div>
					</div>

					{/* Fixed Footer */}
					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting || updateContact.isPending}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={isSubmitting || updateContact.isPending}
						>
							{(isSubmitting || updateContact.isPending) ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>Saving...</span>
								</div>
							) : (
								"Save"
							)}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
} 