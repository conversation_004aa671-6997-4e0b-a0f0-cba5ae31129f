"use client";

import React, { useState } from "react";
import { useContactTasks, useUpdateContactTask, useDeleteContactTask, useQuickUpdateContactTask, normalizeTask, normalizeTaskId } from "@app/tasks/lib/api";
import { KanbanCard } from "../../../../../app/(authenticated)/app/(organizations)/[organizationSlug]/tasks/board/_components/card";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import EmptyContainer from "@app/shared/components/EmptyContainer";
import { Skeleton } from "@ui/components/skeleton";
import { IconPlus, IconSquareRoundedCheck, IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";
import type { Task } from "@repo/database/src/zod";
import GridItem from "@ui/components/grid-item";
import { toast } from "sonner";
import { TASK_PRIORITY, type TaskPriority } from "@app/shared/lib/constants";
import { NumberBadge } from "@ui/components/badge";

interface ContactTasksProps {
	contact: any;
	organization: ActiveOrganization;
	user: any;
}

export default function ContactTasks({ contact, organization, user }: ContactTasksProps) {
	const { data: tasks = [], isLoading } = useContactTasks(contact?.id, organization?.id);
	const [viewCompletedTasks, setViewCompletedTasks] = useState<boolean>(false);
	const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
	const { openCreateTask, openEditTask } = useTasks();
	
	const updateTaskMutation = useUpdateContactTask(contact?.id, organization?.id);
	const quickUpdateTaskMutation = useQuickUpdateContactTask(contact?.id, organization?.id);
	const deleteTaskMutation = useDeleteContactTask(contact?.id, organization?.id);

	const normalizedTasks = tasks.map(task => {
		const normalized = normalizeTask(task);
		if (process.env.NODE_ENV === 'development' && task === tasks[0]) {
			console.group('Task Normalization');
			console.log('Original task:', task);
			console.log('Normalized task:', normalized);
			console.groupEnd();
		}
		return normalized;
	});

	const handleTaskSelect = (taskId: string) => {
		setSelectedTaskId(selectedTaskId === taskId ? null : taskId);
	};

	const handleTaskEdit = (taskId: string) => {
		const task = normalizedTasks.find(t => t.id === taskId);
		if (task) {
			openEditTask(task);
		}
	};

	const handleTaskStatusChange = (taskId: string, newStatus: string) => {
		const task = normalizedTasks.find(t => t.id === taskId);
		if (!task) return;

		quickUpdateTaskMutation.mutate({
			taskId,
			status: newStatus.toLowerCase(),
		}, {
			onSuccess: () => {
				toast.success(`Task marked as ${newStatus}`);
			},
			onError: (error) => {
				toast.error("Failed to update task status");
				console.error("Failed to update task:", error);
			},
		});
	};

	const handleTaskPriorityChange = (taskId: string, newPriority: string) => {
		const task = normalizedTasks.find(t => t.id === taskId);
		if (!task) return;
		
		quickUpdateTaskMutation.mutate({
			taskId,
			priority: newPriority,
		}, {
			onSuccess: () => {
				const priorityLabel = TASK_PRIORITY.find(p => p.value === newPriority)?.label || newPriority;
				toast.success(`Task priority set to ${priorityLabel}`);
			},
			onError: (error) => {
				toast.error("Failed to update task priority");
				console.error("Failed to update task priority:", error);
			},
		});
	};

	const handleTaskDelete = (taskId: string) => {
		const normalizedId = normalizeTaskId(taskId);
		
		deleteTaskMutation.mutate(normalizedId, {
			onSuccess: () => {
				toast.success("Task deleted successfully");
				// Clear selection if deleted task was selected
				if (selectedTaskId === normalizedId) {
					setSelectedTaskId(null);
				}
			},
			onError: (error) => {
				toast.error("Failed to delete task");
				console.error("Failed to delete task:", error);
			},
		});
	};

	const handleCreateTask = () => {
		const relatedObject = {
			id: contact.id,
			name: contact.name || "Unnamed Contact",
			recordType: "contact" as const,
		};
		
		openCreateTask({
			defaultStatus: "todo",
			relatedObject,
		});
	};

	const handleViewCompletedTasks = () => {
		setViewCompletedTasks(!viewCompletedTasks);
	};

	if (isLoading) {
		return (
			<div className="space-y-3">
				{[...Array(3)].map((_, index) => (
					<Skeleton key={index} className="h-24 w-full rounded-lg" />
				))}
			</div>
		);
	}

	if (!normalizedTasks || normalizedTasks.length === 0) {
		return (
			<div className="space-y-4">
				<div className="border border-dashed rounded-md opacity-60 hover:opacity-100 transition-opacity">
					<GridItem
						value="Create Task"
						onClick={handleCreateTask}
						icon={<IconPlus className="h-4 w-4 text-muted-foreground" />}
					/>
				</div>
			</div>
		);
	}

	const activeTasks = normalizedTasks.filter(task => task.status !== "done");
	const completedTasks = normalizedTasks.filter(task => task.status === "done");

	const renderCompletedTasks = () => {
		if (!viewCompletedTasks || completedTasks.length === 0) return null;
		
		return (
			<div className="space-y-1">
				{completedTasks.map((task: Task & { assignee?: any; favorite?: boolean }) => (
					<div key={task.id} className="opacity-70 hover:opacity-100 transition-opacity">
						<KanbanCard
							cursor="default"
							checkbox={false}
							task={task}
							isSelected={selectedTaskId === task.id}
							onSelect={handleTaskSelect}
							onEdit={handleTaskEdit}
							onStatusChange={handleTaskStatusChange}
							onPriorityChange={handleTaskPriorityChange}
							onDelete={handleTaskDelete}
						/>
					</div>
				))}
			</div>
		);
	};
	
	// Render toggle for completed tasks
	const renderCompletedTasksToggle = () => {
		if (completedTasks.length === 0) return null;
		
		return (
			<div 
				className="flex items-center gap-1.5 text-xs text-muted-foreground hover:text-foreground cursor-pointer py-1" 
				onClick={handleViewCompletedTasks}
			>
				{viewCompletedTasks ? (
					<>
						<IconSquareRoundedCheckFilled className="h-3.5 w-3.5" />
						Hide completed tasks <NumberBadge number={completedTasks.length} className="!bg-muted/50 !border-muted/90 !text-[10px]" />
					</>
				) : (
					<>
						<IconSquareRoundedCheck className="h-3.5 w-3.5" />
						View completed tasks <NumberBadge number={completedTasks.length} className="!bg-muted/50 !border-muted/90 !text-[10px]" />
					</>
				)}
			</div>
		);
	};

	if (activeTasks.length === 0) {
		return (
			<div className="space-y-1">
				{activeTasks.length === 0 && (
					<GridItem
						value="Create New Task"
						onClick={handleCreateTask}
						icon={<IconPlus className="h-4 w-4 text-muted-foreground" />}
					/>
				)}
				
				{renderCompletedTasksToggle()}
				{renderCompletedTasks()}
			</div>
		);
	}

	return (
		<div className="space-y-3">
			{activeTasks.map((task: Task & { assignee?: any; favorite?: boolean }) => (
				<KanbanCard
					cursor="default"
					checkbox={false}
					key={task.id}
					task={task}
					isSelected={selectedTaskId === task.id}
					onSelect={handleTaskSelect}
					onEdit={handleTaskEdit}
					onStatusChange={handleTaskStatusChange}
					onPriorityChange={handleTaskPriorityChange}
					onDelete={handleTaskDelete}
				/>
			))}
			
			{renderCompletedTasksToggle()}
			{renderCompletedTasks()}
		</div>
	);
} 