"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { useUpdateContact } from "../../lib/api";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { IconMapPin, IconPlus, IconTrash } from "@tabler/icons-react";
import { cn } from "@ui/lib";

interface AddressModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	addresses: any[];
	editingIndex: number | null;
	onAddressesChange: (addresses: any[]) => void;
	contactId?: string;
	organizationId?: string;
}

const addressSchema = z.object({
	address: z
		.array(
			z.object({
				label: z.string().default("Work"),
				street: z.string().min(1, "Street address is required"),
				street2: z.string().optional(),
				city: z.string().min(1, "City is required"),
				state: z.string().min(1, "State is required"),
				zip: z.string().min(1, "ZIP code is required"),
				country: z.string().default("United States"),
				isPrimary: z.boolean().default(false),
			}),
		)
		.min(1, "At least one address is required"),
});

type AddressFormData = z.infer<typeof addressSchema>;

export function AddressModal({
	open,
	onOpenChange,
	addresses,
	editingIndex,
	onAddressesChange,
	contactId,
	organizationId,
}: AddressModalProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [customLabelIndices, setCustomLabelIndices] = useState<Set<number>>(new Set());
	const updateContact = useUpdateContact(organizationId);

	const form = useForm<AddressFormData>({
		resolver: zodResolver(addressSchema),
		mode: "onSubmit",
		reValidateMode: "onSubmit",
		defaultValues: {
			address: [],
		},
	});

	// Initialize form data when modal opens
	React.useEffect(() => {
		if (open) {
			let addressesToSet: any[] = [];
			
			if (editingIndex !== null && addresses[editingIndex]) {
				// Editing a specific address
				addressesToSet = [addresses[editingIndex]];
			} else if (addresses.length > 0) {
				// Editing all addresses
				addressesToSet = addresses;
			} else {
				// Adding first address
				addressesToSet = [
					{
						label: "Work",
						street: "",
						street2: "",
						city: "",
						state: "",
						zip: "",
						country: "United States",
						isPrimary: true,
					},
				];
			}
			
			form.reset({
				address: addressesToSet,
			});
			
			// Initialize custom label indices for existing custom labels
			const predefinedLabels = ["Work", "Home", "Mailing", "Billing", "Other"];
			const customIndices = new Set<number>();
			addressesToSet.forEach((address, index) => {
				if (address.label && !predefinedLabels.includes(address.label)) {
					customIndices.add(index);
				}
			});
			setCustomLabelIndices(customIndices);
		}
	}, [open, addresses, editingIndex, form]);

	// Reset form when modal closes
	React.useEffect(() => {
		if (!open) {
			form.reset({
				address: [],
			});
			setCustomLabelIndices(new Set());
		}
	}, [open, form]);

	async function onSubmit(formData: AddressFormData) {
		setIsSubmitting(true);

		try {
			// Filter out empty addresses
			const validAddresses = formData.address.filter(
				(address) => address.street.trim() !== ""
			);

			if (validAddresses.length === 0) {
				toast.error("Please enter at least one address");
				setIsSubmitting(false);
				return;
			}

			let finalAddresses: any[];

			// If editing a specific address, replace it in the original array
			if (editingIndex !== null) {
				finalAddresses = [...addresses];
				finalAddresses[editingIndex] = validAddresses[0];
			} else {
				// Replace all addresses or add new ones
				finalAddresses = validAddresses;
			}

			// Update in database if contactId is provided
			if (contactId) {
				await updateContact.mutateAsync({
					id: contactId,
					address: finalAddresses.map((address) => ({
						label: address.label,
						street: address.street,
						street2: address.street2,
						city: address.city,
						state: address.state,
						zip: address.zip,
						country: address.country,
					})),
				});
				toast.success(
					editingIndex !== null
						? "Address updated successfully"
						: "Addresses saved successfully"
				);
			} else {
				// Fallback to callback for non-database scenarios (like during contact creation)
				onAddressesChange(finalAddresses);
				toast.success("Addresses updated");
			}

			onOpenChange(false);
		} catch (error: any) {
			toast.error(error?.message || "Failed to save addresses");
		} finally {
			setIsSubmitting(false);
		}
	}

	// Show form errors as toast messages
	React.useEffect(() => {
		const errors = form.formState.errors;
		if (Object.keys(errors).length > 0) {
			const firstError = Object.values(errors)[0];
			if (firstError?.message) {
				toast.error(firstError.message as string);
			}
		}
	}, [form.formState.errors]);

	const title = editingIndex !== null ? "Edit Address" : "Manage Addresses";

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0"
				onPointerDownOutside={(e) => e.preventDefault()}
			>
				<DialogTitle className="sr-only">{title}</DialogTitle>
				<DialogDescription className="sr-only">
					{editingIndex !== null
						? "Edit an existing address"
						: "Add or edit addresses"}
				</DialogDescription>

				<div className="flex items-center justify-between px-4 pt-2 pb-1">
					<div className="flex flex-row items-center gap-x-2">
						<IconMapPin className="h-5 w-5" />
						<span className="text-md font-light">{title}</span>
					</div>
				</div>

				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="flex flex-col flex-1 min-h-0 overflow-y-auto max-h-[600px]"
				>
					<div className="flex flex-col px-2 pt-2 pb-4 space-y-4 overflow-y-auto flex-1">
						<div className="space-y-1">
							<div className="space-y-3">
								{form.watch("address")?.map((address, index) => (
									<div key={index} className="border rounded-lg p-4 space-y-3 bg-muted/50">
										{/* Label Select/Input */}
										<div className="space-y-1">
											<Label className="text-xs text-muted-foreground">
												Label
											</Label>
											<div>
												{(() => {
													const predefinedLabels = ["Work", "Home", "Mailing", "Billing", "Other"];
													const isCustomMode = customLabelIndices.has(index);
													const currentLabel = address.label || "";
													
													// Determine select value based on custom mode state, not label content
													const selectValue = isCustomMode ? "Other" : (currentLabel || "Work");

													return (
														<>
															<Select
																value={selectValue}
																onValueChange={(value) => {
																	const currentAddresses = form.watch("address") || [];
																	const newAddresses = [...currentAddresses];
																	
																	if (value === "Other") {
																		// Switch to custom mode
																		setCustomLabelIndices(prev => new Set(Array.from(prev).concat(index)));
																		// Clear the label when switching to custom mode
																		newAddresses[index] = {
																			...newAddresses[index],
																			label: "",
																		};
																	} else {
																		// Switch to predefined mode
																		setCustomLabelIndices(prev => {
																			const newSet = new Set(prev);
																			newSet.delete(index);
																			return newSet;
																		});
																		newAddresses[index] = {
																			...newAddresses[index],
																			label: value,
																		};
																	}
																	form.setValue("address", newAddresses);
																}}
															>
																<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
																	<SelectValue placeholder="Select label..." />
																</SelectTrigger>
																<SelectContent>
																	<SelectItem value="Work">Work</SelectItem>
																	<SelectItem value="Home">Home</SelectItem>
																	<SelectItem value="Mailing">Mailing</SelectItem>
																	<SelectItem value="Billing">Billing</SelectItem>
																	<SelectItem value="Other">Other</SelectItem>
																</SelectContent>
															</Select>
															
															{isCustomMode && (
																<Input
																	value={currentLabel}
																	onChange={(e) => {
																		const currentAddresses = form.watch("address") || [];
																		const newAddresses = [...currentAddresses];
																		newAddresses[index] = {
																			...newAddresses[index],
																			label: e.target.value,
																		};
																		form.setValue("address", newAddresses);
																	}}
																	placeholder="Custom label..."
																	className="mt-1 cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
																	autoFocus
																/>
															)}
														</>
													);
												})()}
											</div>
										</div>

										{/* Address Fields */}
										<div className="space-y-3">
											<div>
												<Label className="text-xs text-muted-foreground">
													Street Address
												</Label>
												<Input
													value={address.street}
													onChange={(e) => {
														const currentAddresses = form.watch("address") || [];
														const newAddresses = [...currentAddresses];
														newAddresses[index] = {
															...newAddresses[index],
															street: e.target.value,
														};
														form.setValue("address", newAddresses);
													}}
													placeholder="123 Main St"
													className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
													autoFocus={editingIndex === null && index === 0}
												/>
											</div>

											<div>
												<Label className="text-xs text-muted-foreground">
													Apartment, suite, etc.
												</Label>
												<Input
													value={address.street2 || ""}
													onChange={(e) => {
														const currentAddresses = form.watch("address") || [];
														const newAddresses = [...currentAddresses];
														newAddresses[index] = {
															...newAddresses[index],
															street2: e.target.value,
														};
														form.setValue("address", newAddresses);
													}}
													placeholder="Apt 4B"
													className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
												/>
											</div>

											<div className="grid grid-cols-3 gap-3">
												<div>
													<Label className="text-xs text-muted-foreground">
														City
													</Label>
													<Input
														value={address.city}
														onChange={(e) => {
															const currentAddresses = form.watch("address") || [];
															const newAddresses = [...currentAddresses];
															newAddresses[index] = {
																...newAddresses[index],
																city: e.target.value,
															};
															form.setValue("address", newAddresses);
														}}
														placeholder="City"
														className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
													/>
												</div>

												<div>
													<Label className="text-xs text-muted-foreground">
														State
													</Label>
													<Input
														value={address.state}
														onChange={(e) => {
															const currentAddresses = form.watch("address") || [];
															const newAddresses = [...currentAddresses];
															newAddresses[index] = {
																...newAddresses[index],
																state: e.target.value,
															};
															form.setValue("address", newAddresses);
														}}
														placeholder="State"
														className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
													/>
												</div>

												<div>
													<Label className="text-xs text-muted-foreground">
														ZIP Code
													</Label>
													<Input
														value={address.zip}
														onChange={(e) => {
															const currentAddresses = form.watch("address") || [];
															const newAddresses = [...currentAddresses];
															newAddresses[index] = {
																...newAddresses[index],
																zip: e.target.value,
															};
															form.setValue("address", newAddresses);
														}}
														placeholder="ZIP"
														className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
													/>
												</div>
											</div>

											<div>
												<Label className="text-xs text-muted-foreground">
													Country
												</Label>
												<Input
													value={address.country}
													onChange={(e) => {
														const currentAddresses = form.watch("address") || [];
														const newAddresses = [...currentAddresses];
														newAddresses[index] = {
															...newAddresses[index],
															country: e.target.value,
														};
														form.setValue("address", newAddresses);
													}}
													placeholder="Country"
													className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
												/>
											</div>
										</div>

										{/* Delete Button */}
										<div className="flex items-center justify-end">
											<div className="flex items-center justify-end">
												{form.watch("address").length > 1 && (
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => {
															const currentAddresses = form.watch("address") || [];
															const newAddresses = currentAddresses.filter((_, i) => i !== index);
															form.setValue("address", newAddresses);
															
															// Update custom label indices when removing an address
															setCustomLabelIndices(prev => {
																const newSet = new Set<number>();
																Array.from(prev).forEach(i => {
																	if (i < index) {
																		newSet.add(i);
																	} else if (i > index) {
																		newSet.add(i - 1);
																	}
																	// Skip i === index as it's being removed
																});
																return newSet;
															});
														}}
														className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
													>
														<IconTrash className="h-4 w-4" />
													</Button>
												)}
											</div>
										</div>
									</div>
								))}

								{/* Add Address Button */}
								<Button
									type="button"
									variant="outline"
									onClick={() => {
										const currentAddresses = form.watch("address") || [];
										const newAddresses = [
											...currentAddresses,
											{
												label: "Work",
												street: "",
												street2: "",
												city: "",
												state: "",
												zip: "",
												country: "United States",
												isPrimary: currentAddresses.length === 0,
											},
										];
										form.setValue("address", newAddresses);
									}}
									className="w-full border-dashed !bg-transparent hover:!bg-muted/30 items-center gap-1"
								>
									<IconPlus className="h-4 w-4" />
									Add Address
								</Button>
							</div>
						</div>
					</div>

					{/* Fixed Footer */}
					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting || updateContact.isPending}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={isSubmitting || updateContact.isPending}
						>
							{(isSubmitting || updateContact.isPending) ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>Saving...</span>
								</div>
							) : (
								"Save"
							)}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
} 