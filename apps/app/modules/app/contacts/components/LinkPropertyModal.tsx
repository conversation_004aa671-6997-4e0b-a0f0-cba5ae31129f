"use client";

import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { 
  IconHome, 
  IconSearch, 
  IconArrowsLeftRight, 
  IconX,
  IconMapPin,
  IconCurrencyDollar,
  IconSwitch3,
  IconMail,
  IconBuilding,
  IconPhone,
  IconNotes,
  IconInfoCircle,
  IconArrowsUpDown,
  IconChevronDown,
  IconTrash,
  IconEdit,
} from "@tabler/icons-react";

import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { cn } from "@ui/lib";

import type { ActiveOrganization } from "@repo/auth";
import { 
  useLinkPropertyToContact, 
  useUnlinkPropertyFromContact, 
  useUpdatePropertyRelation 
} from "@shared/hooks/useContactProperties";
import { useSearchDebounced, type SearchResult } from "@app/search/lib/api";
import { AlertDefault } from "@ui/components/alert";
import { ComboBox } from "@ui/components/combobox";

interface PropertyData extends SearchResult {
  relation?: string;
  linkedAt?: Date;
  financials?: {
    listPrice?: number;
    salePrice?: number;
    estimatedValue?: number;
  };
}

interface ContactData {
  id: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string[];
  image?: string;
}

interface LinkPropertyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contact: ContactData;
  organization: ActiveOrganization;
  mode?: "add" | "edit";
  propertyToEdit?: PropertyData;
}

const linkPropertySchema = z.object({
  propertyId: z.string().min(1, "Property is required"),
  relation: z.string().min(1, "Relation is required"),
});

type LinkPropertyFormData = z.infer<typeof linkPropertySchema>;

const defaultFormValues: LinkPropertyFormData = {
  propertyId: "",
  relation: "",
};

export function LinkPropertyModal({
  open,
  onOpenChange,
  contact,
  organization,
  mode = "add",
  propertyToEdit,
}: LinkPropertyModalProps) {
  const queryClient = useQueryClient();
  const [selectedProperty, setSelectedProperty] = useState<PropertyData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const form = useForm<LinkPropertyFormData>({
    resolver: zodResolver(linkPropertySchema),
    defaultValues: defaultFormValues,
  });

  const { data: searchResponse, isLoading } = useSearchDebounced(
    searchTerm || " ", // Use space to get all properties when empty
    organization?.id || '',
    "property",
    50, // limit
    0,  // offset
    300 // debounce ms
  );

  const allProperties = searchResponse?.results || [];

  // Effect to initialize edit mode
  useEffect(() => {
    if (mode === "edit" && propertyToEdit && open) {
      setSelectedProperty(propertyToEdit);
      form.setValue("propertyId", propertyToEdit.id);
      form.setValue("relation", propertyToEdit.relation || "");
    }
  }, [mode, propertyToEdit, open, form]);

  // Effect to initialize search on mount for add mode
  useEffect(() => {
    if (open && organization?.id && mode === "add" && !searchTerm) {
      setSearchTerm(" "); // Initialize with space to load all properties
    }
  }, [open, organization?.id, mode]); // Remove searchTerm from dependencies to avoid loop

  // Reset state when modal is closed
  useEffect(() => {
    if (!open) {
      setSelectedProperty(null);
      setSearchTerm("");
      form.reset(defaultFormValues);
    }
  }, [open, form]);

  const linkPropertyMutation = useLinkPropertyToContact();
  const unlinkPropertyMutation = useUnlinkPropertyFromContact();
  const updatePropertyRelationMutation = useUpdatePropertyRelation();

  const formatAddress = (location?: PropertyData["location"]) => {
    if (!location?.address) return "No address";
    
    const { address } = location;
    const parts = [
      address.street,
      [address.city, address.state, address.zip].filter(Boolean).join(", ")
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(", ") : "No address";
  };

  const handlePropertySelect = (propertyId: string) => {
    const property = allProperties.find(p => p.id === propertyId);
    setSelectedProperty(property || null);
    form.setValue("propertyId", propertyId);
  };

  const handleDelete = async () => {
    if (!propertyToEdit || mode !== "edit") return;

    try {
      setIsSubmitting(true);
      
      await unlinkPropertyMutation.mutateAsync({
        contactId: contact.id,
        propertyId: propertyToEdit.id,
        organizationId: organization?.id || '',
      });

      toast.success("Property unlinked successfully");
      onOpenChange(false);
      
      queryClient.invalidateQueries({
        queryKey: ["contact-properties", contact.id],
      });
    } catch (error) {
      console.error("Error unlinking property:", error);
      toast.error("Failed to unlink property");
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmit = async (formData: LinkPropertyFormData) => {
    if (!formData.relation?.trim()) {
      toast.error("Please specify the relation");
      return;
    }

    try {
      setIsSubmitting(true);
      
      if (mode === "edit" && propertyToEdit) {
        // Update existing relation
        await updatePropertyRelationMutation.mutateAsync({
          contactId: contact.id,
          propertyId: propertyToEdit.id,
          organizationId: organization?.id || '',
          relation: formData.relation.trim(),
        });
        toast.success("Property relation updated successfully");
      } else {
        // Add new property link
        if (!selectedProperty) {
          toast.error("Please select a property");
          return;
        }
        
        await linkPropertyMutation.mutateAsync({
          contactId: contact.id,
          propertyId: formData.propertyId,
          organizationId: organization?.id || '',
          relation: formData.relation.trim(),
        });
        toast.success("Property linked successfully");
      }

      onOpenChange(false);
      
      form.reset(defaultFormValues);
      setSelectedProperty(null);
      
      queryClient.invalidateQueries({
        queryKey: ["contact-properties", contact.id],
      });
    } catch (error) {
      console.error("Error with property operation:", error);
      toast.error(mode === "edit" ? "Failed to update property relation" : "Failed to link property");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    return mode === "edit" ? "Edit Property Relation" : "Link Property";
  };

  const getModalDescription = () => {
    return mode === "edit" 
      ? "Update the relationship between this contact and property, or remove the link entirely."
      : "Link a property to this contact. The relationship will be established between the records.";
  };

  const getSubmitButtonText = () => {
    if (isSubmitting) {
      return mode === "edit" ? "Updating..." : "Linking...";
    }
    return mode === "edit" ? "Update Relation" : "Link Property";
  };

  return (
    <StandardizedModal
      open={open}
      onOpenChange={onOpenChange}
      title={getModalTitle()}
      description={getModalDescription()}
      icon={<IconHome className="h-4 w-4 text-muted-foreground" />}
      footer={
        <StandardizedModalFooter
          leftContent={
            mode === "edit" && (
              <Button
                type="button"
                variant="error"
                size="sm"
                onClick={handleDelete}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <>
                    <IconTrash className="h-4 w-4" />
                  </>
                )}
              </Button>
            )
          }
        >
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="action"
            size="sm"
            disabled={isSubmitting || (mode === "add" && !selectedProperty)}
            onClick={form.handleSubmit(onSubmit)}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>{getSubmitButtonText()}</span>
              </div>
            ) : (
              getSubmitButtonText()
            )}
          </Button>
        </StandardizedModalFooter>
      }
    >
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <ScrollArea className="w-full h-full">
          <div className="mb-4">
            <AlertDefault icon={<IconInfoCircle className="h-4 w-4" />}>
              {mode === "edit" 
                ? "Update the relationship or delete the link between this contact and property."
                : "Our system intelligently combines your data. Note that the second selected record holds higher priority."
              }
            </AlertDefault>
          </div>

          <div className="space-y-4">
            {mode === "edit" && propertyToEdit ? (
              // Edit Mode - Show selected property (read-only)
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Property Being Edited
                </label>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="flex items-center gap-3">
                    <PropertyAvatar 
                      name={propertyToEdit.title || propertyToEdit.name || 'Property'} 
                      avatarUrl={propertyToEdit.avatarUrl} 
                      className="h-8 w-8" 
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium truncate">
                          {propertyToEdit.title || propertyToEdit.name || 'Untitled Property'}
                        </span>
                        <IconEdit className="h-3 w-3 text-muted-foreground" />
                      </div>
                      <span className="text-xs text-muted-foreground truncate block">
                        {formatAddress(propertyToEdit.location)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // Add Mode - Show property selection
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Select Property to Link
                </label>
                <ComboBox<PropertyData>
                  items={allProperties}
                  value={form.watch("propertyId")}
                  onValueChange={handlePropertySelect}
                  onSearchChange={setSearchTerm}
                  placeholder="Search and select a property..."
                  searchPlaceholder="Search properties..."
                  emptyMessage="No properties found"
                  groupLabel="Properties"
                  disabled={isSubmitting}
                  className="w-full min-w-0"
                  itemClassName="h-8"
                  getSearchableText={(property: PropertyData) => {
                    // Include formatted address and individual components for better searching
                    const fullAddress = formatAddress(property.location);
                    const addressParts = property.location?.address ? [
                      property.location.address.street || '',
                      property.location.address.city || '',
                      property.location.address.state || '',
                      property.location.address.zip || ''
                    ].filter(Boolean).join(' ') : '';
                    
                    return `${property.title || property.name || ''} ${fullAddress} ${addressParts} ${property.propertyType || ''} ${property.status || ''}`;
                  }}
                  getDisplayText={(property: PropertyData) => property.title || property.name || 'Untitled Property'}
                  renderButton={(selectedProperty: PropertyData | null, placeholder: string) => (
                    <>
                      {selectedProperty ? (
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <PropertyAvatar name={selectedProperty.title || selectedProperty.name || 'Property'} avatarUrl={selectedProperty.avatarUrl} className="h-5 w-5" />
                          <div className="flex flex-row items-center gap-2 min-w-0 flex-1 text-left">
                            <span className="text-sm font-medium truncate">
                              {selectedProperty.title || selectedProperty.name || 'Untitled Property'}
                            </span>
                            <span className="text-xs text-muted-foreground truncate">
                              {formatAddress(selectedProperty.location)}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">{placeholder}</span>
                      )}
                      <IconChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </>
                  )}
                  renderItem={(property: PropertyData, isSelected: boolean) => (
                    <>
                      <PropertyAvatar name={property.title || property.name || 'Property'} avatarUrl={property.avatarUrl} className="h-5 w-5" />
                      
                      <div className="flex flex-col min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium truncate">
                            {property.title || property.name || 'Untitled Property'}
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                />
                {form.formState.errors.propertyId && (
                  <p className="text-sm text-red-600 mt-1">
                    {form.formState.errors.propertyId.message}
                  </p>
                )}
              </div>
            )}

            {/* Relation Input - Always show when there's a property */}
            {(selectedProperty || (mode === "edit" && propertyToEdit)) && (
              <div className="space-y-2">
                <label htmlFor="relation" className="text-sm font-medium block">
                  Relation to Property
                </label>
                <div className="space-y-2">
                  <Input
                    id="relation"
                    type="text"
                    className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="e.g. Owner, Tenant, Manager"
                    {...form.register("relation", {
                      required: "Please specify the relation",
                      maxLength: { value: 64, message: "Relation is too long" },
                    })}
                    disabled={isSubmitting}
                    autoComplete="off"
                  />
                  {form.formState.errors.relation && (
                    <p className="text-sm text-red-600 mt-1">
                      {form.formState.errors.relation.message}
                    </p>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {["Owner", "Tenant", "Manager", "Agent"].map((relation) => (
                      <button
                        key={relation}
                        type="button"
                        className={cn(
                          "px-2 py-1 text-xs rounded-md border transition-colors",
                          form.watch("relation") === relation
                            ? "bg-primary text-white border-primary"
                            : "bg-background hover:bg-muted border-border"
                        )}
                        onClick={() => form.setValue("relation", relation)}
                      >
                        {relation}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </form>
    </StandardizedModal>
  );
}
