"use client";

import {
	extractPhoneNumber,
	formatDisplayPhone,
	formatPhoneNumber,
} from "@shared/lib/utils";
import { IconPlus, IconX } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import React, { useEffect, useRef, useState } from "react";

interface PhoneItem {
	label: string;
	number: string;
	isPrimary: boolean;
	isBad: boolean;
}

interface PhoneFieldProps {
	phones: PhoneItem[];
	onPhonesChange: (phones: PhoneItem[]) => void;
	placeholder?: string;
}

export function PhoneField({
	phones,
	onPhonesChange,
	placeholder = "Set Phone numbers",
}: PhoneFieldProps) {
	const [open, setOpen] = useState(false);
	const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
	const [recentlyAdded, setRecentlyAdded] = useState<number[]>([]);
	const [focusIndex, setFocusIndex] = useState<number | null>(null);
	const [formattedValues, setFormattedValues] = useState<string[]>([]);
	const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

	const hasPhones = phones.some((phone) => phone.number.trim() !== "");
	const displayText = hasPhones
		? phones
				.filter((p) => p.number.trim() !== "")
				.map((p) => formatDisplayPhone(p.number))
				.join(", ")
		: placeholder;

	useEffect(() => {
		setFormattedValues(
			phones.map((phone) => formatPhoneNumber(phone.number)),
		);
	}, [phones.length]);

	useEffect(() => {
		if (focusIndex !== null && inputRefs.current[focusIndex]) {
			inputRefs.current[focusIndex]?.focus();
			setFocusIndex(null);
		}
	}, [focusIndex, recentlyAdded]);

	const addPhone = () => {
		const newIndex = phones.length;
		const newPhones = [
			...phones,
			{
				label: "Work",
				number: "",
				isPrimary: phones.length === 0,
				isBad: false,
			},
		];
		setFormattedValues([...formattedValues, ""]);
		setRecentlyAdded([...recentlyAdded, newIndex]);
		setFocusIndex(newIndex);
		onPhonesChange(newPhones);
	};

	const removePhone = (index: number) => {
		const newPhones = phones.filter((_, i) => i !== index);
		const newFormattedValues = formattedValues.filter(
			(_, i) => i !== index,
		);
		setRecentlyAdded(
			recentlyAdded
				.filter((i) => i !== index)
				.map((i) => (i > index ? i - 1 : i)),
		);
		setFormattedValues(newFormattedValues);
		onPhonesChange(newPhones);
	};

	const updatePhone = (index: number, formattedInput: string) => {
		const newFormattedValues = [...formattedValues];
		const formatted = formatPhoneNumber(formattedInput);
		newFormattedValues[index] = formatted;
		setFormattedValues(newFormattedValues);

		const rawNumber = extractPhoneNumber(formattedInput);
		const newPhones = [...phones];
		newPhones[index] = { ...newPhones[index], number: rawNumber };

		if (rawNumber.trim() !== "" && recentlyAdded.includes(index)) {
			setRecentlyAdded(recentlyAdded.filter((i) => i !== index));
		}
		onPhonesChange(newPhones);
	};

	const clearPhone = (index: number) => {
		updatePhone(index, "");
	};

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className="w-full justify-start bg-transparent text-left font-normal"
				>
					<span
						className={cn(
							hasPhones
								? "text-foreground"
								: "text-muted-foreground",
							"truncate",
						)}
					>
						{displayText}
					</span>
				</Button>
			</PopoverTrigger>
			<PopoverContent
				className="w-[var(--radix-popover-trigger-width)] p-1 bg-muted/50 backdrop-blur-lg"
				align="start"
			>
				<div className="space-y-2">
					{phones.map((phone, originalIndex) => {
						const shouldShow =
							phone.number.trim() !== "" ||
							recentlyAdded.includes(originalIndex);
						if (!shouldShow) return null;

						return (
							<div
								key={originalIndex}
								className="relative"
								onMouseEnter={() =>
									setHoveredIndex(originalIndex)
								}
								onMouseLeave={() => setHoveredIndex(null)}
							>
								<div className="flex items-center px-4 py-2">
									<Input
										ref={(el) => {
											inputRefs.current[originalIndex] =
												el;
										}}
										value={
											formattedValues[originalIndex] || ""
										}
										onChange={(e) =>
											updatePhone(
												originalIndex,
												e.target.value,
											)
										}
										placeholder="(*************"
										className="border-0 shadow-none focus-visible:ring-0 bg-neutral-800 !p-0 h-auto flex-1 text-white"
									/>
									{phones.length > 1 &&
										hoveredIndex === originalIndex && (
											<Button
												type="button"
												variant="ghost"
												size="sm"
												onClick={() =>
													removePhone(originalIndex)
												}
												className="h-3 w-3 p-0 hover:bg-muted hover:text-primary rounded-lg flex-shrink-0 ml-1"
											>
												<IconX className="h-4 w-4" />
											</Button>
										)}
								</div>
							</div>
						);
					})}

					<Button
						type="button"
						variant="ghost"
						onClick={addPhone}
						className="w-full justify-start text-muted-foreground hover:text-foreground"
					>
						<IconPlus className="mr-2 h-4 w-4" />
						Add phone
					</Button>
				</div>
			</PopoverContent>
		</Popover>
	);
}
