export interface ModelCapability {
	type: "thinking" | "vision" | "tools";
}

export interface Model {
	id: string;
	name: string;
	model: string;
	provider: "azure" | "openrouter";
	icon:
		| "anthropic"
		| "claude"
		| "deepseek"
		| "gemini"
		| "google"
		| "grok"
		| "meta"
		| "mistral"
		| "ollama"
		| "openai"
		| "openrouter"
		| "x"
		| "xai";
	capabilities: ("thinking" | "vision" | "tools")[];
	description: string;
	isPremium: boolean;
	isDisabled: boolean;
	cost: number;
}

// Database model interface for lookups
export interface DatabaseModel {
	id: string; // ObjectId
	name: string;
	model: string;
	provider: "azure" | "openrouter";
	icon: string;
	capabilities: string[];
	description: string;
	isPremium: boolean;
	isDisabled: boolean;
	cost: number;
}

// Cached database models for fast lookup
let databaseModelsCache: DatabaseModel[] | null = null;

// Function to fetch and cache database models
async function fetchDatabaseModels(): Promise<DatabaseModel[]> {
	if (databaseModelsCache) {
		return databaseModelsCache;
	}

	try {
		const response = await fetch("/api/admin/models", {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
			},
		});

		if (response.ok) {
			databaseModelsCache = await response.json();
			return databaseModelsCache || [];
		}
	} catch (error) {
		console.warn("[Models] Failed to fetch database models:", error);
	}

	return [];
}

// Create a mapping from static model names to database models
function createModelNameMapping(
	dbModels: DatabaseModel[],
): Map<string, DatabaseModel> {
	const mapping = new Map();

	for (const dbModel of dbModels) {
		// Direct name matching
		mapping.set(dbModel.name, dbModel);

		// Try to match with static models by name
		const staticModel = models.find(
			(m) =>
				m.name === dbModel.name ||
				m.model === dbModel.model ||
				m.name.toLowerCase().replace(/\s+/g, "-") ===
					dbModel.name.toLowerCase().replace(/\s+/g, "-"),
		);

		if (staticModel) {
			mapping.set(staticModel.id, dbModel);
		}
	}

	return mapping;
}

export const models: Model[] = [
	{
		id: "deepseek-v3",
		name: "DeepSeek V3",
		model: "deepseek/deepseek-chat-v3-0324:free",
		provider: "openrouter",
		icon: "deepseek",
		capabilities: ["tools"],
		description: "Smaller model by DeepSeek with fewer capabilities.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "deepseek-r1",
		name: "DeepSeek R1",
		model: "deepseek/deepseek-r1-0528:free",
		provider: "openrouter",
		icon: "deepseek",
		capabilities: ["thinking"],
		description: "Flagship model by DeepSeek.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "claude-sonnet-3-7",
		name: "Claude Sonnet 3.7",
		model: "anthropic/claude-3.7-sonnet:thinking",
		provider: "openrouter",
		icon: "anthropic",
		capabilities: ["thinking", "vision", "tools"],
		description: "Sonnet 3.7 model by Anthropic.",
		isPremium: true,
		isDisabled: false,
		cost: 5,
	},
	{
		id: "claude-sonnet-4",
		name: "Claude Sonnet 4",
		model: "anthropic/claude-4-sonnet-20250522",
		provider: "openrouter",
		icon: "anthropic",
		capabilities: ["thinking", "vision", "tools"],
		description: "Latest Sonnet model by Anthropic.",
		isPremium: true,
		isDisabled: false,
		cost: 5,
	},
	{
		id: "claude-opus-4",
		name: "Claude Opus 4",
		model: "anthropic/claude-opus-4",
		provider: "openrouter",
		icon: "anthropic",
		capabilities: ["thinking", "vision", "tools"],
		description: "Latest Opus model by Anthropic.",
		isPremium: true,
		isDisabled: true,
		cost: 20,
	},
	{
		id: "gemini-2-0-flash",
		name: "Gemini 2.0 Flash",
		model: "google/gemini-2.0-flash-001",
		provider: "openrouter",
		icon: "gemini",
		capabilities: ["vision", "tools"],
		description: "Fast model tuned for low latency.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "gemini-2-5-flash",
		name: "Gemini 2.5 Flash",
		model: "google/gemini-2.5-flash-preview-05-20",
		provider: "openrouter",
		icon: "gemini",
		capabilities: ["vision", "tools"],
		description:
			"Preview of next generation Gemini Flash with more capabilities.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "gemini-2-5-flash-lite",
		name: "Gemini 2.5 Flash Lite",
		model: "google/gemini-2.5-flash-lite-preview-06-17",
		provider: "openrouter",
		icon: "gemini",
		capabilities: ["thinking", "vision", "tools"],
		description:
			"Lightweight reasoning model, with faster token generation than other Gemini models.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "gemini-2-5-flash-thinking",
		name: "Gemini 2.5 Flash (Thinking)",
		model: "google/gemini-2.5-flash-preview-05-20:thinking",
		provider: "openrouter",
		icon: "gemini",
		capabilities: ["thinking", "vision", "tools"],
		description:
			"Preview of next generation Gemini Flash with more capabilities and reasoning.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "gemini-2-5-pro",
		name: "Gemini 2.5 Pro",
		model: "google/gemini-2.5-pro-preview",
		provider: "openrouter",
		icon: "gemini",
		capabilities: ["thinking", "vision", "tools"],
		description:
			"Preview of latest Pro model by Gemini with more capabilities.",
		isPremium: true,
		isDisabled: false,
		cost: 5,
	},
	{
		id: "gpt-4o",
		name: "GPT 4o",
		model: "gpt-4o",
		provider: "azure",
		icon: "openai",
		capabilities: ["vision", "tools"],
		description:
			"Second largest chat model from OpenAI, great for most questions.",
		isPremium: true,
		isDisabled: false,
		cost: 2,
	},
	{
		id: "gpt-4o-mini",
		name: "GPT 4o-mini",
		model: "gpt-4o-mini",
		provider: "azure",
		icon: "openai",
		capabilities: ["vision", "tools"],
		description: "Faster, less accurate version of GPT-4o.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "gpt-4-1",
		name: "GPT 4.1",
		model: "gpt-4.1",
		provider: "azure",
		icon: "openai",
		capabilities: ["vision", "tools"],
		description: "Model from OpenAI tuned for coding tasks.",
		isPremium: true,
		isDisabled: false,
		cost: 2,
	},
	{
		id: "gpt-4-1-nano",
		name: "GPT 4.1 Nano",
		model: "gpt-4.1-mini",
		provider: "azure",
		icon: "openai",
		capabilities: ["vision", "tools"],
		description: "Faster, less accurate version of GPT-4.1.",
		isPremium: false,
		isDisabled: false,
		cost: 0,
	},
	{
		id: "o4-mini",
		name: "o4-mini",
		model: "o4-mini",
		provider: "azure",
		icon: "openai",
		capabilities: ["thinking", "vision", "tools"],
		description: "Smaller, faster version of o4.",
		isPremium: true,
		isDisabled: false,
		cost: 1,
	},
	{
		id: "grok-3-beta",
		name: "Grok 3 Beta",
		model: "x-ai/grok-3-beta",
		provider: "openrouter",
		icon: "xai",
		capabilities: ["thinking", "vision", "tools"],
		description: "Grok 3 Beta model by xAI.",
		isPremium: true,
		isDisabled: false,
		cost: 5,
	},
	{
		id: "grok-3-mini-beta",
		name: "Grok 3 Mini Beta",
		model: "x-ai/grok-3-mini-beta",
		provider: "openrouter",
		icon: "xai",
		capabilities: ["thinking", "vision", "tools"],
		description: "Grok 3 Mini Beta model by xAI.",
		isPremium: true,
		isDisabled: false,
		cost: 0,
	},
];

export function getModelById(id: string): Model | undefined {
	return models.find((model) => model.id === id);
}

// Enhanced function that can resolve both static IDs and database ObjectIds
export async function resolveModelById(id: string): Promise<Model | undefined> {
	// First try static models
	const staticModel = getModelById(id);
	if (staticModel) {
		return staticModel;
	}

	// If it looks like a MongoDB ObjectId (24 character hex string), try database lookup
	if (id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {
		try {
			const dbModels = await fetchDatabaseModels();
			const dbModel = dbModels.find((m) => m.id === id);

			if (dbModel) {
				// Convert database model to frontend model format
				return {
					id: dbModel.id, // Keep the ObjectId for consistency
					name: dbModel.name,
					model: dbModel.model,
					provider: dbModel.provider,
					icon: dbModel.icon as any,
					capabilities: dbModel.capabilities as any[],
					description: dbModel.description,
					isPremium: dbModel.isPremium,
					isDisabled: dbModel.isDisabled,
					cost: dbModel.cost,
				};
			}
		} catch (error) {
			console.warn("[Models] Failed to resolve database model:", error);
		}
	}

	return undefined;
}

// Function to get database model ID from static model ID
export async function getDbModelId(
	staticModelId: string,
): Promise<string | undefined> {
	const dbModels = await fetchDatabaseModels();
	const mapping = createModelNameMapping(dbModels);

	const dbModel = mapping.get(staticModelId);
	return dbModel?.id;
}

export function getAvailableModels(): Model[] {
	return models.filter((model) => !model.isDisabled);
}

export function getModelsByProvider(provider: "azure" | "openrouter"): Model[] {
	return models.filter(
		(model) => model.provider === provider && !model.isDisabled,
	);
}

export function getModelsByCapability(
	capability: "thinking" | "vision" | "tools",
): Model[] {
	return models.filter(
		(model) => model.capabilities.includes(capability) && !model.isDisabled,
	);
}
