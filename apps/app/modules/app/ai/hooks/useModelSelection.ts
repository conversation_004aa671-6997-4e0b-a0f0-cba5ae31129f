"use client";

import { useEffect, useState } from "react";
import type { Model } from "../lib/models";
import { getModelById } from "../lib/models";

const STORAGE_KEY = "relio_selected_model";
const DEFAULT_MODEL_ID = "gpt-4o-mini";

export function useModelSelection() {
	const [selectedModelId, setSelectedModelId] =
		useState<string>(DEFAULT_MODEL_ID);
	const [isLoading, setIsLoading] = useState(true);

	// Load selected model from localStorage on mount
	useEffect(() => {
		try {
			const stored = localStorage.getItem(STORAGE_KEY);
			if (stored) {
				const model = getModelById(stored);
				if (model && !model.isDisabled) {
					setSelectedModelId(stored);
				}
			}
		} catch (error) {
			console.warn(
				"Failed to load selected model from localStorage:",
				error,
			);
		} finally {
			setIsLoading(false);
		}
	}, []);

	// Save selected model to localStorage
	const selectModel = (model: Model) => {
		setSelectedModelId(model.id);
		try {
			localStorage.setItem(STORAGE_KEY, model.id);
		} catch (error) {
			console.warn(
				"Failed to save selected model to localStorage:",
				error,
			);
		}
	};

	const selectedModel = getModelById(selectedModelId);

	return {
		selectedModelId,
		selectedModel,
		selectModel,
		isLoading,
	};
}
