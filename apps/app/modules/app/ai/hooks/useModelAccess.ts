"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { usePurchases } from "@app/payments/hooks/purchases";
import { config } from "@repo/config";

export function useModelAccess() {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();

	// Get purchases based on whether we're in an organization context
	const organizationId = activeOrganization?.id;
	const { activePlan: organizationPlan } = usePurchases(organizationId);
	const { activePlan: userPlan } = usePurchases(); // User plan (no org ID)

	// Determine which plan to use based on config
	let activePlan = null;
	let hasGrowthAccess = false;

	if (
		config.organizations.enableBilling &&
		organizationId &&
		organizationPlan
	) {
		// Use organization plan if organizations have billing enabled
		activePlan = organizationPlan;
		hasGrowthAccess =
			organizationPlan.id === "growth" ||
			organizationPlan.id === "enterprise";
	} else if (config.users.enableBilling && userPlan) {
		// Use user plan if user billing is enabled
		activePlan = userPlan;
		hasGrowthAccess =
			userPlan.id === "growth" || userPlan.id === "enterprise";
	}

	// Fallback to free plan
	if (!activePlan) {
		activePlan = { id: "free" };
		hasGrowthAccess = false;
	}

	return {
		activePlan,
		hasGrowthAccess,
		planType: activePlan?.id || "free",
		isLoading: false,
	};
}
