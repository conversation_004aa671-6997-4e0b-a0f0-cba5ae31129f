"use client";

import { ContactAvatar } from "@shared/components/ContactAvatar";
import {
	IconAt,
	IconBuilding,
	IconCopy,
	IconExternalLink,
	IconMapPin,
	IconMessage,
	IconPhone,
} from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface ContactData {
	id?: string;
	name?: string;
	firstName?: string;
	lastName?: string;
	title?: string;
	email?: string | Array<{ address?: string }> | Array<string>;
	phone?: string | Array<{ number?: string }> | Array<string>;
	company?: string;
	status?: string;
	image?: string;
	location?: string;
	website?: string;
}

interface ContactCardProps {
	contact: ContactData;
	className?: string;
	onEdit?: (contactId: string) => void;
	onMessage?: (contactId: string) => void;
	showActions?: boolean;
}

export function ContactCard({
	contact,
	className,
	onEdit,
	onMessage,
	showActions = true,
}: ContactCardProps) {
	const [isHovered, setIsHovered] = useState(false);

	const getContactName = () => {
		return (
			contact.name ||
			`${contact.firstName || ""} ${contact.lastName || ""}`.trim() ||
			"Unnamed Contact"
		);
	};

	const getPrimaryEmail = () => {
		if (!contact.email) return "";
		if (Array.isArray(contact.email)) {
			const firstEmail = contact.email[0];
			return typeof firstEmail === "object"
				? firstEmail.address || ""
				: firstEmail || "";
		}
		return contact.email;
	};

	const getPrimaryPhone = () => {
		if (!contact.phone) return "";
		if (Array.isArray(contact.phone)) {
			const firstPhone = contact.phone[0];
			return typeof firstPhone === "object"
				? firstPhone.number || ""
				: firstPhone || "";
		}
		return contact.phone;
	};

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((part) => part.charAt(0))
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	const getStatusColor = (status: string) => {
		switch (status?.toLowerCase()) {
			case "active":
				return "success";
			case "inactive":
				return "error";
			case "prospect":
				return "warning";
			case "customer":
				return "info";
			default:
				return "info";
		}
	};

	const handleCopy = useCallback((value: string, label: string) => {
		navigator.clipboard.writeText(value);
		toast.success(`${label} copied to clipboard`);
	}, []);

	const handleEdit = useCallback(() => {
		if (contact.id) {
			onEdit?.(contact.id);
		}
	}, [contact.id, onEdit]);

	const handleMessage = useCallback(() => {
		if (contact.id) {
			onMessage?.(contact.id);
		}
	}, [contact.id, onMessage]);

	const contactName = getContactName();
	const primaryEmail = getPrimaryEmail();
	const primaryPhone = getPrimaryPhone();

	return (
		<motion.div
			initial={{ opacity: 0, y: 10 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className={cn("w-full", className)}
		>
			<Card
				className="hover:shadow-md transition-all duration-200 cursor-pointer"
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
			>
				<CardContent className="p-4">
					<div className="flex items-start gap-3">
						<div className="flex-shrink-0">
							<ContactAvatar
								name={contactName}
								avatarUrl={contact.image}
								className="w-12 h-12"
							/>
						</div>

						<div className="flex-1 min-w-0">
							<div className="space-y-1">
								<h4 className="font-semibold text-sm text-foreground truncate">
									{contactName}
								</h4>
								{contact.title && (
									<p className="text-xs text-muted-foreground truncate">
										{contact.title}
									</p>
								)}
							</div>

							<div className="mt-3 space-y-2">
								{primaryEmail && (
									<div className="flex items-center gap-2 text-xs">
										<IconAt className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<div className="flex items-center gap-1 min-w-0 flex-1">
											<a
												href={`mailto:${primaryEmail}`}
												className="text-blue-600 hover:underline truncate"
												onClick={(e) =>
													e.stopPropagation()
												}
											>
												{primaryEmail}
											</a>
											{isHovered && (
												<Button
													variant="ghost"
													size="sm"
													className="h-4 w-4 p-0 hover:bg-muted/50"
													onClick={(e) => {
														e.stopPropagation();
														handleCopy(
															primaryEmail,
															"Email",
														);
													}}
												>
													<IconCopy className="w-3 h-3" />
												</Button>
											)}
										</div>
									</div>
								)}

								{primaryPhone && (
									<div className="flex items-center gap-2 text-xs">
										<IconPhone className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<div className="flex items-center gap-1 min-w-0 flex-1">
											<a
												href={`tel:${primaryPhone}`}
												className="text-blue-600 hover:underline"
												onClick={(e) =>
													e.stopPropagation()
												}
											>
												{primaryPhone}
											</a>
											{isHovered && (
												<Button
													variant="ghost"
													size="sm"
													className="h-4 w-4 p-0 hover:bg-muted/50"
													onClick={(e) => {
														e.stopPropagation();
														handleCopy(
															primaryPhone,
															"Phone",
														);
													}}
												>
													<IconCopy className="w-3 h-3" />
												</Button>
											)}
										</div>
									</div>
								)}

								{contact.company && (
									<div className="flex items-center gap-2 text-xs">
										<IconBuilding className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<span className="text-muted-foreground truncate">
											{contact.company}
										</span>
									</div>
								)}

								{contact.location && (
									<div className="flex items-center gap-2 text-xs">
										<IconMapPin className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<span className="text-muted-foreground truncate">
											{contact.location}
										</span>
									</div>
								)}
							</div>

							<div className="mt-3 flex items-center justify-between">
								<div className="flex items-center gap-2">
									{contact.status && (
										<Badge
											status={getStatusColor(
												contact.status,
											)}
											className="text-xs"
										>
											{contact.status}
										</Badge>
									)}
								</div>

								{showActions && isHovered && (
									<motion.div
										initial={{ opacity: 0, scale: 0.9 }}
										animate={{ opacity: 1, scale: 1 }}
										className="flex items-center gap-1"
									>
										{contact.website && (
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-6 w-6 p-0 hover:bg-muted/80"
														onClick={(e) => {
															e.stopPropagation();
															window.open(
																contact.website?.startsWith(
																	"http",
																)
																	? contact.website
																	: `https://${contact.website}`,
																"_blank",
															);
														}}
													>
														<IconExternalLink className="w-3 h-3" />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Visit website
												</TooltipContent>
											</Tooltip>
										)}

										{primaryEmail && (
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-6 w-6 p-0 hover:bg-muted/80"
														onClick={handleMessage}
													>
														<IconMessage className="w-3 h-3" />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Send message
												</TooltipContent>
											</Tooltip>
										)}
									</motion.div>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
