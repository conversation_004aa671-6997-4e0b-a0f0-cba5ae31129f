"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Sidebar,
	SidebarContent,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from "@ui/components/sidebar";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Edit2, MessageSquare, Plus, Search, Trash2 } from "lucide-react";
import { useState } from "react";
import { SidebarCreditsStatus } from "./SidebarCreditsStatus";

interface Chat {
	id: string;
	title: string;
	messages: Array<{
		role: "user" | "assistant";
		content: string;
	}>;
	createdAt: string;
	updatedAt: string;
}

interface ChatSidebarProps {
	chats: Chat[];
	currentChatId?: string;
	onChatSelect: (chatId: string) => void;
	onNewChat: () => void;
	onDeleteChat?: (chatId: string) => void;
	onRenameChat?: (chatId: string, newTitle: string) => void;
	organizationId: string;
	organizationSlug: string;
	className?: string;
}

export function ChatSidebar({
	chats,
	currentChatId,
	onChatSelect,
	onNewChat,
	onDeleteChat,
	onRenameChat,
	organizationId,
	organizationSlug,
	className,
}: ChatSidebarProps) {
	const [editingChatId, setEditingChatId] = useState<string | null>(null);
	const [editingTitle, setEditingTitle] = useState("");
	const [searchQuery, setSearchQuery] = useState("");

	const handleStartEdit = (chat: Chat) => {
		setEditingChatId(chat.id);
		setEditingTitle(chat.title);
	};

	const handleFinishEdit = () => {
		if (editingChatId && editingTitle.trim() && onRenameChat) {
			onRenameChat(editingChatId, editingTitle.trim());
		}
		setEditingChatId(null);
		setEditingTitle("");
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			handleFinishEdit();
		} else if (e.key === "Escape") {
			setEditingChatId(null);
			setEditingTitle("");
		}
	};

	const now = Date.now();
	const oneDayAgo = now - 24 * 60 * 60 * 1000;
	const twoDaysAgo = now - 2 * 24 * 60 * 60 * 1000;
	const thirtyDaysAgo = now - 30 * 24 * 60 * 60 * 1000;

	const filteredChats = chats.filter(
		(chat) =>
			chat.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			chat.messages.some((msg) =>
				msg.content.toLowerCase().includes(searchQuery.toLowerCase()),
			),
	);

	const todayChats = filteredChats.filter(
		(chat) => new Date(chat.updatedAt).getTime() >= oneDayAgo,
	);

	const yesterdayChats = filteredChats.filter((chat) => {
		const time = new Date(chat.updatedAt).getTime();
		return time >= twoDaysAgo && time < oneDayAgo;
	});

	const lastThirtyDaysChats = filteredChats.filter((chat) => {
		const time = new Date(chat.updatedAt).getTime();
		return time >= thirtyDaysAgo && time < twoDaysAgo;
	});

	const historyChats = filteredChats.filter(
		(chat) => new Date(chat.updatedAt).getTime() < thirtyDaysAgo,
	);

	const renderChatGroup = (chatGroup: Chat[], label: string) => {
		if (chatGroup.length === 0) return null;

		return (
			<SidebarGroup>
				<SidebarGroupLabel>{label}</SidebarGroupLabel>
				<SidebarGroupContent>
					<SidebarMenu>
						{chatGroup.map((chat) => (
							<SidebarMenuItem
								key={chat.id}
								className="relative group/chat-item"
							>
								<SidebarMenuButton
									className="w-full !rounded-lg"
									onClick={() => onChatSelect(chat.id)}
									isActive={currentChatId === chat.id}
								>
									<div className="flex-1 min-w-0">
										{editingChatId === chat.id ? (
											<Input
												value={editingTitle}
												onChange={(e) =>
													setEditingTitle(
														e.target.value,
													)
												}
												onBlur={handleFinishEdit}
												onKeyDown={handleKeyDown}
												className="h-6 text-sm font-medium bg-background"
												autoFocus
											/>
										) : (
											<>
												<span className="truncate font-medium">
													{chat.title}
												</span>
											</>
										)}
									</div>
								</SidebarMenuButton>

								{editingChatId !== chat.id && (
									<>
										<div className="absolute top-0 right-0 bottom-0 pointer-events-none flex justify-end gap-2 px-4 items-center group-hover/chat-item:opacity-100 opacity-0 transition-all duration-100 bg-gradient-to-l from-sidebar to-transparent w-full rounded-r-lg" />
										<div className="absolute top-0 right-0 bottom-0 translate-x-full group-hover/chat-item:translate-x-0 flex justify-end gap-2 px-2 items-center group-hover/chat-item:opacity-100 opacity-0 transition-all duration-100 rounded-r-lg">
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														size="icon"
														variant="ghost"
														className="size-6 rounded-[10px]"
														onClick={(e) => {
															e.stopPropagation();
															handleStartEdit(
																chat,
															);
														}}
													>
														<Edit2 className="size-4" />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													<p>Edit</p>
												</TooltipContent>
											</Tooltip>

											{onDeleteChat && (
												<Tooltip>
													<TooltipTrigger asChild>
														<Button
															size="icon"
															variant="ghost"
															className="size-6 rounded-[10px]"
															onClick={(e) => {
																e.stopPropagation();
																onDeleteChat(
																	chat.id,
																);
															}}
														>
															<Trash2 className="size-4" />
														</Button>
													</TooltipTrigger>
													<TooltipContent>
														<p>Delete</p>
													</TooltipContent>
												</Tooltip>
											)}
										</div>
									</>
								)}
							</SidebarMenuItem>
						))}
					</SidebarMenu>
				</SidebarGroupContent>
			</SidebarGroup>
		);
	};

	return (
		<div
			className={cn(
				"w-64 h-full flex flex-col bg-sidebar border-r",
				className,
			)}
		>
			<div className="p-3 border-b flex items-center gap-3">
				<div className="relative flex-1">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Search chats..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="pl-9 h-9"
					/>
				</div>
				<Button
					onClick={onNewChat}
					size="sm"
					variant="relio"
					className="h-8 w-8 p-0"
					tooltip="New Chat"
				>
					<Plus className="h-4 w-4" />
				</Button>
			</div>

			<ScrollArea className="flex-1">
				{filteredChats.length === 0 ? (
					<div className="text-center py-12 px-4">
						<MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
						<h3 className="font-medium mb-2">
							{searchQuery ? "No chats found" : "No chats yet"}
						</h3>
						<p className="text-sm text-muted-foreground mb-4">
							{searchQuery
								? "Try adjusting your search terms"
								: "Start a new conversation to analyze your CRM data"}
						</p>
						{!searchQuery && (
							<Button onClick={onNewChat} size="sm">
								<Plus className="h-4 w-4" />
								New Chat
							</Button>
						)}
					</div>
				) : (
					<div className="py-2">
						{renderChatGroup(todayChats, "Today")}
						{renderChatGroup(yesterdayChats, "Yesterday")}
						{renderChatGroup(lastThirtyDaysChats, "Last 30 Days")}
						{renderChatGroup(historyChats, "History")}
					</div>
				)}
			</ScrollArea>

			<SidebarCreditsStatus
				organizationId={organizationId}
				organizationSlug={organizationSlug}
			/>
		</div>
	);
}
