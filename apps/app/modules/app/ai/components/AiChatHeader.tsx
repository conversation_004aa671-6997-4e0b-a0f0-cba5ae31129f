"use client";

import {
	IconLayoutSidebarLeftCollapseFilled,
	IconLayoutSidebarLeftExpandFilled,
	IconLayoutSidebarRightCollapseFilled,
	IconLayoutSidebarRightExpand,
	IconLayoutSidebarRightExpandFilled,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Bot, Plus, Settings, Share2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useModelAccess } from "../hooks/useModelAccess";
import type { Model } from "../lib/models";
import { ModelSelector } from "./ModelSelector";

interface AiChatHeaderProps {
	currentChatTitle?: string;
	onNewChat: () => void;
	showSidebar?: boolean;
	onToggleSidebar?: () => void;
	className?: string;
	selectedModelId?: string;
	onModelSelect?: (model: Model) => void;
	organizationSlug?: string;
}

export function AiChatHeader({
	currentChatTitle,
	onNewChat,
	showSidebar = true,
	onToggleSidebar,
	className,
	selectedModelId,
	onModelSelect,
	organizationSlug,
}: AiChatHeaderProps) {
	const { hasGrowthAccess } = useModelAccess();
	const router = useRouter();
	return (
		<div className={cn("", className)}>
			<div className="flex items-center justify-between p-3">
				{/* Left Section */}
				<div className="flex items-center gap-3">
					{/* Sidebar Toggle (when sidebar is hidden) */}
					{!showSidebar && onToggleSidebar && (
						<Button
							variant="ghost"
							size="icon"
							onClick={onToggleSidebar}
						>
							<IconLayoutSidebarRightCollapseFilled className="h-4 w-4" />
						</Button>
					)}

					{showSidebar && onToggleSidebar && (
						<Button
							variant="ghost"
							size="icon"
							onClick={onToggleSidebar}
						>
							<IconLayoutSidebarRightExpand className="h-4 w-4" />
						</Button>
					)}

					{/* Model Selector */}
					<div className="hidden sm:flex">
						<ModelSelector
							selectedModelId={selectedModelId}
							onModelSelect={onModelSelect}
							hasGrowthAccess={hasGrowthAccess}
							className="w-auto"
						/>
					</div>

					{/* New Chat (when sidebar is hidden) */}
					{!showSidebar && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									onClick={onNewChat}
								>
									<Plus className="h-4 w-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>New Chat</TooltipContent>
						</Tooltip>
					)}
				</div>

				{/* Right Section */}
				<div className="flex items-center gap-2">
					{/* Share Button */}
					{/* {currentChatTitle && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon">
									<Share2 className="h-4 w-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Share Chat</TooltipContent>
						</Tooltip>
					)} */}

					{/* Settings */}
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="ghost"
								size="icon"
								onClick={() => {
									router.push(
										`/app/${organizationSlug}/settings/ai`,
									);
								}}
							>
								<Settings className="h-4 w-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Settings</TooltipContent>
					</Tooltip>
				</div>
			</div>
		</div>
	);
}
