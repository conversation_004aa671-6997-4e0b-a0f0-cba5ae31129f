"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Textarea } from "@ui/components/textarea";
import {
	<PERSON>lt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import {
	ArrowUp,
	BarChart3,
	Building,
	CheckSquare,
	Search,
	Square,
	Users,
} from "lucide-react";
import React, {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from "react";

type PromptInputContextType = {
	isLoading: boolean;
	value: string;
	setValue: (value: string) => void;
	maxHeight: number | string;
	onSubmit?: () => void;
	disabled?: boolean;
};

const PromptInputContext = createContext<PromptInputContextType>({
	isLoading: false,
	value: "",
	setValue: () => {},
	maxHeight: 240,
	onSubmit: undefined,
	disabled: false,
});

function usePromptInput() {
	const context = useContext(PromptInputContext);
	if (!context) {
		throw new Error("usePromptInput must be used within a PromptInput");
	}
	return context;
}

type PromptInputProps = {
	isLoading?: boolean;
	value?: string;
	onValueChange?: (value: string) => void;
	maxHeight?: number | string;
	onSubmit?: () => void;
	children: React.ReactNode;
	className?: string;
};

function PromptInput({
	className,
	isLoading = false,
	maxHeight = 240,
	value,
	onValueChange,
	onSubmit,
	children,
}: PromptInputProps) {
	const [internalValue, setInternalValue] = useState(value || "");

	const handleChange = useCallback(
		(newValue: string) => {
			setInternalValue(newValue);
			onValueChange?.(newValue);
		},
		[onValueChange],
	);

	const contextValue = useMemo(
		() => ({
			isLoading,
			value: value ?? internalValue,
			setValue: onValueChange ?? handleChange,
			maxHeight,
			onSubmit,
			disabled: isLoading,
		}),
		[
			isLoading,
			value,
			internalValue,
			onValueChange,
			handleChange,
			maxHeight,
			onSubmit,
		],
	);

	return (
		<TooltipProvider>
			<PromptInputContext.Provider value={contextValue}>
				<div
					className={cn(
						"border-input bg-background rounded-3xl p-2 shadow-xs border-0 md:border-1",
						className,
					)}
				>
					{children}
				</div>
			</PromptInputContext.Provider>
		</TooltipProvider>
	);
}

export type PromptInputTextareaProps = {
	disableAutosize?: boolean;
} & React.ComponentProps<typeof Textarea>;

function PromptInputTextarea({
	className,
	onKeyDown,
	disableAutosize = false,
	...props
}: PromptInputTextareaProps) {
	const { value, setValue, maxHeight, onSubmit, disabled } = usePromptInput();
	const textareaRef = useRef<HTMLTextAreaElement>(null);

	useEffect(() => {
		if (disableAutosize) return;

		if (!textareaRef.current) return;
		textareaRef.current.style.height = "auto";
		textareaRef.current.style.height =
			typeof maxHeight === "number"
				? `${Math.min(textareaRef.current.scrollHeight, maxHeight)}px`
				: `min(${textareaRef.current.scrollHeight}px, ${maxHeight})`;
	}, [value, maxHeight, disableAutosize]);

	useEffect(() => {
		if (textareaRef.current && !disabled) {
			textareaRef.current.focus();
		}
	}, [disabled]);

	const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			onSubmit?.();
		}
		onKeyDown?.(e);
	};

	return (
		<Textarea
			ref={textareaRef}
			value={value}
			onChange={(e) => setValue(e.target.value)}
			onKeyDown={handleKeyDown}
			className={cn(
				"text-primary min-h-[44px] w-full resize-none border-none bg-transparent shadow-none outline-none focus-visible:ring-0 focus-visible:ring-offset-0",
				className,
			)}
			rows={1}
			disabled={disabled}
			{...props}
		/>
	);
}

type PromptInputActionsProps = React.HTMLAttributes<HTMLDivElement>;

function PromptInputActions({
	children,
	className,
	...props
}: PromptInputActionsProps) {
	return (
		<div className={cn("flex items-center gap-2", className)} {...props}>
			{children}
		</div>
	);
}

type PromptInputActionProps = {
	className?: string;
	tooltip: React.ReactNode;
	children: React.ReactNode;
	side?: "top" | "bottom" | "left" | "right";
} & React.ComponentProps<typeof Tooltip>;

function PromptInputAction({
	tooltip,
	children,
	className,
	side = "top",
	...props
}: PromptInputActionProps) {
	const { disabled } = usePromptInput();

	return (
		<Tooltip {...props}>
			<TooltipTrigger asChild>
				<span style={{ pointerEvents: disabled ? "none" : "auto" }}>
					{children}
				</span>
			</TooltipTrigger>
			<TooltipContent side={side} className={className}>
				{tooltip}
			</TooltipContent>
		</Tooltip>
	);
}

interface CRMPromptInputProps {
	input: string;
	onInputChange: (value: string) => void;
	onSubmit: () => void;
	isLoading: boolean;
	disabled?: boolean;
	placeholder?: string;
	className?: string;
	tool?: string;
	onToolChange?: (tool: string | undefined) => void;
	supportsTools?: boolean;
}

function CRMPromptInput({
	input,
	onInputChange,
	onSubmit,
	isLoading,
	disabled = false,
	placeholder = "Ask me about your contacts, tasks, analytics, or anything else...",
	className,
	tool,
	onToolChange,
	supportsTools = true,
}: CRMPromptInputProps) {
	const handleSubmit = useCallback(() => {
		if (isLoading || !input.trim()) return;
		onSubmit();
	}, [isLoading, input, onSubmit]);

	const hasText = input.trim() !== "";

	const handleAnalyticsClick = useCallback(() => {
		onInputChange("What are my analytics for this month?");
	}, [onInputChange]);

	const handleContactsClick = useCallback(() => {
		onInputChange("Show me my recent contacts");
	}, [onInputChange]);

	const handleTasksClick = useCallback(() => {
		onInputChange("What tasks are overdue?");
	}, [onInputChange]);

	const handleCompaniesClick = useCallback(() => {
		onInputChange("Find companies in tech industry");
	}, [onInputChange]);

	const handleSearchToggle = useCallback(() => {
		if (!supportsTools || !onToolChange) return;
		if (tool === "search") {
			onToolChange(undefined);
		} else {
			onToolChange("search");
		}
	}, [tool, onToolChange, supportsTools]);

	return (
		<PromptInput
			value={input}
			onValueChange={onInputChange}
			isLoading={isLoading}
			onSubmit={handleSubmit}
			className={cn(
				"w-full max-w-3xl mx-auto p-3 md:rounded-3xl bg-background/50",
				className,
			)}
		>
			<PromptInputTextarea
				className="bg-transparent!"
				placeholder={placeholder}
				disabled={disabled}
			/>

			<PromptInputActions className="flex items-center justify-between gap-2 pt-2">
				<div className="flex items-center gap-1">
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="h-8 rounded-full text-xs gap-2"
								onClick={handleAnalyticsClick}
								disabled={isLoading}
							>
								<BarChart3 className="size-3" />
								Analytics
							</Button>
						</TooltipTrigger>
						<TooltipContent>Quick analytics queries</TooltipContent>
					</Tooltip>

					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="h-8 rounded-full text-xs gap-2"
								onClick={handleContactsClick}
								disabled={isLoading}
							>
								<Users className="size-3" />
								Contacts
							</Button>
						</TooltipTrigger>
						<TooltipContent>Quick contact queries</TooltipContent>
					</Tooltip>

					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="h-8 rounded-full text-xs gap-2"
								onClick={handleTasksClick}
								disabled={isLoading}
							>
								<CheckSquare className="size-3" />
								Tasks
							</Button>
						</TooltipTrigger>
						<TooltipContent>Quick task queries</TooltipContent>
					</Tooltip>

					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="h-8 rounded-full text-xs gap-2"
								onClick={handleCompaniesClick}
								disabled={isLoading}
							>
								<Building className="size-3" />
								Companies
							</Button>
						</TooltipTrigger>
						<TooltipContent>Quick company queries</TooltipContent>
					</Tooltip>

					{supportsTools && onToolChange && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="outline"
									className={cn(
										"h-8 rounded-full text-xs gap-2",
										tool === "search" &&
											"text-primary hover:text-primary border-primary bg-primary/10",
									)}
									onClick={handleSearchToggle}
									disabled={isLoading}
								>
									<Search className="size-3" />
									Search
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								{tool === "search"
									? "Disable web search"
									: "Enable web search"}
							</TooltipContent>
						</Tooltip>
					)}
				</div>

				<div className="flex-1" />

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="primary"
							size="icon"
							className="h-8 w-8 rounded-full"
							disabled={!hasText && !isLoading}
							onClick={handleSubmit}
						>
							{isLoading ? (
								<Square className="size-4 fill-current" />
							) : (
								<ArrowUp className="size-4" />
							)}
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						{isLoading
							? "Stop generation"
							: hasText
								? "Send message"
								: "Please enter a message"}
					</TooltipContent>
				</Tooltip>
			</PromptInputActions>
		</PromptInput>
	);
}

export {
	PromptInput,
	PromptInputTextarea,
	PromptInputActions,
	PromptInputAction,
	CRMPromptInput,
};
