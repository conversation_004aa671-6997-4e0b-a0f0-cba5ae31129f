import { CodeBlock, CodeBlockCode } from "@ui/components/code-block";
import { cn } from "@ui/lib";
import { marked } from "marked";
import { memo, useId, useMemo } from "react";
import ReactMarkdown, { type Components } from "react-markdown";
import remarkBreaks from "remark-breaks";
import remarkGfm from "remark-gfm";

export type MarkdownProps = {
	children: string;
	id?: string;
	className?: string;
	components?: Partial<Components>;
};

function parseMarkdownIntoBlocks(markdown: string): string[] {
	const tokens = marked.lexer(markdown);
	return tokens.map((token) => token.raw);
}

function extractLanguage(className?: string): string {
	if (!className) return "plaintext";
	const match = className.match(/language-(\w+)/);
	return match ? match[1] : "plaintext";
}

const INITIAL_COMPONENTS: Partial<Components> = {
	code: function CodeComponent({ className, children, ...props }) {
		const isInline =
			!props.node?.position?.start.line ||
			props.node?.position?.start.line === props.node?.position?.end.line;

		if (isInline) {
			return (
				<span
					className={cn(
						"bg-muted rounded-sm px-1 font-mono text-sm",
						className,
					)}
					{...props}
				>
					{children}
				</span>
			);
		}

		const language = extractLanguage(className);

		return (
			<CodeBlock className={className}>
				<CodeBlockCode code={children as string} language={language} />
			</CodeBlock>
		);
	},
	pre: function PreComponent({ children }) {
		return <>{children}</>;
	},
	h1: ({ children }) => (
		<h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">{children}</h1>
	),
	h2: ({ children }) => (
		<h2 className="text-xl font-semibold mb-3 mt-5 first:mt-0">
			{children}
		</h2>
	),
	h3: ({ children }) => (
		<h3 className="text-lg font-medium mb-2 mt-4 first:mt-0">{children}</h3>
	),
	h4: ({ children }) => (
		<h4 className="text-base font-medium mb-2 mt-3 first:mt-0">
			{children}
		</h4>
	),
	p: ({ children }) => (
		<p className="mb-3 last:mb-0 leading-relaxed">{children}</p>
	),
	ul: ({ children }) => (
		<ul className="list-disc list-outside ml-6 mb-3 space-y-1">
			{children}
		</ul>
	),
	ol: ({ children }) => (
		<ol className="list-decimal list-outside ml-6 mb-3 space-y-1">
			{children}
		</ol>
	),
	li: ({ children }) => <li className="leading-relaxed">{children}</li>,
	blockquote: ({ children }) => (
		<blockquote className="border-l-4 border-muted-foreground/30 pl-4 py-2 mb-3 italic text-muted-foreground">
			{children}
		</blockquote>
	),
	table: ({ children }) => (
		<div className="overflow-x-auto mb-3">
			<table className="min-w-full border-collapse border border-border">
				{children}
			</table>
		</div>
	),
	th: ({ children }) => (
		<th className="border border-border bg-muted px-3 py-2 text-left font-semibold">
			{children}
		</th>
	),
	td: ({ children }) => (
		<td className="border border-border px-3 py-2">{children}</td>
	),
	a: ({ children, href }) => (
		<a
			href={href}
			className="text-primary hover:underline"
			target={href?.startsWith("http") ? "_blank" : undefined}
			rel={href?.startsWith("http") ? "noopener noreferrer" : undefined}
		>
			{children}
		</a>
	),
	strong: ({ children }) => (
		<strong className="font-semibold">{children}</strong>
	),
	em: ({ children }) => <em className="italic">{children}</em>,
	hr: () => <hr className="border-border my-6" />,
};

const MemoizedMarkdownBlock = memo(
	function MarkdownBlock({
		content,
		components = INITIAL_COMPONENTS,
	}: {
		content: string;
		components?: Partial<Components>;
	}) {
		return (
			<ReactMarkdown
				remarkPlugins={[remarkGfm, remarkBreaks]}
				components={components}
			>
				{content}
			</ReactMarkdown>
		);
	},
	function propsAreEqual(prevProps, nextProps) {
		return prevProps.content === nextProps.content;
	},
);

MemoizedMarkdownBlock.displayName = "MemoizedMarkdownBlock";

function MarkdownComponent({
	children,
	id,
	className,
	components = INITIAL_COMPONENTS,
}: MarkdownProps) {
	const generatedId = useId();
	const blockId = id ?? generatedId;
	const blocks = useMemo(() => parseMarkdownIntoBlocks(children), [children]);

	return (
		<div className={className}>
			{blocks.map((block, index) => (
				<MemoizedMarkdownBlock
					key={`${blockId}-block-${index}`}
					content={block}
					components={components}
				/>
			))}
		</div>
	);
}

const Markdown = memo(MarkdownComponent);
Markdown.displayName = "Markdown";

export { Markdown };
