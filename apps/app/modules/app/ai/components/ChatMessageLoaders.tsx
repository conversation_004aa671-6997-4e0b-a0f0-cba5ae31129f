"use client";

import { cn } from "@ui/lib";

interface LoaderProps {
	variant?: "typing" | "dots" | "pulse" | "circular";
	size?: "sm" | "md" | "lg";
	className?: string;
}

export function ChatMessageLoaders({
	variant = "typing",
	size = "sm",
	className,
}: LoaderProps) {
	const sizeClasses = {
		sm: "h-4",
		md: "h-5",
		lg: "h-6",
	};

	switch (variant) {
		case "typing":
			return <TypingLoader size={size} className={className} />;
		case "dots":
			return <DotsLoader size={size} className={className} />;
		case "pulse":
			return <PulseLoader size={size} className={className} />;
		case "circular":
			return <CircularLoader size={size} className={className} />;
		default:
			return <TypingLoader size={size} className={className} />;
	}
}

function TypingLoader({
	size = "sm",
	className,
}: {
	size?: "sm" | "md" | "lg";
	className?: string;
}) {
	const dotSizes = {
		sm: "h-1 w-1",
		md: "h-1.5 w-1.5",
		lg: "h-2 w-2",
	};

	const containerSizes = {
		sm: "h-4",
		md: "h-5",
		lg: "h-6",
	};

	return (
		<div
			className={cn(
				"flex items-center space-x-1",
				containerSizes[size],
				className,
			)}
		>
			{[...Array(3)].map((_, i) => (
				<div
					key={i}
					className={cn(
						"bg-primary animate-[typing_1s_infinite] rounded-full",
						dotSizes[size],
					)}
					style={{
						animationDelay: `${i * 250}ms`,
					}}
				/>
			))}
			<span className="sr-only">Loading</span>
		</div>
	);
}

function DotsLoader({
	size = "sm",
	className,
}: {
	size?: "sm" | "md" | "lg";
	className?: string;
}) {
	const dotSizes = {
		sm: "h-1.5 w-1.5",
		md: "h-2 w-2",
		lg: "h-2.5 w-2.5",
	};

	const containerSizes = {
		sm: "h-4",
		md: "h-5",
		lg: "h-6",
	};

	return (
		<div
			className={cn(
				"flex items-center space-x-1",
				containerSizes[size],
				className,
			)}
		>
			{[...Array(3)].map((_, i) => (
				<div
					key={i}
					className={cn(
						"bg-primary animate-[bounce-dots_1.4s_ease-in-out_infinite] rounded-full",
						dotSizes[size],
					)}
					style={{
						animationDelay: `${i * 160}ms`,
					}}
				/>
			))}
			<span className="sr-only">Loading</span>
		</div>
	);
}

function PulseLoader({
	size = "sm",
	className,
}: {
	size?: "sm" | "md" | "lg";
	className?: string;
}) {
	const sizeClasses = {
		sm: "size-4",
		md: "size-5",
		lg: "size-6",
	};

	return (
		<div className={cn("relative", sizeClasses[size], className)}>
			<div className="border-primary absolute inset-0 animate-[thin-pulse_1.5s_ease-in-out_infinite] rounded-full border-2" />
			<span className="sr-only">Loading</span>
		</div>
	);
}

function CircularLoader({
	size = "sm",
	className,
}: {
	size?: "sm" | "md" | "lg";
	className?: string;
}) {
	const sizeClasses = {
		sm: "size-4",
		md: "size-5",
		lg: "size-6",
	};

	return (
		<div
			className={cn(
				"border-primary animate-spin rounded-full border-2 border-t-transparent",
				sizeClasses[size],
				className,
			)}
		>
			<span className="sr-only">Loading</span>
		</div>
	);
}
