"use client";

import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	Activity,
	BarChart3,
	Building,
	CheckSquare,
	ChevronDown,
	ExternalLink,
	Plus,
	Users,
} from "lucide-react";
import { useState } from "react";
import { CompanyCard } from "./CompanyCard";
import { ContactCard } from "./ContactCard";

interface ToolCall {
	name: string;
	parameters: any;
	result?: any;
}

interface ToolResultsProps {
	toolCalls: ToolCall[];
	className?: string;
}

export function ToolResults({ toolCalls, className }: ToolResultsProps) {
	const [expandedTools, setExpandedTools] = useState<Set<string>>(new Set());

	const filteredToolCalls = toolCalls.filter(
		(toolCall) => toolCall.name !== "search",
	);

	const toggleTool = (toolName: string) => {
		const newExpanded = new Set(expandedTools);
		if (newExpanded.has(toolName)) {
			newExpanded.delete(toolName);
		} else {
			newExpanded.add(toolName);
		}
		setExpandedTools(newExpanded);
	};

	const getToolIcon = (toolName: string) => {
		switch (toolName) {
			case "searchContacts":
				return <Users className="w-4 h-4" />;
			case "searchCompanies":
				return <Building className="w-4 h-4" />;
			case "getTaskSummary":
				return <CheckSquare className="w-4 h-4" />;
			case "getAnalytics":
				return <BarChart3 className="w-4 h-4" />;
			case "getRecentActivity":
				return <Activity className="w-4 h-4" />;
			case "createTask":
				return <Plus className="w-4 h-4" />;
			case "search":
				return <ExternalLink className="w-4 h-4" />;
			default:
				return <BarChart3 className="w-4 h-4" />;
		}
	};

	const getToolDisplayName = (toolName: string) => {
		switch (toolName) {
			case "searchContacts":
				return "Contact Search";
			case "searchCompanies":
				return "Company Search";
			case "getTaskSummary":
				return "Task Summary";
			case "getAnalytics":
				return "Analytics";
			case "getRecentActivity":
				return "Recent Activity";
			case "createTask":
				return "Task Created";
			case "search":
				return "Web Search";
			default:
				return toolName;
		}
	};

	if (filteredToolCalls.length === 0) {
		return null;
	}

	return (
		<div className={cn("space-y-3", className)}>
			{filteredToolCalls.map((toolCall, index) => {
				const isExpanded = expandedTools.has(
					`${toolCall.name}-${index}`,
				);
				const hasResult = toolCall.result && toolCall.result.success;

				return (
					<motion.div
						key={`${toolCall.name}-${index}`}
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.3, delay: index * 0.1 }}
						className="border rounded-xl bg-muted/30"
					>
						<button
							onClick={() =>
								toggleTool(`${toolCall.name}-${index}`)
							}
							className="w-full px-4 py-3 flex items-center justify-between hover:bg-muted/50 transition-colors rounded-t-xl"
						>
							<div className="flex items-center gap-3">
								<div className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary/10 text-primary">
									{getToolIcon(toolCall.name)}
								</div>
								<div className="text-left">
									<div className="font-medium text-sm">
										{getToolDisplayName(toolCall.name)}
									</div>
									{hasResult && (
										<div className="text-xs text-muted-foreground">
											{toolCall.result.metadata
												?.recordCount || 0}{" "}
											results
										</div>
									)}
								</div>
							</div>

							<div className="flex items-center gap-2">
								{hasResult ? (
									<Badge className="text-xs bg-green-100 text-green-800 border-green-200">
										Success
									</Badge>
								) : (
									<Badge className="text-xs bg-red-100 text-red-800 border-red-200">
										Error
									</Badge>
								)}
								<ChevronDown
									className={cn(
										"w-4 h-4 transition-transform",
										isExpanded && "rotate-180",
									)}
								/>
							</div>
						</button>

						{isExpanded && (
							<motion.div
								initial={{ opacity: 0, height: 0 }}
								animate={{ opacity: 1, height: "auto" }}
								exit={{ opacity: 0, height: 0 }}
								className="border-t px-4 pb-4"
							>
								{hasResult ? (
									<ToolResultDisplay toolCall={toolCall} />
								) : (
									<div className="pt-3 text-sm text-muted-foreground">
										{toolCall.result?.error ||
											"No results available"}
									</div>
								)}
							</motion.div>
						)}
					</motion.div>
				);
			})}
		</div>
	);
}

function ToolResultDisplay({ toolCall }: { toolCall: ToolCall }) {
	const { name, result } = toolCall;

	switch (name) {
		case "searchContacts":
			return <ContactResults data={result.data} />;
		case "searchCompanies":
			return <CompanyResults data={result.data} />;
		case "getTaskSummary":
			return <TaskSummaryResults data={result.data} />;
		case "getAnalytics":
			return <AnalyticsResults data={result.data} />;
		case "getRecentActivity":
			return <ActivityResults data={result.data} />;
		case "createTask":
			return <TaskCreatedResults data={result.data} />;
		default:
			return <GenericResults data={result.data} />;
	}
}

function ContactResults({ data }: { data: any }) {
	if (!data) {
		return (
			<div className="pt-3 text-sm text-muted-foreground">
				No contact data available
			</div>
		);
	}

	return (
		<div className="pt-3 space-y-4">
			<div className="text-sm text-muted-foreground">
				Found {data.contacts?.length || 0} contacts for "
				{data.query || "search"}"
			</div>
			<div className="space-y-3">
				{data.contacts
					?.slice(0, 5)
					.map((contact: any, index: number) => (
						<ContactCard
							key={contact.id || index}
							contact={{
								id: contact.id,
								name: contact.name,
								firstName: contact.firstName,
								lastName: contact.lastName,
								title: contact.title,
								email: contact.email,
								phone: contact.phone,
								company: contact.company,
								status: contact.status,
								image: contact.image,
								location: contact.location,
								website: contact.website,
							}}
							showActions={false}
						/>
					))}
				{data.contacts?.length > 5 && (
					<div className="text-xs text-muted-foreground text-center py-2 border-t pt-3">
						... and {data.contacts.length - 5} more contacts
					</div>
				)}
			</div>
		</div>
	);
}

function CompanyResults({ data }: { data: any }) {
	if (!data) {
		return (
			<div className="pt-3 text-sm text-muted-foreground">
				No company data available
			</div>
		);
	}

	return (
		<div className="pt-3 space-y-4">
			<div className="text-sm text-muted-foreground">
				Found {data.companies?.length || 0} companies for "
				{data.query || "search"}"
			</div>
			<div className="space-y-3">
				{data.companies
					?.slice(0, 5)
					.map((company: any, index: number) => (
						<CompanyCard
							key={company.id || index}
							company={{
								id: company.id,
								name: company.name,
								industry: company.industry,
								location: company.location,
								website: company.website,
								email: company.email,
								logo: company.logo,
								description: company.description,
								contactCount: company.contactCount,
								status: company.status,
							}}
							showActions={false}
						/>
					))}
				{data.companies?.length > 5 && (
					<div className="text-xs text-muted-foreground text-center py-2 border-t pt-3">
						... and {data.companies.length - 5} more companies
					</div>
				)}
			</div>
		</div>
	);
}

function TaskSummaryResults({ data }: { data: any }) {
	return (
		<div className="pt-3 space-y-3">
			<div className="grid grid-cols-2 gap-4">
				<div className="p-3 bg-background rounded-lg">
					<div className="text-lg font-semibold">
						{data.summary?.total || 0}
					</div>
					<div className="text-xs text-muted-foreground">
						Total Tasks
					</div>
				</div>
				<div className="p-3 bg-background rounded-lg">
					<div className="text-lg font-semibold text-green-600">
						{data.summary?.statusBreakdown?.done || 0}
					</div>
					<div className="text-xs text-muted-foreground">
						Completed
					</div>
				</div>
			</div>

			{data.summary?.statusBreakdown && (
				<div className="space-y-2">
					<div className="text-sm font-medium">Status Breakdown</div>
					{Object.entries(data.summary.statusBreakdown).map(
						([status, count]: [string, any]) => (
							<div
								key={status}
								className="flex justify-between text-sm"
							>
								<span className="capitalize">
									{status.replace("_", " ")}
								</span>
								<span>
									{typeof count === "object"
										? count.total || 0
										: count}
								</span>
							</div>
						),
					)}
				</div>
			)}
		</div>
	);
}

function AnalyticsResults({ data }: { data: any }) {
	if (!data) {
		return (
			<div className="pt-3 text-sm text-muted-foreground">
				No analytics data available
			</div>
		);
	}

	const metrics = data.metrics || data;

	return (
		<div className="pt-3 space-y-3">
			<div className="text-sm text-muted-foreground">
				Analytics for {data.period || "selected period"}
			</div>
			<div className="grid grid-cols-2 gap-4">
				{metrics.contacts !== undefined && (
					<div className="p-3 bg-background rounded-lg">
						<div className="text-lg font-semibold">
							{typeof metrics.contacts === "object"
								? metrics.contacts.total || 0
								: metrics.contacts}
						</div>
						<div className="text-xs text-muted-foreground">
							Contacts
						</div>
					</div>
				)}
				{metrics.tasks && (
					<div className="p-3 bg-background rounded-lg">
						<div className="text-lg font-semibold">
							{typeof metrics.tasks === "object"
								? metrics.tasks.total || 0
								: metrics.tasks}
						</div>
						<div className="text-xs text-muted-foreground">
							Tasks
						</div>
					</div>
				)}
				{metrics.companies !== undefined && (
					<div className="p-3 bg-background rounded-lg">
						<div className="text-lg font-semibold">
							{typeof metrics.companies === "object"
								? metrics.companies.total || 0
								: metrics.companies}
						</div>
						<div className="text-xs text-muted-foreground">
							Companies
						</div>
					</div>
				)}
			</div>

			{metrics.tasks?.byStatus && (
				<div className="space-y-2">
					<div className="text-sm font-medium">Task Status</div>
					<div className="grid grid-cols-2 gap-2">
						{Object.entries(metrics.tasks.byStatus).map(
							([status, count]: [string, any]) => (
								<div
									key={status}
									className="flex justify-between text-sm p-2 bg-background rounded"
								>
									<span className="capitalize">
										{status.replace("_", " ")}
									</span>
									<span className="font-medium">
										{typeof count === "object"
											? count.total || 0
											: count}
									</span>
								</div>
							),
						)}
					</div>
				</div>
			)}
		</div>
	);
}

function ActivityResults({ data }: { data: any }) {
	return (
		<div className="pt-3 space-y-2">
			<div className="text-sm text-muted-foreground">
				Recent activity ({data.activities?.length || 0} items)
			</div>
			{data.activities
				?.slice(0, 5)
				.map((activity: any, index: number) => (
					<div
						key={activity.id}
						className="flex items-center gap-3 p-2 bg-background rounded-lg"
					>
						<div className="w-2 h-2 bg-primary rounded-full" />
						<div className="flex-1">
							<div className="text-sm">{activity.title}</div>
							<div className="text-xs text-muted-foreground">
								{new Date(
									activity.createdAt,
								).toLocaleDateString()}
							</div>
						</div>
					</div>
				))}
		</div>
	);
}

function TaskCreatedResults({ data }: { data: any }) {
	return (
		<div className="pt-3 space-y-3">
			<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
				<div className="text-sm text-green-800">✅ {data.message}</div>
			</div>
			{data.task && (
				<div className="p-3 bg-background rounded-lg space-y-2">
					<div className="font-medium text-sm">{data.task.title}</div>
					{data.task.description && (
						<div className="text-xs text-muted-foreground">
							{data.task.description}
						</div>
					)}
					<div className="flex gap-2">
						<Badge className="text-xs bg-orange-100 text-orange-800 border-orange-200">
							{data.task.priority}
						</Badge>
						<Badge className="text-xs bg-blue-100 text-blue-800 border-blue-200">
							{data.task.status}
						</Badge>
					</div>
				</div>
			)}
		</div>
	);
}

function GenericResults({ data }: { data: any }) {
	return (
		<div className="pt-3">
			<pre className="text-xs bg-background p-3 rounded-lg overflow-auto">
				{JSON.stringify(data, null, 2)}
			</pre>
		</div>
	);
}
