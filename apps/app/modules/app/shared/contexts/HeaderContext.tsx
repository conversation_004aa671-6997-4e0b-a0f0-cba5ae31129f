"use client";

import React, {
	createContext,
	type PropsWithChildren,
	useContext,
} from "react";

interface HeaderContextType {
	title: string;
	subtitle?: string;
	setHeader: (title: string, subtitle?: string) => void;
}

const HeaderContext = createContext<HeaderContextType | undefined>(undefined);

export function HeaderProvider({
	children,
	defaultTitle,
	defaultSubtitle,
}: PropsWithChildren<{
	defaultTitle: string;
	defaultSubtitle?: string;
}>) {
	const [title, setTitle] = React.useState(defaultTitle);
	const [subtitle, setSubtitle] = React.useState(defaultSubtitle);

	const setHeader = (newTitle: string, newSubtitle?: string) => {
		setTitle(newTitle);
		setSubtitle(newSubtitle);
	};

	return (
		<HeaderContext.Provider value={{ title, subtitle, setHeader }}>
			{children}
		</HeaderContext.Provider>
	);
}

export function useHeader() {
	const context = useContext(HeaderContext);
	if (!context) {
		throw new Error("useHeader must be used within a HeaderProvider");
	}
	return context;
}
