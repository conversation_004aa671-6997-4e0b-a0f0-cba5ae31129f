import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { TaggableObjectType } from "@repo/database/src/types/object";
import type { Tag, ObjectTag } from "@prisma/client";

interface TagWithCreator extends Tag {
  creator: {
    id: string;
    name: string;
    image: string | null;
  };
}

interface ObjectTagWithDetails extends ObjectTag {
  tag: Tag;
  adder: {
    id: string;
    name: string;
    image: string | null;
  };
}

// Get all tags for an organization and object type
export function useOrganizationTags(objectType: TaggableObjectType) {
  return useQuery<TagWithCreator[]>({
    queryKey: ["tags", objectType],
    queryFn: async () => {
      const response = await fetch(`/api/tags/${objectType}`);
      if (!response.ok) throw new Error("Failed to fetch tags");
      return response.json();
    },
  });
}

// Get tags for a specific object
export function useObjectTags(objectId: string, objectType: TaggableObjectType) {
  return useQuery<ObjectTagWithDetails[]>({
    queryKey: ["objectTags", objectId],
    queryFn: async () => {
      const response = await fetch(`/api/tags/object/${objectId}/${objectType}`);
      if (!response.ok) throw new Error("Failed to fetch object tags");
      return response.json();
    },
    enabled: !!objectId && objectId.trim() !== "", // Only run query if objectId is not empty
  });
}

// Helper function to get plural form for object types
function getObjectTypePlural(objectType: TaggableObjectType): string {
  switch (objectType) {
    case "contact":
      return "contacts";
    case "company":
      return "companies";
    case "property":
      return "properties";
    default:
      return `${objectType}s`;
  }
}

// Helper function to invalidate infinite queries and facets
function invalidateInfiniteQueries(queryClient: any, objectType: TaggableObjectType) {
  const pluralObjectType = getObjectTypePlural(objectType);
  
  // Invalidate infinite queries using the correct plural pattern
  queryClient.invalidateQueries({ 
    predicate: (query: any) => {
      const queryKey = query.queryKey;
      return Array.isArray(queryKey) && 
             queryKey.length > 0 && 
             typeof queryKey[0] === 'string' &&
             queryKey[0] === `${pluralObjectType}-infinite`;
    }
  });
  
  // Also invalidate the object queries using plural forms
  queryClient.invalidateQueries({ 
    predicate: (query: any) => {
      const queryKey = query.queryKey;
      return Array.isArray(queryKey) && 
             queryKey.length > 0 && 
             queryKey[0] === pluralObjectType;
    }
  });
}

// Helper function to optimistically update facets counts in infinite queries
function updateFacetsOptimistically(
  queryClient: any, 
  objectType: TaggableObjectType, 
  tagName: string, 
  increment: number
) {
  const pluralObjectType = getObjectTypePlural(objectType);
  
  // Find and update all infinite queries for this object type
  queryClient.setQueriesData(
    { 
      predicate: (query: any) => {
        const queryKey = query.queryKey;
        return Array.isArray(queryKey) && 
               queryKey.length > 0 && 
               typeof queryKey[0] === 'string' &&
               queryKey[0] === `${pluralObjectType}-infinite`;
      }
    },
    (oldData: any) => {
      if (!oldData || !oldData.pages) return oldData;
      
      return {
        ...oldData,
        pages: oldData.pages.map((page: any) => {
          if (!page.meta?.facets?.tags?.rows) return page;
          
          return {
            ...page,
            meta: {
              ...page.meta,
              facets: {
                ...page.meta.facets,
                tags: {
                  ...page.meta.facets.tags,
                  rows: page.meta.facets.tags.rows.map((row: any) => {
                    if (row.value === tagName) {
                      return {
                        ...row,
                        total: Math.max(0, row.total + increment)
                      };
                    }
                    return row;
                  }),
                  total: Math.max(0, page.meta.facets.tags.total + increment)
                }
              }
            }
          };
        })
      };
    }
  );
}

// Create a new tag
export function useCreateTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      name,
      color,
      objectType,
    }: {
      name: string;
      color: string;
      objectType: TaggableObjectType;
    }) => {
      const response = await fetch("/api/tags", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, color, objectType }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create tag");
      }

      return response.json();
    },
    onMutate: async ({ name, color, objectType }) => {
      // Cancel any outgoing refetches for tags
      await queryClient.cancelQueries({ queryKey: ["tags", objectType] });

      // Snapshot the previous value
      const previousTags = queryClient.getQueryData<TagWithCreator[]>(["tags", objectType]);

              // Optimistically update tags list with new tag
        if (previousTags) {
          const optimisticTag: TagWithCreator = {
            id: `temp-${Date.now()}`, // Temporary ID
            name,
            color,
            organizationId: '', // Will be filled by server
            objectType,
            createdBy: '', // Will be filled by server
            createdAt: new Date(),
            updatedAt: new Date(),
            creator: {
              id: '',
              name: 'You',
              image: null,
            }
          };
        
        queryClient.setQueryData<TagWithCreator[]>(["tags", objectType], [
          ...previousTags,
          optimisticTag
        ]);

        // Optimistically add the new tag to facets with count 0
        const pluralObjectType = getObjectTypePlural(objectType);
        queryClient.setQueriesData(
          { 
            predicate: (query: any) => {
              const queryKey = query.queryKey;
              return Array.isArray(queryKey) && 
                     queryKey.length > 0 && 
                     typeof queryKey[0] === 'string' &&
                     queryKey[0] === `${pluralObjectType}-infinite`;
            }
          },
          (oldData: any) => {
            if (!oldData || !oldData.pages) return oldData;
            
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => {
                if (!page.meta?.facets) return page;
                
                const currentFacets = page.meta.facets.tags || { rows: [], total: 0 };
                
                return {
                  ...page,
                  meta: {
                    ...page.meta,
                    facets: {
                      ...page.meta.facets,
                      tags: {
                        ...currentFacets,
                        rows: [
                          ...currentFacets.rows,
                          { value: name, total: 0 }
                        ]
                      }
                    }
                  }
                };
              })
            };
          }
        );
      }

      // Return a context object with the snapshotted value
      return { previousTags };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousTags) {
        queryClient.setQueryData(["tags", variables.objectType], context.previousTags);
      }
    },
    onSuccess: (newTag, { objectType }) => {
      // Invalidate tags list for this object type so it appears in other components
      queryClient.invalidateQueries({ queryKey: ["tags", objectType] });
      
      // Delayed invalidation to allow server to process the change first
      // This prevents the flickering by giving the server time to update before refetching
      setTimeout(() => {
        invalidateInfiniteQueries(queryClient, objectType);
      }, 1000); // 1 second delay
    },
  });
}

// Update a tag
export function useUpdateTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      color,
    }: {
      id: string;
      color: string;
    }) => {
      const response = await fetch(`/api/tags/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ color }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update tag");
      }

      return response.json();
    },
    onMutate: async ({ id, color }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["tags"] });
      await queryClient.cancelQueries({ queryKey: ["objectTags"] });

      // Snapshot the previous value
      const previousTags = queryClient.getQueryData<TagWithCreator[]>(["tags"]);
      const previousObjectTags = queryClient.getQueryData<ObjectTagWithDetails[]>(["objectTags"]);

      // Optimistically update tags
      if (previousTags) {
        queryClient.setQueryData<TagWithCreator[]>(["tags"], old => {
          if (!old) return old;
          return old.map(tag => 
            tag.id === id ? { ...tag, color } : tag
          );
        });
      }

      // Optimistically update object tags
      if (previousObjectTags) {
        queryClient.setQueryData<ObjectTagWithDetails[]>(["objectTags"], old => {
          if (!old) return old;
          return old.map(objectTag => 
            objectTag.tag.id === id 
              ? { ...objectTag, tag: { ...objectTag.tag, color } }
              : objectTag
          );
        });
      }

      // Return a context object with the snapshotted value
      return { previousTags, previousObjectTags };
    },
    onError: (err, newTodo, context) => {
      // Rollback on error
      if (context?.previousTags) {
        queryClient.setQueryData(["tags"], context.previousTags);
      }
      if (context?.previousObjectTags) {
        queryClient.setQueryData(["objectTags"], context.previousObjectTags);
      }
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we're up to date
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      queryClient.invalidateQueries({ queryKey: ["objectTags"] });
    },
  });
}

// Add a tag to an object
export function useAddTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      tagId,
      objectId,
      objectType,
    }: {
      tagId: string;
      objectId: string;
      objectType: TaggableObjectType;
    }) => {
      const response = await fetch(`/api/tags/${tagId}/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ objectId, objectType }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to add tag");
      }

      return response.json();
    },
        onMutate: async ({ tagId, objectId, objectType }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["objectTags", objectId] });

      // Snapshot the previous value
      const previousObjectTags = queryClient.getQueryData<ObjectTagWithDetails[]>(["objectTags", objectId]);

      // Get the tag details from the tags query to add optimistically
      const tagsQuery = queryClient.getQueryData<TagWithCreator[]>(["tags", objectType]);
      const tagToAdd = tagsQuery?.find(tag => tag.id === tagId);

      if (previousObjectTags && tagToAdd) {
        const optimisticObjectTag: ObjectTagWithDetails = {
          id: `temp-${Date.now()}`,
          tagId,
          objectId,
          objectType,
          createdAt: new Date(),
          addedBy: '', // Will be filled by server
          tag: tagToAdd,
          adder: {
            id: '',
            name: 'You',
            image: null,
          }
        };

        queryClient.setQueryData<ObjectTagWithDetails[]>(["objectTags", objectId], [
          ...previousObjectTags,
          optimisticObjectTag
        ]);

        // Optimistically update facets count for this tag (+1)
        updateFacetsOptimistically(queryClient, objectType, tagToAdd.name, 1);
      }

      return { previousObjectTags, tagToAdd };
    },
    onError: (err, { objectId, objectType }, context) => {
      // Rollback on error
      if (context?.previousObjectTags) {
        queryClient.setQueryData(["objectTags", objectId], context.previousObjectTags);
      }
      
      // Rollback facets count if we had optimistically updated it
      if (context?.tagToAdd) {
        updateFacetsOptimistically(queryClient, objectType, context.tagToAdd.name, -1);
      }
    },
    onSuccess: (_, { objectId, objectType }) => {
      // Invalidate object tags for this specific object
      queryClient.invalidateQueries({ queryKey: ["objectTags", objectId] });
      
      // Delayed invalidation to allow server to process the change first
      // This prevents the flickering by giving the server time to update before refetching
      setTimeout(() => {
        invalidateInfiniteQueries(queryClient, objectType);
      }, 1000); // 1 second delay
    },
  });
}

// Remove a tag from an object
export function useRemoveTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      tagId,
      objectId,
      objectType,
    }: {
      tagId: string;
      objectId: string;
      objectType?: TaggableObjectType;
    }) => {
      const response = await fetch(`/api/tags/${tagId}/remove/${objectId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to remove tag");
      }

      return response.json();
    },
    onMutate: async ({ tagId, objectId, objectType }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["objectTags", objectId] });

      // Snapshot the previous value
      const previousObjectTags = queryClient.getQueryData<ObjectTagWithDetails[]>(["objectTags", objectId]);

      // Find the tag being removed to get its name for facets update
      const tagToRemove = previousObjectTags?.find(objectTag => objectTag.tagId === tagId);

      // Optimistically remove the tag
      if (previousObjectTags) {
        queryClient.setQueryData<ObjectTagWithDetails[]>(["objectTags", objectId], 
          previousObjectTags.filter(objectTag => objectTag.tagId !== tagId)
        );

        // Optimistically update facets count for this tag (-1)
        if (tagToRemove && objectType) {
          updateFacetsOptimistically(queryClient, objectType, tagToRemove.tag.name, -1);
        }
      }

      return { previousObjectTags, tagToRemove };
    },
    onError: (err, { objectId, objectType }, context) => {
      // Rollback on error
      if (context?.previousObjectTags) {
        queryClient.setQueryData(["objectTags", objectId], context.previousObjectTags);
      }
      
      // Rollback facets count if we had optimistically updated it
      if (context?.tagToRemove && objectType) {
        updateFacetsOptimistically(queryClient, objectType, context.tagToRemove.tag.name, 1);
      }
    },
    onSuccess: (_, { objectId, objectType }) => {
      // Invalidate object tags for this specific object
      queryClient.invalidateQueries({ queryKey: ["objectTags", objectId] });
      
      // Delayed invalidation to allow server to process the change first
      // This prevents the flickering by giving the server time to update before refetching
      setTimeout(() => {
        if (objectType) {
          invalidateInfiniteQueries(queryClient, objectType);
        }
      }, 1000); // 1 second delay
    },
  });
}

// Delete a tag
export function useDeleteTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tagId: string) => {
      const response = await fetch(`/api/tags/${tagId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete tag");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      queryClient.invalidateQueries({ queryKey: ["allTags"] });
    },
  });
}

// Get all tags across all object types for management interface
export function useAllTags() {
  return useQuery({
    queryKey: ["allTags"],
    queryFn: async () => {
      const response = await fetch("/api/tags");
      if (!response.ok) throw new Error("Failed to fetch all tags");
      return response.json();
    },
  });
}

// Get organization members for permission assignment
export function useOrganizationMembers() {
  return useQuery({
    queryKey: ["organizationMembers"],
    queryFn: async () => {
      const response = await fetch("/api/tags/members");
      if (!response.ok) throw new Error("Failed to fetch organization members");
      return response.json();
    },
  });
}

// Get tag permissions
export function useTagPermissions(tagId: string) {
  return useQuery({
    queryKey: ["tagPermissions", tagId],
    queryFn: async () => {
      const response = await fetch(`/api/tags/${tagId}/permissions`);
      if (!response.ok) throw new Error("Failed to fetch tag permissions");
      return response.json();
    },
    enabled: !!tagId,
  });
}

// Add tag permission
export function useAddTagPermission() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      tagId,
      userId,
      role,
    }: {
      tagId: string;
      userId: string;
      role: "viewer" | "editor" | "admin";
    }) => {
      const response = await fetch(`/api/tags/${tagId}/permissions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, role }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to add tag permission");
      }

      return response.json();
    },
    onSuccess: (_, { tagId }) => {
      queryClient.invalidateQueries({ queryKey: ["tagPermissions", tagId] });
      queryClient.invalidateQueries({ queryKey: ["allTags"] });
    },
  });
}

// Remove tag permission
export function useRemoveTagPermission() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      tagId,
      userId,
    }: {
      tagId: string;
      userId: string;
    }) => {
      const response = await fetch(`/api/tags/${tagId}/permissions/${userId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to remove tag permission");
      }

      return response.json();
    },
    onSuccess: (_, { tagId }) => {
      queryClient.invalidateQueries({ queryKey: ["tagPermissions", tagId] });
      queryClient.invalidateQueries({ queryKey: ["allTags"] });
    },
  });
} 