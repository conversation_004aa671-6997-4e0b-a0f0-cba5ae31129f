import * as React from "react";

// Function overload for debouncing values
export function useDebounce<T>(value: T, delay?: number): T;
// Function overload for debouncing functions
export function useDebounce<T extends (...args: any[]) => any>(
	func: T,
	delay?: number,
): (...args: Parameters<T>) => void;

// Implementation
export function useDebounce<T>(
	valueOrFunc: T | ((...args: any[]) => any),
	delay = 500,
) {
	// If it's a function, return a debounced version
	if (typeof valueOrFunc === "function") {
		const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

		return React.useCallback(
			(...args: any[]) => {
				if (timeoutRef.current) {
					clearTimeout(timeoutRef.current);
				}

				timeoutRef.current = setTimeout(() => {
					(valueOrFunc as Function)(...args);
				}, delay);
			},
			[valueOrFunc, delay],
		);
	}

	// If it's a value, debounce the value updates
	const [debouncedValue, setDebouncedValue] = React.useState<T>(valueOrFunc);

	React.useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedValue(valueOrFunc);
		}, delay);

		return () => {
			clearTimeout(timer);
		};
	}, [valueOrFunc, delay]);

	return debouncedValue;
}
