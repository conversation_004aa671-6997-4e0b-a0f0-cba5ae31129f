import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useHelp } from "@app/shared/lib/help-context";
import { getPlatformShortcuts } from "@app/shared/lib/utils";
import {
	type Icon,
	IconBookmark,
	IconBuildingSkyscraper,
	IconCreditCard,
	IconFile,
	IconHelp,
	IconLayoutDashboard,
	IconLifebuoy,
	IconMoon,
	IconSettings,
	IconSun,
	IconUserCircle,
} from "@tabler/icons-react";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";

export interface CommandItem {
	icon: Icon;
	label: string;
	shortcut?: string;
	href?: string;
	isExternal?: boolean;
	action?: () => void;
	buttonText?: string;
}

export interface CommandGroup {
	heading: string;
	items: CommandItem[];
}

export function useCommandGroups(): CommandGroup[] {
	const { openHelp } = useHelp();
	const { setTheme } = useTheme();
	const t = useTranslations("app.command");
	const { activeOrganization } = useActiveOrganization();

	return [
		{
			heading: t("actions"),
			items: [
				{
					icon: IconLayoutDashboard,
					label: t("goToDashboard"),
					href: "/app",
					buttonText: t("button.open"),
				},
				{
					icon: IconSettings,
					label: t("openSettings"),
					href: `/app/${activeOrganization?.slug}/settings/account`,
					buttonText: t("button.open"),
				},
				{
					icon: IconSun,
					label: t("useLightTheme"),
					action: () => setTheme("light"),
					buttonText: t("button.enableTheme"),
				},
				{
					icon: IconMoon,
					label: t("useDarkTheme"),
					action: () => setTheme("dark"),
					buttonText: t("button.enableTheme"),
				},
				{
					icon: IconBookmark,
					label: t("addToBookmarks"),
					shortcut: "⌘D",
					buttonText: t("button.addToBookmarks"),
					action: () => {
						if (typeof window !== "undefined") {
							const { shortcutSymbol } = getPlatformShortcuts();
							alert(
								`Press ${shortcutSymbol}D to bookmark this page.`,
							);
						}
					},
				},
			],
		},
		{
			heading: "Resources",
			items: [
				{
					icon: IconUserCircle,
					label: t("accountSettings"),
					href: `/app/${activeOrganization?.slug}/settings/account`,
					buttonText: t("button.open"),
				},
				{
					icon: IconBuildingSkyscraper,
					label: t("organizationSettings"),
					href: `/app/${activeOrganization?.slug}/settings/organization`,
					buttonText: t("button.open"),
				},
				{
					icon: IconCreditCard,
					label: t("billing"),
					href: `/app/${activeOrganization?.slug}/settings/organization/billing`,
					buttonText: t("button.open"),
				},
			],
		},
		{
			heading: "Help & Support",
			items: [
				{
					icon: IconHelp,
					label: t("openHelpDrawer"),
					shortcut: "⌘H",
					action: openHelp,
					buttonText: t("button.open"),
				},
				{
					icon: IconFile,
					label: t("viewHelp"),
					href: "https://reliocrm.com/help",
					isExternal: true,
					buttonText: t("button.openNewTab"),
				},
				{
					icon: IconLifebuoy,
					label: t("support"),
					href: "https://discord.com/invite/nk6C2qTeCq",
					isExternal: true,
					buttonText: t("button.openNewTab"),
				},
			],
		},
	];
}
