"use client";

import { useCallback, useEffect, useState } from "react";

export const setLocalStorage = (key: string, value: string): void => {
	if (typeof window !== "undefined") {
		localStorage.setItem(key, value);
	}
};

export const getLocalStorage = (key: string): string | null => {
	if (typeof window !== "undefined") {
		return localStorage.getItem(key);
	}
	return null;
};

export const removeLocalStorage = (key: string) => {
	if (typeof window !== "undefined") {
		localStorage.removeItem(key);
	}
};

function getItemFromLocalStorage(key: string) {
	const item = window?.localStorage.getItem(key);
	if (item) {
		try {
			return JSON.parse(item);
		} catch (error) {
			console.warn(`Failed to parse localStorage item "${key}":`, error);
			// Remove corrupted item
			window?.localStorage.removeItem(key);
			return null;
		}
	}

	return null;
}

export function useLocalStorage<T>(
	key: string,
	initialValue: T,
): [T, React.Dispatch<React.SetStateAction<T>>] {
	// Initialize state with localStorage value or fallback to initialValue
	const [storedValue, setStoredValue] = useState<T>(() => {
		// During SSR, always use initialValue
		if (typeof window === "undefined") {
			return initialValue;
		}
		
		try {
			const stored = getItemFromLocalStorage(key);
			return stored !== null ? stored : initialValue;
		} catch (error) {
			console.warn(`Error reading localStorage key "${key}":`, error);
			return initialValue;
		}
	});

	const setValue: React.Dispatch<React.SetStateAction<T>> = useCallback(
		(value) => {
			try {
				// Allow value to be a function so we have the same API as useState
				const valueToStore = value instanceof Function ? value(storedValue) : value;
				
				// Save state
				setStoredValue(valueToStore);
				
				// Save to localStorage
				if (typeof window !== "undefined") {
					window.localStorage.setItem(key, JSON.stringify(valueToStore));
				}
			} catch (error) {
				console.warn(`Error setting localStorage key "${key}":`, error);
			}
		},
		[key, storedValue],
	);

	return [storedValue, setValue];
}
