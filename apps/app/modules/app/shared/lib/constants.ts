import {
	IconAlertSquareRoundedFilled,
	IconAntennaBars1,
	IconAntennaBars2,
	IconAntennaBars3,
	IconAntennaBars5,
	IconBrandAsana,
	IconCircle,
	IconCircleDotted,
	IconCircleHalf2,
	IconLock,
	IconSquareRounded<PERSON>heck,
	IconWorld,
} from "@tabler/icons-react";

export const STORAGE_PREFIX = "relio_";
export const DATA_TABLE_PREFIX = "data_table_";

export const STORAGE_KEYS: Record<string, string> = {
	TASKS_VIEW: `${STORAGE_PREFIX}tasks_view`,
	TASKS_PREFERENCES: `${STORAGE_PREFIX}tasks_preferences`,
	CALENDAR_VIEW: `${STORAGE_PREFIX}calendar_view`,
	SIDEBAR_STATE: `${STORAGE_PREFIX}sidebar_state`,
	NOTES_VIEW: `${STORAGE_PREFIX}notes_view`,
	NOTES_PREFERENCES: `${STORAGE_PREFIX}notes_preferences`,
	DATA_TABLE_COLUMN_ORDER: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}column_order`,
	DATA_TABLE_COLUMN_VISIBILITY: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}column_visibility`,
	DATA_TABLE_COLUMN_SIZING: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}column_sizing`,
	DATA_TABLE_COLUMN_AGGREGATIONS: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}column_aggregations`,
	DATA_TABLE_COLUMN_FILTERS: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}column_filters`,
	DATA_TABLE_COLUMN_SORTING: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}column_sorting`,
	DATA_TABLE_COMMAND: `${STORAGE_PREFIX}${DATA_TABLE_PREFIX}command`,
};

export const TASK_PRIORITY = [
	{
		value: "urgent",
		label: "Urgent",
		icon: IconAlertSquareRoundedFilled,
		color: "text-red-500",
	},
	{
		value: "high",
		label: "High",
		icon: IconAntennaBars5,
		color: "text-zinc-500",
	},
	{
		value: "medium",
		label: "Medium",
		icon: IconAntennaBars3,
		color: "text-zinc-500",
	},
	{
		value: "low",
		label: "Low",
		icon: IconAntennaBars2,
		color: "text-zinc-500",
	},
	{
		value: "no_priority",
		label: "No priority",
		icon: IconAntennaBars1,
		color: "text-zinc-500",
	},
] as const;

export const TASK_STATUS = [
	{
		label: "Backlog",
		value: "backlog",
		icon: IconCircleDotted,
		color: "text-zinc-500",
	},
	{
		label: "Todo",
		value: "todo",
		icon: IconCircle,
		color: "text-zinc-500",
	},
	{
		label: "In Progress",
		value: "in_progress",
		icon: IconCircleHalf2,
		color: "text-yellow-500",
	},
	{
		label: "Review",
		value: "review",
		icon: IconBrandAsana,
		color: "text-orange-500",
	},
	{
		label: "Done",
		value: "done",
		icon: IconSquareRoundedCheck,
		color: "text-blue-400",
	},
] as const;

export const NOTE_STATUS = [
	{
		label: "Public",
		value: "public",
		icon: IconWorld,
		color: "text-zinc-500",
	},
	{
		label: "Private",
		value: "private",
		icon: IconLock,
		color: "text-zinc-500",
	},
] as const;

export type TaskStatus = (typeof TASK_STATUS)[number]["value"];
export type TaskPriority = (typeof TASK_PRIORITY)[number]["value"];
export type NoteStatus = (typeof NOTE_STATUS)[number]["value"];

export interface TaskPreferences {
	view: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	showCompleted?: boolean;
	columnVisibility?: Record<string, boolean>;
	statusColumnVisibility?: Record<TaskStatus, boolean>;
	groupBy?: string;
	filters?: {
		status?: string[];
		priority?: string[];
		assignee?: string[];
		title?: string;
	};
}

export interface NotePreferences {
	view: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	groupBy?: string;
	showFavorites?: boolean;
}

export const FILTER_TYPES: string[] = [
	"Today",
	"Yesterday",
	"This Week",
	"This Month",
	"This Quarter",
	"This Year",
	"Last 7 Days",
	"Last 30 Days",
	"Last 6 Months",
	"Last 12 Months",
	"All Time",
];
export type FilterType = (typeof FILTER_TYPES)[number];

export const CALL_RESULTS: { label: string; value: string }[] = [
	{
		label: "All",
		value: "all",
	},
	{
		label: "Reached",
		value: "reached",
	},
	{
		label: "Left Message",
		value: "left_message",
	},
	{
		label: "No Answer",
		value: "no_answer",
	},
	{
		label: "Busy",
		value: "busy",
	},
	{
		label: "Wrong Number",
		value: "wrong_number",
	},
	{
		label: "Call Back",
		value: "call_back",
	},
	{
		label: "Not Available",
		value: "not_available",
	},
	{
		label: "Disconnected",
		value: "disconnected",
	},
	{
		label: "Do Not Call",
		value: "do_not_call",
	},
] as const;
export type CallResult = (typeof CALL_RESULTS)[number];

export const CREDIT_PACKAGES = [
		{
			id: "starter",
			name: "Starter Pack",
			credits: 500,
			price: 9.99,
			priceId:
				process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER ||
				"price_ai_credits_starter",
			description: "Perfect for light usage",
			features: [
				"500 AI credits",
				"Valid for 12 months",
				"All AI features included",
				"Email support",
			],
			popular: false,
		},
		{
			id: "professional",
			name: "Professional Pack",
			credits: 2000,
			price: 29.99,
			priceId:
				process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL ||
				"price_ai_credits_professional", // Replace with your actual Stripe price ID
			description: "Great for regular users",
			features: [
				"2,000 AI credits",
				"Valid for 12 months",
				"All AI features included",
				"Priority email support",
				"Advanced analytics",
			],
			popular: true,
			savings: "Save 25%",
		},
		{
			id: "enterprise",
			name: "Enterprise Pack",
			credits: 5000,
			price: 59.99,
			priceId:
				process.env.NEXT_PUBLIC_STRIPE_PRICE_ENTERPRISE ||
				"price_ai_credits_enterprise", // Replace with your actual Stripe price ID
			description: "For power users and teams",
			features: [
				"5,000 AI credits",
				"Valid for 12 months",
				"All AI features included",
				"Priority support",
				"Advanced analytics",
				"Custom integrations",
			],
			popular: false,
			savings: "Save 40%",
		},
	] as const;
export type CreditPackage = (typeof CREDIT_PACKAGES)[number];

export const CONTACT_STATUS: { label: string; value: string }[] = [
	{
		label: "None",
		value: "none",
	},
	{
		label: "New",
		value: "new",
	},
	{
		label: "Active",
		value: "active",
	},
	{
		label: "Inactive",
		value: "inactive",
	},
	{
		label: "1031 Exchange",
		value: "1031_exchange",
	},
	{
		label: "Needs Research",
		value: "needs_research",
	},
	{
		label: "Client",
		value: "client",
	},
] as const;
export type ContactStatus = (typeof CONTACT_STATUS)[number]["value"];

export const COMPANY_STATUS: { label: string; value: string }[] = [
	// Active Statuses
	{
		label: "Active",
		value: "active",
	},
	{
		label: "Operating",
		value: "operating",
	},
	{
		label: "Public",
		value: "public",
	},
	{
		label: "Private",
		value: "private",
	},

	// Inactive/Closed Statuses
	{
		label: "Closed",
		value: "closed",
	},
	{
		label: "Defunct",
		value: "defunct",
	},
	{
		label: "Dissolved",
		value: "dissolved",
	},
	{
		label: "Bankrupt",
		value: "bankrupt",
	},
	{
		label: "Out of Business",
		value: "out_of_business",
	},

	// Transaction-Related Statuses
	{
		label: "Acquired",
		value: "acquired",
	},
	{
		label: "Merged",
		value: "merged",
	},
	{
		label: "Sold",
		value: "sold",
	},

	// Special Statuses
	{
		label: "Subsidiary",
		value: "subsidiary",
	},
	{
		label: "Dormant",
		value: "dormant",
	},
	{
		label: "Under Investigation",
		value: "under_investigation",
	},
	{
		label: "Restructuring",
		value: "restructuring",
	},
] as const;
export type CompanyStatus = (typeof COMPANY_STATUS)[number]["value"];

export const PROPERTY_STATUS: { label: string; value: string }[] = [
  {
      label: "For Sale",
      value: "for-sale"
  },
  {
      label: "In Escrow",
      value: "in-escrow"
  },
  {
      label: "Sold",
      value: "sold"
  },
  {
      label: "Cancelled",
      value: "cancelled"
  },
  {
      label: "Proposal",
      value: "proposal"
  },
  {
      label: "Vacant",
      value: "vacant"
  },
  {
      label: "Occupied",
      value: "occupied"
  },
  {
      label: "None",
      value: "none"
  }
] as const;
export type PropertyStatus = (typeof PROPERTY_STATUS)[number]["value"];

export const CUSTOM_OBJECT_STATUS: { label: string; value: string }[] = [
	{
		label: "Active",
		value: "active",
	},
	{
		label: "Inactive",
		value: "inactive",
	},
	{
		label: "Closed",
		value: "closed",
	},
] as const;
export type CustomObjectStatus = (typeof CUSTOM_OBJECT_STATUS)[number]["value"];

export const CONTACT_STAGE: { label: string; value: string }[] = [
	{ label: "None", value: "none" },
	{ label: "Lead", value: "lead" },
	{ label: "Hot Prospect", value: "hot_prospect" },
	{ label: "Nurture", value: "nurture" },
	{ label: "Active Client", value: "active_client" },
	{ label: "Unresponsive", value: "unresponsive" },
	{ label: "Pending", value: "pending" },
	{ label: "Closed", value: "closed" },
	{ label: "Past Client", value: "past_client" },
	{ label: "Trash", value: "trash" },
	{ label: "Lost", value: "lost" },
] as const;
export type ContactStage = (typeof CONTACT_STAGE)[number]["value"];

export const COMPANY_STAGE: { label: string; value: string }[] = [
	{ label: "None", value: "none" },
	{ label: "Lead", value: "lead" },
	{ label: "Hot Prospect", value: "hot_prospect" },
	{ label: "Nurture", value: "nurture" },
	{ label: "Active Client", value: "active_client" },
	{ label: "Unresponsive", value: "unresponsive" },
	{ label: "Pending", value: "pending" },
] as const;

export const PROPERTY_STAGE: { label: string; value: string }[] = [
	{ label: "None", value: "none" },
	{ label: "Lead", value: "lead" },
	{ label: "Hot Prospect", value: "hot_prospect" },
	{ label: "Nurture", value: "nurture" },
	{ label: "Active Client", value: "active_client" },
	{ label: "Unresponsive", value: "unresponsive" },
	{ label: "Pending", value: "pending" },
] as const;

export const CUSTOM_OBJECT_STAGE: { label: string; value: string }[] = [
	{ label: "None", value: "none" },
	{ label: "Lead", value: "lead" },
	{ label: "Hot Prospect", value: "hot_prospect" },
	{ label: "Nurture", value: "nurture" },
	{ label: "Active Client", value: "active_client" },
	{ label: "Unresponsive", value: "unresponsive" },
	{ label: "Pending", value: "pending" },
] as const;

export const CONTACT_PERSONA: { label: string; value: string }[] = [
	{ label: "None", value: "none" },
	{ label: "Attorney", value: "attorney" },
	{ label: "Broker", value: "broker" },
	{ label: "Lender", value: "lender" },
	{ label: "Manager", value: "manager" },
	{ label: "Principal", value: "principal" },
	{ label: "Title Company", value: "title_company" },
	{ label: "Client", value: "client" },
	{ label: "Vendor", value: "vendor" },
	{ label: "Other", value: "other" },
] as const;
export type Persona = (typeof CONTACT_PERSONA)[number];
