import { ObjectType } from "@repo/database";
import type { Icon } from "@tabler/icons-react";

export interface FavoriteFolder {
	id: string;
	name: string;
	isOpen?: boolean;
	position?: number;
	favorites: Array<{
		id: string;
		objectId: string;
		title?: string;
		folderId?: string;
		position?: number;
		organizationId: string;
		userId: string;
		createdAt: Date;
	}>;
}

export interface FavoriteItem {
	id: string;
	name: string;
	icon?: Icon | null;
	noteIcon?: string | null;
	isActive?: boolean;
	folderId?: string;
	position?: number;
	objectId: string;
	objectType?: ObjectType;
	organizationId: string;
	contactData?: any;
	viewData?: any;
}

export interface DroppableProps {
	id: string;
	children: React.ReactNode;
	isFolder?: boolean;
}
