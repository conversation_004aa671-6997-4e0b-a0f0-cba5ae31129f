export function pickUpdatableFields(obj: any) {
	const result: {
		name?: string;
		isOpen?: boolean | null;
		position?: number | null;
	} = {};
	if (typeof obj.name === "string") result.name = obj.name;
	if (typeof obj.isOpen === "boolean" || obj.isOpen === null)
		result.isOpen = obj.isOpen;
	if (typeof obj.position === "number" || obj.position === null)
		result.position = obj.position;
	return result;
}
