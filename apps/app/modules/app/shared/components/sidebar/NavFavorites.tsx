"use client";

import { fetchContacts } from "@app/contacts/lib/api";
import {
	fetchFavoriteFolders,
	fetchFavorites,
	useCreateFavorite,
	useCreateFavoriteFolder,
	useDeleteFavorite,
	useDeleteFavoriteFolder,
	useToggleFavorite,
	useUpdateFavorite,
	useUpdateFavoriteFolder,
} from "@app/favorites/lib/api";
import { fetchNotes } from "@app/notes/lib/api";
import { fetchObjectViews } from "@app/object-views/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { fetchProperties } from "@app/properties/lib/api";
import { fetchTasks } from "@app/tasks/lib/api";
import {
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import { SortableContext } from "@dnd-kit/sortable";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@radix-ui/react-collapsible";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	type Icon,
	IconBuilding,
	IconChevronDown,
	IconDotsVertical,
	IconFolder,
	IconFolderOpen,
	IconFolderPlus,
	IconHome,
	IconNotebook,
	IconPencil,
	IconSquareRoundedCheck,
	IconStar,
	IconTable,
	IconTrash,
	IconUser,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import {
	SidebarGroup,
	SidebarGroupLabel,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@ui/components/sidebar";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import React, { useState } from "react";
import { DraggableFolder } from "./nav-favorites/DraggableFolder";
import { DraggableItem } from "./nav-favorites/DraggableItem";
import { Droppable } from "./nav-favorites/Droppable";
import { FavoriteItemContent } from "./nav-favorites/FavoriteItemContent";
import { pickUpdatableFields } from "./nav-favorites/helpers";
import type { FavoriteFolder, FavoriteItem } from "./nav-favorites/types";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";

// Add skeleton components
const FolderSkeleton = ({ isCollapsed }: { isCollapsed: boolean }) => {
	if (isCollapsed) {
		return (
			<SidebarMenuItem className="flex justify-center w-full p-0">
				<SidebarMenuButton className="transition-colors duration-200 flex items-center justify-center h-8 p-0 m-0">
					<Skeleton className="size-4" />
				</SidebarMenuButton>
			</SidebarMenuItem>
		);
	}

	return (
		<div className="!w-full relative rounded-md">
			<div className="hover:bg-muted/50 !border !border-transparent h-8 transition-colors duration-200 w-full flex items-center px-1 rounded-md">
				<div className="flex items-center flex-1 gap-1">
					<Skeleton className="h-4 w-4" />
					<Skeleton className="h-4 w-20" />
				</div>
				<Skeleton className="h-4 w-4" />
			</div>
		</div>
	);
};

const FavoriteItemSkeleton = ({ isCollapsed }: { isCollapsed: boolean }) => {
	if (isCollapsed) {
		return (
			<SidebarMenuItem className="flex justify-center">
				<SidebarMenuButton className="transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0">
					<Skeleton className="size-4" />
				</SidebarMenuButton>
			</SidebarMenuItem>
		);
	}

	return (
		<SidebarMenuItem>
			<SidebarMenuButton className="gap-1">
				<Skeleton className="h-4 w-4 rounded-md" />
				<Skeleton className="h-4 w-24" />
			</SidebarMenuButton>
		</SidebarMenuItem>
	);
};

export function NavFavorites() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const organizationId = activeOrganization?.id;

	const {
		data: favorites = [],
		isLoading: isLoadingFavorites,
		error: errorFavorites,
	} = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const {
		data: folders = [],
		isLoading: isLoadingFolders,
		error: errorFolders,
	} = useQuery({
		queryKey: ["favoriteFolders", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavoriteFolders(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const { data: tasks = [], error: errorTasks } = useQuery({
		queryKey: ["tasks", organizationId],
		queryFn: () =>
			organizationId ? fetchTasks(organizationId) : Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const { data: notes = [], error: errorNotes } = useQuery({
		queryKey: ["notes", organizationId],
		queryFn: () => organizationId ? fetchNotes(organizationId) : Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const { data: contacts = [], error: errorContacts } = useQuery({
		queryKey: ["contacts", organizationId],
		queryFn: async () => {
			if (!organizationId) return [];

			try {
				const result = await fetchContacts(organizationId);
				return result;
			} catch (error) {
				console.error("Contacts API error:", error);
				throw error;
			}
		},
		enabled: !!organizationId && loaded,
	});

	const { data: properties = [], error: errorProperties, isLoading: isLoadingProperties } = useQuery({
		queryKey: ["properties", organizationId],
		queryFn: async () => {
			if (!organizationId) return [];

			try {
				const result = await fetchProperties(organizationId);
				return result;
			} catch (error) {
				console.error("Properties API error:", error);
				throw error;
			}
		},
		enabled: !!organizationId && loaded,
	});

	const { data: contactViews = [], error: errorContactViews } = useQuery({
		queryKey: ["objectViews", organizationId, "contacts"],
		queryFn: () =>
			organizationId
				? fetchObjectViews(organizationId, "contact")
				: Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const { data: companyViews = [], error: errorCompanyViews } = useQuery({
		queryKey: ["objectViews", organizationId, "companies"],
		queryFn: () =>
			organizationId
				? fetchObjectViews(organizationId, "company")
				: Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const { data: propertyViews = [], error: errorPropertyViews } = useQuery({
		queryKey: ["objectViews", organizationId, "properties"],
		queryFn: () =>
			organizationId
				? fetchObjectViews(organizationId, "property")
				: Promise.resolve([]),
		enabled: !!organizationId && loaded,
	});

	const taskTitleMap = React.useMemo(() => {
		const map = new Map<string, string>();
		if (Array.isArray(tasks)) {
			for (const task of tasks) {
				if (!task) continue;

				const taskId = task.id || (task as any)._id;
				if (taskId) {
					map.set(taskId, task.title);
				}
			}
		}
		return map;
	}, [tasks]);

	const notesTitleMap = React.useMemo(() => {
		const map = new Map<string, string>();
		if (Array.isArray(notes)) {
			for (const note of notes) {
				if (!note) continue;
				
				const noteId = note.id || (note as any)._id;
				if (noteId) {
					map.set(noteId, note.title || "Untitled");
				}
			}
		}
		return map;
	}, [notes]);

	const contactDataMap = React.useMemo(() => {
		const map = new Map<string, any>();
		if (Array.isArray(contacts)) {
			for (const contact of contacts) {
				if (!contact) continue;
				
				const contactId = contact.id || (contact as any)._id;
				if (contactId) {
					map.set(contactId, contact);
				}
			}
		}
		return map;
	}, [contacts]);

	const propertyDataMap = React.useMemo(() => {
		const map = new Map<string, any>();
		if (Array.isArray(properties)) {
			for (const property of properties) {
				if (!property) continue;
				
				const propertyId = property.id || (property as any)._id;
				if (propertyId) {
					map.set(propertyId, property);
				}
			}
		}
		return map;
	}, [properties]);

	const viewsDataMap = React.useMemo(() => {
		const map = new Map<string, any>();
		const allViews = [
			...(Array.isArray(contactViews) ? contactViews : []),
			...(Array.isArray(companyViews) ? companyViews : []),
			...(Array.isArray(propertyViews) ? propertyViews : [])
		];
		
		allViews.forEach((view) => {
			// Ensure view exists and is not null/undefined
			if (!view) return;
			
			const viewId = view.id || (view as any)._id;
			if (viewId) {
				map.set(viewId, view);
			}
		});
		return map;
	}, [contactViews, companyViews, propertyViews]);

	const getDisplayName = (fav: any) => {
		if (!fav || !fav.objectId || !fav.objectType) {
			return "Invalid Favorite";
		}

		switch (fav.objectType) {
			case "note": {
				const name = notesTitleMap.get(fav.objectId);
				return name || "Untitled Note";
			}
			case "task": {
				const name = taskTitleMap.get(fav.objectId);
				return name || "Untitled Task";
			}
			case "contact": {
				const contact = contactDataMap.get(fav.objectId);

				return contact?.name || "Untitled Contact";
			}
			case "company":
			case "property": {
				const property = propertyDataMap.get(fav.objectId);
				return (
					property?.name ||
					property?.location?.address?.street ||
					property?.address?.street || 
					property?.title ||
					"Untitled Property"
				);
			}
			case "view": {
				const view = viewsDataMap.get(fav.objectId);
				return view?.name || "Untitled View";
			}
			default:
				return `Unknown (${fav.objectType})`;
		}
	};

	const getIcon = (item: FavoriteItem) => {
		if (item.objectType === "property") {
			return <PropertyAvatar name={item.name} className="h-4 w-4" />;
		}

		if (item.objectType === "task") {
			return <IconSquareRoundedCheck className="h-4 w-4" />;
		}

		if (item.objectType === "note") {
			return item.noteIcon ? (
				<span className="text-base">{item.noteIcon}</span>
			) : (
				<IconNotebook className="h-4 w-4" />
			);
		}

		if (item.objectType === "view") {
			return <IconTable className="h-4 w-4" />;
		}

		return <IconStar className="h-4 w-4" />;
	};

	const getNoteIcon = (fav: any) => {
		if (fav.objectType === "note" && Array.isArray(notes)) {
			const note = notes.find((n) => n && (n.id || (n as any)._id) === fav.objectId);
			return note?.icon;
		}
		return null;
	};

	const foldersWithId = (Array.isArray(folders) ? folders : [])
		.filter((f: any) => f).map((f: any) => ({ ...f, id: f.id || f._id }));

	const toggleFavorite = useToggleFavorite(organizationId);
	const createFolderMutation = useCreateFavoriteFolder(organizationId);
	const updateFolderMutation = useUpdateFavoriteFolder(organizationId);
	const deleteFolderMutation = useDeleteFavoriteFolder(organizationId);
	const updateFavoriteMutation = useUpdateFavorite(organizationId);
	const deleteFavoriteMutation = useDeleteFavorite(organizationId);

	// State
	const [hoveredItem, setHoveredItem] = useState<string | null>(null);
	const [hoveredFolder, setHoveredFolder] = useState<string | null>(null);
	const [isCreatingFolder, setIsCreatingFolder] = useState(false);
	const [newFolderName, setNewFolderName] = useState("");
	const [isFavoritesHovered, setIsFavoritesHovered] = useState(false);
	const [activeId, setActiveId] = useState<string | null>(null);
	const [editingFolder, setEditingFolder] = useState<{
		id: string;
		name: string;
	} | null>(null);
	const [draggedItem, setDraggedItem] = useState<any>(null);

	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
	);

	// Map API data to UI shape for favorites
	const mapFavoriteToItem = (fav: any) => {
		const contactData =
			fav.objectType === "contact"
				? contactDataMap.get(fav.objectId)
				: undefined;
		const propertyData =
			fav.objectType === "property"
				? propertyDataMap.get(fav.objectId)
				: undefined;
		const viewData =
			fav.objectType === "view"
				? viewsDataMap.get(fav.objectId)
				: undefined;

		return {
			id: fav.id,
			name: getDisplayName(fav),
			icon: getIcon(fav),
			noteIcon: getNoteIcon(fav),
			isActive: false,
			folderId: fav.folderId || undefined,
			position: fav.position || 0,
			objectId: fav.objectId,
			objectType: fav.objectType,
			organizationId: fav.organizationId,
			contactData,
			propertyData,
			viewData,
		};
	};

	const folderItems = new Map<string, any[]>();
	foldersWithId.forEach((folder) => {
		const items = favorites
			.filter((fav) => fav.folderId === folder.id)
			.map(mapFavoriteToItem);
		folderItems.set(folder.id, items);
	});

	const rootItems = favorites
		.filter((fav) => !fav.folderId)
		.map(mapFavoriteToItem);

	const sidebar = useSidebar();
	const isCollapsed = sidebar?.state === "collapsed";

	const handleCreateFolder = async () => {
		if (!isCreatingFolder) {
			setIsCreatingFolder(true);
			return;
		}
		if (newFolderName.trim() && organizationId) {
			await createFolderMutation.mutateAsync({
				name: newFolderName,
				organizationId,
			});
			setNewFolderName("");
			setIsCreatingFolder(false);
		}
	};

	const handleFolderClick = async (folder: any) => {
		if (!organizationId) return;
		await updateFolderMutation.mutateAsync({
			id: folder.id,
			...pickUpdatableFields({ isOpen: !folder.isOpen }),
		});
	};

	const handleRenameFolder = async (folderId: string, name: string) => {
		if (!organizationId) return;
		await updateFolderMutation.mutateAsync({
			id: folderId,
			...pickUpdatableFields({ name }),
		});
	};

	const handleDeleteFolder = async (folderId: string) => {
		if (!organizationId) return;
		await deleteFolderMutation.mutateAsync(folderId);
	};

	const handleUpdateFavorite = async (favoriteId: string, data: any) => {
		if (!organizationId) return;
		await updateFavoriteMutation.mutateAsync({
			id: favoriteId,
			...data,
		});
	};

	const handleDeleteFavorite = async (favoriteId: string) => {
		if (!organizationId) return;
		await deleteFavoriteMutation.mutateAsync(favoriteId);
	};

	// Drag and drop logic (update favorite position/folder)
	// ... (implement as needed, using handleUpdateFavorite)

	const handleDragStart = (event: DragStartEvent) => {
		const { active } = event;
		setActiveId(active.id as string);
		setDraggedItem(active.data.current?.item);
	};

	const handleDragEnd = async (event: DragEndEvent) => {
		const { active, over } = event;

		if (!over) {
			setActiveId(null);
			setDraggedItem(null);
			return;
		}

		const activeData = active.data.current as {
			type: "favorite" | "folder";
			item: FavoriteItem | FavoriteFolder;
		};
		const overData = over.data.current as {
			type: "folder" | "root";
			accepts: string[];
		};

		if (activeData.type === "favorite") {
			const favoriteItem = activeData.item as FavoriteItem;

			if (!favoriteItem.id) {
				setActiveId(null);
				setDraggedItem(null);
				return;
			}

			try {
				if (overData.type === "folder") {
					const folderId = over.id
						? String(over.id).startsWith("folder-content-")
							? String(over.id).replace("folder-content-", "")
							: String(over.id)
						: undefined;
					if (!folderId) {
						setActiveId(null);
						setDraggedItem(null);
						return;
					}
					const targetItems = folderItems.get(folderId) || [];
					const newPosition =
						targetItems.length > 0
							? Math.max(
									...targetItems.map((i) => i.position || 0),
								) + 1000
							: 1000;
					await handleUpdateFavorite(favoriteItem.id, {
						folderId,
						position: newPosition,
					});
				} else if (overData.type === "root") {
					// Dropped into root
					const newPosition =
						rootItems.length > 0
							? Math.max(
									...rootItems.map((i) => i.position || 0),
								) + 1000
							: 1000;
					await handleUpdateFavorite(favoriteItem.id, {
						folderId: null,
						position: newPosition,
					});
				}

				// If dropped on remove-from-folder-{folder.id}, set folderId: null
				if (
					over.id &&
					String(over.id).startsWith("remove-from-folder-")
				) {
					const newPosition =
						rootItems.length > 0
							? Math.max(
									...rootItems.map((i) => i.position || 0),
								) + 1000
							: 1000;
					await handleUpdateFavorite(favoriteItem.id, {
						folderId: null,
						position: newPosition,
					});
					setActiveId(null);
					setDraggedItem(null);
					return;
				}

				if (
					over.id &&
					(String(over.id).startsWith("before-") ||
						String(over.id).startsWith("after-"))
				) {
					const overId = String(over.id);
					const isBefore = overId.startsWith("before-");
					const targetId = overId.replace(/^before-|^after-/, "");
					// Find the target item (in folder or root)
					let targetItem = null;
					let siblings = [];
					// Check if in a folder
					for (const [folderId, items] of Array.from(
						folderItems.entries(),
					) as [string, any[]][]) {
						const found = items.find((i: any) => i.id === targetId);
						if (found) {
							targetItem = found;
							siblings = items;
							break;
						}
					}
					// If not in a folder, check root
					if (!targetItem) {
						targetItem = rootItems.find((i) => i.id === targetId);
						siblings = rootItems;
					}
					if (!targetItem) {
						setActiveId(null);
						setDraggedItem(null);
						return;
					}
					// Find the index of the target item
					const targetIdx = siblings.findIndex(
						(i) => i.id === targetId,
					);
					// Calculate new position
					let newPosition;
					if (isBefore) {
						const prev = siblings[targetIdx - 1];
						if (prev) {
							newPosition =
								(prev.position + targetItem.position) / 2;
						} else {
							newPosition = targetItem.position - 1000;
						}
					} else {
						const next = siblings[targetIdx + 1];
						if (next) {
							newPosition =
								(targetItem.position + next.position) / 2;
						} else {
							newPosition = targetItem.position + 1000;
						}
					}
					// Set folderId to match the target item (or undefined for root)
					const newFolderId = targetItem.folderId || null;
					await handleUpdateFavorite(favoriteItem.id, {
						folderId: newFolderId,
						position: newPosition,
					});
					setActiveId(null);
					setDraggedItem(null);
					return;
				}
			} catch (error) {
				console.error("Error handling favorite:", error);
			}
		}

		setActiveId(null);
		setDraggedItem(null);
	};

	return (
		<DndContext
			sensors={sensors}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
		>
			<Collapsible defaultOpen className="group/collapsible">
				<SidebarGroup className={cn(isCollapsed && "!mt-6")}>
					<SidebarGroupLabel
						className="group-data-[collapsible=icon]:opacity-100 !p-0"
						onMouseEnter={() => setIsFavoritesHovered(true)}
						onMouseLeave={() => setIsFavoritesHovered(false)}
					>
						<CollapsibleTrigger
							className={cn(
								"h-8 flex items-center px-2 rounded-md w-full cursor-pointer",
								"hover:!bg-muted/50 hover:!text-accent-foreground !border !border-transparent hover:!border-border",
							)}
						>
							<div className="flex items-center justify-between w-full">
								{isCollapsed ? (
									<IconStar className="h-4 w-4" />
								) : (
									<div className="flex items-center gap-2">
										<IconChevronDown className="h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
										<span className="text-xs">
											Favorites
										</span>
									</div>
								)}
								{isFavoritesHovered && !isCollapsed && (
									<IconFolderPlus
										className="h-4 w-4 text-muted-foreground hover:text-accent-foreground cursor-pointer"
										onClick={(e) => {
											e.stopPropagation();
											handleCreateFolder();
										}}
									/>
								)}
							</div>
						</CollapsibleTrigger>
					</SidebarGroupLabel>

					<CollapsibleContent className="overflow-hidden data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up">
						<SidebarMenu className="space-y-1">
							<AnimatePresence>
								<>
									{isCreatingFolder && (
										<motion.div
											key="create-folder-input"
											initial={{ opacity: 0, height: 0 }}
											animate={{
												opacity: 1,
												height: "auto",
											}}
											exit={{ opacity: 0, height: 0 }}
											transition={{
												duration: 0.2,
												ease: "easeInOut",
											}}
											className="overflow-hidden rounded-md mt-1"
										>
											<Input
												value={newFolderName}
												onChange={(e) =>
													setNewFolderName(
														e.target.value,
													)
												}
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														handleCreateFolder();
													}

													if (e.key === "Escape") {
														setIsCreatingFolder(
															false,
														);
													}
												}}
												onBlur={() =>
													setIsCreatingFolder(false)
												}
												leftIcon={<IconFolder />}
												placeholder="Folder name"
												className="h-8 text-sm !border-blue-500 rounded-md"
												autoFocus
											/>
										</motion.div>
									)}

									{!isLoadingFolders &&
										!isLoadingFavorites &&
										rootItems.length < 1 &&
										foldersWithId.length < 1 && (
											<motion.span
												key="no-favorites"
												initial={{ opacity: 0 }}
												animate={{ opacity: 1 }}
												exit={{ opacity: 0 }}
												transition={{ duration: 0.2 }}
												className={cn(
													"text-xs cursor-default text-foreground/50 pl-3",
													"group-data-[collapsible=icon]:hidden",
												)}
											>
												No favorites yet
											</motion.span>
										)}
								</>
							</AnimatePresence>

							<Droppable id="root">
								<div>
									{/* Show loading skeletons */}
									{(isLoadingFolders ||
										isLoadingFavorites) && (
										<>
											{/* Folder skeletons */}
											{isLoadingFolders &&
												Array.from({ length: 2 }).map(
													(_, i) => (
														<FolderSkeleton
															key={`folder-skeleton-${i}`}
															isCollapsed={
																isCollapsed
															}
														/>
													),
												)}
											{/* Favorite item skeletons */}
											{isLoadingFavorites &&
												Array.from({ length: 3 }).map(
													(_, i) => (
														<FavoriteItemSkeleton
															key={`favorite-skeleton-${i}`}
															isCollapsed={
																isCollapsed
															}
														/>
													),
												)}
										</>
									)}

									{/* Show actual content when loaded */}
									{!isLoadingFolders &&
										!isLoadingFavorites && (
											<SortableContext
												items={[
													...foldersWithId.map(
														(f) => f.id,
													),
													...rootItems.map(
														(i) => i.id,
													),
												]}
											>
												{/* Render folders */}
												{foldersWithId.map((folder) => (
													<Droppable
														key={folder.id}
														id={`remove-from-folder-${folder.id}`}
														isFolder={false}
													>
														{(dropProps) => (
															<DraggableFolder
																folder={folder}
															>
																{isCollapsed ? (
																	<SidebarMenuItem className="flex justify-center w-full p-0">
																		<SidebarMenuButton
																			tooltip={
																				folder.name
																			}
																			className={cn(
																				"transition-colors duration-200 flex items-center justify-center h-8 p-0 m-0",
																			)}
																			onClick={() =>
																				handleFolderClick(
																					folder,
																				)
																			}
																		>
																			{folder.isOpen ? (
																				<IconFolderOpen className="size-4 mx-auto" />
																			) : (
																				<IconFolder className="size-4 mx-auto" />
																			)}
																		</SidebarMenuButton>
																	</SidebarMenuItem>
																) : (
																	<div
																		className={cn(
																			"!w-full relative rounded-md",
																			dropProps.isOver &&
																				"border-dashed border-blue-500 bg-blue-100/60",
																		)}
																		style={{
																			transition:
																				"border-color 0.2s, background 0.2s",
																		}}
																	>
																		<Droppable
																			id={`before-${folder.id}`}
																		>
																			<div
																				className={cn(
																					"absolute w-full h-px -top-1 z-10",
																					"transition-all duration-200",
																				)}
																			/>
																		</Droppable>

																		<Droppable
																			id={
																				folder.id
																			}
																			isFolder
																		>
																			<div className="">
																				<div
																					className={cn(
																						"hover:bg-muted/50 !border !border-transparent hover:!border-border h-8 transition-colors duration-200 w-full flex items-center px-1 rounded-md cursor-pointer group/folder",
																						isCollapsed &&
																							"justify-center p-0",
																					)}
																					onMouseEnter={() =>
																						setHoveredFolder(
																							folder.id,
																						)
																					}
																					onMouseLeave={() =>
																						setHoveredFolder(
																							null,
																						)
																					}
																				>
																					<div
																						className="flex items-center flex-1 gap-1"
																						onClick={() =>
																							handleFolderClick(
																								folder,
																							)
																						}
																					>
																						<div className="relative w-4 h-4">
																							<IconFolderOpen
																								className={cn(
																									"absolute h-4 w-4 stroke-1 transition-all duration-300 ease-in-out transform",
																									folder.isOpen
																										? "opacity-100 scale-100"
																										: "opacity-0 scale-95",
																								)}
																							/>
																							<IconFolder
																								className={cn(
																									"absolute h-4 w-4 stroke-1 transition-all duration-300 ease-in-out transform",
																									folder.isOpen
																										? "opacity-0 scale-95"
																										: "opacity-100 scale-100",
																								)}
																							/>
																						</div>
																						{editingFolder?.id ===
																						folder.id ? (
																							<Input
																								value={
																									editingFolder?.name ??
																									""
																								}
																								onChange={(
																									e,
																								) =>
																									setEditingFolder(
																										editingFolder
																											? {
																													id: editingFolder.id,
																													name: e
																														.target
																														.value,
																												}
																											: null,
																									)
																								}
																								onKeyDown={(
																									e,
																								) => {
																									if (
																										e.key ===
																											"Enter" &&
																										editingFolder
																									) {
																										handleRenameFolder(
																											editingFolder.id,
																											editingFolder.name,
																										);
																										setEditingFolder(
																											null,
																										);
																									}
																								}}
																								onBlur={() => {
																									if (
																										editingFolder
																									) {
																										handleRenameFolder(
																											editingFolder.id,
																											editingFolder.name,
																										);
																									}
																									setEditingFolder(
																										null,
																									);
																								}}
																								className="h-6 text-sm"
																								autoFocus
																							/>
																						) : (
																							<span className="text-sm font-medium">
																								{
																									folder.name
																								}
																							</span>
																						)}
																					</div>

																					<DropdownMenu>
																						<DropdownMenuTrigger
																							asChild
																						>
																							<IconDotsVertical
																								className={cn(
																									"h-4 w-4 opacity-0 group-hover/folder:opacity-100 transition-opacity text-muted-foreground hover:text-accent-foreground",
																									hoveredFolder ===
																										folder.id &&
																										"opacity-100",
																								)}
																								onClick={(
																									e,
																								) =>
																									e.stopPropagation()
																								}
																							/>
																						</DropdownMenuTrigger>
																						<DropdownMenuContent
																							align="start"
																							className="!p-1 space-y-1"
																						>
																							<DropdownMenuItem
																								onClick={() =>
																									setEditingFolder(
																										{
																											id: folder.id,
																											name: folder.name,
																										},
																									)
																								}
																								className="flex items-center gap-2"
																							>
																								<IconPencil className="h-4 w-4" />
																								Rename
																							</DropdownMenuItem>
																							<DropdownMenuItem
																								className="flex items-center gap-2 text-red-500 hover:!text-red-500"
																								onClick={() =>
																									handleDeleteFolder(
																										folder.id,
																									)
																								}
																							>
																								<IconTrash className="h-4 w-4 text-red-500" />
																								Delete
																								folder
																							</DropdownMenuItem>
																						</DropdownMenuContent>
																					</DropdownMenu>
																				</div>

																				<AnimatePresence>
																					{folder.isOpen && (
																						<motion.div
																							initial={{
																								height: 0,
																								opacity: 0,
																							}}
																							animate={{
																								height: "auto",
																								opacity: 1,
																							}}
																							exit={{
																								height: 0,
																								opacity: 0,
																							}}
																							transition={{
																								duration: 0.2,
																								ease: "easeInOut",
																							}}
																							className="overflow-hidden"
																						>
																							<Droppable
																								id={`folder-content-${folder.id}`}
																								isFolder
																							>
																								<div className="pl-5">
																									{(() => {
																										const items =
																											folderItems.get(
																												folder.id,
																											) ||
																											[];
																										if (
																											items.length >
																											0
																										) {
																											return (
																												<SortableContext
																													items={items.map(
																														(
																															i,
																														) =>
																															i.id,
																													)}
																												>
																													{items.flatMap(
																														(
																															item,
																															idx,
																														) => [
																															<Droppable
																																key={`before-${item.id}`}
																																id={`before-${item.id}`}
																															>
																																<div className="h-px" />
																															</Droppable>,
																															<DraggableItem
																																key={
																																	item.id
																																}
																																item={
																																	item
																																}
																															>
																																<FavoriteItemContent
																																	item={
																																		item
																																	}
																																	onHover={
																																		setHoveredItem
																																	}
																																	isHovered={
																																		hoveredItem ===
																																		item.id
																																	}
																																	toggleFavorite={
																																		toggleFavorite.mutate
																																	}
																																	updateItem={
																																		handleUpdateFavorite
																																	}
																																/>
																															</DraggableItem>,
																															idx ===
																																items.length -
																																	1 && (
																																<Droppable
																																	key={`after-${item.id}`}
																																	id={`after-${item.id}`}
																																>
																																	<div className="h-px" />
																																</Droppable>
																															),
																														],
																													)}
																												</SortableContext>
																											);
																										}
																										return (
																											<div className="pl-2 py-1 text-xs text-muted-foreground">
																												No
																												items
																											</div>
																										);
																									})()}
																								</div>
																							</Droppable>
																						</motion.div>
																					)}
																				</AnimatePresence>
																			</div>
																		</Droppable>

																		<Droppable
																			id={`after-${folder.id}`}
																		>
																			<div
																				className={cn(
																					"absolute w-full h-px -bottom-px z-10",
																					"transition-all duration-200",
																				)}
																			/>
																		</Droppable>
																	</div>
																)}
															</DraggableFolder>
														)}
													</Droppable>
												))}

												{/* Render root items */}
												{rootItems.map((item) => (
													<DraggableItem
														key={item.id}
														item={item as any}
													>
														<FavoriteItemContent
															item={item as any}
															onHover={
																setHoveredItem
															}
															isHovered={
																hoveredItem ===
																item.id
															}
															toggleFavorite={
																toggleFavorite.mutate
															}
															updateItem={
																handleUpdateFavorite
															}
														/>
													</DraggableItem>
												))}
											</SortableContext>
										)}
								</div>
							</Droppable>

							<DragOverlay>
								{draggedItem && (
									<div className="opacity-80 bg-blue-800 rounded-md shadow-lg border border-blue-700">
										{Object.hasOwn(draggedItem, "id") ? (
											<FavoriteItemContent
												item={
													draggedItem as FavoriteItem
												}
												onHover={() => {}}
												isHovered={false}
												toggleFavorite={() => {}}
												updateItem={() => {}}
											/>
										) : (
											<div className="flex items-center gap-2 px-2 py-1">
												<IconFolder className="h-4 w-4 text-blue-500" />
												<span className="text-sm">
													{draggedItem.name}
												</span>
											</div>
										)}
									</div>
								)}
							</DragOverlay>
						</SidebarMenu>
					</CollapsibleContent>
				</SidebarGroup>
			</Collapsible>
		</DndContext>
	);
}
