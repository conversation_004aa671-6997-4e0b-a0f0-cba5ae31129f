"use client";

import { useHelp } from "@app/shared/lib/help-context";
import { CommandShortcut } from "@ui/components/command";

interface CommandShortcutDisplayProps {
	shortcut: string;
}

export function CommandShortcutDisplay({
	shortcut,
}: CommandShortcutDisplayProps) {
	const { shortcutSymbol } = useHelp();
	const displayShortcut = shortcut.replace("⌘", shortcutSymbol);
	return <CommandShortcut>{displayShortcut}</CommandShortcut>;
}
