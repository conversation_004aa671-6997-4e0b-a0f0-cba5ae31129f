"use client";

import { useState } from "react";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@ui/components/popover";
import { IconSelector, IconX } from "@tabler/icons-react";
import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import { useOrganizationTags, useObjectTags, useAddTag, useRemoveTag, useCreateTag, useUpdateTag } from "../hooks/useTags";
import type { TaggableObjectType } from "@repo/database/src/types/object";
import { cn } from "@ui/lib";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { ColorPicker, getRandomColor } from "@ui/components/color-picker";
import { normalizeTagName, validateTagName } from "@repo/api/src/lib/tag-utils";

interface TagSelectorProps {
  objectId: string;
  objectType: TaggableObjectType;
  className?: string;
  onTagClick?: (tag: { id: string; name: string; color: string }) => void;
  readOnly?: boolean;
}

export function TagSelector({ objectId, objectType, className, onTagClick, readOnly = false }: TagSelectorProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState("");
  const [newTagColor, setNewTagColor] = useState(getRandomColor());
  const [editingTagId, setEditingTagId] = useState<string | null>(null);

  const { data: objectTags, isLoading: isLoadingObjectTags } = useObjectTags(
    objectId,
    objectType
  );
  const { data: availableTags, isLoading: isLoadingTags } =
    useOrganizationTags(objectType);

  const addTag = useAddTag();
  const removeTag = useRemoveTag();
  const createTag = useCreateTag();
  const updateTag = useUpdateTag();

  const handleAddTag = async (tagId: string) => {
    try {
      await addTag.mutateAsync({ tagId, objectId, objectType });
    } catch (error) {
      console.error("Failed to add tag:", error);
    }
  };

  const handleRemoveTag = async (tagId: string) => {
    try {
      await removeTag.mutateAsync({ tagId, objectId, objectType });
    } catch (error) {
      console.error("Failed to remove tag:", error);
    }
  };

  const handleCreateTag = async () => {
    if (!value.trim()) return;

    const validation = validateTagName(value.trim());
    if (!validation.isValid) {
      console.error("Invalid tag name:", validation.error);
      return;
    }

    const normalizedName = normalizeTagName(value.trim());

    try {
      const tag = await createTag.mutateAsync({
        name: normalizedName,
        color: newTagColor,
        objectType,
      });
      await handleAddTag(tag.id);
      setValue("");
      setNewTagColor(getRandomColor());
      setOpen(false);
    } catch (error) {
      console.error("Failed to create tag:", error);
    }
  };

  const handleUpdateTagColor = async (tagId: string, color: string) => {
    try {
      await updateTag.mutateAsync({ id: tagId, color });
      setEditingTagId(null);
    } catch (error) {
      console.error("Failed to update tag color:", error);
    }
  };

  const handleTagToggle = async (tagId: string) => {
    try {
      const isTagged = objectTags?.some(ot => ot.tag.id === tagId);
      
      if (isTagged) {
        await handleRemoveTag(tagId);
      } else {
        await handleAddTag(tagId);
      }
    } catch (error) {
      console.error("Failed to toggle tag:", error);
    }
  };

  const handleTagClick = (tag: { id: string; name: string; color: string }) => {
    if (onTagClick) {
      onTagClick(tag);
    } else {
      // Get the current URL segments
      const urlSegments = pathname.split('/');
      const orgSlug = urlSegments[2];
      
      // Determine the object type plural form for the URL
      let objectTypePlural = objectType + 's'; // Default pluralization
      if (objectType === 'company') objectTypePlural = 'companies';
      if (objectType === 'property') objectTypePlural = 'properties';

      // Extract viewId from current pathname (e.g., /app/org/contacts/view/viewId)
      const viewIndex = urlSegments.findIndex(segment => segment === 'view');
      let currentViewId = viewIndex !== -1 && urlSegments[viewIndex + 1] ? urlSegments[viewIndex + 1] : null;

      // If no viewId in path, try to get it from search params (for cases where we're on a filtered view)
      if (!currentViewId) {
        currentViewId = searchParams.get("viewId");
      }

      // Create the new URL parameters with simple tags parameter
      const params = new URLSearchParams(searchParams);
      // Use the normalized tag name to ensure consistent filtering
      params.set("tags", normalizeTagName(tag.name));

      // Construct the view URL with tag filter
      let viewUrl;
      if (currentViewId) {
        viewUrl = `/app/${orgSlug}/${objectTypePlural}/view/${currentViewId}?${params.toString()}`;
      } else {
        // If still no viewId, navigate to base objects page and let it redirect to default view
        // The redirect will preserve our query parameters
        viewUrl = `/app/${orgSlug}/${objectTypePlural}?${params.toString()}`;
      }
      
      // Navigate to the view
      router.push(viewUrl);
    }
  };

  if (isLoadingObjectTags || isLoadingTags) {
    return <div className="animate-pulse h-6 bg-muted rounded w-full" />;
  }

  const currentTags = objectTags?.map(ot => ot.tag) ?? [];
  const unusedTags = availableTags?.filter(
    (tag) => !objectTags?.some((ot) => ot.tag.id === tag.id)
  ) ?? [];

  return (
    <div className={cn("space-y-2", className)}>
      {!readOnly && (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between text-muted-foreground hover:text-foreground"
            >
              <span className="truncate">Add tags...</span>
              <IconSelector className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0" align="start">
            <Command>
              <CommandInput
                placeholder="Search or create tag..."
                value={value}
                onValueChange={setValue}
              />
              <CommandList>
                <CommandEmpty className="p-1 text-center text-sm">
                  {value.trim() ? (
                    <div className="">
                      <div className="flex items-center gap-1">
                        <ColorPicker
                          value={newTagColor}
                          onValueChange={setNewTagColor}
                          className="h-6 w-6"
                        />
                        <Badge
                          style={{
                            backgroundColor: `${newTagColor}20`,
                            border: `1px solid ${newTagColor}`,
                            color: newTagColor,
                          }}
                          className="rounded-md h-6"
                        >
                          {normalizeTagName(value.trim())}
                        </Badge>
                        <Button
                          size="sm"
                          className="ml-auto h-6"
                          variant="relio"
                          onClick={handleCreateTag}
                          disabled={!validateTagName(value.trim()).isValid}
                        >
                          Create
                        </Button>
                      </div>
                      {!validateTagName(value.trim()).isValid && (
                        <p className="text-xs text-destructive">
                          {validateTagName(value.trim()).error}
                        </p>
                      )}
                    </div>
                  ) : (
                    "No tags found."
                  )}
                </CommandEmpty>
                {unusedTags.length > 0 && (
                  <CommandGroup>
                    {unusedTags.map((tag) => {
                      const isEditing = editingTagId === tag.id;

                      return (
                        <CommandItem
                          key={tag.id}
                          value={tag.name}
                          onSelect={() => {
                            if (isEditing) return;
                            handleTagToggle(tag.id);
                            setOpen(false);
                          }}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center gap-2">
                            {isEditing ? (
                              <ColorPicker
                                value={tag.color || "#000000"}
                                onValueChange={(color: string) =>
                                  handleUpdateTagColor(tag.id, color)
                                }
                                className="h-4 w-4"
                              />
                            ) : (
                              <div
                                className="h-4 w-4 rounded-full cursor-pointer"
                                style={{ backgroundColor: tag.color || "#000000" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingTagId(tag.id);
                                }}
                              />
                            )}
                            <span>{tag.name}</span>
                          </div>
                          <IconSquareRoundedCheckFilled
                            className={cn(
                              "ml-2 h-4 w-4",
                              objectTags?.some(
                                (ot) => ot.tag.id === tag.id
                              )
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}

      {currentTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {currentTags.map((tag) => (
            <div
              key={tag.id}
              className={cn(
                "relative inline-flex items-center rounded-lg text-sm font-medium transition-all px-2 py-0.5 cursor-pointer group/tag overflow-hidden",
                "bg-secondary/30 border border-border"
              )}
              style={{
                borderColor: tag.color || "#000000",
                backgroundColor: `${tag.color || "#000000"}20`,
              }}
              onClick={() => handleTagClick(tag as any)}
            >
              <span className="flex-1 min-w-0 truncate transition-all duration-150 group-hover/tag:max-w-[calc(100%-1.5rem)]">
                {tag.name}
              </span>

              {/* Gradient overlay that appears on hover */}
              <div className="absolute top-0 right-0 bottom-0 pointer-events-none flex justify-end px-2 items-center opacity-0 group-hover/tag:opacity-100 transition-all duration-150 bg-gradient-to-l from-[var(--gradient-from)] to-transparent w-8 rounded-r-lg"
                style={{
                  '--gradient-from': `${tag.color}20`,
                } as any}
              />

              {!readOnly && (
                /* Delete button that slides in from the right */
                <div className="absolute top-0 right-0 bottom-0 flex justify-end px-1 items-center translate-x-full group-hover/tag:translate-x-0 opacity-0 group-hover/tag:opacity-100 transition-all duration-150">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveTag(tag.id);
                    }}
                    className="h-5 w-5 p-0 rounded-md hover:bg-destructive/10 hover:text-destructive"
                    aria-label="Remove tag"
                  >
                    <IconX className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 