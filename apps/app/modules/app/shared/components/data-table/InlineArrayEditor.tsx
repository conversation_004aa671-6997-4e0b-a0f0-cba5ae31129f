"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { Check, Edit2, GripVertical, Plus, Trash2, X } from "lucide-react";
import * as React from "react";
import { CopyableValue } from "../../../../shared/components/CopyableValue";
import { ComposeEmailModal } from "../../../../shared/components/ComposeEmailModal";

interface InlineArrayEditorProps {
	value: string[];
	onSave: (value: string[]) => Promise<void>;
	onCancel: () => void;
	fieldType?: "email" | "phone" | "text";
	placeholder?: string;
	addButtonText?: string;
	className?: string;
	children: React.ReactNode; // The cell content to click on
	isEditing?: boolean;
}

export function InlineArrayEditor({
	value = [],
	onSave,
	onCancel,
	fieldType = "text",
	placeholder,
	addButtonText,
	className,
	children,
	isEditing = false,
}: InlineArrayEditorProps) {
	const [isOpen, setIsOpen] = React.useState(false);
	const [items, setItems] = React.useState<string[]>(value);
	const [newItem, setNewItem] = React.useState("");
	const [isLoading, setIsLoading] = React.useState(false);
	const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
	
	// Email compose modal state
	const [composeModalOpen, setComposeModalOpen] = React.useState(false);
	const [selectedEmail, setSelectedEmail] = React.useState<string>("");

	const handleEmailClick = (email: string) => {
		setSelectedEmail(email);
		setComposeModalOpen(true);
	};

	// Control popover state based on isEditing prop
	React.useEffect(() => {
		setIsOpen(isEditing);
	}, [isEditing]);

	// Reset items when value changes or popover opens
	React.useEffect(() => {
		if (isOpen) {
			setItems([...value]);
			setNewItem("");
		}
	}, [isOpen, value]);

	const getPlaceholder = () => {
		if (placeholder) return placeholder;
		switch (fieldType) {
			case "email":
				return "New email address";
			case "phone":
				return "New phone number";
			default:
				return "New item";
		}
	};

	const getAddButtonText = () => {
		if (addButtonText) return addButtonText;
		switch (fieldType) {
			case "email":
				return "Add email address";
			case "phone":
				return "Add phone number";
			default:
				return "Add item";
		}
	};

	const validateItem = (item: string): boolean => {
		if (fieldType === "email") {
			return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(item);
		}
		return item.trim().length > 0;
	};

	const handleAddItem = () => {
		if (newItem.trim() && validateItem(newItem)) {
			setItems([...items, newItem.trim()]);
			setNewItem("");
		}
	};

	const handleRemoveItem = (index: number) => {
		setItems(items.filter((_, i) => i !== index));
	};

	const handleItemChange = (index: number, newValue: string) => {
		const updatedItems = [...items];
		updatedItems[index] = newValue;
		setItems(updatedItems);
	};

	const handleSave = async () => {
		setIsLoading(true);
		try {
			await onSave(items.filter((item) => validateItem(item)));
			setIsOpen(false);
		} catch (error) {
			console.error("Failed to save:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const handleCancel = () => {
		setItems([...value]);
		setNewItem("");
		setEditingIndex(null);
		setIsOpen(false);
		onCancel();
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			handleAddItem();
		} else if (e.key === "Escape") {
			e.preventDefault();
			handleCancel();
		}
	};

	const getInputType = () => {
		switch (fieldType) {
			case "email":
				return "email";
			case "phone":
				return "tel";
			default:
				return "text";
		}
	};

		return (
		<>
			<Popover open={isOpen} onOpenChange={setIsOpen}>
				<PopoverTrigger asChild>
					<div className="cursor-pointer w-full">{children}</div>
				</PopoverTrigger>
				<PopoverContent
					className="w-80 p-0"
					align="start"
					onInteractOutside={(e) => {
						// Prevent closing when clicking inside the popover
						e.preventDefault();
					}}
				>
					<div className="p-3 space-y-3">
						<div className="flex items-center justify-between">
							<h4 className="font-medium text-sm">
								@{" "}
								{fieldType === "email"
									? "Email addresses"
									: fieldType === "phone"
										? "Phone numbers"
										: "Items"}
							</h4>
							<Button
								size="sm"
								variant="ghost"
								onClick={handleCancel}
								className="h-6 w-6 p-0"
							>
								<X className="h-3.5 w-3.5" />
							</Button>
						</div>

						{/* Existing items */}
						<div className="space-y-2 max-h-48 overflow-y-auto">
							{items.map((item, index) => (
								<div
									key={index}
									className="flex items-center gap-2 p-2 border rounded-md bg-muted/50 group"
								>
									<GripVertical className="h-3.5 w-3.5 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity cursor-grab" />

									{editingIndex === index ? (
										<Input
											value={item}
											onChange={(e) =>
												handleItemChange(
													index,
													e.target.value,
												)
											}
											type={getInputType()}
											className={cn(
												"flex-1 border-none bg-transparent p-0 h-auto focus-visible:ring-0 text-sm",
												!validateItem(item) &&
													item.length > 0 &&
													"text-destructive",
											)}
											onBlur={() => setEditingIndex(null)}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													setEditingIndex(null);
												} else if (e.key === "Escape") {
													// Reset to original value and exit edit mode
													const originalItems = [
														...value,
													];
													setItems(originalItems);
													setEditingIndex(null);
												}
											}}
											autoFocus
										/>
									) : (
										<div className="flex-1 flex items-center">
											{fieldType === "email" ||
											fieldType === "phone" ? (
												<div
													onClick={() =>
														setEditingIndex(index)
													}
													className="flex-1"
												>
													<CopyableValue
														value={item}
														type={fieldType}
														className="flex-1"
														showCopyButton={true}
														showDeleteButton={true}
														onDelete={() =>
															handleRemoveItem(index)
														}
														onEmailClick={fieldType === "email" ? handleEmailClick : undefined}
													/>
												</div>
											) : (
												<span
													className="text-sm text-foreground truncate cursor-pointer hover:text-primary transition-colors flex-1"
													onClick={() =>
														setEditingIndex(index)
													}
												>
													{item}
												</span>
											)}
										</div>
									)}

									<Button
										size="sm"
										variant="ghost"
										onClick={() =>
											editingIndex === index
												? setEditingIndex(null)
												: setEditingIndex(index)
										}
										className="h-5 w-5 p-0 text-muted-foreground hover:text-foreground opacity-0 group-hover:opacity-100 transition-opacity"
									>
										<Edit2 className="h-3 w-3" />
									</Button>

									{!(
										fieldType === "email" ||
										fieldType === "phone"
									) && (
										<Button
											size="sm"
											variant="ghost"
											onClick={() => handleRemoveItem(index)}
											className="h-5 w-5 p-0 text-muted-foreground hover:text-destructive opacity-0 group-hover:opacity-100 transition-opacity"
										>
											<Trash2 className="h-3 w-3" />
										</Button>
									)}
								</div>
							))}
						</div>

						{/* Add new item */}
						<div className="space-y-2">
							<Input
								value={newItem}
								onChange={(e) => setNewItem(e.target.value)}
								placeholder={getPlaceholder()}
								type={getInputType()}
								onKeyDown={handleKeyDown}
								className={cn(
									"text-sm",
									!validateItem(newItem) &&
										newItem.length > 0 &&
										"border-destructive",
								)}
								autoFocus
							/>

							<Button
								size="sm"
								variant="outline"
								onClick={handleAddItem}
								disabled={!newItem.trim() || !validateItem(newItem)}
								className="w-full text-xs"
							>
								<Plus className="h-3 w-3 mr-1" />
								{getAddButtonText()}
							</Button>
						</div>

						{/* Action buttons */}
						<div className="flex gap-2 pt-2 border-t">
							<Button
								size="sm"
								onClick={handleSave}
								disabled={isLoading}
								className="flex-1 text-xs"
							>
								{isLoading ? (
									<div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent mr-1" />
								) : (
									<Check className="h-3 w-3 mr-1" />
								)}
								Save
							</Button>
							<Button
								size="sm"
								variant="outline"
								onClick={handleCancel}
								disabled={isLoading}
								className="flex-1 text-xs"
							>
								<X className="h-3 w-3 mr-1" />
								Cancel
							</Button>
						</div>
					</div>
				</PopoverContent>
			</Popover>
			
			{/* Email compose modal - only render if fieldType is email */}
			{fieldType === "email" && (
				<ComposeEmailModal
					open={composeModalOpen}
					onOpenChange={setComposeModalOpen}
					toEmail={selectedEmail}
				/>
			)}
		</>
	);
}
