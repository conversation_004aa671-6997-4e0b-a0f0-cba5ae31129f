"use client";

import Image from "next/image";
import { useTheme } from "next-themes";

export function Logo({ className = "" }: { className?: string }) {
	const { resolvedTheme } = useTheme();

	const src =
		resolvedTheme === "dark"
			? "/images/relio/logo-white.png"
			: "/images/relio/logo-black.png";

	return (
		<Image
			src={src}
			alt="Relio Logo"
			width={160}
			height={48}
			className={className}
			priority
		/>
	);
}
