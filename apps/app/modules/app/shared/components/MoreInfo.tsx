import React, { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@ui/components/form";
import {
  Select,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from '@ui/components/select';
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { format } from "date-fns";
import { IconCake, IconExternalLink } from "@tabler/icons-react";
import { DatePicker } from "@ui/components/date-picker";

// Constants for dropdown options
const CONTACT_STATUS = ["active", "inactive", "lead", "prospect", "customer"];
const CONTACT_PERSONA = ["owner", "agent", "investor", "tenant", "vendor"];
const CONTACT_STAGE = ["new", "qualified", "proposal", "negotiation", "closed"];

const COMPANY_SIZE = ["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"];
const COMPANY_INDUSTRY = ["real_estate", "technology", "finance", "construction", "other"];

const PROPERTY_TYPE = ["residential", "commercial", "land", "industrial", "mixed_use"];
const PROPERTY_STATUS = ["active", "pending", "sold", "off_market", "in_contract"];

// Form schemas for different object types
const contactFormSchema = z.object({
  title: z.string().optional(),
  status: z.string().optional(),
  persona: z.string().optional(),
  source: z.string().optional(),
  stage: z.string().optional(),
  website: z.string().url().optional().or(z.string().length(0)),
  spouseName: z.string().optional(),
  birthday: z.date().optional(),
  age: z.number().optional().nullable(),
  summary: z.string().optional(),
});

const companyFormSchema = z.object({
  name: z.string().optional(),
  website: z.string().url().optional().or(z.string().length(0)),
  industry: z.string().optional(),
  size: z.string().optional(),
  description: z.string().optional(),
});

const propertyFormSchema = z.object({
  name: z.string().optional(),
  recordType: z.string().optional(),
  propertyType: z.string().optional(),
  propertySubType: z.string().optional(),
  market: z.string().optional(),
  subMarket: z.string().optional(),
  listingId: z.string().optional(),
  status: z.string().optional(),
});

type ContactFormData = z.infer<typeof contactFormSchema>;
type CompanyFormData = z.infer<typeof companyFormSchema>;
type PropertyFormData = z.infer<typeof propertyFormSchema>;

type FormData = ContactFormData | CompanyFormData | PropertyFormData;

// Custom input component with label
const DetailInput = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label: string;
    check?: React.ReactNode;
  }
>(({ className, label, check, ...props }, ref) => (
  <div className="flex flex-row items-center">
    <div className="ml-3 w-[16%] text-[10px] uppercase font-normal text-muted-foreground">
      {label}
    </div>
    <div className="flex-1 relative w-full">
      <Input
        ref={ref}
        className={cn(
          "w-full h-8 border-transparent rounded-lg",
          className
        )}
        {...props}
      />
      {check && (
        <div className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer">
          {check}
        </div>
      )}
    </div>
  </div>
));
DetailInput.displayName = "DetailInput";

// Skeleton components
const DetailInputSkeleton = ({ label }: { label: string }) => (
  <div className="flex flex-row items-center">
    <div className="ml-3 w-[16%] text-[10px] uppercase font-normal text-muted-foreground">
      {label}
    </div>
    <div className="flex-1 relative w-full">
      <Skeleton className="w-full h-6 rounded-lg" />
    </div>
  </div>
);

const SelectFieldSkeleton = ({ label }: { label: string }) => (
  <div className="flex flex-row items-center">
    <div className="ml-3 w-[20%] text-[10px] uppercase font-normal text-muted-foreground">
      {label}
    </div>
    <Skeleton className="w-full h-6 rounded-lg" />
  </div>
);

const BirthdayFieldSkeleton = () => (
  <div className="flex flex-row items-center">
    <div className="ml-3 w-[20%] text-[10px] uppercase font-normal text-muted-foreground">
      Birthday
    </div>
    <Skeleton className="w-full h-6 rounded-lg" />
  </div>
);

interface MoreInfoProps {
  data: any;
  objectType: "contact" | "company" | "property";
  organization: { id: string };
  onUpdate: (data: any) => Promise<void>;
  loading?: boolean;
}

export function MoreInfo({ data, objectType, organization, onUpdate, loading = false }: MoreInfoProps) {
  const formSchema = {
    contact: contactFormSchema,
    company: companyFormSchema,
    property: propertyFormSchema,
  }[objectType];

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: data || {},
  });

  useEffect(() => {
    if (data) {
      // Handle birthday conversion if it exists
      const formData = { ...data };
      if (data.birthday && typeof data.birthday === 'string') {
        try {
          formData.birthday = new Date(data.birthday);
        } catch (error) {
          console.error('Failed to parse birthday date:', error);
          formData.birthday = null;
        }
      }
      form.reset(formData);
    }
  }, [data, form]);

  // Handle field updates
  const handleFieldUpdate = async (field: string, value: any) => {
    try {
      await onUpdate({
        id: data.id,
        [field]: value,
      });
    } catch (error) {
      console.error(`Failed to update ${field}:`, error);
    }
  };

  const renderContactFields = () => {
    if (loading) {
      return (
        <>
          <DetailInputSkeleton label="Title" />
          <SelectFieldSkeleton label="Status" />
          <SelectFieldSkeleton label="Persona" />
          <SelectFieldSkeleton label="Stage" />
          <DetailInputSkeleton label="Source" />
          <DetailInputSkeleton label="Website" />
          <DetailInputSkeleton label="Spouse" />
          <BirthdayFieldSkeleton />
        </>
      );
    }

    return (
      <>
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Title"
                  placeholder="Add title"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    if (field.value !== data.title) {
                      handleFieldUpdate("title", field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-3 w-[20%] text-[10px] uppercase font-normal text-muted-foreground">
                    Status
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      if (value !== data.status) {
                        handleFieldUpdate("status", value);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg bg-transparent hover:bg-accent/50">
                      <SelectValue placeholder="Select status..." />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTACT_STATUS.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="persona"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-3 w-[20%] text-[10px] uppercase font-normal text-muted-foreground">
                    Persona
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      if (value !== data.persona) {
                        handleFieldUpdate("persona", value);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg bg-transparent hover:bg-accent/50">
                      <SelectValue placeholder="Select persona..." />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTACT_PERSONA.map((persona) => (
                        <SelectItem key={persona} value={persona}>
                          {persona.charAt(0).toUpperCase() + persona.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="stage"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-3 w-[20%] text-[10px] uppercase font-normal text-muted-foreground">
                    Stage
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      if (value !== data.stage) {
                        handleFieldUpdate("stage", value);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg bg-transparent hover:bg-accent/50">
                      <SelectValue placeholder="Select stage..." />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTACT_STAGE.map((stage) => (
                        <SelectItem key={stage} value={stage}>
                          {stage.charAt(0).toUpperCase() + stage.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="source"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Source"
                  placeholder="Add source"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    if (field.value !== data.source) {
                      handleFieldUpdate("source", field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="website"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Website"
                  placeholder="Add website"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    const value = field.value?.trim();
                    const formattedValue = value && !value.startsWith('http') ? `https://${value}` : value;
                    if (formattedValue !== data.website) {
                      handleFieldUpdate("website", formattedValue);
                    }
                  }}
                  check={
                    field.value && (
                      <IconExternalLink
                        className="h-4 w-4 text-muted-foreground hover:text-foreground"
                        onClick={() => window.open(field.value, "_blank")}
                      />
                    )
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="spouseName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Spouse"
                  placeholder="Add spouse name"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    if (field.value !== data.spouseName) {
                      handleFieldUpdate("spouseName", field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="birthday"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-3 w-[20%] text-[10px] uppercase font-normal text-muted-foreground">
                    Birthday
                  </div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="w-full h-8 border-transparent rounded-lg bg-transparent hover:bg-accent/50 flex justify-start"
                      >
                        {(() => {
                          return null;
                        })()}
                        {field.value &&
                        field.value instanceof Date &&
                        !Number.isNaN(field.value.getTime()) ? (
                          <div className="flex items-center gap-2">
                            <IconCake className="h-4 w-4 text-pink-400" />
                            {format(field.value, "MMM d, yyyy")}
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <IconCake className="h-4 w-4 text-pink-400" />
                            Birthday
                          </div>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-[600px] p-0 rounded-2xl"
                      align="start"
                    >
                      <DatePicker
                        value={field.value}
                        onChange={(date) => {
                          field.onChange(date);
                          if (date) {
                            // Convert Date to ISO string for API
                            const isoString = date.toISOString();
                            handleFieldUpdate("birthday", isoString);
                          } else {
                            // Handle clearing the birthday
                            handleFieldUpdate("birthday", null);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    );
  };

  const renderCompanyFields = () => {
    if (loading) {
      return (
        <>
          <DetailInputSkeleton label="Name" />
          <DetailInputSkeleton label="Website" />
          <SelectFieldSkeleton label="Industry" />
          <SelectFieldSkeleton label="Size" />
          <DetailInputSkeleton label="Description" />
        </>
      );
    }

    return (
      <>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Name"
                  placeholder="Company name"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    if (field.value !== data.name) {
                      handleFieldUpdate("name", field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="website"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Website"
                  placeholder="Add website"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    const value = field.value?.trim();
                    const formattedValue = value && !value.startsWith('http') ? `https://${value}` : value;
                    if (formattedValue !== data.website) {
                      field.onChange(formattedValue);
                      handleFieldUpdate("website", formattedValue);
                    }
                  }}
                  check={
                    field.value && (
                      <IconExternalLink
                        className="h-4 w-4 text-muted-foreground hover:text-foreground"
                        onClick={() => window.open(field.value, "_blank")}
                      />
                    )
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="industry"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
                    Industry
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleFieldUpdate("industry", value);
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg">
                      <SelectValue placeholder="Select industry..." />
                    </SelectTrigger>
                    <SelectContent>
                      {COMPANY_INDUSTRY.map((industry) => (
                        <SelectItem key={industry} value={industry}>
                          {industry.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="size"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
                    Size
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleFieldUpdate("size", value);
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg">
                      <SelectValue placeholder="Select company size..." />
                    </SelectTrigger>
                    <SelectContent>
                      {COMPANY_SIZE.map((size) => (
                        <SelectItem key={size} value={size}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Description"
                  placeholder="Add description"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    if (field.value !== data.description) {
                      handleFieldUpdate("description", field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    );
  };

  const renderPropertyFields = () => {
    if (loading) {
      return (
        <>
          <DetailInputSkeleton label="Name" />
          <SelectFieldSkeleton label="Type" />
          <SelectFieldSkeleton label="Status" />
        </>
      );
    }

    return (
      <>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DetailInput
                  label="Name"
                  placeholder="Property name"
                  {...field}
                  value={field.value || ""}
                  onBlur={() => {
                    if (field.value !== data.name) {
                      handleFieldUpdate("name", field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="propertyType"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
                    Type
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleFieldUpdate("propertyType", value);
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg">
                      <SelectValue placeholder="Select property type..." />
                    </SelectTrigger>
                    <SelectContent>
                      {PROPERTY_TYPE.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex flex-row items-center">
                  <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
                    Status
                  </div>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleFieldUpdate("status", value);
                    }}
                  >
                    <SelectTrigger className="w-full h-8 border-transparent rounded-lg">
                      <SelectValue placeholder="Select status..." />
                    </SelectTrigger>
                    <SelectContent>
                      {PROPERTY_STATUS.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    );
  };

  return (
    <Form {...form}>
      <form className="space-y-1">
        {objectType === "contact" && renderContactFields()}
        {objectType === "company" && renderCompanyFields()}
        {objectType === "property" && renderPropertyFields()}
      </form>
    </Form>
  );
}

export default MoreInfo; 