"use client";

import { LocaleLink } from "@i18n/routing";
import { authClient } from "@repo/auth/client";
import { clearCache } from "@shared/lib/cache";
import { cn } from "@ui/lib";
import { config } from "@repo/config";

export function Footer({ showSignOut = false }: { showSignOut?: boolean }) {

	const onLogout = () => {
		authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					window.location.href = new URL(
						config.auth.redirectAfterLogout,
						window.location.origin,
					).toString();
				},
			},
		});
	};
	
	return (
		<footer
			className={cn(
				"container max-w-6xl py-6 text-center text-foreground/60 text-xs space-x-4",
			)}
		>
			<span>
				© {new Date().getFullYear()} Relio. All rights reserved.
			</span>

			<span className="cursor-pointer hover:underline" onClick={onLogout}>
				Sign out
			</span>

			<LocaleLink
				className="hover:underline"
				href="/legal/privacy-policy"
			>
				Privacy policy
			</LocaleLink>
			<LocaleLink className="hover:underline" href="/legal/terms">
				Terms and conditions
			</LocaleLink>
		</footer>
	);
}
