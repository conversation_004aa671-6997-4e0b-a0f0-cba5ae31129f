"use client";

import { TASK_STATUS, type TaskStatus } from "@app/shared/lib/constants";
import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import React, { useEffect, useId, useState } from "react";

interface StatusSelectorProps {
	status: TaskStatus;
	taskId?: string;
	onStatusChange?: (taskId: string, status: TaskStatus) => void;
	readonly?: boolean;
}

export function StatusSelector({
	status,
	taskId,
	onStatusChange,
	readonly,
}: StatusSelectorProps) {
	const id = useId();
	const [open, setOpen] = useState<boolean>(false);
	const [value, setValue] = useState<TaskStatus>(status);

	useEffect(() => {
		setValue(status);
	}, [status]);

	const handleStatusChange = (statusId: TaskStatus) => {
		setValue(statusId);
		setOpen(false);

		if (taskId && onStatusChange) {
			onStatusChange(taskId, statusId);
		}
	};

	if (readonly) {
		const selectedItem = TASK_STATUS.find((item) => item.value === status);
		if (!selectedItem) return null;
		const Icon = selectedItem.icon;
		return (
			<span
				title={selectedItem.label}
				className="inline-flex items-center justify-center"
			>
				<Icon className={`size-4 ${selectedItem.color}`} />
			</span>
		);
	}

	return (
		<div className="*:not-first:mt-2" onClick={(e) => e.stopPropagation()}>
			<Popover
				open={open}
				onOpenChange={(isOpen) => {
					setOpen(isOpen);
				}}
			>
				<PopoverTrigger asChild>
					<Button
						id={id}
						className="size-7 flex items-center justify-center"
						size="icon"
						variant="ghost"
						role="combobox"
						aria-expanded={open}
						onClick={(e) => e.stopPropagation()}
					>
						{(() => {
							const selectedItem = TASK_STATUS.find(
								(item) => item.value === value,
							);
							if (selectedItem) {
								const Icon = selectedItem.icon;
								return (
									<Icon
										className={`size-4 ${selectedItem.color}`}
									/>
								);
							}
							return null;
						})()}
					</Button>
				</PopoverTrigger>
				<PopoverContent
					className="border-input p-0 bg-zinc-100 dark:bg-sidebar min-w-[var(--radix-popper-anchor-width)]"
					align="start"
				>
					<Command className="bg-zinc-100 dark:bg-sidebar">
						<CommandInput placeholder="Set status..." />
						<CommandList className="!h-fit">
							<CommandEmpty>No status found.</CommandEmpty>
							<CommandGroup>
								{TASK_STATUS.map((item) => (
									<CommandItem
										key={item.value}
										value={item.value}
										onSelect={() =>
											handleStatusChange(item.value)
										}
										className="flex items-center justify-between"
									>
										<div className="flex items-center gap-2">
											{React.createElement(item.icon, {
												className: `size-4 ${item.color}`,
											})}
											{item.label}
										</div>
										{value === item.value && (
											<IconSquareRoundedCheckFilled
												size={16}
												className="ml-auto text-blue-400"
											/>
										)}
									</CommandItem>
								))}
							</CommandGroup>
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>
		</div>
	);
}
