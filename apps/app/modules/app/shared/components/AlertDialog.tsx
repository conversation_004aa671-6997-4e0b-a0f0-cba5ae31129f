import {
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
	AlertDialog as RadixAlertDialog,
} from "@ui/components/alert-dialog";
import React from "react";

export {
	RadixAlertDialog as AlertDialogRoot,
	AlertDialogTrigger,
	AlertDialogContent,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogCancel,
	AlertDialogAction,
};

interface AlertDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	title: string;
	description: string;
	confirmLabel?: string;
	cancelLabel?: string;
	confirmClassName?: string;
	onConfirm: () => void;
	loading?: boolean;
}

const AlertDialog: React.FC<AlertDialogProps> = ({
	open,
	onOpenChange,
	title,
	description,
	confirmLabel = "Confirm",
	cancelLabel = "Cancel",
	confirmClassName = "",
	onConfirm,
	loading = false,
}) => (
	<RadixAlertDialog open={open} onOpenChange={onOpenChange}>
		<AlertDialogContent>
			<AlertDialogHeader>
				<AlertDialogTitle>{title}</AlertDialogTitle>
				<AlertDialogDescription>{description}</AlertDialogDescription>
			</AlertDialogHeader>
			<AlertDialogFooter>
				<AlertDialogCancel>{cancelLabel}</AlertDialogCancel>
				<AlertDialogAction
					className={confirmClassName}
					onClick={onConfirm}
					disabled={loading}
				>
					{loading ? "Processing..." : confirmLabel}
				</AlertDialogAction>
			</AlertDialogFooter>
		</AlertDialogContent>
	</RadixAlertDialog>
);

export default AlertDialog;

// Reusable AlertDialog for confirmation dialogs
// Usage:
// <AlertDialog
//   open={open}
//   onOpenChange={setOpen}
//   title="Delete Task"
//   description="Are you sure you want to delete this task? This action cannot be undone."
//   confirmLabel="Delete"
//   cancelLabel="Cancel"
//   confirmClassName="bg-red-500 hover:bg-red-600 text-white"
//   onConfirm={handleDelete}
// />
