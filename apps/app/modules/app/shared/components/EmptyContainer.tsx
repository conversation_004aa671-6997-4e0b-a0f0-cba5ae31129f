import type { Icon } from "@tabler/icons-react";
import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import React from "react";

interface EmptyContainerProps {
	title: string;
	subtitle?: string;
	button?: string;
	icon?: Icon;
	onClick?: () => void;
	className?: string;
}
const EmptyContainer = ({
	title,
	subtitle,
	button,
	icon,
	onClick,
	className,
}: EmptyContainerProps) => {
	return (
		<div
			className={cn(
				"rounded-lg border border-dashed shadow-sm h-auto flex items-center justify-center",
				className,	
			)}
		>
			<div className="flex flex-col items-center gap-1 p-3 text-center">
				<h3 className="text-xl font-semibold">{title}</h3>
				{subtitle && (
					<p className="text-sm text-muted-foreground text-center w-[300px]">
						{subtitle}
					</p>
				)}
				{button && (
					<Button
						onClick={(e) => {
							e.preventDefault();
							if (onClick) onClick();
						}}
						className="mt-4 gap-1 bg-blue-500 hover:bg-blue-600 text-white !py-1 px-2 rounded-lg w-fit !h-8"
					>
						{icon &&
							React.createElement(icon, {
								className: "mr-1 w-4 h-4 stroke-white",
							})}{" "}
						{button}
					</Button>
				)}
			</div>
		</div>
	);
};

export default EmptyContainer;
