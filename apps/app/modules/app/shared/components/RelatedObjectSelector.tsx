"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useSearch } from "@app/search";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import {
	IconAt,
	IconBuilding,
	IconLink,
	IconMapPin,
	IconSquareRoundedCheckFilled,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import React, { useEffect, useId, useState } from "react";

// Types for related objects
interface RelatedObject {
	id: string;
	name: string;
	recordType: "contact" | "company" | "property";
	subtitle?: string;
}

interface RelatedObjectSelectorProps {
	value?: RelatedObject | null;
	onValueChange?: (relatedObject: RelatedObject | null) => void;
	displayMode?: "icon" | "full";
	size?: "sm" | "md" | "lg" | "xl";
	readonly?: boolean;
	placeholder?: string;
	className?: string;
}

// Helper function to convert search results to RelatedObject format
function mapSearchResultToRelatedObject(result: any): RelatedObject | null {
	// Skip tasks, notes, and users - we only want linkable objects
	if (result.type === 'task' || result.type === 'note' || result.type === 'user') {
		return null;
	}

	// Map contact results
	if (result.type === 'contact') {
		return {
			id: result.id,
			name: result.name || result.title || 'Unnamed Contact',
			recordType: 'contact' as const,
			// subtitle: result.email || result.jobTitle || undefined,
		};
	}

	// Map company results
	if (result.type === 'company') {
		return {
			id: result.id,
			name: result.name || result.title || 'Unnamed Company',
			recordType: 'company' as const,
			subtitle: result.description || undefined,
		};
	}

	// Map property results
	if (result.type === 'property') {
		return {
			id: result.id,
			name: result.name || result.title || 'Unnamed Property',
			recordType: 'property' as const,
			subtitle: result.description || undefined,
		};
	}

	return null;
}

export function RelatedObjectSelector({
	value,
	onValueChange,
	displayMode = "full",
	size = "md",
	readonly = false,
	placeholder = "Link a record",
	className,
}: RelatedObjectSelectorProps) {
	const id = useId();
	const [open, setOpen] = useState<boolean>(false);
	const [searchTerm, setSearchTerm] = useState("");
	const { activeOrganization } = useActiveOrganization();

	// Use the unified search system
	const shouldSearch = searchTerm.length >= 2 && !!activeOrganization?.id;
	const {
		data: searchResults,
		isLoading: searchLoading,
	} = useSearch(
		{
			query: searchTerm,
			organizationId: activeOrganization?.id || "",
			type: "all",
			limit: 20,
		},
		shouldSearch,
	);

	// Convert search results to RelatedObject format
	const relatedObjectsToShow = React.useMemo(() => {
		if (!shouldSearch || !searchResults?.results) return [];
		
		return searchResults.results
			.map(mapSearchResultToRelatedObject)
			.filter((obj): obj is RelatedObject => obj !== null);
	}, [shouldSearch, searchResults]);

	// Group related objects by type
	const groupedRelatedObjects = relatedObjectsToShow.reduce((acc: Record<string, RelatedObject[]>, obj: RelatedObject) => {
		if (!acc[obj.recordType]) acc[obj.recordType] = [];
		acc[obj.recordType].push(obj);
		return acc;
	}, {} as Record<string, RelatedObject[]>);

	const handleValueChange = (relatedObject: RelatedObject | null) => {
		setOpen(false);
		setSearchTerm("");
		onValueChange?.(relatedObject);
	};

	const renderIcon = (recordType?: string, name?: string) => {
		switch (recordType) {
			case 'contact':
				return <ContactAvatar name={name || 'Contact'} className="h-4 w-4" />;
			case 'company':
				return <IconBuilding className="h-4 w-4" />;
			case 'property':
				return <IconMapPin className="h-4 w-4" />;
			default:
				return <IconLink className="h-4 w-4" />;
		}
	};

	const getButtonSize = () => {
		switch (size) {
			case "sm":
				return displayMode === "icon" ? "size-6" : "h-6 px-2";
			case "lg":
				return displayMode === "icon" ? "size-8" : "h-8 px-3";
			case "xl":
				return displayMode === "icon" ? "size-9" : "h-9 px-3";
			default:
				return displayMode === "icon" ? "size-7" : "h-7 px-2";
		}
	};

	const getTextSize = () => {
		switch (size) {
			case "sm":
				return "text-xs";
			case "md":
				return "text-xs";
			case "lg":
				return "text-xs";
			case "xl":
				return "text-xs";
			default:
				return "text-xs";
		}
	};

	if (readonly) {
		if (!value) return null;
		
		return (
			<span
				title={value.name}
				className="inline-flex items-center justify-center"
			>
				{renderIcon(value.recordType, value.name)}
				{displayMode === "full" && (
					<span className={`ml-1 ${getTextSize()}`}>{value.name}</span>
				)}
			</span>
		);
	}

	return (
		<div className="*:not-first:mt-2" onClick={(e) => e.stopPropagation()}>
			<Popover
				open={open}
				onOpenChange={(isOpen) => {
					setOpen(isOpen);
					if (!isOpen) {
						setSearchTerm("");
					}
				}}
			>
				<PopoverTrigger asChild className="w-full">
					<Button
						id={id}
						className={`${getButtonSize()} flex items-center gap-1 !rounded-lg`}
						size="icon"
						variant="ghost"
						role="combobox"
						aria-expanded={open}
						onClick={(e) => e.stopPropagation()}
					>
						{value ? (
							<>
								{renderIcon(value.recordType, value.name)}
								{displayMode === "full" && (
									<span className={`${getTextSize()} truncate max-w-64 ${className}`}>
										{value.name}
									</span>
								)}
							</>
						) : (
							<>
								<IconLink className="h-4 w-4 text-muted-foreground" />
								{displayMode === "full" && (
									<span className={`${getTextSize()} text-muted-foreground`}>
										{placeholder}
									</span>
								)}
							</>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent
					className="w-80 p-0 border-input bg-zinc-100 dark:bg-sidebar"
					align="start"
				>
					<Command className="bg-zinc-100 dark:bg-sidebar">
						<CommandInput
							placeholder="Search contacts, companies, properties..."
							value={searchTerm}
							onValueChange={setSearchTerm}
						/>
						<CommandList className="!h-fit max-h-[300px]">
							<CommandEmpty>
								{shouldSearch && searchLoading
									? "Searching..."
									: shouldSearch && relatedObjectsToShow.length === 0
									? "No objects found."
									: "Type to search contacts, companies, and properties..."}
							</CommandEmpty>
							
							{/* Unlink option */}
							{value && (
								<CommandGroup>
									<CommandItem
										value="unlink"
										onSelect={() => handleValueChange(null)}
									>
										<IconLink className="h-4 w-4 mr-2" />
										Remove link
										{!value && (
											<IconSquareRoundedCheckFilled
												size={16}
												className="ml-auto text-blue-400"
											/>
										)}
									</CommandItem>
								</CommandGroup>
							)}
							
							{/* Show grouped results */}
							{Object.entries(groupedRelatedObjects).map(([recordType, objects]: [string, RelatedObject[]]) => (
								objects.length > 0 && (
									<div key={recordType}>
										<CommandSeparator className="my-1" />
										<CommandGroup
											heading={
												recordType === 'contact' ? 'Contacts' :
												recordType === 'company' ? 'Companies' :
												recordType === 'property' ? 'Properties' :
												'Objects'
											}
										>
											{objects.map((obj: RelatedObject) => {
												const renderIconForItem = () => {
													switch (recordType) {
														case 'contact':
															return <ContactAvatar name={obj.name} className="h-4 w-4" />;
														case 'company':
															return <IconBuilding className="h-4 w-4 mr-2" />;
														case 'property':
															return <IconMapPin className="h-4 w-4 mr-2" />;
														default:
															return <IconLink className="h-4 w-4 mr-2" />;
													}
												};
												
												const isSelected = value?.id === obj.id;
												
												return (
													<CommandItem
														key={`${obj.recordType}-${obj.id}`}
														value={`${obj.recordType}-${obj.name}`}
														onSelect={() => handleValueChange(obj)}
														className="flex items-center justify-between"
													>
														<div className="flex items-center gap-2 flex-1 min-w-0">
															{renderIconForItem()}
															<div className="flex flex-col flex-1 min-w-0">
																<span className="truncate">{obj.name}</span>
																{obj.subtitle && (
																	<span className="text-xs text-muted-foreground truncate">
																		{obj.subtitle}
																	</span>
																)}
															</div>
														</div>
														{isSelected && (
															<IconSquareRoundedCheckFilled
																size={16}
																className="ml-2 text-blue-400 flex-shrink-0"
															/>
														)}
													</CommandItem>
												);
											})}
										</CommandGroup>
									</div>
								)
							))}
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>
		</div>
	);
} 