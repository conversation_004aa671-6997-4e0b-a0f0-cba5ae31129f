import type { Favorite, FavoriteFolder } from "@repo/database/src/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";

// FAVORITES
export interface CreateFavoritePayload {
	objectId: string;
	objectType: string;
	organizationId: string;
	folderId?: string | null;
	position?: number | null;
}

export async function fetchFavorites(
	organizationId: string,
): Promise<Favorite[]> {
	const res = await fetch(`/api/favorites?organizationId=${organizationId}`);
	if (!res.ok) {
		throw new Error("Failed to fetch favorites");
	}
	return res.json();
}

export async function createFavorite(
	payload: CreateFavoritePayload,
): Promise<Favorite> {
	const safePayload = {
		...payload,
		folderId: payload.folderId ?? null,
		position: payload.position ?? null,
	};
	const res = await fetch("/api/favorites", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(safePayload),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create favorite");
	}
	return res.json();
}

export interface UpdateFavoritePayload {
	id: string;
	folderId?: string | null;
	position?: number | null;
}

export async function updateFavorite(
	payload: UpdateFavoritePayload,
): Promise<Favorite> {
	const { id, ...data } = payload;
	const safePayload = {
		...(data.folderId !== undefined && { folderId: data.folderId ?? null }),
		...(data.position !== undefined && { position: data.position ?? null }),
	};

	const res = await fetch(`/api/favorites/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(safePayload),
	});
	if (!res.ok) {
		const error = await res.json();
		console.error("❌ API error response:", error);
		throw new Error(error.error || "Failed to update favorite");
	}
	return res.json();
}

export async function deleteFavorite(id: string): Promise<void> {
	const res = await fetch(`/api/favorites/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete favorite");
	}
}

// FAVORITE FOLDERS
export async function fetchFavoriteFolders(
	organizationId: string,
): Promise<FavoriteFolder[]> {
	const res = await fetch(
		`/api/favorite-folders?organizationId=${organizationId}`,
	);
	if (!res.ok) {
		throw new Error("Failed to fetch favorite folders");
	}
	return res.json();
}

export interface CreateFavoriteFolderPayload {
	name: string;
	organizationId: string;
	isOpen?: boolean | null;
	position?: number | null;
}

export async function createFavoriteFolder(
	payload: CreateFavoriteFolderPayload,
): Promise<FavoriteFolder> {
	const safePayload = {
		...payload,
		isOpen: payload.isOpen ?? null,
		position: payload.position ?? null,
	};
	const res = await fetch("/api/favorite-folders", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(safePayload),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create favorite folder");
	}
	return res.json();
}

export interface UpdateFavoriteFolderPayload {
	id: string;
	name?: string;
	isOpen?: boolean | null;
	position?: number | null;
}

export async function updateFavoriteFolder(
	payload: UpdateFavoriteFolderPayload,
): Promise<FavoriteFolder> {
	const { id, ...data } = payload;
	const safePayload: Record<string, any> = {};
	if (data.name !== undefined) safePayload.name = data.name;
	if (data.isOpen !== undefined) safePayload.isOpen = data.isOpen;
	if (data.position !== undefined) safePayload.position = data.position;

	if (Object.keys(safePayload).length === 0) {
		throw new Error("No valid fields to update");
	}

	const res = await fetch(`/api/favorite-folders/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(safePayload),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update favorite folder");
	}
	return res.json();
}

export async function deleteFavoriteFolder(id: string): Promise<void> {
	const res = await fetch(`/api/favorite-folders/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete favorite folder");
	}
}

// Toggle favorite (create if not exists, delete if exists)
export async function toggleFavorite({
	objectId,
	objectType,
	organizationId,
}: {
	objectId: string;
	objectType: string;
	organizationId: string;
}) {
	const favorites = await fetchFavorites(organizationId);
	const existing = favorites.find((f) => f.objectId === objectId);
	if (existing) {
		await deleteFavorite(existing.id);
		return { removed: true };
	}
	await createFavorite({ objectId, objectType, organizationId });
	return { added: true };
}

export function useToggleFavorite(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: toggleFavorite,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["favorites", organizationId],
			});
		},
	});
}

// React Query hooks for optimistic updates
export function useCreateFavorite(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createFavorite,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["favorites", organizationId],
			});
		},
	});
}

export function useUpdateFavorite(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: updateFavorite,
		onMutate: async (updatedFavorite) => {
			await queryClient.cancelQueries({
				queryKey: ["favorites", organizationId],
			});
			const previousFavorites = queryClient.getQueryData([
				"favorites",
				organizationId,
			]);
			queryClient.setQueryData(
				["favorites", organizationId],
				(old: Favorite[] = []) => {
					return old.map((fav) =>
						fav.id === updatedFavorite.id
							? { ...fav, ...updatedFavorite }
							: fav,
					);
				},
			);
			return { previousFavorites };
		},
		onError: (err, newFavorite, context) => {
			if (context?.previousFavorites) {
				queryClient.setQueryData(
					["favorites", organizationId],
					context.previousFavorites,
				);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({
				queryKey: ["favorites", organizationId],
			});
		},
	});
}

export function useDeleteFavorite(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: deleteFavorite,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["favorites", organizationId],
			});
		},
	});
}

export function useCreateFavoriteFolder(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createFavoriteFolder,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["favoriteFolders", organizationId],
			});
		},
	});
}

export function useUpdateFavoriteFolder(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: updateFavoriteFolder,
		onMutate: async (updatedFolder) => {
			await queryClient.cancelQueries({
				queryKey: ["favoriteFolders", organizationId],
			});
			const previousFolders = queryClient.getQueryData([
				"favoriteFolders",
				organizationId,
			]);
			queryClient.setQueryData(
				["favoriteFolders", organizationId],
				(old: FavoriteFolder[] = []) => {
					return old.map((folder) =>
						folder.id === updatedFolder.id
							? { ...folder, ...updatedFolder }
							: folder,
					);
				},
			);
			return { previousFolders };
		},
		onError: (err, newFolder, context) => {
			if (context?.previousFolders) {
				queryClient.setQueryData(
					["favoriteFolders", organizationId],
					context.previousFolders,
				);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({
				queryKey: ["favoriteFolders", organizationId],
			});
		},
	});
}

export function useDeleteFavoriteFolder(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: deleteFavoriteFolder,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["favoriteFolders", organizationId],
			});
		},
	});
}
