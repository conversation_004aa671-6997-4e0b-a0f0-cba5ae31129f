import { useMutation, useQuery } from '@tanstack/react-query';
import { authClient } from '@repo/auth/client';

export type NotificationSettings = {
	id?: string;
	userId: string;
	emailMentions: boolean;
	emailComments: boolean;
	emailActivities: boolean;
	pushMentions: boolean;
	pushComments: boolean;
	pushActivities: boolean;
};

export type NotificationType = 'mention' | 'comment' | 'activity' | 'system';

export type MentionNotificationData = {
	activityId: string;
	mentionedBy: {
		id: string;
		name: string;
		image?: string;
	};
	organizationId: string;
	organizationName?: string;
};

export async function archiveNotification(id: string): Promise<void> {
	const res = await fetch(`/api/notifications/${id}/archive`, {
		method: "PATCH",
		credentials: "include",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to archive notification");
	}
}

export async function unarchiveNotification(id: string): Promise<void> {
	const res = await fetch(`/api/notifications/${id}/unarchive`, {
		method: "PATCH",
		credentials: "include",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to unarchive notification");
	}
}

export async function deleteNotification(id: string): Promise<void> {
	const res = await fetch(`/api/notifications/${id}`, {
		method: "DELETE",
		credentials: "include",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete notification");
	}
}

export async function markNotificationAsRead(id: string): Promise<void> {
	const res = await fetch(`/api/notifications/${id}/read`, {
		method: "PATCH",
		credentials: "include",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to mark notification as read");
	}
}

export async function createMentionNotification(data: {
	userId: string;
	mentionedById: string;
	mentionedByName: string;
	mentionedByImage?: string | null;
	activityId: string;
	organizationId: string;
	organizationName?: string;
	message?: string;
	recordType?: string;
	recordId?: string;
	replyId?: string;
}): Promise<void> {
	const res = await fetch('/api/notifications', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		credentials: 'include',
		body: JSON.stringify({
			userId: data.userId,
			organizationId: data.organizationId,
			type: 'mention',
			title: `${data.mentionedByName} mentioned you in a comment`,
			body: data.message || null,
			data: {
				activityId: data.activityId,
				mentionedBy: {
					id: data.mentionedById,
					name: data.mentionedByName,
					image: data.mentionedByImage,
				},
				organizationId: data.organizationId,
				organizationName: data.organizationName,
				message: data.message,
				recordType: data.recordType,
				recordId: data.recordId,
			},
			read: false,
			archived: false,
		}),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || 'Failed to create mention notification');
	}
	
	// Send email notification if user has email notifications enabled
	try {
		// Get user notification settings
		const notificationSettings = await getUserNotificationSettings(data.userId);
		
		// Check if email notifications for mentions are enabled
		if (notificationSettings?.emailMentions) {
			try {
				const { data: orgData, error } = await authClient.organization.getFullOrganization({
					query: {
						organizationId: data.organizationId,
					},
				});
				
				if (error) {
					console.error('[createMentionNotification] Failed to get organization data:', error.message);
					return;
				}
				
				const memberData = orgData.members?.find((member: any) => member.user?.id === data.userId);
				const email = memberData?.user?.email;
				
				if (!email) {
					console.error('[createMentionNotification] No email found for user', { userId: data.userId });
					return;
				}
				
				// Construct activity URL
				// Use a relative URL that will work in both client and server contexts
				// Use organization slug instead of ID and singular form of record type
				let activityUrl = `/app/${orgData.slug}`;
				
				if (data.recordType && data.recordId) {
					// Use singular form of record type (remove trailing 's' if present)
					const recordType = data.recordType.endsWith('s') ? 
						data.recordType.slice(0, -1) : data.recordType;
					activityUrl += `/${recordType}/${data.recordId}`;
				}
				
				// Add highlight parameter if replyId is provided
				if (data.replyId) {
					activityUrl += `?highlight=${data.replyId}`;
				}
				
				// If running in browser context, make it an absolute URL
				if (typeof window !== 'undefined') {
					activityUrl = `${window.location.origin}${activityUrl}`;
				}
				
				// Send email notification using the API endpoint in the activities router
				const emailRes = await fetch('/api/activities/mail/send', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						to: email,
						templateId: 'mentionNotification',
						context: {
							mentionedByName: data.mentionedByName,
							mentionedByImage: data.mentionedByImage,
							message: data.message || '',
							activityUrl,
							organizationName: data.organizationName || 'Relio',
						},
					}),
				});
				
				const emailResult = await emailRes.json();

				if (!emailRes.ok) {
					console.error('[createMentionNotification] Failed to send email:', emailResult);
				}
			} catch (orgError) {
				console.error('[createMentionNotification] Error fetching organization or sending email:', orgError);
			}
		}
	} catch (error) {
		console.error('[createMentionNotification] Error sending email notification:', error);
	}
}

export async function getUserNotificationSettings(userId?: string): Promise<NotificationSettings> {
	const url = userId 
		? `/api/notifications/settings?userId=${encodeURIComponent(userId)}` 
		: '/api/notifications/settings';
	
	const res = await fetch(url, {
		method: 'GET',
		credentials: 'include',
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || 'Failed to fetch notification settings');
	}

	return res.json();
}

export async function updateUserNotificationSettings(settings: Partial<NotificationSettings>): Promise<NotificationSettings> {
	const res = await fetch('/api/notifications/settings', {
		method: 'PATCH',
		headers: {
			'Content-Type': 'application/json',
		},
		credentials: 'include',
		body: JSON.stringify(settings),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || 'Failed to update notification settings');
	}

	return res.json();
}

export function useNotificationSettings(userId?: string) {
	return useQuery({
		queryKey: ['notificationSettings', userId],
		queryFn: () => getUserNotificationSettings(userId),
	});
}

export function useUpdateNotificationSettings() {
	return useMutation({
		mutationFn: updateUserNotificationSettings,
	});
}
