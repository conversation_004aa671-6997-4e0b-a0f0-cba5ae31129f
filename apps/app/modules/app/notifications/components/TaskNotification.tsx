import { StatusSelector } from "@app/shared/components/StatusSelector";
import {
	TASK_PRIORITY,
	TASK_STATUS,
	type TaskStatus,
} from "@app/shared/lib/constants";
import { ActiveOrganization } from "@repo/auth";
import type { Notification } from "@repo/database";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconArchive, IconTrash } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface NotificationData {
	assigner?: string;
	assignerId?: string;
	assignerImage?: string;
	title?: string;
	taskId?: string;
	priority?: "high" | "urgent";
	status?: string;
}

function getPriorityHex(color: string): string {
	const map: Record<string, string> = {
		"text-red-500": "#ef4444",
		"text-zinc-500": "#71717a",
		"text-zinc-400": "#a1a1aa",
	};
	return map[color] ?? "#71717a";
}

function isTaskStatus(value: any): value is TaskStatus {
	return TASK_STATUS.some((item) => item.value === value);
}

export function TaskNotification({
	organization,
	notification,
	onArchive,
	onDelete,
	asChild = false,
}: {
	organization: ActiveOrganization;
	notification: Notification & { data: NotificationData };
	onArchive?: (id: string) => void;
	onDelete?: (id: string) => void;
	asChild?: boolean;
}) {
	const [isRead, setIsRead] = useState(notification.read);
	const [isArchived, setIsArchived] = useState(
		notification.archived === true,
	);
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();
	const assigner = notification.data?.assigner || "Someone";
	const assignerImage = notification.data?.assignerImage;
	const taskTitle =
		notification.data?.title || notification.body || "New task";
	const taskId = notification.data?.taskId;
	const priority = notification.data?.priority;
	const createdAt = new Date(notification.createdAt);
	const statusRaw = notification.data?.status;
	const taskStatus: TaskStatus = isTaskStatus(statusRaw) ? statusRaw : "todo";

	const taskListUrl = taskId
		? `/app/${organization?.slug}/tasks?highlight=${taskId}`
		: undefined;

	const priorityObj = TASK_PRIORITY.find((p) => p.value === priority);
	const priorityHex = priorityObj
		? getPriorityHex(priorityObj.color)
		: getPriorityHex("text-zinc-400");

	async function handleClick(e: React.MouseEvent) {
		if (!isRead) {
			setIsRead(true);
			try {
				await fetch(`/api/notifications/${notification.id}/read`, {
					method: "PATCH",
					credentials: "include",
				});
			} catch (err) {}
		}
		if (taskListUrl) {
			e.preventDefault();
			router.push(taskListUrl);
		}
	}

	async function handleArchive(e: React.MouseEvent) {
		e.stopPropagation();
		setIsLoading(true);
		try {
			await fetch(`/api/notifications/${notification.id}/archive`, {
				method: "PATCH",
				credentials: "include",
			});
			setIsArchived(true);
			if (onArchive) onArchive(notification.id);
		} finally {
			setIsLoading(false);
		}
	}

	async function handleDelete(e: React.MouseEvent) {
		e.stopPropagation();
		setIsLoading(true);
		try {
			await fetch(`/api/notifications/${notification.id}`, {
				method: "DELETE",
				credentials: "include",
			});
			if (onDelete) onDelete(notification.id);
		} finally {
			setIsLoading(false);
		}
	}

	function handleKeyDown(e: React.KeyboardEvent) {
		if (e.key === "Enter" || e.key === " ") {
			e.preventDefault();
			handleClick(e as any);
		}
	}

	const content = (
		<div
			className={cn(
				"group flex items-center gap-2 p-2 cursor-pointer rounded-xl transition-colors border !border-transparent hover:!bg-muted/50 hover:!border hover:!border-border dark:border-zinc-700",
				!isRead
					? "bg-sky-400/10 !border dark:!border-sky-700 !border-sky-200"
					: "",
			)}
		>
			<div className="relative">
				<UserAvatar name={assigner} avatarUrl={assignerImage ?? null} />
				{priority && (
					<div
						className={cn(
							"absolute right-0 bottom-0 h-2 w-2 rounded-full",
						)}
						style={{ backgroundColor: priorityHex }}
					/>
				)}
			</div>
			<div className="flex-1">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-1">
						<span className="font-medium text-sm">{assigner}</span>
						<span className="text-muted-foreground text-sm">
							assigned you a task
						</span>
					</div>
					<span className="text-muted-foreground text-xs">
						{createdAt.toLocaleTimeString([], {
							hour: "2-digit",
							minute: "2-digit",
						})}
					</span>
				</div>
				<div className="flex items-center justify-center">
					<div className="flex items-center gap-1 text-muted-foreground text-xs w-full">
						<StatusSelector
							status={taskStatus}
							taskId={taskId ?? ""}
							readonly
						/>
						<span>{taskTitle}</span>
					</div>
					{!isArchived ? (
						<Button
							size="icon"
							variant="ghost"
							onClick={handleArchive}
							disabled={isLoading}
							className="opacity-0 group-hover:opacity-100 transition-opacity !p-0 h-6 w-6 text-muted-foreground"
							aria-label="Archive notification"
							tooltip={{
								content: "Archive",
								side: "right",
							}}
						>
							<IconArchive className="w-4 h-4" />
						</Button>
					) : (
						<Button
							size="icon"
							tooltip={{
								content: "Delete",
								side: "right",
							}}
							variant="ghost"
							onClick={handleDelete}
							disabled={isLoading}
							className="opacity-0 group-hover:opacity-100 transition-opacity !p-0 h-6 w-6 text-red-500 hover:text-red-600 hover:bg-red-500/10"
							aria-label="Delete notification"
						>
							<IconTrash className="w-4 h-4" />
						</Button>
					)}
				</div>
			</div>
		</div>
	);

	if (asChild) {
		return content;
	}

	if (taskListUrl) {
		return (
			<button
				type="button"
				aria-label={`Go to task: ${taskTitle}`}
				onClick={handleClick}
				className="w-full text-left bg-transparent border-none p-0 m-0"
			>
				{content}
			</button>
		);
	}

	return (
		<button
			type="button"
			aria-label={taskTitle}
			onClick={handleClick}
			className="w-full text-left bg-transparent border-none p-0 m-0"
		>
			{content}
		</button>
	);
}
