"use client";

import { UserAvatar } from "@shared/components/UserAvatar";
import { formatDistanceToNow } from "date-fns";
import { MessageSquare } from "lucide-react";
import { cn } from "@ui/lib";
import { IconBrandLine, IconArchive, IconTrash, IconArchiveOff } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { ActiveOrganization } from "@repo/auth";
import { Button } from "@ui/components/button";
import AlertDialog from "@app/shared/components/AlertDialog";

type MentionNotificationProps = {
	notification: {
		id: string;
		title: string;
		body?: string;
		createdAt: string;
		updatedAt: string;
		read: boolean;
		archived?: boolean;
		data: {
			mentionedBy: {
				id: string;
				name: string;
				image?: string;
			};
			message?: string;
			activityId: string;
			recordType?: string;
			recordId?: string;
		};
	};
	onArchive?: (id: string) => void;
	onUnarchive?: (id: string) => void;
	onDelete?: (id: string) => void;
	onMarkAsRead?: (id: string) => void;
	organization?: ActiveOrganization
};

const MentionNotification = ({
	notification,
	onArchive,
	onUnarchive,
	onDelete,
	onMarkAsRead,
	organization
}: MentionNotificationProps) => {
	const router = useRouter();
	const { data, createdAt, updatedAt, read } = notification;
	const mentionedBy = data.mentionedBy;
	const messageContent = notification.body || data.message;
	const [isArchived, setIsArchived] = useState(notification.archived === true);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);

	const handleNotificationClick = () => {
		if (!read && onMarkAsRead) {
			onMarkAsRead(notification.id);
		}

		if (data.recordType && data.recordId) {
			const recordPath = `/app/${organization?.slug}/${data.recordType}/${data.recordId}?highlight=${data.activityId}`;
			router.push(recordPath);
		}
	};

	const renderMessageContent = (content: string) => {
		if (!content) return null;

		const enhancedContent = content
			.replace(
				/<span([^>]*)(data-mention="true"|class="mention")([^>]*)>/gi,
				'<span$1$2$3 class="text-blue-600 bg-blue-600/10 px-1 py-0.5 rounded font-medium">'
			)
			.replace(/@([\w\s]+)/g, '<span class="text-blue-600 bg-blue-600/10 px-1 py-0.5 rounded font-medium">@$1</span>');

		return (
			<div 
				className="text-sm text-foreground leading-5 whitespace-pre-wrap break-words"
				dangerouslySetInnerHTML={{ __html: enhancedContent }}
			/>
		);
	};

	const handleArchive = async (e: React.MouseEvent) => {
		e.stopPropagation();
		if (onArchive) {
			try {
				setIsArchived(true); // Set local state immediately to prevent UI flicker
				await fetch(`/api/notifications/${notification.id}/archive`, {
					method: "PATCH",
					credentials: "include",
				});
				onArchive(notification.id);
			} catch (error) {
				setIsArchived(false); // Revert if the API call fails
				console.error('Failed to archive notification:', error);
			}
		}
	};

	const handleDeleteClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		setShowDeleteDialog(true);
	};

	const handleConfirmDelete = async () => {
		if (onDelete) {
			setIsDeleting(true);
			try {
				await fetch(`/api/notifications/${notification.id}`, {
					method: "DELETE",
					credentials: "include",
				});
				onDelete(notification.id);
			} catch (error) {
				console.error('Failed to delete notification:', error);
			} finally {
				setIsDeleting(false);
				setShowDeleteDialog(false);
			}
		}
	};

	if ((isArchived && !notification.archived) || (!isArchived && notification.archived)) {
		return null;
	}

	return (
		<div
			onClick={handleNotificationClick}
			className={cn(
				"group flex overflow-hidden flex-row p-3 border border-border rounded-2xl cursor-pointer transition-all duration-200 relative",
				!read && "bg-muted/30",
				"hover:bg-muted/50",
			)}
		>
			<div className="relative h-8 w-8 flex-shrink-0">
				<UserAvatar
					name={mentionedBy.name}
					avatarUrl={mentionedBy.image}
					className="h-8 w-8 rounded-full"
				/>
				<div className="absolute -bottom-1 -right-1 rounded-full border-2 border-background flex items-center justify-center">
					<div className="h-4 w-4 rounded-full bg-blue-600 flex items-center justify-center">
						<IconBrandLine className="h-2.5 w-2.5 text-white" />
					</div>
				</div>
			</div>

			<div className="ml-3 flex flex-col overflow-hidden flex-1">
				<div className="text-sm font-medium text-muted-foreground pb-1.5 overflow-wrap break-word">
					<span className="text-primary font-medium">{mentionedBy.name}</span>{" "}
					mentioned you in a comment.
				</div>

				{!isArchived && <div className="flex items-center text-xs font-medium text-muted-foreground mb-4">
					{formatDistanceToNow(new Date(createdAt), { addSuffix: true })}
				</div>}
				{isArchived && (
					<div className="flex flex-col gap-1 mb-4">
						<div className="flex items-center text-xs font-medium text-muted-foreground">
							Created {formatDistanceToNow(new Date(createdAt), { addSuffix: true })}
						</div>
						<div className="flex items-center text-xs font-medium text-muted-foreground">
							Archived {formatDistanceToNow(new Date(updatedAt), { addSuffix: true })}
						</div>
					</div>
				)}
				{messageContent && (
					<div className="pl-4 border-l-2 border-border">
						<div className="min-w-0">
							<div
								className={cn(
									"text-sm text-primary font-medium break-words whitespace-pre-wrap",
									"max-h-30 overflow-hidden"
								)
							}
						>
							{renderMessageContent(messageContent)}
						</div>
					</div>
					</div>
				)}
			</div>

			{read && onArchive && !notification.archived && (
				<div className="absolute top-2 right-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
					<div className="flex items-center gap-1 border border-border rounded-lg p-0.5 bg-sidebar shadow-sm">
						<Button
							size="icon"
							variant="ghost"
							onClick={handleArchive}
							className="h-6 w-6 p-1.5 hover:bg-muted"
							aria-label="Archive notification"
							tooltip={{
								content: "Archive",
								side: "left",
							}}
						>
							<IconArchive className="w-4 h-4" />
						</Button>
					</div>
				</div>
			)}
			
			{notification.archived && onDelete && (
				<div className="absolute top-2 right-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
					<div className="flex items-center gap-1 border border-border rounded-lg p-0.5 bg-sidebar shadow-sm">
						<Button
							size="icon"
							variant="ghost"
							onClick={handleDeleteClick}
							className="h-6 w-6 p-1.5 hover:bg-muted text-red-500 hover:text-red-600 hover:bg-red-500/10"
								aria-label="Delete notification"
								tooltip={{
									content: "Delete",
									side: "left",
								}}
							>
								<IconTrash className="w-4 h-4" />
							</Button>
						</div>
				</div>
			)}
			
			<AlertDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				title="Delete Notification"
				description="Are you sure you want to delete this notification? This action cannot be undone."
				confirmLabel="Delete"
				cancelLabel="Cancel"
				confirmClassName="bg-destructive text-destructive-foreground hover:bg-destructive/90"
				onConfirm={handleConfirmDelete}
				loading={isDeleting}
			/>
		</div>
	);
};

export default MentionNotification;
