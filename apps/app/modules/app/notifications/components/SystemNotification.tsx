import type { Organization } from "@repo/database";
import { TaskNotification } from "./TaskNotification";
import MentionNotification from "./MentionNotification";
import { ActiveOrganization } from "@repo/auth";

type JoinNotificationProps = {
	notification: any;
	organization: ActiveOrganization;
	onArchive?: (id: string) => void;
	onUnarchive?: (id: string) => void;
	onDelete?: (id: string) => void;
	onMarkAsRead?: (id: string) => void;
};

const SystemNotification = ({
	notification,
	organization,
	onArchive,
	onUnarchive,
	onDelete,
	onMarkAsRead,
}: JoinNotificationProps) => {
	if (notification.type === "task_assigned") {
		return (
			<TaskNotification
				notification={notification}
				organization={organization as ActiveOrganization}
				onArchive={onArchive}
				onDelete={onDelete}
				asChild
			/>
		);
	}

	if (notification.type === "mention") {
		return (
			<MentionNotification
				notification={notification}
				onArchive={onArchive}
				onUnarchive={onUnarchive}
				onDelete={onDelete}
				onMarkAsRead={onMarkAsRead}
				organization={organization as ActiveOrganization}
			/>
		);
	}

	return (
		<div className="p-2 py-4 max-w-sm border-b border-zinc-800 relative">
			<p className="text-sm font-medium dark:text-zinc-200">
				{notification.body ||
					notification.message ||
					"You have a new notification."}
			</p>
		</div>
	);
};

export default SystemNotification;
