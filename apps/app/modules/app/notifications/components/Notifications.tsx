"use client";

import { useSession } from "@app/auth/hooks/use-session";
import {
	archiveNotification,
	deleteNotification,
	markNotificationAsRead,
	unarchiveNotification,
} from "@app/notifications/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconBell } from "@tabler/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { NumberBadge } from "@ui/components/badge";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@ui/components/sidebar";
import {
	Tabs,
	TabsContent,
	TabsListAlt,
	TabsTriggerAlt,
} from "@ui/components/tabs";
import { cn } from "@ui/lib";
import Image from "next/image";
import { useEffect, useState } from "react";
import JoinNotification from "./JoinNotification";
import SystemNotification from "./SystemNotification";

const queryKey = ["notifications"];

const Notifications = () => {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const [isOpen, setIsOpen] = useState(false);
	const queryClient = useQueryClient();
	const sidebar = useSidebar();
	const isCollapsed = sidebar?.state === "collapsed";

	const {
		data: notifications = [],
		isLoading,
		isError,
	} = useQuery({
		queryKey,
		queryFn: async () => {
			const res = await fetch("/api/notifications", {
				credentials: "include",
			});

			if (!res.ok) {
				throw new Error(`Failed to fetch notifications: ${res.status}`);
			}

			const data = await res.json();

			// Ensure we always return an array
			if (!Array.isArray(data)) {
				console.error(
					"Notifications API returned non-array data:",
					data,
				);
				return [];
			}

			return data;
		},
		enabled: !!user,
	});

	const archiveMutation = useMutation({
		mutationFn: archiveNotification,
		onMutate: async (id: string) => {
			await queryClient.cancelQueries({ queryKey });
			const previous = queryClient.getQueryData(queryKey);
			queryClient.setQueryData(queryKey, (old: any[] = []) =>
				old.map((n) =>
					n.id === id || n.id === id ? { ...n, archived: true } : n,
				),
			);
			return { previous };
		},
		onError: (err, id, context) => {
			if (context?.previous) {
				queryClient.setQueryData(queryKey, context.previous);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});

	const unarchiveMutation = useMutation({
		mutationFn: unarchiveNotification,
		onMutate: async (id: string) => {
			await queryClient.cancelQueries({ queryKey });
			const previous = queryClient.getQueryData(queryKey);
			queryClient.setQueryData(queryKey, (old: any[] = []) =>
				old.map((n) =>
					n.id === id || n.id === id ? { ...n, archived: false } : n,
				),
			);
			return { previous };
		},
		onError: (err, id, context) => {
			if (context?.previous) {
				queryClient.setQueryData(queryKey, context.previous);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});

	const deleteMutation = useMutation({
		mutationFn: deleteNotification,
		onMutate: async (id: string) => {
			await queryClient.cancelQueries({ queryKey });
			const previous = queryClient.getQueryData(queryKey);
			queryClient.setQueryData(queryKey, (old: any[] = []) =>
				old.filter((n) => n.id !== id && n.id !== id),
			);
			return { previous };
		},
		onError: (err, id, context) => {
			if (context?.previous) {
				queryClient.setQueryData(queryKey, context.previous);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});

	const markAsReadMutation = useMutation({
		mutationFn: markNotificationAsRead,
		onMutate: async (id: string) => {
			await queryClient.cancelQueries({ queryKey });
			const previous = queryClient.getQueryData(queryKey);
			queryClient.setQueryData(queryKey, (old: any[] = []) =>
				old.map((n) =>
					n.id === id ? { ...n, read: true } : n,
				),
			);
			return { previous };
		},
		onError: (err, id, context) => {
			if (context?.previous) {
				queryClient.setQueryData(queryKey, context.previous);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});

	// Ensure notifications is always an array
	const safeNotifications = Array.isArray(notifications) ? notifications : [];

	const unreadCount = safeNotifications.filter(
		(n: any) => !n.read && !n.archived,
	).length;
	const requests = [] as any[];
	const archived = [] as any[];

	const inboxNotifications = safeNotifications.filter(
		(n: any) => n.archived !== true,
	);
	const archivedNotifications = safeNotifications.filter(
		(n: any) => n.archived === true,
	);

	function handleArchive(id: string) {
		archiveMutation.mutate(id);
	}
	function handleUnarchive(id: string) {
		unarchiveMutation.mutate(id);
	}
	function handleDelete(id: string) {
		deleteMutation.mutate(id);
	}
	function handleMarkAsRead(id: string) {
		markAsReadMutation.mutate(id);
	}

	return (
		<SidebarMenu>
			<SidebarMenuItem
				className={cn("w-full", isCollapsed && "justify-center")}
			>
				<Popover open={isOpen} onOpenChange={setIsOpen}>
					<PopoverTrigger className="w-full" asChild>
						<SidebarMenuButton
							tooltip={"Notifications"}
							className={cn(
								"!w-full flex items-center justify-between",
								isOpen &&
									"bg-muted/50 text-sidebar-accent-foreground !border !border-border",
								isCollapsed && "justify-center p-0 h-9 w-9",
							)}
						>
							<div
								className={cn(
									"flex items-center gap-2 w-full",
									isCollapsed && "justify-center",
								)}
							>
								<IconBell
									className={cn(
										"size-4 text-muted-foreground",
										isCollapsed && "mx-auto",
									)}
								/>
								{!isCollapsed && <span>Notifications</span>}
							</div>
							{/* Expanded State */}
							{!isCollapsed &&
								(unreadCount > 0 || requests.length > 0) && (
									<div className="flex items-center">
										<NumberBadge
											number={
												unreadCount + requests.length
											}
											color="blue"
										/>
									</div>
								)}
							{/* Collapsed State */}
							{isCollapsed &&
								(unreadCount > 0 || requests.length > 0) && (
									<div className="absolute left-1 top-1 z-20 size-3 text-[8px] text-accent-foreground w-fit px-0.5 font-medium font-mono rounded border flex items-center justify-center bg-blue-200 dark:bg-blue-700 border-blue-300 dark:border-blue-600 text-blue-900 dark:text-blue-100">
										{unreadCount + requests.length}
									</div>
								)}
						</SidebarMenuButton>
					</PopoverTrigger>
					<PopoverContent
						side="right"
						align="end"
						className="ml-2 !min-h-[600px] !max-h-[800px] w-[400px] !max-w-[400px] relative top-14 z-50"
					>
						<div className="flex flex-row items-center justify-between">
							<h2 className={"text-xl"}>Notifications</h2>
							{/* {notifications.length > 0 && notifications.some(notification => !notification.read) && (
								<span
									className="text-xs text-muted-foreground hover:underline cursor-pointer"
									onClick={() => markAllAsRead({
										orgId: organization?.id as Id<"organization">,
										userId: user?.id as Id<"users">
									})}
								>
									Mark all as read
								</span>
							)} */}
						</div>
						<Tabs defaultValue="inbox">
							<TabsListAlt>
								<TabsTriggerAlt value="inbox">
									Inbox ({unreadCount})
								</TabsTriggerAlt>
								<TabsTriggerAlt value="request">
									Requests ({requests.length})
								</TabsTriggerAlt>
								<TabsTriggerAlt value="archived">
									Archived ({archivedNotifications?.length})
								</TabsTriggerAlt>
							</TabsListAlt>
							<TabsContent
								value="inbox"
								className="border-t dark:border-zinc-800 border-zinc-200 overflow-y-auto h-[500px]"
							>
								{isLoading ? (
									<div className="p-4 text-center text-muted-foreground">
										Loading...
									</div>
								) : isError ? (
									<div className="p-4 text-center text-red-500">
										Failed to load notifications
									</div>
								) : inboxNotifications.length === 0 ? (
									<div className="flex flex-col space-y-4 items-center justify-center p-4">
										<Image
											src="/images/no-notifications.png"
											width={250}
											height={250}
											alt="No Notifications"
										/>
										<div className="flex flex-col items-center">
											<span>No Notifications</span>
											<span className="text-sm text-muted-foreground text-center">
												We&apos;ll notify you here about
												activities linked to your
												account.
											</span>
										</div>
									</div>
								) : (
									activeOrganization && (
										<div className="space-y-2 first:mt-1">
											{inboxNotifications.map(
												(notification: any) => (
													<SystemNotification
														key={notification.id}
														notification={
															notification
														}
														organization={
															activeOrganization as any
														}
														onArchive={
															handleArchive
														}
														onDelete={handleDelete}
														onMarkAsRead={handleMarkAsRead}
													/>
												),
											)}
										</div>
									)
								)}
							</TabsContent>
							<TabsContent
								value="request"
								className="border-t dark:border-zinc-800 border-zinc-200  overflow-y-auto h-[500px]"
							>
								{requests.length === 0 ? (
									<div className="flex space-y-4 items-center justify-center flex-col p-4">
										<Image
											src="/images/no-notifications.png"
											width={250}
											height={250}
											alt="No Requests"
										/>
										<div className="flex flex-col items-center justify-center align-center">
											<span>No Requests</span>
											<span className="text-sm text-muted-foreground text-center">
												We&lsquo;ll notify you here
												about requests from other users.
											</span>
										</div>
									</div>
								) : (
									requests.map((request) => (
										<JoinNotification
											key={request.id}
											notification={request}
										/>
									))
								)}
							</TabsContent>
							<TabsContent
								value="archived"
								className="border-t dark:border-zinc-800 border-zinc-200 overflow-y-auto h-[500px]"
							>
								{archivedNotifications.length === 0 ? (
									<div className="flex space-y-4 items-center justify-center flex-col p-4">
										<Image
											src="/images/no-notifications.png"
											width={250}
											height={250}
											alt="No Archived Notifications"
										/>
										<div className="flex flex-col items-center justify-center align-center">
											<span>
												No Archived Notifications
											</span>
											<span className="text-sm text-muted-foreground">
												You have no archived
												notifications.
											</span>
										</div>
									</div>
								) : (
									activeOrganization && (
										<div className="space-y-2 first:mt-1">
											{archivedNotifications.map(
												(notification: any) => (
													<SystemNotification
														key={notification.id}
														notification={{
															...notification,
															archived: true // Explicitly mark as archived for UI rendering
														}}
														organization={
															activeOrganization as any
														}
														onArchive={
															handleArchive
														}
														onUnarchive={
															handleUnarchive
														}
														onDelete={handleDelete}
														onMarkAsRead={handleMarkAsRead}
													/>
												),
											)}
										</div>
									)
								)}
							</TabsContent>
						</Tabs>
					</PopoverContent>
				</Popover>
			</SidebarMenuItem>
		</SidebarMenu>
	);
};

export default Notifications;
