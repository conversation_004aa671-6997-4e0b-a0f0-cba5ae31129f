"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import {
	IconFile,
	IconFolder,
	IconSearch,
	IconUpload,
	IconX,
} from "@tabler/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import FileUpload from "./FileUpload";

interface File {
	id: string;
	name: string;
	originalName: string;
	size: number;
	mimeType: string;
	fileType: string;
	url: string;
	thumbnailUrl?: string;
	folderId: string | null;
	createdAt: string;
	uploader: {
		id: string;
		name: string;
		image?: string;
	};
}

interface AttachFileModalProps {
	isOpen: boolean;
	onClose: () => void;
	attachToId: string;
	attachToType: "contact" | "company" | "property";
	attachToName?: string;
}

const AttachFileModal = ({
	isOpen,
	onClose,
	attachToId,
	attachToType,
	attachToName,
}: AttachFileModalProps) => {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
	const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);

	// Fetch available files
	const { data: files = [], isLoading } = useQuery({
		queryKey: ["files", activeOrganization?.id, currentFolderId, searchQuery],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const params = new URLSearchParams({
				organizationId: activeOrganization.id,
			});
			if (currentFolderId) {
				params.append("folderId", currentFolderId);
			}
			if (searchQuery) {
				params.append("search", searchQuery);
			}
			const response = await fetch(`/api/files?${params}`);
			if (!response.ok) throw new Error("Failed to fetch files");
			return response.json();
		},
		enabled: !!activeOrganization?.id && isOpen,
	});

	// Attach files mutation
	const attachFilesMutation = useMutation({
		mutationFn: async (fileIds: string[]) => {
			const response = await fetch("/api/attachments/bulk", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					fileIds: fileIds,
					attachedToId: attachToId,
					attachedToType: attachToType,
					organizationId: activeOrganization?.id,
				}),
			});

			if (!response.ok) throw new Error("Failed to attach files");
			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-attachments", activeOrganization?.id, attachToId, attachToType],
			});
			toast.success(`${selectedFiles.length} file(s) attached successfully`);
			onClose();
			setSelectedFiles([]);
		},
		onError: (error) => {
			console.error("Failed to attach files:", error);
			toast.error("Failed to attach files");
		},
	});

	const handleFileSelect = (fileId: string) => {
		setSelectedFiles(prev =>
			prev.includes(fileId)
				? prev.filter(id => id !== fileId)
				: [...prev, fileId]
		);
	};

	const handleAttachFiles = () => {
		if (selectedFiles.length === 0) {
			toast.error("Please select at least one file to attach");
			return;
		}
		attachFilesMutation.mutate(selectedFiles);
	};

	const handleUploadComplete = (uploadedFiles: any[]) => {
		// Files are automatically attached during upload, so refresh the attachments list
		queryClient.invalidateQueries({
			queryKey: ["file-attachments", activeOrganization?.id, attachToId, attachToType],
		});
		
		// Refresh the files list
		queryClient.invalidateQueries({
			queryKey: ["files", activeOrganization?.id, currentFolderId],
		});
		
		// Show success message and close modal since upload + attachment is complete
		toast.success(`${uploadedFiles.length} file(s) uploaded and attached successfully`);
		onClose();
		setSelectedFiles([]);
	};

	const formatFileSize = (bytes: number) => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	};

	const getFileIcon = (fileType: string) => {
		switch (fileType) {
			case "images":
				return "🖼️";
			case "documents":
				return "📄";
			case "videos":
				return "🎥";
			case "audio":
				return "🎵";
			case "archives":
				return "📦";
			default:
				return "📄";
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
				<DialogHeader>
					<DialogTitle>Attach Files</DialogTitle>
					<DialogDescription>
						Attach files to {attachToName || `this ${attachToType}`}
					</DialogDescription>
				</DialogHeader>

				<Tabs defaultValue="existing" className="flex-1 flex flex-col min-h-0">
					<TabsList className="grid w-full grid-cols-2">
						<TabsTrigger value="existing">Existing Files</TabsTrigger>
						<TabsTrigger value="upload">Upload New</TabsTrigger>
					</TabsList>

					<TabsContent value="existing" className="flex-1 flex flex-col min-h-0 mt-4">
						<div className="flex items-center gap-4 mb-4">
							<div className="relative flex-1">
								<IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder="Search files..."
									value={searchQuery}
									onChange={(e) => setSearchQuery(e.target.value)}
									className="pl-10"
								/>
							</div>
							<div className="text-sm text-muted-foreground">
								{selectedFiles.length} selected
							</div>
						</div>

						<div className="flex-1 overflow-y-auto border rounded-lg">
							{isLoading ? (
								<div className="p-4 space-y-3">
									{Array.from({ length: 5 }).map((_, i) => (
										<div key={i} className="flex items-center gap-3">
											<Skeleton className="h-4 w-4" />
											<Skeleton className="h-10 w-10" />
											<div className="flex-1 space-y-2">
												<Skeleton className="h-4 w-3/4" />
												<Skeleton className="h-3 w-1/2" />
											</div>
										</div>
									))}
								</div>
							) : files.length === 0 ? (
								<div className="flex flex-col items-center justify-center h-32 text-center p-4">
									<IconFile className="h-8 w-8 text-muted-foreground mb-2" />
									<p className="text-sm text-muted-foreground">
										{searchQuery ? "No files match your search" : "No files found"}
									</p>
								</div>
							) : (
								<div className="divide-y">
									{files.map((file: File) => (
										<div
											key={file.id}
											onClick={() => handleFileSelect(file.id)}
											className={cn(
												"flex items-center gap-3 p-3 cursor-pointer hover:bg-muted/50 transition-colors",
												selectedFiles.includes(file.id) && "bg-primary/10"
											)}
										>
											<input
												type="checkbox"
												checked={selectedFiles.includes(file.id)}
												onChange={() => handleFileSelect(file.id)}
												className="rounded"
											/>
											{file.thumbnailUrl ? (
												<img
													src={file.thumbnailUrl}
													alt={file.name}
													className="h-10 w-10 object-cover rounded border"
												/>
											) : (
												<div className="h-10 w-10 flex items-center justify-center text-xl border rounded">
													{getFileIcon(file.fileType)}
												</div>
											)}
											<div className="flex-1 min-w-0">
												<div className="font-medium truncate">{file.name}</div>
												<div className="text-sm text-muted-foreground">
													{formatFileSize(file.size)} • {file.uploader.name}
												</div>
											</div>
										</div>
									))}
								</div>
							)}
						</div>
					</TabsContent>

					<TabsContent value="upload" className="flex-1 mt-4">
						<FileUpload
							onUploadComplete={handleUploadComplete}
							attachToId={attachToId}
							attachToType={attachToType}
							folderId={currentFolderId}
							maxFiles={10}
						/>
					</TabsContent>
				</Tabs>

				<DialogFooter>
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleAttachFiles}
						disabled={selectedFiles.length === 0 || attachFilesMutation.isPending}
					>
						{attachFilesMutation.isPending
							? "Attaching..."
							: `Attach ${selectedFiles.length} File${selectedFiles.length !== 1 ? "s" : ""}`}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default AttachFileModal;
