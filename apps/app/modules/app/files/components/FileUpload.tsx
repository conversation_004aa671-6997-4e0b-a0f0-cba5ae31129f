"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import { cn } from "@ui/lib";
import {
	IconFile,
	IconUpload,
	IconX,
	IconCheck,
	IconAlertCircle,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { useEdgeStore } from "@repo/storage";

interface FileUploadProps {
	onUploadComplete?: (files: any[]) => void;
	attachToId?: string;
	attachToType?: "contact" | "company" | "property";
	folderId?: string | null;
	maxFiles?: number;
	maxSize?: number; // in bytes
	acceptedFileTypes?: string[];
	className?: string;
}

interface UploadingFile {
	id: string;
	file: File;
	progress: number;
	status: "uploading" | "success" | "error";
	error?: string;
	uploadedFile?: any;
}

const FileUpload = ({
	onUploadComplete,
	attachToId,
	attachToType,
	folderId,
	maxFiles = 10,
	maxSize = 50 * 1024 * 1024, // 50MB default
	acceptedFileTypes,
	className,
}: FileUploadProps) => {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();
	const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
	const { edgestore } = useEdgeStore();

	// Helper to determine EdgeStore bucket based on file type
	const getEdgeStoreBucket = (file: File) => {
		const type = file.type.toLowerCase();
		if (type.startsWith("image/")) return "images";
		if (type.includes("pdf") || type.includes("document") || type.includes("text") || type.includes("spreadsheet") || type.includes("presentation")) return "documents";
		if (type.startsWith("video/")) return "videos";
		if (type.startsWith("audio/")) return "audio";
		if (type.includes("zip") || type.includes("rar") || type.includes("tar") || type.includes("gzip")) return "archives";
		return "other";
	};

	// Upload mutation
	const uploadMutation = useMutation({
		mutationFn: async ({ file, fileId }: { file: File; fileId: string }) => {
			// Step 1: Upload to EdgeStore
			const bucket = getEdgeStoreBucket(file) as keyof typeof edgestore;
			const edgeStoreResult = await edgestore[bucket].upload({
				file,
				onProgressChange: (progress) => {
					setUploadingFiles(prev =>
						prev.map(f =>
							f.id === fileId
								? { ...f, progress: Math.min(progress, 90) }
								: f
						)
					);
				},
			});

			// Step 2: Create File record in database
			const fileData: any = {
				name: file.name,
				originalName: file.name,
				size: file.size,
				mimeType: file.type,
				url: edgeStoreResult.url,
				edgeStoreUrl: edgeStoreResult.url,
				folderId: folderId || null,
				organizationId: activeOrganization?.id,
				isPublic: false,
			};

			// Add thumbnail URL if available (only for images)
			if (bucket === "images" && "thumbnailUrl" in edgeStoreResult) {
				fileData.thumbnailUrl = (edgeStoreResult as any).thumbnailUrl;
			}

			const fileResponse = await fetch("/api/files", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(fileData),
			});

			if (!fileResponse.ok) {
				throw new Error("Failed to create file record");
			}

			const uploadedFile = await fileResponse.json();

			// Step 3: If we need to attach to a record, do that now
			if (attachToId && attachToType) {
				const attachResponse = await fetch("/api/attachments", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						fileId: uploadedFile.id,
						attachedToId: attachToId,
						attachedToType: attachToType,
						organizationId: activeOrganization?.id,
					}),
				});

				if (!attachResponse.ok) {
					throw new Error("Failed to attach file");
				}
			}

			return uploadedFile;
		},
		onSuccess: (uploadedFile, { fileId }) => {
			setUploadingFiles(prev =>
				prev.map(f =>
					f.id === fileId
						? { ...f, status: "success", progress: 100, uploadedFile }
						: f
				)
			);
		},
		onError: (error, { fileId }) => {
			setUploadingFiles(prev =>
				prev.map(f =>
					f.id === fileId
						? { ...f, status: "error", error: error.message }
						: f
				)
			);
		},
	});

	const onDrop = useCallback(
		(acceptedFiles: File[]) => {
			const newFiles: UploadingFile[] = acceptedFiles.map(file => ({
				id: Math.random().toString(36).substr(2, 9),
				file,
				progress: 0,
				status: "uploading",
			}));

			setUploadingFiles(prev => [...prev, ...newFiles]);

			// Start uploads
			newFiles.forEach(({ file, id }) => {
				uploadMutation.mutate({ file, fileId: id });
			});
		},
		[uploadMutation]
	);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		maxFiles,
		maxSize,
		accept: acceptedFileTypes
			? acceptedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {})
			: undefined,
	});

	const removeFile = (fileId: string) => {
		setUploadingFiles(prev => prev.filter(f => f.id !== fileId));
	};

	const clearCompleted = () => {
		const completedFiles = uploadingFiles.filter(f => f.status === "success");
		if (completedFiles.length > 0 && onUploadComplete) {
			onUploadComplete(completedFiles.map(f => f.uploadedFile));
		}
		setUploadingFiles(prev => prev.filter(f => f.status === "uploading"));
		
		// Invalidate queries to refresh file lists
		if (attachToId && attachToType) {
			queryClient.invalidateQueries({
				queryKey: ["file-attachments", activeOrganization?.id, attachToId, attachToType],
			});
		}
		queryClient.invalidateQueries({
			queryKey: ["files", activeOrganization?.id],
		});
	};

	const formatFileSize = (bytes: number) => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	};

	const hasCompletedFiles = uploadingFiles.some(f => f.status === "success");

	return (
		<div className={cn("space-y-4", className)}>
			{/* Drop Zone */}
			<Card>
				<CardContent className="p-6">
					<div
						{...getRootProps()}
						className={cn(
							"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
							isDragActive
								? "border-primary bg-primary/5"
								: "border-muted-foreground/25 hover:border-muted-foreground/50"
						)}
					>
						<input {...getInputProps()} />
						<IconUpload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
						{isDragActive ? (
							<p className="text-lg font-medium">Drop files here...</p>
						) : (
							<div>
								<p className="text-lg font-medium mb-2">
									Drag & drop files here, or click to select
								</p>
								<p className="text-sm text-muted-foreground">
									Maximum {maxFiles} files, up to {formatFileSize(maxSize)} each
								</p>
								{acceptedFileTypes && (
									<p className="text-xs text-muted-foreground mt-1">
										Accepted: {acceptedFileTypes.join(", ")}
									</p>
								)}
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Upload Progress */}
			{uploadingFiles.length > 0 && (
				<Card>
					<CardContent className="p-4">
						<div className="flex items-center justify-between mb-4">
							<h4 className="font-medium">
								Uploading {uploadingFiles.length} file{uploadingFiles.length !== 1 ? "s" : ""}
							</h4>
							{hasCompletedFiles && (
								<Button variant="outline" size="sm" onClick={clearCompleted}>
									<IconCheck className="h-4 w-4 mr-1" />
									Clear Completed
								</Button>
							)}
						</div>
						<div className="space-y-3">
							{uploadingFiles.map(file => (
								<div key={file.id} className="flex items-center gap-3">
									<div className="flex-shrink-0">
										{file.status === "success" ? (
											<IconCheck className="h-5 w-5 text-green-500" />
										) : file.status === "error" ? (
											<IconAlertCircle className="h-5 w-5 text-red-500" />
										) : (
											<IconFile className="h-5 w-5 text-muted-foreground" />
										)}
									</div>
									<div className="flex-1 min-w-0">
										<div className="flex items-center justify-between">
											<p className="text-sm font-medium truncate">{file.file.name}</p>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => removeFile(file.id)}
												className="h-6 w-6 p-0"
											>
												<IconX className="h-3 w-3" />
											</Button>
										</div>
										<div className="flex items-center gap-2">
											<Progress value={file.progress} className="flex-1 h-2" />
											<span className="text-xs text-muted-foreground">
												{file.status === "success"
													? "Complete"
													: file.status === "error"
													? "Failed"
													: `${file.progress}%`}
											</span>
										</div>
										{file.error && (
											<p className="text-xs text-red-500 mt-1">{file.error}</p>
										)}
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
};

export default FileUpload;
