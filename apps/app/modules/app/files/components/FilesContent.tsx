"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import {
	IconFile,
	IconFolder,
	IconPlus,
	IconSearch,
	IconFolderOpen,
	IconFolderPlus,
	IconDotsVertical,
	IconPencil,
	IconTrash,
	IconEdit,
	IconPhoto,
	IconVideo,
	IconMusic,
	IconArchive,
	IconDownload,
} from "@tabler/icons-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import AttachFileModal from "./AttachFileModal";
import {
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	PointerSensor,
	useSensor,
	useSensors,
	useDroppable,
} from "@dnd-kit/core";
import { SortableContext, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { AnimatePresence, motion } from "framer-motion";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
	DropdownMenuSeparator,
} from "@ui/components/dropdown-menu";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import { Badge } from "@ui/components/badge";

interface FileAttachment {
	id: string;
	fileId: string;
	attachedToId: string;
	attachedToType: string;
	createdAt: string;
	file: {
		id: string;
		name: string;
		originalName: string;
		size: number;
		mimeType: string;
		fileType: string;
		extension?: string;
		url: string;
		thumbnailUrl?: string;
		isDeleted: boolean;
		folderId?: string;
	};
	attacher: {
		id: string;
		name: string;
		image?: string;
	};
}

interface FileFolder {
	id: string;
	name: string;
	description?: string;
	parentId?: string;
	isOpen: boolean;
	createdAt: string;
}

interface FilesContentProps {
	objectId: string;
	objectType: "contact" | "company" | "property";
	objectName?: string;
}

const getFileIcon = (fileType: string) => {
	switch (fileType) {
		case "images":
			return <IconPhoto className="h-4 w-4" />;
		case "documents":
			return <IconFile className="h-4 w-4" />;
		case "videos":
			return <IconVideo className="h-4 w-4" />;
		case "audio":
			return <IconMusic className="h-4 w-4" />;
		case "archives":
			return <IconArchive className="h-4 w-4" />;
		default:
			return <IconFile className="h-4 w-4" />;
	}
};

const formatFileSize = (bytes: number) => {
	if (bytes === 0) return "0 Bytes";
	const k = 1024;
	const sizes = ["Bytes", "KB", "MB", "GB"];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const SortableFileItem = ({ 
	attachment, 
	onRemove, 
	onDownload, 
	isRemoving,
	onClick 
}: {
	attachment: FileAttachment;
	onRemove: (id: string) => void;
	onDownload: (file: FileAttachment["file"]) => void;
	isRemoving: boolean;
	onClick?: (file: FileAttachment["file"]) => void;
}) => {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: attachment.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
	};

	const handleClick = (e: React.MouseEvent) => {
		if (isDragging) return;
		
		if ((e.target as HTMLElement).closest('[data-radix-popper-content-wrapper]') || 
			(e.target as HTMLElement).closest('button')) {
			return;
		}

		onClick?.(attachment.file);
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			{...attributes}
			{...listeners}
			className={cn(
				isDragging && "z-50"
			)}
		>
			<Card 
				className={cn(
					"rounded-md bg-transparent hover:bg-muted/50 border border-transparent hover:border-border"
				)}
				onClick={handleClick}
			>
				<CardContent className="p-1 px-2">
					<div className="flex items-center gap-1">
						{attachment.file.thumbnailUrl ? (
							<img
								src={attachment.file.thumbnailUrl}
								alt={attachment.file.name}
								className="h-6 w-6 object-cover rounded-md border"
							/>
						) : (
							<div className={cn(
								"h-6 w-6 flex items-center justify-center text-xl border rounded-md", 
								attachment.file.fileType === "images" ? "bg-blue-500" : "bg-gray-500",
								attachment.file.fileType === "documents" ? "bg-sky-500" : "bg-gray-500",
								attachment.file.fileType === "videos" ? "bg-purple-500" : "bg-gray-500",
								attachment.file.fileType === "audio" ? "bg-green-500" : "bg-gray-500",
								attachment.file.fileType === "archives" ? "bg-red-500" : "bg-gray-500",
							)}>
								{getFileIcon(attachment.file.fileType)}
							</div>
						)}
						
						<div className="flex-1 min-w-0">
							<div className="flex items-center">
								<h4 className="font-medium truncate text-sm">{attachment.file.name}</h4>
								{attachment.file.name !== attachment.file.originalName && (
									<span className="text-xs text-muted-foreground">
										({attachment.file.originalName})
									</span>
								)}
							</div>
						</div>

						<div className="flex items-center gap-1">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" size="icon">
									<span className="sr-only">Open menu</span>
									<IconDotsVertical className="h-4 w-4" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem className="gap-2">
									<IconEdit className="h-4 w-4" />
									Rename
								</DropdownMenuItem>
								{/* <DropdownMenuItem onClick={handleShare}>
									<IconShare className="mr-2 h-4 w-4" />
									Share
								</DropdownMenuItem> */}
								<DropdownMenuSeparator />
								<DropdownMenuItem 
									className="text-red-600 gap-2"
									onClick={() => onRemove(attachment.id)}
									disabled={isRemoving}
								>
									<IconTrash className="h-4 w-4" />
									{isRemoving ? "Deleting..." : "Delete"}
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

// Sortable Folder Component
const SortableFolder = ({ 
	folder, 
	children, 
	onToggle, 
	onRename, 
	onDelete,
	onCreateSubfolder
}: {
	folder: FileFolder;
	children: React.ReactNode;
	onToggle: (id: string) => void;
	onRename: (id: string, name: string) => void;
	onDelete: (id: string) => void;
	onCreateSubfolder: (parentId: string) => void;
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [editName, setEditName] = useState(folder.name);

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: `folder-${folder.id}` });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
	};

	const handleRename = () => {
		if (editName.trim() && editName !== folder.name) {
			onRename(folder.id, editName.trim());
		}
		setIsEditing(false);
	};

	return (
		<div ref={setNodeRef} style={style}>
			<div className="flex items-center justify-between p-1 px-2 rounded-md hover:bg-muted/50 border border-transparent hover:border-border transition-colors">
				<div 
					className="flex items-center gap-2 flex-1 cursor-pointer"
					onClick={(e) => {
						e.stopPropagation();
						onToggle(folder.id);
					}}
					{...attributes}
					{...listeners}
				>
					<div className="relative w-4 h-4">
						<IconFolderOpen
							className={cn(
								"absolute h-4 w-4 text-blue-600 transition-all duration-200 ease-in-out",
								folder.isOpen
									? "opacity-100 scale-100"
									: "opacity-0 scale-95"
							)}
						/>
						<IconFolder
							className={cn(
								"absolute h-4 w-4 text-blue-600 transition-all duration-200 ease-in-out",
								folder.isOpen
									? "opacity-0 scale-95"
									: "opacity-100 scale-100"
							)}
						/>
					</div>
					
					{isEditing ? (
						<Input
							value={editName}
							onChange={(e) => setEditName(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === "Enter") handleRename();
								if (e.key === "Escape") setIsEditing(false);
							}}
							onBlur={handleRename}
							autoFocus
						/>
					) : (
						<span className="text-sm font-medium">{folder.name}</span>
					)}
				</div>

				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button variant="ghost" size="icon">
							<IconDotsVertical className="h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuItem className="gap-2" onClick={() => setIsEditing(true)}>
							<IconPencil className="h-4 w-4" />
							Rename
						</DropdownMenuItem>
						{/* <DropdownMenuItem className="gap-2" onClick={() => onCreateSubfolder(folder.id)}>
							<IconFolderPlus className="h-4 w-4" />
							Create Subfolder
						</DropdownMenuItem> */}
						<DropdownMenuSeparator />
						<DropdownMenuItem 
							onClick={() => onDelete(folder.id)}
							className="text-destructive gap-2"
						>
							<IconTrash className="h-4 w-4" />
							Delete
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</div>

			<AnimatePresence initial={false}>
				{folder.isOpen && (
					<motion.div
						key="folder-content"
						initial={{ height: 0, opacity: 0 }}
						animate={{ height: "auto", opacity: 1 }}
						exit={{ height: 0, opacity: 0 }}
						transition={{ 
							duration: 0.15, 
							ease: "easeInOut",
							opacity: { duration: 0.1 }
						}}
						className="overflow-hidden pl-6 mt-1"
					>
						{children}
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};

// Droppable Components
const Droppable = ({ 
	id, 
	children,
	isFolder = false,
	className = ""
}: { 
	id: string;
	children: React.ReactNode;
	isFolder?: boolean;
	className?: string;
}) => {
	const { setNodeRef, isOver } = useDroppable({
		id,
		data: {
			type: isFolder ? "folder" : "root",
			accepts: ["file"],
		},
	});

	return (
		<div 
			ref={setNodeRef}
			className={cn(
				className,
				isOver && isFolder && "bg-blue-50 border-blue-200 border-dashed border-2 rounded-md",
				isOver && !isFolder && "bg-muted/50"
			)}
		>
			{children}
		</div>
	);
};

const DroppableArea = ({ 
	folderId, 
	children 
}: { 
	folderId: string | null; 
	children: React.ReactNode; 
}) => {
	return (
		<Droppable id={folderId || "root"} isFolder={!!folderId}>
			<div className="min-h-4 space-y-1">
				{children}
			</div>
		</Droppable>
	);
};

const FilesContent = ({ objectId, objectType, objectName }: FilesContentProps) => {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();
	const [searchQuery, setSearchQuery] = useState("");
	const [showAttachModal, setShowAttachModal] = useState(false);
	const [activeId, setActiveId] = useState<string | null>(null);
	const [draggedItem, setDraggedItem] = useState<any>(null);
	const [isCreatingFolder, setIsCreatingFolder] = useState(false);
	const [newFolderName, setNewFolderName] = useState("");
	const [previewFile, setPreviewFile] = useState<FileAttachment["file"] | null>(null);

	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		})
	);

	// Fetch file attachments
	const {
		data: attachments = [],
		isLoading,
		refetch,
	} = useQuery({
		queryKey: ["file-attachments", activeOrganization?.id, objectId, objectType],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const params = new URLSearchParams({
				organizationId: activeOrganization.id,
				attachedToId: objectId,
				attachedToType: objectType,
			});
			const response = await fetch(`/api/attachments?${params}`);
			if (!response.ok) throw new Error("Failed to fetch file attachments");
			return response.json();
		},
		enabled: !!activeOrganization?.id && !!objectId,
	});

	// Fetch folders (scoped to this record)
	const {
		data: folders = [],
		isLoading: isLoadingFolders,
	} = useQuery({
		queryKey: ["file-folders", activeOrganization?.id, objectId, objectType],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const params = new URLSearchParams({
				organizationId: activeOrganization.id,
				scope: `${objectType}:${objectId}`, // Scope folders to this record
			});
			const response = await fetch(`/api/folders?${params}`);
			if (!response.ok) throw new Error("Failed to fetch folders");
			return response.json();
		},
		enabled: !!activeOrganization?.id && !!objectId,
	});

	// Create folder mutation
	const createFolderMutation = useMutation({
		mutationFn: async ({ name, parentId }: { name: string; parentId?: string }) => {
			const response = await fetch("/api/folders", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					name,
					organizationId: activeOrganization?.id,
					scope: `${objectType}:${objectId}`,
					...(parentId && { parentId }),
				}),
			});
			if (!response.ok) throw new Error("Failed to create folder");
			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-folders", activeOrganization?.id, objectId, objectType],
			});
			toast.success("Folder created");
			setNewFolderName("");
			setIsCreatingFolder(false);
		},
		onError: () => {
			toast.error("Failed to create folder");
		},
	});

	// Update folder mutation
	const updateFolderMutation = useMutation({
		mutationFn: async ({ id, ...data }: { id: string; [key: string]: any }) => {
			const response = await fetch(`/api/folders/${id}`, {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data),
			});
			if (!response.ok) throw new Error("Failed to update folder");
			return response.json();
		},
		onSuccess: (updatedFolder) => {
			// Update the specific folder in the cache instead of invalidating
			queryClient.setQueryData(
				["file-folders", activeOrganization?.id, objectId, objectType],
				(oldData: any) => {
					if (!oldData) return oldData;
					return oldData.map((f: FileFolder) =>
						f.id === updatedFolder.id ? { ...f, ...updatedFolder } : f
					);
				}
			);
		},
		onError: (error, variables) => {
			// Revert optimistic update on error
			queryClient.setQueryData(
				["file-folders", activeOrganization?.id, objectId, objectType],
				(oldData: any) => {
					if (!oldData) return oldData;
					return oldData.map((f: FileFolder) =>
						f.id === variables.id ? { ...f, isOpen: !f.isOpen } : f
					);
				}
			);
			toast.error("Failed to update folder");
		},
	});

	// Delete folder mutation
	const deleteFolderMutation = useMutation({
		mutationFn: async (id: string) => {
			const response = await fetch(`/api/folders/${id}`, {
				method: "DELETE",
			});
			if (!response.ok) throw new Error("Failed to delete folder");
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-folders", activeOrganization?.id, objectId, objectType],
			});
			toast.success("Folder deleted");
		},
		onError: () => {
			toast.error("Failed to delete folder");
		},
	});

	// Remove attachment mutation
	const removeAttachmentMutation = useMutation({
		mutationFn: async (attachmentId: string) => {
			const response = await fetch(`/api/attachments/${attachmentId}`, {
				method: "DELETE",
			});
			if (!response.ok) throw new Error("Failed to remove attachment");
		},
		onMutate: async (attachmentId: string) => {
			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["file-attachments", activeOrganization?.id, objectId, objectType],
			});

			// Snapshot the previous value
			const previousAttachments = queryClient.getQueryData([
				"file-attachments", 
				activeOrganization?.id, 
				objectId, 
				objectType
			]);

			// Optimistically remove the attachment
			queryClient.setQueryData(
				["file-attachments", activeOrganization?.id, objectId, objectType],
				(oldData: FileAttachment[]) => {
					if (!oldData) return [];
					return oldData.filter((attachment) => attachment.id !== attachmentId);
				}
			);

			// Return a context object with the snapshotted value
			return { previousAttachments };
		},
		onSuccess: () => {
			toast.success("File attachment removed");
		},
		onError: (error, attachmentId, context) => {
			// If the mutation fails, use the context returned from onMutate to roll back
			if (context?.previousAttachments) {
				queryClient.setQueryData(
					["file-attachments", activeOrganization?.id, objectId, objectType],
					context.previousAttachments
				);
			}
			console.error("Failed to remove attachment:", error);
			toast.error("Failed to remove file attachment");
		},
		onSettled: () => {
			// Always refetch after error or success to ensure we have the latest data
			queryClient.invalidateQueries({
				queryKey: ["file-attachments", activeOrganization?.id, objectId, objectType],
			});
		},
	});

	// Move file mutation
	const moveFileMutation = useMutation({
		mutationFn: async ({ fileId, folderId }: { fileId: string; folderId: string | null }) => {
			const response = await fetch(`/api/files/${fileId}/move`, {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ folderId }),
			});
			if (!response.ok) throw new Error("Failed to move file");
			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-attachments", activeOrganization?.id, objectId, objectType],
			});
			toast.success("File moved");
		},
		onError: () => {
			toast.error("Failed to move file");
		},
	});

	// Group attachments by folder
	const groupedAttachments = React.useMemo(() => {
		const groups: Record<string, FileAttachment[]> = {
			root: [],
		};

		attachments.forEach((attachment: FileAttachment) => {
			const folderId = attachment.file.folderId || "root";
			if (!groups[folderId]) {
				groups[folderId] = [];
			}
			groups[folderId].push(attachment);
		});

		return groups;
	}, [attachments]);

	const filteredAttachments = React.useMemo(() => {
		if (!searchQuery) return groupedAttachments;

		const filtered: Record<string, FileAttachment[]> = {};
		Object.entries(groupedAttachments).forEach(([folderId, attachments]) => {
			const matchingAttachments = attachments.filter((attachment) =>
				attachment.file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				attachment.file.originalName.toLowerCase().includes(searchQuery.toLowerCase())
			);
			if (matchingAttachments.length > 0) {
				filtered[folderId] = matchingAttachments;
			}
		});

		return filtered;
	}, [groupedAttachments, searchQuery]);

	const handleRemoveAttachment = (attachmentId: string) => {
		removeAttachmentMutation.mutate(attachmentId);
	};

	const handleDownloadFile = (file: FileAttachment["file"]) => {
		window.open(file.url, "_blank");
	};

	const handleCreateFolder = () => {
		if (newFolderName.trim()) {
			createFolderMutation.mutate({ name: newFolderName.trim() });
		}
	};

	const handleCreateSubfolder = (parentId: string) => {
		// For now, create a default named subfolder. In the future, you could show an input modal
		const subfolderName = `New Subfolder ${Date.now()}`;
		createFolderMutation.mutate({ name: subfolderName, parentId });
	};

	const handleToggleFolder = (folderId: string) => {
		const folder = folders.find((f: FileFolder) => f.id === folderId);
		if (!folder) return;

		const newIsOpen = !folder.isOpen;

		// Optimistic update
		queryClient.setQueryData(
			["file-folders", activeOrganization?.id, objectId, objectType],
			(oldData: any) => {
				if (!oldData) return oldData;
				return oldData.map((f: FileFolder) =>
					f.id === folderId ? { ...f, isOpen: newIsOpen } : f
				);
			}
		);

		// Update on server
		updateFolderMutation.mutate({
			id: folderId,
			isOpen: newIsOpen,
		});
	};

	const handleRenameFolder = (folderId: string, name: string) => {
		updateFolderMutation.mutate({
			id: folderId,
			name,
		});
	};

	const handleDeleteFolder = (folderId: string) => {
		deleteFolderMutation.mutate(folderId);
	};

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
		setDraggedItem(event.active.data.current?.item);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;
		setActiveId(null);
		setDraggedItem(null);

		if (!over) return;

		const activeId = active.id as string;
		const overId = over.id as string;

		// Find the dragged attachment
		const draggedAttachment = attachments.find((a: FileAttachment) => a.id === activeId);
		if (!draggedAttachment) return;

		// Determine target folder
		let targetFolderId: string | null = null;
		if (overId.startsWith("folder-")) {
			targetFolderId = overId.replace("folder-", "");
		} else if (overId === "root") {
			targetFolderId = null;
		} else {
			// Dropped on another file, find its folder
			const targetAttachment = attachments.find((a: FileAttachment) => a.id === overId);
			if (targetAttachment) {
				targetFolderId = targetAttachment.file.folderId || null;
			}
		}

		// Move the file if folder changed
		if (targetFolderId !== (draggedAttachment.file.folderId || null)) {
			moveFileMutation.mutate({
				fileId: draggedAttachment.file.id,
				folderId: targetFolderId,
			});
		}
	};

	const totalAttachments = attachments.length;

	return (
		<DndContext
			sensors={sensors}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
		>
			<div className="h-full flex flex-col">
				<div className="flex-shrink-0 p-2">
					<div className="flex items-center justify-between">
						<div>
						<div className="relative">
							<IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Search attached files..."
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
								className="pl-10"
							/>
						</div>
						</div>
						<div className="flex items-center gap-2">
							<Button
								variant="action"
								size="sm"
								onClick={() => setIsCreatingFolder(true)}
								title="Create folder"
								className="gap-1"
							>
								<IconFolderPlus className="h-4 w-4" />
								New Folder
							</Button>
							<Button
								variant="action"
								size="sm"
								onClick={() => setShowAttachModal(true)}
								className="gap-1"
							>
								<IconPlus className="h-4 w-4" />
								Attach File
							</Button>
						</div>
					</div>
				</div>

				<div className="flex-1 overflow-y-auto p-2">
					{isCreatingFolder && (
						<motion.div
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: "auto" }}
							exit={{ opacity: 0, height: 0 }}
							className="mb-2"
						>
							<div className="flex items-center gap-2 p-2 rounded-md">
								<IconFolder className="h-4 w-4 text-blue-600" />
								<Input
									placeholder="Folder name"
									value={newFolderName}
									onChange={(e) => setNewFolderName(e.target.value)}
									onKeyDown={(e) => {
										if (e.key === "Enter") handleCreateFolder();
										if (e.key === "Escape") setIsCreatingFolder(false);
									}}
									onBlur={() => setIsCreatingFolder(false)}
									className="h-8"
									autoFocus
								/>
							</div>
						</motion.div>
					)}

					{isLoading || isLoadingFolders ? (
						<div>
							{Array.from({ length: 3 }).map((_, i) => (
								<Card key={i}>
									<CardContent className="p-2">
										<div className="flex items-center gap-3">
											<Skeleton className="h-8 w-8 rounded" />
											<div className="flex-1 space-y-1">
												<Skeleton className="h-4 w-3/4" />
												<Skeleton className="h-3 w-1/2" />
											</div>
											<Skeleton className="h-8 w-8 rounded" />
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					) : Object.keys(filteredAttachments).length === 0 ? (
						<div className="flex flex-col items-center justify-center h-64 text-center">
							<IconFile className="h-12 w-12 text-muted-foreground mb-4" />
							<h4 className="text-lg font-medium mb-2">No files attached</h4>
							<p className="text-sm text-muted-foreground mb-4">
								{searchQuery
									? "No files match your search criteria"
									: `Attach files to this ${objectType} to see them here`}
							</p>
							<Button
								variant="outline"
								onClick={() => setShowAttachModal(true)}
							>
								<IconPlus className="h-4 w-4 mr-1" />
								Attach First File
							</Button>
						</div>
					) : (
						<DroppableArea folderId={null}>
							<div className="space-y-1">
								<SortableContext items={[...folders.map((f: FileFolder) => f.id), ...Object.values(filteredAttachments).flat().map((a: FileAttachment) => a.id)]}>
									{/* Render folders */}
									{folders.map((folder: FileFolder, idx: number) => (
										<div key={folder.id}>
											{/* Drop zone before folder */}
											<Droppable id={`before-folder-${folder.id}`}>
												<div />
											</Droppable>
											
											<SortableFolder
												folder={folder}
												onToggle={handleToggleFolder}
												onRename={handleRenameFolder}
												onDelete={handleDeleteFolder}
												onCreateSubfolder={handleCreateSubfolder}
											>
												{folder.isOpen && filteredAttachments[folder.id] && (
													<div className="pl-4 space-y-1">
														{filteredAttachments[folder.id].map((attachment, attachIdx) => (
															<div key={attachment.id}>
																{/* Drop zone before file */}
																<Droppable id={`before-${attachment.id}`} className="h-1">
																	<div />
																</Droppable>
																
																<SortableFileItem
																	attachment={attachment}
																	onRemove={handleRemoveAttachment}
																	onDownload={handleDownloadFile}
																	isRemoving={removeAttachmentMutation.isPending}
																	onClick={(file) => setPreviewFile(file)}
																/>
																
																{/* Drop zone after file */}
																{attachIdx === filteredAttachments[folder.id].length - 1 && (
																	<Droppable id={`after-${attachment.id}`} className="h-1">
																		<div />
																	</Droppable>
																)}
															</div>
														))}
													</div>
												)}
											</SortableFolder>
											
											{/* Drop zone after folder */}
											{idx === folders.length - 1 && (
												<Droppable id={`after-folder-${folder.id}`} className="h-1" >
													<div />
												</Droppable>
											)}
										</div>
									))}

									{/* Render root files */}
									{filteredAttachments.root && filteredAttachments.root.map((attachment, idx) => (
										<div key={attachment.id}>
											{/* Drop zone before root file */}
											<Droppable id={`before-${attachment.id}`} className="h-1" >
												<div />
											</Droppable>
											
											<SortableFileItem
												attachment={attachment}
												onRemove={handleRemoveAttachment}
												onDownload={handleDownloadFile}
												isRemoving={removeAttachmentMutation.isPending}
												onClick={(file) => setPreviewFile(file)}
											/>
											
											{/* Drop zone after root file */}
											{idx === filteredAttachments.root.length - 1 && (
												<Droppable id={`after-${attachment.id}`} className="h-1" >
													<div />
												</Droppable>
											)}
										</div>
									))}
								</SortableContext>
							</div>
						</DroppableArea>
					)}
				</div>

				<AttachFileModal
					isOpen={showAttachModal}
					onClose={() => setShowAttachModal(false)}
					attachToId={objectId}
					attachToType={objectType}
					attachToName={objectName}
				/>

				<DragOverlay>
					{draggedItem && (
						<div className="opacity-80 bg-white rounded-md shadow-lg border">
							<Card>
								<CardContent className="p-4">
									<div className="flex items-center gap-3">
										<div className="h-10 w-10 flex items-center justify-center text-xl border rounded">
											📄
										</div>
										<div className="flex-1 min-w-0">
											<h4 className="font-medium truncate">{draggedItem.file?.name}</h4>
										</div>
									</div>
								</CardContent>
							</Card>
						</div>
					)}
				</DragOverlay>

				{/* Image Preview Dialog */}
				<StandardizedModal
					open={!!previewFile}
					onOpenChange={() => setPreviewFile(null)}
					title={previewFile?.name || ""}
					description={previewFile?.name || ""}
					icon={getFileIcon(previewFile?.fileType || "")}
					hideCloseButton={false}
					footer={
						<StandardizedModalFooter
							leftContent={
								<div className="flex items-center justify-start gap-2">
									<span className="text-sm text-muted-foreground">
										{previewFile?.mimeType}
									</span>
									<Badge variant="views" className="text-xs text-muted-foreground">
										{formatFileSize(previewFile?.size || 0)}
									</Badge>
								</div>
							}
						>
							<Button variant="action" className="gap-1" onClick={() => previewFile && handleDownloadFile(previewFile)}>
								<IconDownload className="h-4 w-4" />
								Download
							</Button>
						</StandardizedModalFooter>
					}
				>
					<div className="flex items-center justify-center p-4">
						{previewFile && (
							<img
								src={previewFile.url}
								alt={previewFile.name}
								className="max-w-full max-h-[70vh] object-contain rounded-lg"
							/>
						)}
					</div>
				</StandardizedModal>
			</div>
		</DndContext>
	);
};

export default FilesContent;
