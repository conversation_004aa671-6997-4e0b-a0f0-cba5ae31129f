"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import { Switch } from "@ui/components/switch";
import { cn } from "@ui/lib";
import {
	IconCopy,
	IconEye,
	IconEdit,
	IconTrash,
	IconUser,
	IconUsers,
	IconX,
} from "@tabler/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";

interface FilePermission {
	id: string;
	userId: string;
	permission: "view" | "edit" | "admin";
	createdAt: string;
	user: {
		id: string;
		name: string;
		email: string;
		image?: string;
	};
}

interface ShareFileModalProps {
	isOpen: boolean;
	onClose: () => void;
	fileId: string;
	fileName: string;
	isPublic: boolean;
	onPublicToggle: (isPublic: boolean) => void;
}

const ShareFileModal = ({
	isOpen,
	onClose,
	fileId,
	fileName,
	isPublic,
	onPublicToggle,
}: ShareFileModalProps) => {
	const { organization } = useActiveOrganization();
	const queryClient = useQueryClient();
	const [userEmail, setUserEmail] = useState("");
	const [selectedPermission, setSelectedPermission] = useState<"view" | "edit" | "admin">("view");

	// Fetch file permissions
	const { data: permissions = [], isLoading } = useQuery({
		queryKey: ["file-permissions", fileId],
		queryFn: async () => {
			const response = await fetch(`/api/files/${fileId}/permissions`);
			if (!response.ok) throw new Error("Failed to fetch permissions");
			return response.json();
		},
		enabled: isOpen && !!fileId,
	});

	// Fetch organization users
	const { data: users = [] } = useQuery({
		queryKey: ["organization-users", organization?.id],
		queryFn: async () => {
			if (!organization?.id) return [];
			const response = await fetch(`/api/organizations/${organization.id}/users`);
			if (!response.ok) throw new Error("Failed to fetch users");
			return response.json();
		},
		enabled: isOpen && !!organization?.id,
	});

	// Add permission mutation
	const addPermissionMutation = useMutation({
		mutationFn: async ({ userId, permission }: { userId: string; permission: string }) => {
			const response = await fetch(`/api/files/${fileId}/permissions`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					userId,
					permission,
					organizationId: organization?.id,
				}),
			});
			if (!response.ok) throw new Error("Failed to add permission");
			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-permissions", fileId],
			});
			setUserEmail("");
			toast.success("Permission added successfully");
		},
		onError: (error) => {
			console.error("Failed to add permission:", error);
			toast.error("Failed to add permission");
		},
	});

	// Remove permission mutation
	const removePermissionMutation = useMutation({
		mutationFn: async (permissionId: string) => {
			const response = await fetch(`/api/files/${fileId}/permissions/${permissionId}`, {
				method: "DELETE",
			});
			if (!response.ok) throw new Error("Failed to remove permission");
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-permissions", fileId],
			});
			toast.success("Permission removed");
		},
		onError: (error) => {
			console.error("Failed to remove permission:", error);
			toast.error("Failed to remove permission");
		},
	});

	// Update permission mutation
	const updatePermissionMutation = useMutation({
		mutationFn: async ({ permissionId, permission }: { permissionId: string; permission: string }) => {
			const response = await fetch(`/api/files/${fileId}/permissions/${permissionId}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ permission }),
			});
			if (!response.ok) throw new Error("Failed to update permission");
			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["file-permissions", fileId],
			});
			toast.success("Permission updated");
		},
		onError: (error) => {
			console.error("Failed to update permission:", error);
			toast.error("Failed to update permission");
		},
	});

	const handleAddPermission = () => {
		if (!userEmail.trim()) {
			toast.error("Please enter a user email");
			return;
		}

		const user = users.find((u: any) => u.email.toLowerCase() === userEmail.toLowerCase());
		if (!user) {
			toast.error("User not found in organization");
			return;
		}

		// Check if user already has permission
		const existingPermission = permissions.find((p: FilePermission) => p.userId === user.id);
		if (existingPermission) {
			toast.error("User already has permission for this file");
			return;
		}

		addPermissionMutation.mutate({
			userId: user.id,
			permission: selectedPermission,
		});
	};

	const handleRemovePermission = (permissionId: string) => {
		removePermissionMutation.mutate(permissionId);
	};

	const handleUpdatePermission = (permissionId: string, permission: string) => {
		updatePermissionMutation.mutate({ permissionId, permission });
	};

	const copyShareLink = () => {
		const shareUrl = `${window.location.origin}/shared/files/${fileId}`;
		navigator.clipboard.writeText(shareUrl);
		toast.success("Share link copied to clipboard");
	};

	const getPermissionIcon = (permission: string) => {
		switch (permission) {
			case "view":
				return <IconEye className="h-4 w-4" />;
			case "edit":
				return <IconEdit className="h-4 w-4" />;
			case "admin":
				return <IconTrash className="h-4 w-4" />;
			default:
				return <IconUser className="h-4 w-4" />;
		}
	};

	const getPermissionColor = (permission: string) => {
		switch (permission) {
			case "view":
				return "text-blue-600";
			case "edit":
				return "text-green-600";
			case "admin":
				return "text-red-600";
			default:
				return "text-gray-600";
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>Share "{fileName}"</DialogTitle>
					<DialogDescription>
						Manage who can access this file and their permissions
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Public Access */}
					<div className="flex items-center justify-between p-4 border rounded-lg">
						<div className="flex items-center gap-3">
							<IconUsers className="h-5 w-5 text-muted-foreground" />
							<div>
								<div className="font-medium">Public Access</div>
								<div className="text-sm text-muted-foreground">
									Anyone with the link can view this file
								</div>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Switch
								checked={isPublic}
								onCheckedChange={onPublicToggle}
							/>
							{isPublic && (
								<Button variant="outline" size="sm" onClick={copyShareLink}>
									<IconCopy className="h-4 w-4 mr-1" />
									Copy Link
								</Button>
							)}
						</div>
					</div>

					{/* Add User Permission */}
					<div className="space-y-4">
						<Label className="text-base font-medium">Add People</Label>
						<div className="flex gap-2">
							<Input
								placeholder="Enter user email..."
								value={userEmail}
								onChange={(e) => setUserEmail(e.target.value)}
								className="flex-1"
							/>
							<Select value={selectedPermission} onValueChange={(value: any) => setSelectedPermission(value)}>
								<SelectTrigger className="w-32">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="view">View</SelectItem>
									<SelectItem value="edit">Edit</SelectItem>
									<SelectItem value="admin">Admin</SelectItem>
								</SelectContent>
							</Select>
							<Button
								onClick={handleAddPermission}
								disabled={addPermissionMutation.isPending}
							>
								Add
							</Button>
						</div>
					</div>

					{/* Current Permissions */}
					<div className="space-y-4">
						<Label className="text-base font-medium">People with Access</Label>
						<div className="space-y-2 max-h-64 overflow-y-auto">
							{isLoading ? (
								<div className="space-y-2">
									{Array.from({ length: 3 }).map((_, i) => (
										<div key={i} className="flex items-center gap-3 p-3">
											<Skeleton className="h-8 w-8 rounded-full" />
											<div className="flex-1 space-y-1">
												<Skeleton className="h-4 w-32" />
												<Skeleton className="h-3 w-24" />
											</div>
											<Skeleton className="h-8 w-20" />
										</div>
									))}
								</div>
							) : permissions.length === 0 ? (
								<div className="text-center py-8 text-muted-foreground">
									<IconUser className="h-8 w-8 mx-auto mb-2 opacity-50" />
									<p>No specific permissions set</p>
									<p className="text-sm">Add people to share this file</p>
								</div>
							) : (
								permissions.map((permission: FilePermission) => (
									<div key={permission.id} className="flex items-center gap-3 p-3 border rounded-lg">
										{permission.user.image ? (
											<img
												src={permission.user.image}
												alt={permission.user.name}
												className="h-8 w-8 rounded-full"
											/>
										) : (
											<div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
												<IconUser className="h-4 w-4" />
											</div>
										)}
										<div className="flex-1 min-w-0">
											<div className="font-medium truncate">{permission.user.name}</div>
											<div className="text-sm text-muted-foreground truncate">
												{permission.user.email}
											</div>
										</div>
										<div className="flex items-center gap-2">
											<Select
												value={permission.permission}
												onValueChange={(value) => handleUpdatePermission(permission.id, value)}
											>
												<SelectTrigger className="w-24">
													<div className={cn("flex items-center gap-1", getPermissionColor(permission.permission))}>
														{getPermissionIcon(permission.permission)}
														<SelectValue />
													</div>
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="view">View</SelectItem>
													<SelectItem value="edit">Edit</SelectItem>
													<SelectItem value="admin">Admin</SelectItem>
												</SelectContent>
											</Select>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => handleRemovePermission(permission.id)}
												disabled={removePermissionMutation.isPending}
											>
												<IconX className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))
							)}
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={onClose}>
						Done
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default ShareFileModal;
