/**
 * @function restoreCountDown
 * @param {string} date - The date to restore
 * @returns {number} - The days left to restore
 * @description This function will return the days left to restore the deleted record
 */
export function restoreCountDown(date: string): number {
	const currentDate = new Date();
	const deletedDate = new Date(date);

	if (isNaN(deletedDate.getTime())) {
		return 0;
	}

	const timeDifference = currentDate.getTime() - deletedDate.getTime();
	const daysDifference = timeDifference / (1000 * 3600 * 24);
	const daysLeft = Math.floor(30 - daysDifference);

	return daysLeft > 0 ? daysLeft : 0;
}

export function formatPhoneNumber(value: string): string {
	// Remove all non-numeric characters
	const phoneNumber = value.replace(/[^\d]/g, "");
	const phoneNumberLength = phoneNumber.length;

	// US phone number formatting
	if (phoneNumberLength < 4) return phoneNumber;
	if (phoneNumberLength < 7) {
		return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
	}
	return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
}

export function extractPhoneNumber(formattedValue: string): string {
	return formattedValue.replace(/[^\d]/g, "");
}

export function formatDisplayPhone(rawNumber: string): string {
	if (!rawNumber) return "";

	const cleanNumber = rawNumber.replace(/[^\d]/g, "");
	if (cleanNumber.length === 10) {
		return `+1 ${formatPhoneNumber(cleanNumber)}`;
	}
	if (cleanNumber.length === 11 && cleanNumber.startsWith("1")) {
		return `+${cleanNumber.slice(0, 1)} ${formatPhoneNumber(cleanNumber.slice(1))}`;
	}
	return formatPhoneNumber(cleanNumber);
}

/**
 * Format a number with commas as thousands separators
 * @param value - The number to format (can be number, string, null, or undefined)
 * @returns Formatted string with commas
 */
export function formatNumberWithCommas(value: number | string | null | undefined): string {
	if (value === null || value === undefined || value === "") {
		return "";
	}
	
	const numValue = typeof value === "string" ? parseFloat(value) : value;
	
	if (isNaN(numValue)) {
		return "";
	}
	
	return numValue.toLocaleString();
}

/**
 * Remove commas from a formatted number string and return the numeric value
 * @param value - The formatted string with commas
 * @returns Number without commas, or null if invalid
 */
export function removeCommasFromNumber(value: string): number | null {
	if (!value || typeof value !== "string") {
		return null;
	}
	
	const cleanValue = value.replace(/,/g, "");
	const numValue = parseFloat(cleanValue);
	
	return isNaN(numValue) ? null : numValue;
}
