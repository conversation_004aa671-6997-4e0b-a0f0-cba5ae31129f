import type { Organization, Session } from "@repo/auth";
import { apiClient } from "@shared/lib/api-client";
import type { NextRequest } from "next/server";

export const getSession = async (req: NextRequest): Promise<Session | null> => {
	const protocol = req.headers.get("x-forwarded-proto") || "https";
	const host = req.headers.get("x-forwarded-host") || req.headers.get("host");
	const baseUrl = `${protocol}://${host}`;

	const response = await fetch(
		`${baseUrl}/api/auth/get-session?disableCookieCache=true`,
		{
			headers: {
				cookie: req.headers.get("cookie") || "",
			},
		},
	);

	if (!response.ok) {
		return null;
	}

	return await response.json();
};

export const getOrganizationsForSession = async (
	req: NextRequest,
): Promise<Organization[]> => {
	const response = await fetch(
		new URL("/api/auth/organization/list", req.nextUrl.origin),
		{
			headers: {
				cookie: req.headers.get("cookie") || "",
			},
		},
	);

	if (!response.ok) {
		return [];
	}

	return (await response.json()) ?? [];
};

export const getPurchasesForSession = async (
	req: NextRequest,
	organizationId?: string,
) => {
	const response = await apiClient.payments.purchases.$get(
		{
			query: organizationId ? { organizationId } : {},
		},
		{
			headers: {
				cookie: req.headers.get("cookie") || "",
			},
		},
	);

	if (!response.ok) {
		return [];
	}

	const purchases = await response.json();

	return purchases;
};
