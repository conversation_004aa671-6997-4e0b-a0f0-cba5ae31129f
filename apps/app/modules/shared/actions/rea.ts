export interface ReaAutocompleteParams {
  query: string;
}

export interface ReaAutocompleteSuggestion {
  address: string;
  city?: string;
  state?: string;
  zip?: string;
  id?: string;
}

export async function getReaAutocomplete({ query }: ReaAutocompleteParams) {
  try {
    if (!query || query.length < 3) {
      return { suggestions: [] };
    }

    const response = await fetch('/api/rea/autocomplete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        query,
      }),
    });

    if (!response.ok) {
      throw new Error(`REA Autocomplete API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Transform REA API response to our format
    // REA AutoComplete API returns different response structure
    const suggestions: ReaAutocompleteSuggestion[] = 
      data.data?.map((item: any) => ({
        address: item.address || item.description || item.text || '',
        city: item.city || '',
        state: item.state || item.stateCode || '',
        zip: item.zip || item.zipCode || item.postalCode || '',
        id: item.id?.toString() || item.propertyId?.toString() || '',
      })) || [];

    return { suggestions };
  } catch (error) {
    console.error('REA Autocomplete error:', error);
    return { 
      error: error instanceof Error ? error.message : 'Internal server error', 
      suggestions: [] 
    };
  }
}

export async function getReaPropertyDetails(address: string) {
  try {
    if (!address) {
      throw new Error('Address is required');
    }

    const response = await fetch('/api/rea/property-info', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        address,
      }),
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // No property data found
      }
      throw new Error(`REA Property Info API error: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.error) {
      throw new Error(result.error);
    }

    return result.data;
  } catch (error) {
    console.error('REA Property Details error:', error);
    return null;
  }
} 