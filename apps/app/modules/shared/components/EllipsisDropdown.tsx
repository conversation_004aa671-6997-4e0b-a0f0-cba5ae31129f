import { IconDotsVertical } from "@tabler/icons-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import type { FC, ReactNode } from "react";

interface EllipsisDropdownProps {
	children: ReactNode;
	className?: string;
	sideOffset?: number;
	side?: "top" | "right" | "bottom" | "left";
	align?: "start" | "center" | "end";
}

const EllipsisDropdownItem: FC<
	React.ComponentProps<typeof DropdownMenuItem>
> = (props) => {
	return <DropdownMenuItem {...props} />;
};

export const EllipsisDropdown: FC<EllipsisDropdownProps> & {
	Item: typeof EllipsisDropdownItem;
} = ({
	children,
	className,
	sideOffset = 8,
	side = "bottom",
	align = "end",
}) => {
	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<button
					className={cn(
						"cursor-pointer",
						"flex items-center justify-center rounded-md p-2 hover:bg-muted/60 transition",
						className,
					)}
					aria-label="Open menu"
				>
					<IconDotsVertical className="w-4 h-4 text-muted-foreground" />
				</button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				side={side}
				align={align}
				sideOffset={sideOffset}
				className={cn(
					"z-50 min-w-[180px] rounded-xl border border-border bg-popover p-1 text-popover-foreground shadow-lg focus:outline-none animate-in fade-in-0 slide-in-from-top-1 space-y-1",
					"bg-sidebar",
				)}
			>
				{children}
			</DropdownMenuContent>
		</DropdownMenu>
	);
};

EllipsisDropdown.Item = EllipsisDropdownItem;
