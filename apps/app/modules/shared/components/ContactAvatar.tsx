import { config } from "@repo/config";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { forwardRef, useMemo } from "react";
import { ContactIcon } from "@ui/icons/contact";

export const ContactAvatar = forwardRef<
	HTMLSpanElement,
	{
		name: string;
		avatarUrl?: string | null;
		className?: string;
	}
>(({ name, avatarUrl, className }, ref) => {
	const initials = useMemo(() => {
		if (!name || name.trim() === "") return "";
		const parts = name.trim().split(" ").filter(Boolean);
		return parts
			.slice(0, 2)
			.map((n) => n[0]?.toUpperCase())
			.join("");
	}, [name]);

	const avatarSrc = useMemo(
		() =>
			avatarUrl
				? avatarUrl.startsWith("http")
					? avatarUrl
					: `/image-proxy/${config.storage.bucketNames.avatars}/${avatarUrl}`
				: undefined,
		[avatarUrl],
	);

	return (
		<Avatar ref={ref} className={className}>
			{avatarSrc && <AvatarImage src={avatarSrc} />}
			<AvatarFallback className="cursor-default bg-secondary border border-input rounded-full flex items-center justify-center">
				<ContactIcon className="h-full w-full" />
			</AvatarFallback>
		</Avatar>
	);
});

ContactAvatar.displayName = "ContactAvatar";
