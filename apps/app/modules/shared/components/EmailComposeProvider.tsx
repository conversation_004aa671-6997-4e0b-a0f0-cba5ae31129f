"use client";

import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";
import { toast } from "sonner";

export interface EmailDraft {
	to: string;
	cc: string | string[];
	bcc: string | string[];
	subject: string;
	body: string;
	fromAccountId: string;
	recipientName?: string;
	recipientAvatarUrl?: string;
	recipientType?: string;
}

interface EmailComposeContextType {
	// Modal state
	isOpen: boolean;
	isMinimized: boolean;
	savedDraft: EmailDraft | null;
	
	// Actions
	openCompose: (toEmail?: string, subject?: string) => void;
	closeCompose: () => void;
	minimizeCompose: () => void;
	maximizeCompose: () => void;
	saveDraft: (draft: EmailDraft) => void;
	clearDraft: () => void;
	
	// Props for prefilling
	initialToEmail?: string;
	initialSubject?: string;
}

const EmailComposeContext = createContext<EmailComposeContextType | undefined>(undefined);

interface EmailComposeProviderProps {
	children: ReactNode;
}

export function EmailComposeProvider({ children }: EmailComposeProviderProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isMinimized, setIsMinimized] = useState(false);
	const [savedDraft, setSavedDraft] = useState<EmailDraft | null>(null);
	const [initialToEmail, setInitialToEmail] = useState<string | undefined>();
	const [initialSubject, setInitialSubject] = useState<string | undefined>();

	const openCompose = useCallback((toEmail?: string, subject?: string) => {
		setInitialToEmail(toEmail);
		setInitialSubject(subject);
		setIsOpen(true);
		setIsMinimized(false);
	}, []);

	const closeCompose = useCallback(() => {
		setIsOpen(false);
		setIsMinimized(false);
	}, []);

	const minimizeCompose = useCallback(() => {
		setIsOpen(false);
		setIsMinimized(true);
	}, []);

	const maximizeCompose = useCallback(() => {
		setIsOpen(true);
		setIsMinimized(false);
	}, []);

	const saveDraft = useCallback((draft: EmailDraft) => {
		setSavedDraft(draft);
		toast.success("Email saved as draft");
	}, []);

	const clearDraft = useCallback(() => {
		setSavedDraft(null);
	}, []);

	const value: EmailComposeContextType = {
		isOpen,
		isMinimized,
		savedDraft,
		openCompose,
		closeCompose,
		minimizeCompose,
		maximizeCompose,
		saveDraft,
		clearDraft,
		initialToEmail,
		initialSubject,
	};

	return (
		<EmailComposeContext.Provider value={value}>
			{children}
		</EmailComposeContext.Provider>
	);
}

export function useEmailCompose() {
	const context = useContext(EmailComposeContext);
	if (!context) {
		throw new Error("useEmailCompose must be used within an EmailComposeProvider");
	}
	return context;
} 