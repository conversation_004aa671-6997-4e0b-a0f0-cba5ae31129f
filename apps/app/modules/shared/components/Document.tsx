import { cookies } from "next/headers";
import type { PropsWithChildren } from "react";
import { ClientDocument } from "./ClientDocument";

export async function Document({
	children,
	locale,
}: PropsWithChildren<{ locale: string }>) {
	const cookieStore = await cookies();
	const consentCookie = cookieStore.get("consent");

	return (
		<ClientDocument
			locale={locale}
			initialConsent={consentCookie?.value === "true"}
		>
			{children}
		</ClientDocument>
	);
}
