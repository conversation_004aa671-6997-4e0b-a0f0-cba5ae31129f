import React, { useState } from "react";
import { Button } from "@ui/components/button";
import { StandardizedModal } from "@ui/components/standardized-modal";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { ScrollArea } from "@ui/components/scroll-area";
import { cn } from "@ui/lib";

interface Record {
  id: string;
  name: string;
  email?: string;
  avatarUrl?: string;
  stats?: {
    tasks: number;
    emails: number;
    calls: number;
    notes: number;
  };
}

interface MergeRecordsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  records: Record[];
  onMerge: (primaryRecordId: string, secondaryRecordId: string) => void;
}

export function MergeRecordsModal({
  open,
  onOpenChange,
  records,
  onMerge,
}: MergeRecordsModalProps) {
  const [selectedRecords, setSelectedRecords] = useState<Record[]>([]);
  const [isMerging, setIsMerging] = useState(false);

  const toggleRecordSelection = (record: Record) => {
    setSelectedRecords((prev) => {
      const exists = prev.some((r) => r.id === record.id);
      if (exists) {
        return prev.filter((r) => r.id !== record.id);
      }
      if (prev.length < 2) {
        return [...prev, record];
      }
      return prev;
    });
  };

  const handleMerge = async () => {
    if (selectedRecords.length !== 2) return;
    
    setIsMerging(true);
    try {
      await onMerge(selectedRecords[0].id, selectedRecords[1].id);
      onOpenChange(false);
    } finally {
      setIsMerging(false);
    }
  };

  const isRecordSelected = (recordId: string) => 
    selectedRecords.some(r => r.id === recordId);

  const getRecordPosition = (recordId: string) => {
    const index = selectedRecords.findIndex(r => r.id === recordId);
    return index >= 0 ? index + 1 : 0;
  };

  return (
    <StandardizedModal
      open={open}
      onOpenChange={onOpenChange}
      title="Merge Records"
      maxWidth="3xl"
      hideCloseButton={false}
    >
      <div className="flex flex-col h-full">
        <div className="p-4 text-sm text-muted-foreground bg-muted/50 rounded-lg mb-4">
          Our system intelligently combines your data. Note that the second selected record holds higher priority.
        </div>

        <ScrollArea className="flex-1 px-4">
          <div className="flex flex-col md:flex-row gap-6 mb-6">
            {/* Left side - Available Records */}
            <div className="flex-1">
              <h3 className="text-sm font-medium mb-3">Available Records</h3>
              <div className="grid gap-2">
                {records.map((record) => (
                  <div
                    key={record.id}
                    onClick={() => toggleRecordSelection(record)}
                    className={cn(
                      "p-3 border rounded-lg cursor-pointer transition-colors",
                      isRecordSelected(record.id)
                        ? "border-primary bg-primary/5"
                        : "hover:bg-muted/50"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={record.avatarUrl} />
                        <AvatarFallback>
                          {record.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{record.name}</p>
                        {record.email && (
                          <p className="text-sm text-muted-foreground truncate">
                            {record.email}
                          </p>
                        )}
                      </div>
                      {isRecordSelected(record.id) && (
                        <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
                          {getRecordPosition(record.id)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right side - Merge Preview */}
            <div className="flex-1">
              <h3 className="text-sm font-medium mb-3">Merge Preview</h3>
              <div className="border rounded-lg overflow-hidden">
                {/* Record 1 */}
                <div className="p-4 border-b">
                  {selectedRecords[0] ? (
                    <RecordPreview record={selectedRecords[0]} position={1} />
                  ) : (
                    <div className="h-20 flex items-center justify-center text-muted-foreground">
                      Select first record
                    </div>
                  )}
                </div>
                
                {/* Merge Arrow */}
                <div className="flex items-center justify-center py-2 bg-muted/50">
                  <div className="bg-background p-1.5 rounded-full border">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 5v14" />
                      <path d="m19 12-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                
                {/* Record 2 */}
                <div className="p-4">
                  {selectedRecords[1] ? (
                    <RecordPreview record={selectedRecords[1]} position={2} />
                  ) : (
                    <div className="h-20 flex items-center justify-center text-muted-foreground">
                      Select second record
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="border-t p-4 flex justify-between items-center">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isMerging}
          >
            Cancel
          </Button>
          <Button
            onClick={handleMerge}
            disabled={selectedRecords.length !== 2 || isMerging}
            loading={isMerging}
          >
            Merge Records
          </Button>
        </div>
      </div>
    </StandardizedModal>
  );
}

interface RecordPreviewProps {
  record: Record;
  position: number;
}

function RecordPreview({ record, position }: RecordPreviewProps) {
  return (
    <div className="flex flex-col items-center text-center">
      <div className="relative mb-3">
        <Avatar className="h-16 w-16">
          <AvatarImage src={record.avatarUrl} />
          <AvatarFallback>
            {record.name
              .split(" ")
              .map((n) => n[0])
              .join("")}
          </AvatarFallback>
        </Avatar>
        <div className="absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">
          {position}
        </div>
      </div>
      
      <h4 className="font-medium">{record.name}</h4>
      {record.email && (
        <p className="text-sm text-muted-foreground">{record.email}</p>
      )}
      
      {record.stats && (
        <div className="flex gap-3 mt-3 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <span>{record.stats.tasks}</span>
          </div>
          <div className="flex items-center gap-1">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
            <span>{record.stats.emails}</span>
          </div>
          <div className="flex items-center gap-1">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
            <span>{record.stats.calls}</span>
          </div>
          <div className="flex items-center gap-1">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <span>{record.stats.notes}</span>
          </div>
        </div>
      )}
    </div>
  );
}
