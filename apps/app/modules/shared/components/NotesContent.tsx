import { useNotesForObjectRecord, createNote } from "@app/notes/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useQueryClient } from "@tanstack/react-query";
import { IconPlus } from "@tabler/icons-react";
import { ScrollArea } from "@ui/components/scroll-area";
import { NoteCard } from "../../../app/(authenticated)/app/(organizations)/[organizationSlug]/notes/NoteCard";
import type { ObjectType } from "@repo/database";
import { Button } from "@ui/components/button";

interface NotesContentProps {
	objectId?: string;
	objectType?: ObjectType;
	objectName?: string;
}

export function NotesContent({ objectId, objectType, objectName }: NotesContentProps) {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();
	
	// Always call hooks at the top level - use safe fallback values
	const { data: notes = [], isLoading } = useNotesForObjectRecord(
		objectId || "",
		objectType || "contact",
		activeOrganization?.id
	);
	
	// Early return if required props are missing
	if (!objectId || !objectType || !objectName) {
		return (
			<div className="p-4">
				<div className="text-muted-foreground text-sm">
					Loading record information...
				</div>
			</div>
		);
	}

	if (!activeOrganization) {
		return (
			<div className="p-4">
				<div className="text-muted-foreground text-sm">
					Organization not available
				</div>
			</div>
		);
	}

	const handleCreateNote = async () => {
		try {
			await createNote({
				title: `Note for ${objectName}`,
				objectId: objectId,
				objectType: objectType,
				organizationId: activeOrganization.id,
			});
			
			// Invalidate queries to refresh the notes list
			queryClient.invalidateQueries({
				queryKey: ["notes", "object", objectId, objectType, activeOrganization.id],
			});
		} catch (error) {
			console.error('Failed to create note:', error);
		}
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex justify-end p-2">
				<Button variant="action" className="gap-1" onClick={handleCreateNote}>
					<IconPlus className="h-4 w-4" />
					Create Note
				</Button>
			</div>
			<ScrollArea className="p-2">
				<div className="space-y-3 gap-4 flex flex-row flex-wrap justify-center mx-auto">
					{notes.map((note: any) => (
						<NoteCard key={note.id} note={note} className="!max-w-[325px]" />
					))}
				</div>
			</ScrollArea>
		</div>
	);
}