"use client";

import { IconCopy, IconExternalLink } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Trash2 } from "lucide-react";
import { useState } from "react";

interface CopyableValueProps {
	value: string;
	type: "phone" | "email" | "url";
	className?: string;
	showCopyButton?: boolean;
	showDeleteButton?: boolean;
	onDelete?: () => void;
	onEmailClick?: (email: string) => void; // New prop for email compose functionality
}

export function CopyableValue({
	value,
	type,
	className,
	showCopyButton = true,
	showDeleteButton = false,
	onDelete,
	onEmailClick,
}: CopyableValueProps) {
	const [isHovered, setIsHovered] = useState(false);

	if (!value) return <span className="text-muted-foreground">—</span>;

	const getHref = () => {
		switch (type) {
			case "email":
				return `mailto:${value}`;
			case "phone":
				return `tel:${value}`;
			case "url":
				return value.startsWith("http") ? value : `https://${value}`;
			default:
				return value;
		}
	};

	const getDisplayValue = () => {
		if (type === "url") {
			// For URLs, show a cleaner version without protocol
			return value.replace(/^https?:\/\//, "").replace(/\/$/, "");
		}
		return value;
	};

	const href = getHref();
	const displayValue = getDisplayValue();

	const showActions = showCopyButton || showDeleteButton;

	return (
		<div
			className={cn(
				"inline-flex items-center bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg text-sm font-medium transition-all px-2 py-0.5 relative cursor-pointer group/copyable overflow-hidden",
				className,
			)}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			<a
				// href={href}
				target={type === "url" ? "_blank" : undefined}
				rel={type === "url" ? "noopener noreferrer" : undefined}
				className={cn(
					"text-blue-600 hover:text-blue-500 text-decoration-none flex items-center gap-1 flex-1 min-w-0 transition-all duration-150",
					showCopyButton && isHovered && "max-w-[calc(100%-2rem)]",
				)}
				onClick={(e) => {
					e.stopPropagation();
					if (type === "email" && onEmailClick) {
						e.preventDefault();
						onEmailClick(value);
					}
				}}
			>
				<span
					className={cn(
						"overflow-hidden text-ellipsis whitespace-nowrap",
						showCopyButton && isHovered && "block",
					)}
				>
					{displayValue}
				</span>
				{type === "url" && (
					<IconExternalLink className="h-3 w-3 flex-shrink-0" />
				)}
			</a>

			{showActions && (
				<>
					{/* Gradient overlay that appears on hover */}
					<div className="absolute top-0 right-0 bottom-0 pointer-events-none flex justify-end px-2 items-center group-hover/copyable:opacity-100 opacity-0 transition-all duration-150 bg-gradient-to-l from-blue-500/20 to-transparent w-12 rounded-r-lg" />
					{/* Action buttons that slide in from the right */}
					<div className="absolute top-0 right-0 bottom-0 flex gap-1 justify-end px-1 items-center translate-x-full group-hover/copyable:translate-x-0 group-hover/copyable:opacity-100 opacity-0 transition-all duration-150 rounded-r-lg">
						{showCopyButton && (
							<Button
								size="icon"
								variant="ghost"
								onClick={(e) => {
									e.stopPropagation();
									navigator.clipboard.writeText(value);
								}}
								className="h-5 w-5 p-0 text-blue-600 hover:text-blue-500 hover:bg-blue-500/10 rounded-full"
								aria-label="Copy"
							>
								<IconCopy className="h-3.5 w-3.5" />
							</Button>
						)}
						{showDeleteButton && (
							<Button
								size="icon"
								variant="ghost"
								onClick={(e) => {
									e.stopPropagation();
									onDelete && onDelete();
								}}
								className="h-5 w-5 p-0 text-blue-600 hover:text-destructive hover:bg-blue-500/10 rounded-full"
								aria-label="Delete"
							>
								<Trash2 className="h-3.5 w-3.5" />
							</Button>
						)}
					</div>
				</>
			)}
		</div>
	);
}
