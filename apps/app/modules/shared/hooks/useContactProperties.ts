import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Property } from "@repo/database/src/zod";

// Extended property interface with relation information
export interface PropertyWithRelation extends Property {
  relation?: string;
  linkedAt?: Date;
}

// Response from the contact properties API
export interface ContactPropertiesResponse {
  properties: PropertyWithRelation[];
}

// Link a property to a contact
export async function linkPropertyToContact({
  contactId,
  propertyId,
  organizationId,
  relation,
}: {
  contactId: string;
  propertyId: string;
  organizationId: string;
  relation: string;
}): Promise<PropertyWithRelation> {
  const response = await fetch(
    `/api/objects/contacts/${contactId}/properties`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        propertyId,
        organizationId,
        relation,
      }),
    }
  );
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to link property to contact");
  }
  
  const data = await response.json();
  return {
    ...data.linkedProperty.property,
    relation: data.linkedProperty.relation,
    linkedAt: new Date(data.linkedProperty.createdAt)
  };
}

// Unlink a property from a contact
export async function unlinkPropertyFromContact({
  contactId,
  propertyId,
  organizationId,
}: {
  contactId: string;
  propertyId: string;
  organizationId: string;
}): Promise<Property> {
  const response = await fetch(
    `/api/objects/contacts/${contactId}/properties?propertyId=${propertyId}&organizationId=${organizationId}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to unlink property from contact");
  }
  
  const data = await response.json();
  return data.property;
}

// Fetch properties linked to a contact
export async function fetchContactProperties(
  contactId: string,
  organizationId?: string
): Promise<PropertyWithRelation[]> {
  const url = `/api/objects/contacts/${contactId}/properties${
    organizationId ? `?organizationId=${organizationId}` : ""
  }`;
  
  const response = await fetch(url);
  if (!response.ok) {
    const error = await response.json().catch(() => ({}));
    
    // Enhance error with more context for 404s
    if (response.status === 404) {
      // Check if the error message suggests access issues vs not found
      const errorMessage = error.error || "Not found";
      if (errorMessage.toLowerCase().includes("organization") || errorMessage.toLowerCase().includes("access") || errorMessage.toLowerCase().includes("permission")) {
        const accessError = new Error("ACCESS_DENIED");
        (accessError as any).status = 403;
        (accessError as any).originalMessage = errorMessage;
        throw accessError;
      } else {
        const notFoundError = new Error("CONTACT_NOT_FOUND");
        (notFoundError as any).status = 404;
        (notFoundError as any).originalMessage = errorMessage;
        throw notFoundError;
      }
    }
    
    throw new Error(error.error || "Failed to fetch contact properties");
  }
  
  const data = await response.json();
  return data.properties || [];
}

// React Query Hooks
export function useContactProperties(contactId: string, organizationId: string) {
  return useQuery({
    queryKey: ["contact-properties", contactId, organizationId],
    queryFn: () => fetchContactProperties(contactId, organizationId),
    enabled: !!contactId && !!organizationId,
    staleTime: 1000 * 60, // Cache for 1 minute
  });
}

export function useLinkPropertyToContact() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: linkPropertyToContact,
    onSuccess: (updatedProperty, variables) => {
      // Update the property cache
      queryClient.setQueryData(
        ["property", variables.propertyId],
        updatedProperty
      );

      // Invalidate contact properties query
      queryClient.invalidateQueries({
        queryKey: ["contact-properties", variables.contactId],
      });

      // Invalidate properties queries to reflect the linkedContacts update
      queryClient.invalidateQueries({
        queryKey: ["properties", variables.organizationId],
      });
    },
  });
}

export function useUnlinkPropertyFromContact() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: unlinkPropertyFromContact,
    onSuccess: (updatedProperty, variables) => {
      // Update the property cache
      queryClient.setQueryData(
        ["property", variables.propertyId],
        updatedProperty
      );

      // Invalidate contact properties query
      queryClient.invalidateQueries({
        queryKey: ["contact-properties", variables.contactId],
      });

      // Invalidate properties queries to reflect the linkedContacts update
      queryClient.invalidateQueries({
        queryKey: ["properties", variables.organizationId],
      });
    },
  });
}

// Update a property relation
export async function updatePropertyRelation({
  contactId,
  propertyId,
  organizationId,
  relation,
}: {
  contactId: string;
  propertyId: string;
  organizationId: string;
  relation: string;
}): Promise<PropertyWithRelation> {
  const response = await fetch(
    `/api/objects/contacts/${contactId}/properties`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        propertyId,
        organizationId,
        relation,
      }),
    }
  );
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to update property relation");
  }
  
  const data = await response.json();
  return data.linkedProperty;
}

export function useUpdatePropertyRelation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: updatePropertyRelation,
    onSuccess: (updatedProperty, variables) => {
      // Update the property cache
      queryClient.setQueryData(
        ["property", variables.propertyId],
        updatedProperty
      );

      // Invalidate contact properties query
      queryClient.invalidateQueries({
        queryKey: ["contact-properties", variables.contactId],
      });

      // Invalidate properties queries to reflect the updated relation
      queryClient.invalidateQueries({
        queryKey: ["properties", variables.organizationId],
      });
    },
  });
}
