import { useQuery, type UseQueryResult } from "@tanstack/react-query";

interface PropertyData {
  id: string;
  name: string;
  propertyType?: string;
  status?: string;
  location?: {
    address?: string;
    city?: string;
    state?: string;
    zip?: string;
  };
  financials?: {
    listPrice?: number;
    salePrice?: number;
    estimatedValue?: number;
  };
  image?: string;
}

interface PropertiesResponse {
  properties: PropertyData[];
  count: number;
  totalCount: number;
  hasMore: boolean;
}

/**
 * Fetch all properties for an organization from the API
 */
async function fetchProperties(organizationId: string): Promise<PropertiesResponse> {
  const response = await fetch(
    `/api/objects/properties?organizationId=${organizationId}&limit=1000&skip=0`,
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch properties: ${response.statusText}`);
  }

  const data = await response.json();
  
  // The API returns { properties: [...], count, totalCount, hasMore }
  return {
    properties: data.properties || [],
    count: data.count || 0,
    totalCount: data.totalCount || 0,
    hasMore: data.hasMore || false,
  };
}

/**
 * Hook to fetch properties for an organization
 */
export function useProperties(organizationId: string): UseQueryResult<PropertyData[], Error> {
  return useQuery({
    queryKey: ["properties", organizationId],
    queryFn: async () => {
      const response = await fetchProperties(organizationId);
      return response.properties;
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to fetch all properties with more detailed response info
 */
export function usePropertiesDetailed(organizationId: string): UseQueryResult<PropertiesResponse, Error> {
  return useQuery({
    queryKey: ["properties-detailed", organizationId],
    queryFn: () => fetchProperties(organizationId),
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
