import type { SVGProps } from "react";

export function ZeronIcon(props: SVGProps<SVGSVGElement>) {
	return (
		<svg
			width="100%"
			height="100%"
			viewBox="0 0 3544 3544"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink"
			xmlSpace="preserve"
			style={{
				fillRule: "evenodd",
				clipRule: "evenodd",
				strokeLinejoin: "round",
				strokeMiterlimit: 2,
			}}
			{...props}
		>
			<g transform="matrix(1.88096,0,0,1.88096,-1794.82,-2059.91)">
				<g id="Layer1">
					<text
						x="995.817"
						y="2529.94"
						style={{
							fontFamily:
								"'IBMPlexSerif-Bold', 'IBM Plex Serif', serif",
							fontWeight: 700,
							fontSize: "1467.03px",
							fill: "currentColor",
						}}
					>
						Z
						<tspan x="1946.45" y="2529.94">
							e
						</tspan>
					</text>
				</g>
			</g>
		</svg>
	);
}
