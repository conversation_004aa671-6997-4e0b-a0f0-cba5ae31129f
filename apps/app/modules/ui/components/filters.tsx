import { UserAvatar } from "@shared/components/UserAvatar";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import {
	Calendar,
	CalendarPlus,
	CalendarSync,
	Check,
	Circle,
	CircleAlert,
	CircleCheck,
	CircleDashed,
	CircleDotDashed,
	CircleEllipsis,
	CircleX,
	SignalHigh,
	SignalLow,
	SignalMedium,
	Tag,
	UserCircle,
	X,
	CircleDot,
	CalendarClock,
} from "lucide-react";
import {
	type Dispatch,
	type SetStateAction,
	useEffect,
	useRef,
	useState,
} from "react";
import { v4 as uuidv4 } from "uuid";
import { IconTag } from "@tabler/icons-react";

interface AnimateChangeInHeightProps {
	children: React.ReactNode;
	className?: string;
}

export const AnimateChangeInHeight: React.FC<AnimateChangeInHeightProps> = ({
	children,
	className,
}) => {
	const containerRef = useRef<HTMLDivElement | null>(null);
	const [height, setHeight] = useState<number | "auto">("auto");

	useEffect(() => {
		if (containerRef.current) {
			const resizeObserver = new ResizeObserver((entries) => {
				// We only have one entry, so we can use entries[0].
				const observedHeight = entries[0]?.contentRect.height;
				setHeight(observedHeight ?? "auto");
			});

			resizeObserver.observe(containerRef.current);

			return () => {
				// Cleanup the observer when the component is unmounted
				resizeObserver.disconnect();
			};
		}
	}, []);

	return (
		<motion.div
			className={cn(className, "overflow-hidden")}
			style={{ height }}
			animate={{ height }}
			transition={{ duration: 0.1, damping: 0.2, ease: "easeIn" }}
		>
			<div ref={containerRef}>{children}</div>
		</motion.div>
	);
};

export enum FilterType {
	STATUS = "Status",
	ASSIGNEE = "Assignee",
	PRIORITY = "Priority",
	DUE_DATE = "Due date",
	CREATED_DATE = "Created",
	UPDATED_DATE = "Updated",
	TAG = "Tag",
}

export enum FilterOperator {
	IS = "is",
	IS_NOT = "is not",
	IS_ANY_OF = "is any of",
	BEFORE = "before",
	AFTER = "after",
}

export enum Status {
	BACKLOG = "backlog",
	TODO = "todo",
	IN_PROGRESS = "in_progress",
	REVIEW = "review",
	DONE = "done",
}

export enum Assignee {
	NO_ASSIGNEE = "no_assignee",
}

export enum Priority {
	NO_PRIORITY = "no_priority",
	URGENT = "urgent",
	HIGH = "high",
	MEDIUM = "medium",
	LOW = "low",
}

export enum DueDate {
	TODAY = "today",
	TOMORROW = "tomorrow",
	YESTERDAY = "yesterday",
	THIS_WEEK = "this_week",
	NEXT_WEEK = "next_week",
	LAST_WEEK = "last_week",
	THIS_MONTH = "this_month",
	LAST_MONTH = "last_month",
	IN_THE_PAST = "in_the_past",
}

export type FilterOption = {
	name: FilterType | Status | Priority | DueDate | Assignee;
	icon: React.ReactNode | undefined;
	label?: string;
};

export type Filter = {
	id: string;
	type: FilterType;
	operator: FilterOperator;
	value: string[];
};

interface TagOption {
	id: string;
	name: string;
	color: string;
}

interface MemberOption {
	id: string;
	name: string;
	image?: string | null;
}

const FilterIcon = ({ type }: { type: FilterType | Status | Priority | DueDate | Assignee }) => {
	switch (type) {
		case FilterType.STATUS:
			return <Circle className="size-3.5" />;
		case FilterType.ASSIGNEE:
			return <UserCircle className="size-3.5" />;
		case FilterType.PRIORITY:
			return <SignalHigh className="size-3.5" />;
		case FilterType.DUE_DATE:
			return <Calendar className="size-3.5" />;
		case FilterType.CREATED_DATE:
			return <CalendarPlus className="size-3.5" />;
		case FilterType.UPDATED_DATE:
			return <CalendarClock className="size-3.5" />;
		case FilterType.TAG:
			return <IconTag className="size-3.5" />;
		case Status.BACKLOG:
			return <Circle className="size-3.5 text-muted-foreground" />;
		case Status.TODO:
			return <CircleDot className="size-3.5 text-orange-400" />;
		case Status.IN_PROGRESS:
			return <CircleDotDashed className="size-3.5 text-yellow-400" />;
		case Status.REVIEW:
			return <CircleEllipsis className="size-3.5 text-green-400" />;
		case Status.DONE:
			return <CircleCheck className="size-3.5 text-blue-400" />;
		case Priority.NO_PRIORITY:
			return <Circle className="size-3.5 text-muted-foreground" />;
		case Priority.URGENT:
			return <CircleAlert className="size-3.5 text-red-500" />;
		case Priority.HIGH:
			return <SignalHigh className="size-3.5 text-orange-500" />;
		case Priority.MEDIUM:
			return <SignalMedium className="size-3.5 text-yellow-500" />;
		case Priority.LOW:
			return <SignalLow className="size-3.5 text-green-500" />;
		case Assignee.NO_ASSIGNEE:
			return <UserCircle className="size-3.5" />;
		default:
			return undefined;
	}
};

export const filterViewOptions: FilterOption[][] = [
	[
		{
			name: FilterType.STATUS,
			icon: <FilterIcon type={FilterType.STATUS} />,
		},
		{
			name: FilterType.ASSIGNEE,
			icon: <FilterIcon type={FilterType.ASSIGNEE} />,
		},
		{
			name: FilterType.PRIORITY,
			icon: <FilterIcon type={FilterType.PRIORITY} />,
		},
		{
			name: FilterType.TAG,
			icon: <IconTag className="h-4 w-4" />,
		},
	],
	[
		{
			name: FilterType.DUE_DATE,
			icon: <FilterIcon type={FilterType.DUE_DATE} />,
		},
		{
			name: FilterType.CREATED_DATE,
			icon: <FilterIcon type={FilterType.CREATED_DATE} />,
		},
		{
			name: FilterType.UPDATED_DATE,
			icon: <FilterIcon type={FilterType.UPDATED_DATE} />,
		},
	],
];

export const statusFilterOptions: FilterOption[] = Object.values(Status).map(
	(status) => ({
		name: status,
		icon: <FilterIcon type={status} />,
	}),
);

export const assigneeFilterOptions: FilterOption[] = Object.values(
	Assignee,
).map((assignee) => ({
	name: assignee,
	icon: <UserCircle className="size-3.5" />,
}));

export const priorityFilterOptions: FilterOption[] = Object.values(
	Priority,
).map((priority) => ({
	name: priority,
	icon: <FilterIcon type={priority} />,
}));

export const dateFilterOptions: FilterOption[] = Object.values(DueDate).map(
	(date) => ({
		name: date,
		icon: undefined,
	}),
);

export const filterViewToFilterOptions: Record<FilterType, FilterOption[]> = {
	[FilterType.STATUS]: [
		{ name: Status.BACKLOG, icon: <FilterIcon type={Status.BACKLOG} /> },
		{ name: Status.TODO, icon: <FilterIcon type={Status.TODO} /> },
		{ name: Status.IN_PROGRESS, icon: <FilterIcon type={Status.IN_PROGRESS} /> },
		{ name: Status.REVIEW, icon: <FilterIcon type={Status.REVIEW} /> },
		{ name: Status.DONE, icon: <FilterIcon type={Status.DONE} /> },
	],
	[FilterType.ASSIGNEE]: [],
	[FilterType.PRIORITY]: [
		{ name: Priority.NO_PRIORITY, icon: <FilterIcon type={Priority.NO_PRIORITY} /> },
		{ name: Priority.URGENT, icon: <FilterIcon type={Priority.URGENT} /> },
		{ name: Priority.HIGH, icon: <FilterIcon type={Priority.HIGH} /> },
		{ name: Priority.MEDIUM, icon: <FilterIcon type={Priority.MEDIUM} /> },
		{ name: Priority.LOW, icon: <FilterIcon type={Priority.LOW} /> },
	],
	[FilterType.DUE_DATE]: [
		{ name: DueDate.TODAY, icon: <FilterIcon type={DueDate.TODAY} /> },
		{ name: DueDate.TOMORROW, icon: <FilterIcon type={DueDate.TOMORROW} /> },
		{ name: DueDate.THIS_WEEK, icon: <FilterIcon type={DueDate.THIS_WEEK} /> },
		{ name: DueDate.NEXT_WEEK, icon: <FilterIcon type={DueDate.NEXT_WEEK} /> },
		{ name: DueDate.THIS_MONTH, icon: <FilterIcon type={DueDate.THIS_MONTH} /> },
		{ name: DueDate.IN_THE_PAST, icon: <FilterIcon type={DueDate.IN_THE_PAST} /> },
	],
	[FilterType.CREATED_DATE]: [
		{ name: DueDate.TODAY, icon: <FilterIcon type={DueDate.TODAY} /> },
		{ name: DueDate.YESTERDAY, icon: <FilterIcon type={DueDate.YESTERDAY} /> },
		{ name: DueDate.THIS_WEEK, icon: <FilterIcon type={DueDate.THIS_WEEK} /> },
		{ name: DueDate.LAST_WEEK, icon: <FilterIcon type={DueDate.LAST_WEEK} /> },
		{ name: DueDate.THIS_MONTH, icon: <FilterIcon type={DueDate.THIS_MONTH} /> },
		{ name: DueDate.LAST_MONTH, icon: <FilterIcon type={DueDate.LAST_MONTH} /> },
	],
	[FilterType.UPDATED_DATE]: [
		{ name: DueDate.TODAY, icon: <FilterIcon type={DueDate.TODAY} /> },
		{ name: DueDate.YESTERDAY, icon: <FilterIcon type={DueDate.YESTERDAY} /> },
		{ name: DueDate.THIS_WEEK, icon: <FilterIcon type={DueDate.THIS_WEEK} /> },
		{ name: DueDate.LAST_WEEK, icon: <FilterIcon type={DueDate.LAST_WEEK} /> },
		{ name: DueDate.THIS_MONTH, icon: <FilterIcon type={DueDate.THIS_MONTH} /> },
		{ name: DueDate.LAST_MONTH, icon: <FilterIcon type={DueDate.LAST_MONTH} /> },
	],
	[FilterType.TAG]: [],
};

const filterOperators = ({
	filterType,
	filterValues,
}: {
	filterType: FilterType;
	filterValues: string[];
}) => {
	switch (filterType) {
		case FilterType.STATUS:
		case FilterType.ASSIGNEE:
		case FilterType.PRIORITY:
		case FilterType.TAG:
			if (Array.isArray(filterValues) && filterValues.length > 1) {
				return [FilterOperator.IS_ANY_OF, FilterOperator.IS_NOT];
			}
			return [FilterOperator.IS, FilterOperator.IS_NOT];
		case FilterType.DUE_DATE:
		case FilterType.CREATED_DATE:
		case FilterType.UPDATED_DATE:
			if (filterValues?.includes(DueDate.IN_THE_PAST)) {
				return [FilterOperator.IS, FilterOperator.IS_NOT];
			}
			return [FilterOperator.BEFORE, FilterOperator.AFTER];
		default:
			return [];
	}
};

const FilterOperatorDropdown = ({
	filterType,
	operator,
	filterValues,
	setOperator,
}: {
	filterType: FilterType;
	operator: FilterOperator;
	filterValues: string[];
	setOperator: (operator: FilterOperator) => void;
}) => {
	const operators = filterOperators({ filterType, filterValues });
	return (
		<DropdownMenu>
			<DropdownMenuTrigger className="bg-muted hover:bg-muted/50 px-1.5 py-1 text-muted-foreground hover:text-primary transition shrink-0">
				{operator}
			</DropdownMenuTrigger>
			<DropdownMenuContent align="start" className="w-fit min-w-fit">
				{operators.map((operator) => (
					<DropdownMenuItem
						key={operator}
						onClick={() => setOperator(operator)}
					>
						{operator}
					</DropdownMenuItem>
				))}
			</DropdownMenuContent>
		</DropdownMenu>
	);
};

const FilterValueCombobox = ({
	filterType,
	filterValues,
	setFilterValues,
	options,
}: {
	filterType: FilterType;
	filterValues: string[];
	setFilterValues: (filterValues: string[]) => void;
	options?: string[] | MemberOption[] | TagOption[];
}) => {
	const [open, setOpen] = useState(false);
	const [commandInput, setCommandInput] = useState("");
	const commandInputRef = useRef<HTMLInputElement>(null);
	let availableOptions: any[] = [];
	if (
		filterType.toLowerCase() === "assignee" ||
		filterType.toLowerCase() === "createdby"
	) {
		availableOptions = Array.isArray(options) ? options : [];
	} else if (filterType === FilterType.TAG) {
		availableOptions = (options as TagOption[]) || [];
	} else {
		availableOptions =
			options && options.length > 0
				? (options as string[]).map((name) => ({
						name,
						icon: undefined,
					}))
				: filterViewToFilterOptions[filterType] || [];
	}
	const filteredOptions = commandInput
		? availableOptions.filter((option) =>
				option.name.toLowerCase().includes(commandInput.toLowerCase()),
			)
		: availableOptions;
	const nonSelectedFilterValues = filteredOptions.filter(
		(filter) => !filterValues.includes(filter.name),
	);
	return (
		<Popover
			open={open}
			onOpenChange={(open) => {
				setOpen(open);
				if (!open) {
					setTimeout(() => {
						setCommandInput("");
					}, 200);
				}
			}}
		>
			<PopoverTrigger
				className="rounded-none px-1.5 py-1 bg-muted hover:bg-muted/50 transition
  text-muted-foreground hover:text-primary shrink-0"
			>
				<div className="flex gap-1.5 items-center">
					{filterType !== FilterType.PRIORITY && (
						<div
							className={cn(
								"flex items-center flex-row",
								(filterType as string) === FilterType.PRIORITY
									? "-space-x-1"
									: "-space-x-1.5",
							)}
						>
							<AnimatePresence mode="popLayout">
								{filterValues
									?.slice(0, 3)
									.map((value: string) => (
										<motion.div
											key={value}
											initial={{ opacity: 0, x: -10 }}
											animate={{ opacity: 1, x: 0 }}
											exit={{ opacity: 0, x: -10 }}
											transition={{ duration: 0.2 }}
										>
											{filterType.toLowerCase() ===
												"assignee" ||
											filterType.toLowerCase() ===
												"createdby" ? (
												<UserAvatar
													name={value}
													className="size-4 mr-1"
												/>
											) : filterType === FilterType.TAG ? (
												<div
													className="size-4 mr-1 rounded-full"
													style={{
														backgroundColor: (
															availableOptions.find(
																(opt) =>
																	opt.name ===
																	value,
															) as TagOption
														)?.color,
													}}
												/>
											) : (
												<FilterIcon
													type={value as FilterType}
												/>
											)}
										</motion.div>
									))}
							</AnimatePresence>
						</div>
					)}
					{filterValues?.length === 1
						? filterValues?.[0]
						: `${filterValues?.length} selected`}
				</div>
			</PopoverTrigger>
			<PopoverContent className="w-[200px] p-0">
				<AnimateChangeInHeight>
					<Command>
						<CommandInput
							placeholder={filterType}
							className="h-9"
							value={commandInput}
							onInputCapture={(e) => {
								setCommandInput(e.currentTarget.value);
							}}
							ref={commandInputRef}
						/>
						<CommandList>
							<CommandEmpty>No results found.</CommandEmpty>
							<CommandGroup>
								{filteredOptions.map((value) => (
									<CommandItem
										key={value.name}
										className="group flex gap-2 items-center"
										onSelect={() => {
											setFilterValues(
												filteredOptions.map(
													(v) => v.name,
												),
											);
											setTimeout(() => {
												setCommandInput("");
											}, 200);
											setOpen(false);
										}}
									>
										<Checkbox checked={true} />
										{filterType.toLowerCase() ===
											"assignee" ||
										filterType.toLowerCase() ===
											"createdby" ? (
											<UserAvatar
												name={value.name}
												className="size-4 mr-1"
											/>
										) : filterType === FilterType.TAG ? (
											<div
												className="size-4 mr-1 rounded-full"
												style={{
													backgroundColor:
														value.color,
												}}
											/>
										) : (
											<FilterIcon
												type={value.name as FilterType}
											/>
										)}
										{value.name}
									</CommandItem>
								))}
							</CommandGroup>
							{nonSelectedFilterValues?.length > 0 && (
								<>
									<CommandSeparator />
									<CommandGroup>
										{nonSelectedFilterValues.map(
											(filter, i) => {
												if (
													filterType.toLowerCase() ===
														"assignee" ||
													filterType.toLowerCase() ===
														"createdby"
												) {
													return (
														<CommandItem
															className="group flex gap-2 items-center"
															key={filter.id}
															value={filter.name}
															onSelect={(
																currentValue: string,
															) => {
																setFilterValues(
																	[
																		...filterValues,
																		currentValue,
																	],
																);
																setTimeout(
																	() => {
																		setCommandInput(
																			"",
																		);
																	},
																	200,
																);
																setOpen(false);
															}}
														>
															<Checkbox
																checked={false}
																className="opacity-0 group-data-[selected=true]:opacity-100"
															/>
															<UserAvatar
																name={
																	filter.name
																}
																avatarUrl={
																	filter.image
																}
																className="size-4 mr-1"
															/>
															<span className="text-accent-foreground">
																{filter.name}
															</span>
														</CommandItem>
													);
												}
												if (
													filterType === FilterType.TAG
												) {
													return (
														<CommandItem
															className="group flex gap-2 items-center"
															key={filter.id}
															value={filter.name}
															onSelect={(
																currentValue: string,
															) => {
																setFilterValues(
																	[
																		...filterValues,
																		currentValue,
																	],
																);
																setTimeout(
																	() => {
																		setCommandInput(
																			"",
																		);
																	},
																	200,
																);
																setOpen(false);
															}}
														>
															<Checkbox
																checked={false}
																className="opacity-0 group-data-[selected=true]:opacity-100"
															/>
															<div
																className="size-4 mr-1 rounded-full"
																style={{
																	backgroundColor:
																		filter.color,
																}}
															/>
															<span className="text-accent-foreground">
																{filter.name}
															</span>
														</CommandItem>
													);
												}
												if (
													typeof filter.name !==
														"string" ||
													!(
														filter.name in FilterType
													)
												)
													return null;
												const f =
													filter as FilterOption;
												return (
													<CommandItem
														className="group flex gap-2 items-center"
														key={f.name}
														value={f.name}
														onSelect={(
															currentValue: string,
														) => {
															setFilterValues([
																...filterValues,
																currentValue,
															]);
															setTimeout(() => {
																setCommandInput(
																	"",
																);
															}, 200);
															setOpen(false);
														}}
													>
														<Checkbox
															checked={false}
															className="opacity-0 group-data-[selected=true]:opacity-100"
														/>
														{f.icon}
														<span className="text-accent-foreground">
															{f.name}
														</span>
														{f.label && (
															<span className="text-muted-foreground text-xs ml-auto">
																{f.label}
															</span>
														)}
													</CommandItem>
												);
											},
										)}
									</CommandGroup>
								</>
							)}
						</CommandList>
					</Command>
				</AnimateChangeInHeight>
			</PopoverContent>
		</Popover>
	);
};

const FilterValueDateCombobox = ({
	filterType,
	filterValues,
	setFilterValues,
}: {
	filterType: FilterType;
	filterValues: string[];
	setFilterValues: (filterValues: string[]) => void;
}) => {
	const [open, setOpen] = useState(false);
	const [commandInput, setCommandInput] = useState("");
	const commandInputRef = useRef<HTMLInputElement>(null);
	return (
		<Popover
			open={open}
			onOpenChange={(open) => {
				setOpen(open);
				if (!open) {
					setTimeout(() => {
						setCommandInput("");
					}, 200);
				}
			}}
		>
			<PopoverTrigger
				className="rounded-none px-1.5 py-1 bg-muted hover:bg-muted/50 transition
  text-muted-foreground hover:text-primary shrink-0"
			>
				{filterValues?.[0]}
			</PopoverTrigger>
			<PopoverContent className="w-fit p-0">
				<AnimateChangeInHeight>
					<Command>
						<CommandInput
							placeholder={filterType}
							className="h-9"
							value={commandInput}
							onInputCapture={(e) => {
								setCommandInput(e.currentTarget.value);
							}}
							ref={commandInputRef}
						/>
						<CommandList>
							<CommandEmpty>No results found.</CommandEmpty>
							<CommandGroup>
								{filterViewToFilterOptions[filterType].map(
									(filter: FilterOption) => (
										<CommandItem
											className="group text-muted-foreground flex gap-2 items-center"
											key={filter.name}
											value={filter.name}
											onSelect={(
												currentValue: string,
											) => {
												setFilterValues([currentValue]);
												setTimeout(() => {
													setCommandInput("");
												}, 200);
												setOpen(false);
											}}
										>
											<span className="text-accent-foreground">
												{filter.name}
											</span>
											<Check
												className={cn(
													"ml-auto",
													filterValues.includes(
														filter.name,
													)
														? "opacity-100"
														: "opacity-0",
												)}
											/>
										</CommandItem>
									),
								)}
							</CommandGroup>
						</CommandList>
					</Command>
				</AnimateChangeInHeight>
			</PopoverContent>
		</Popover>
	);
};

export default function Filters({
	filters,
	setFilters,
	filterOptions = {},
}: {
	filters: Filter[];
	setFilters: Dispatch<SetStateAction<Filter[]>>;
	filterOptions?: Record<string, string[] | MemberOption[]>;
}) {
	return (
		<div className="flex gap-2">
			{filters
				.filter((filter) => filter.value?.length > 0)
				.map((filter) => (
					<div
						key={filter.id}
						className="flex gap-[1px] items-center text-xs"
					>
						<div className="flex gap-1.5 shrink-0 rounded-l bg-muted px-1.5 py-1 items-center">
							<FilterIcon type={filter.type} />
							{filter.type}
						</div>
						<FilterOperatorDropdown
							filterType={filter.type}
							operator={filter.operator}
							filterValues={filter.value}
							setOperator={(operator) => {
								setFilters((prev) =>
									prev.map((f) =>
										f.id === filter.id
											? { ...f, operator }
											: f,
									),
								);
							}}
						/>
						{filter.type === FilterType.CREATED_DATE ||
						filter.type === FilterType.UPDATED_DATE ||
						filter.type === FilterType.DUE_DATE ? (
							<FilterValueDateCombobox
								filterType={filter.type}
								filterValues={filter.value}
								setFilterValues={(filterValues) => {
									setFilters((prev) =>
										prev.map((f) =>
											f.id === filter.id
												? { ...f, value: filterValues }
												: f,
										),
									);
								}}
							/>
						) : (
							<FilterValueCombobox
								options={
									filterOptions[filter.type.toLowerCase()] ??
									[]
								}
								filterType={filter.type}
								filterValues={filter.value}
								setFilterValues={(filterValues) => {
									setFilters((prev) =>
										prev.map((f) =>
											f.id === filter.id
												? { ...f, value: filterValues }
												: f,
										),
									);
								}}
							/>
						)}
						<Button
							variant="ghost"
							size="icon"
							onClick={() => {
								setFilters((prev) =>
									prev.filter((f) => f.id !== filter.id),
								);
							}}
							className="bg-muted rounded-l-none rounded-r-sm h-6 w-6 text-muted-foreground hover:text-primary hover:bg-muted/50 transition shrink-0"
						>
							<X className="size-3" />
						</Button>
					</div>
				))}
		</div>
	);
}
