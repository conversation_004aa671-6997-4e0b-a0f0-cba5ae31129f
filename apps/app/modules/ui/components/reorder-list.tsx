"use client";

import { Reorder } from "framer-motion";
import React, { useCallback, useMemo, useEffect, useState } from "react";
import { useLocalStorage } from "@app/shared/lib/local-storage";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface Item {
	id: string;
	title: string;
}

interface ReorderListProps {
	initialItems: Item[];
	renderItem: (item: Item) => React.ReactNode;
	storageKey: string;
}

const ReorderList = ({ initialItems, renderItem, storageKey }: ReorderListProps) => {
	const [storedItems, setStoredItems] = useLocalStorage(storageKey, initialItems);
	const [isMounted, setIsMounted] = useState(false);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	const items = useMemo(() => {
		// During hydration, always use initialItems to prevent mismatch
		if (!isMounted) {
			return initialItems;
		}
		
		if (Array.isArray(storedItems) && storedItems.length > 0) {
			const hasValidStructure = storedItems.every(
				(storedItem) => storedItem && typeof storedItem === 'object' && 'id' in storedItem && 'title' in storedItem
			);
			return hasValidStructure ? storedItems : initialItems;
		}
		return initialItems;
	}, [storedItems, initialItems, isMounted]);

	const handleReorder = useCallback((newOrder: Item[]) => {
		// Only update localStorage after initial mount to prevent hydration issues
		if (isMounted) {
			setStoredItems(newOrder);
		}
	}, [setStoredItems, isMounted]);

	return (
		<Reorder.Group axis="y" onReorder={handleReorder} values={items}>
			{items.map((item) => (
				<div key={item.id}>
					{renderItem(item)}
				</div>
			))}
		</Reorder.Group>
	);
};

export default React.memo(ReorderList); 