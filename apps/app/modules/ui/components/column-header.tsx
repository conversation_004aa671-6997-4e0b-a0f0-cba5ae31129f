import { cn } from "@ui/lib"
import { Icon } from "@tabler/icons-react"

export const ColumnHeader = ({ title, className, icon: ItemIcon }: {
  title: string,
  className?: string,
  icon?: Icon
}) => {
  return (
    <div className="flex items-center gap-1">
      {ItemIcon && <ItemIcon className="text-muted-foreground/50 size-4" />}
      <p className={cn("text-xs cursor-default font-normal text-gray-500", className)}>{title}</p>
    </div>
  )
}