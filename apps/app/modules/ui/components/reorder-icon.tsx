"use client";

import { Reorder, useDragControls } from "framer-motion";
import { IconGripVertical } from "@tabler/icons-react";
import { cn } from "@ui/lib";

interface ReorderIconProps {
	dragControls?: ReturnType<typeof useDragControls>;
	className?: string;
}

export const ReorderIcon = ({ dragControls, className }: ReorderIconProps) => {
	return (
		<div
			className={cn(
				"cursor-grab active:cursor-grabbing rounded flex items-center justify-center p-1",
				className
			)}
			onPointerDown={(e) => dragControls?.start(e)}
		>
			<IconGripVertical className="h-3 w-3 text-muted-foreground" />
		</div>
	);
}; 