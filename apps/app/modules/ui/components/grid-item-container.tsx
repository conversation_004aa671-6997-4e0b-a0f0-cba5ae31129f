"use client";

import React, { useState, useCallback } from "react";
import { Label } from "@ui/components/label";
import { IconChevronUp } from "@tabler/icons-react";
import { cn } from "@ui/lib";
import { Reorder, useDragControls, useMotionValue, motion, AnimatePresence } from "framer-motion";
import { useRaisedShadow } from "@app/shared/hooks/use-raised-shadow";
import { ReorderIcon } from "./reorder-icon";

interface Item {
	id: string;
	title: string;
}

interface GridItemContainerProps {
	children: React.ReactNode;
	item: Item;
	className?: string;
	contentClassName?: string;
	icon?: React.ReactNode;
	icons?: React.ReactNode;
	onIconClick?: () => void;
}

const GridItemContainer = React.memo(({ 
	children, 
	item, 
	className, 
	contentClassName,
	icon, 
	icons, 
	onIconClick,
}: GridItemContainerProps) => {
	const [isOpen, setIsOpen] = useState(true);
	const [isDragging, setIsDragging] = useState(false);
	const [isHovered, setIsHovered] = useState(false);
	const y = useMotionValue(0);
	const boxShadow = useRaisedShadow(y);
	const dragControls = useDragControls();

	const handleToggle = useCallback(() => {
		setIsOpen(prev => !prev);
	}, []);

	const handleDragStart = useCallback(() => {
		setIsDragging(true);
	}, []);

	const handleDragEnd = useCallback(() => {
		setIsDragging(false);
	}, []);

	return (
		<Reorder.Item
			value={item}
			id={item.id}
			style={{ boxShadow, y }}
			dragListener={false}
			dragControls={dragControls}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
			className="cursor-default"
		>
			<div className={cn(
				"border-b border-border",
				isDragging && "rounded-lg shadow-lg border border-border",
				className
			)}>
				<div 
					className="flex items-center justify-between p-3 border-b border-border"
					onMouseEnter={() => {
						setIsHovered(true);
					}}
					onMouseLeave={() => {
						setIsHovered(false);
					}}
				>
					<div className="flex items-center hover:bg-muted/50 rounded-md">
						<div className={cn(
							"transition-opacity duration-200",
							isHovered ? "opacity-100" : "opacity-0"
						)}>
							<ReorderIcon dragControls={dragControls} />
						</div>
						<Label>
							{item.title}
						</Label>
					</div>
					
					<div className="flex items-center gap-2">
						{icon && (
							<button
								onClick={onIconClick}
								className="p-1 rounded-md hover:bg-muted/50 transition-colors duration-200"
								type="button"
							>
								{icon}
							</button>
						)}
						{icons}
						<button 
							onClick={handleToggle} 
							className="p-1 rounded-md hover:bg-muted/50 transition-transform duration-200"
							type="button"
							aria-label={isOpen ? "Collapse section" : "Expand section"}
						>
							<motion.div
								animate={{ rotate: isOpen ? 0 : 180 }}
								transition={{ duration: 0.2, ease: "easeInOut" }}
							>
								<IconChevronUp className="h-4 w-4 text-muted-foreground" />
							</motion.div>
						</button>
					</div>
				</div>

				{/* Content with smooth animation */}
				<AnimatePresence initial={false}>
					{isOpen && (
						<motion.div
							initial={{ height: 0, opacity: 0 }}
							animate={{ 
								height: "auto", 
								opacity: 1,
								transition: {
									height: { duration: 0.3, ease: [0.04, 0.62, 0.23, 0.98] },
									opacity: { duration: 0.2, delay: 0.1 }
								}
							}}
							exit={{ 
								height: 0, 
								opacity: 0,
								transition: {
									height: { duration: 0.3, ease: [0.04, 0.62, 0.23, 0.98] },
									opacity: { duration: 0.2 }
								}
							}}
							style={{ overflow: "hidden" }}
						>
							<div className={cn("p-3", contentClassName)}>
								{children}
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</Reorder.Item>
	);
});

GridItemContainer.displayName = "GridItemContainer";

export default GridItemContainer; 