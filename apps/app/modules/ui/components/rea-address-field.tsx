'use client';

import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from '@ui/components/command';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@ui/components/form';
import { Input } from '@ui/components/input';
import { Popover, PopoverContent, PopoverTrigger } from '@ui/components/popover';
import { cn } from '@ui/lib';
import { Loader2, MapPin } from 'lucide-react';
import * as React from 'react';
import { type FieldPath, type FieldValues, type UseFormRegisterReturn, useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { getReaAutocomplete, type ReaAutocompleteSuggestion } from '@shared/actions/rea';

const DEBOUNCE_TIME = 300;

export const reaAddressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().optional(),
  fullAddress: z.string().optional(),
  reaId: z.string().optional(),
});

export type ReaAddressData = z.infer<typeof reaAddressSchema>;

interface ReaAddressFieldProps extends React.ComponentProps<typeof Input> {
  register: UseFormRegisterReturn<FieldPath<FieldValues>>;
  label?: string;
  placeholder?: string;
  description?: string;
}

export function ReaAddressField({
  register,
  label = 'Address',
  placeholder = 'Enter property address',
  description,
  ...props
}: ReaAddressFieldProps) {
  const [inputValue, setInputValue] = React.useState('');
  const [suggestions, setSuggestions] = React.useState<ReaAutocompleteSuggestion[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [showSuggestions, setShowSuggestions] = React.useState(false);
  const [selectedIndex, setSelectedIndex] = React.useState(-1);
  const [lastSelectedAddress, setLastSelectedAddress] = React.useState<string>('');
  const debounceTimerRef = React.useRef<NodeJS.Timeout | null>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const popoverRef = React.useRef<HTMLDivElement>(null);
  const form = useFormContext();

  const searchAddresses = async (query: string) => {
    if (!query || query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setLoading(true);
    try {
      const result = await getReaAutocomplete({ query });

      if (result.error) {
        throw new Error(result.error);
      }

      setSuggestions(result.suggestions || []);
      setShowSuggestions(result.suggestions?.length > 0);
      setSelectedIndex(-1);
    } catch (error) {
      console.error('Error searching REA addresses:', error);
      setSuggestions([]);
      setShowSuggestions(false);
      // Don't show error toast for now - just fail silently
      // toast.error('Unable to search properties. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Don't search if the current input matches the last selected address
    if (inputValue === lastSelectedAddress) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    // Clear last selected address if user is typing something different
    if (lastSelectedAddress && inputValue !== lastSelectedAddress) {
      setLastSelectedAddress('');
    }

    if (inputValue.length >= 3) {
      debounceTimerRef.current = setTimeout(() => {
        searchAddresses(inputValue);
      }, DEBOUNCE_TIME);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [inputValue, lastSelectedAddress]);

  const handleSelectAddress = async (suggestion: ReaAutocompleteSuggestion) => {
    // Store the selected address to prevent re-searching
    setLastSelectedAddress(suggestion.address);
    setInputValue(suggestion.address);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    setSuggestions([]); // Clear suggestions immediately

    // Populate form fields from REA suggestion
    const addressFieldName = register.name.replace('.fullAddress', '');
    
    // Parse the full address to extract just the street portion
    // Example: "192 Orchard St, New York, NY, 10002" -> "192 Orchard St"
    const addressParts = suggestion.address.split(',');
    const streetPortion = addressParts[0]?.trim() || suggestion.address;
    
    form.setValue(`${addressFieldName}.fullAddress`, suggestion.address);
    form.setValue(`${addressFieldName}.street`, streetPortion);
    form.setValue(`${addressFieldName}.city`, suggestion.city || '');
    form.setValue(`${addressFieldName}.state`, suggestion.state || '');
    form.setValue(`${addressFieldName}.postalCode`, suggestion.zip || '');
    form.setValue(`${addressFieldName}.zip`, suggestion.zip || '');
    form.setValue(`${addressFieldName}.country`, 'United States');
    form.setValue(`${addressFieldName}.reaId`, suggestion.id || '');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex((prev) => (prev < suggestions.length - 1 ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : suggestions.length - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSelectAddress(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleFocus = () => {
    if (suggestions.length > 0 && inputValue.length >= 3) {
      setShowSuggestions(true);
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (popoverRef.current?.contains(relatedTarget)) {
      return;
    }

    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 150);
  };

  return (
    <FormField
      {...register}
      name={`${register.name}.fullAddress`}
      render={({ field }) => (
        <FormItem>
          <FormLabel className='flex items-center justify-between'>
            {label}
            <FormMessage className='max-sm:hidden text-xs opacity-80' />
          </FormLabel>
          <div className='relative'>
            <Popover open={showSuggestions} onOpenChange={setShowSuggestions}>
              <PopoverTrigger asChild>
                <FormControl>
                  <div className='relative'>
                    <Input
                      ref={inputRef}
                      value={inputValue}
                      placeholder={placeholder}
                      onChange={(e) => {
                        const value = e.target.value;
                        setInputValue(value);
                        field.onChange(value);
                      }}
                      onFocus={handleFocus}
                      onBlur={handleBlur}
                      onKeyDown={handleKeyDown}
                      autoComplete='off'
                      {...props}
                    />
                    <div className='absolute inset-y-0 right-0 flex items-center pr-3'>
                      {loading ? (
                        <Loader2 className='size-4 animate-spin text-muted-foreground' />
                      ) : (
                        <MapPin className='size-4 text-muted-foreground' />
                      )}
                    </div>
                  </div>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent
                ref={popoverRef}
                className='p-0'
                align='start'
                onOpenAutoFocus={(e) => e.preventDefault()}
                style={{ width: inputRef.current?.offsetWidth }}
              >
                <Command className='w-full'>
                  <CommandList className='max-h-60'>
                    <CommandEmpty>
                      {loading ? 'Searching property database...' : suggestions.length === 0 && inputValue.length >= 3 ? 'No properties found - try a different address' : 'Type at least 3 characters to search'}
                    </CommandEmpty>
                    <CommandGroup>
                      {suggestions.map((suggestion, index) => (
                        <CommandItem
                          key={`${suggestion.address}-${index}`}
                          value={suggestion.address}
                          onSelect={() => handleSelectAddress(suggestion)}
                          className={cn(
                            'flex items-start h-full space-x-2 p-3 cursor-pointer',
                            selectedIndex === index && 'bg-accent',
                          )}
                        >
                          <MapPin className='size-4 mt-0.5 text-muted-foreground flex-shrink-0' />
                          <div className='flex-1 min-w-0'>
                            <div className='font-medium text-sm'>{suggestion.address}</div>
                            {(suggestion.city || suggestion.state) && (
                              <div className='text-xs text-muted-foreground truncate'>
                                {[suggestion.city, suggestion.state].filter(Boolean).join(', ')}
                              </div>
                            )}
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          {description && <p className='text-sm text-muted-foreground'>{description}</p>}
          <FormMessage className='sm:hidden text-xs text-left opacity-80' />
        </FormItem>
      )}
    />
  );
} 