"use client";

import React, { useState } from "react";
import { cn } from "@ui/lib";
import { formatPhoneNumber } from "@shared/lib/utils";
import { CopyableValue } from "@shared/components/CopyableValue";
import { ComposeEmailModal } from "@shared/components/ComposeEmailModal";

interface Address {
	street: string;
	street2?: string;
	city?: string;
	state?: string;
	zip?: string;
	country?: string;
}

interface GridItemProps {
	value: React.ReactNode;
	badge?: string;
	icon?: React.ReactNode;
	edit?: React.ReactNode;
	add?: React.ReactNode;
	address?: boolean;
	onEdit?: () => void;
	onAdd?: () => void;
	onClick?: () => void;
	badgeClassName?: string;
}

const GridItem = ({
	value,
	badge,
	icon,
	edit,
	add,
	address = false,
	onEdit,
	onAdd,
	onClick,
	badgeClassName,
}: GridItemProps) => {
	const hasActions = (edit && onEdit) || (add && onAdd);
	
	// Email compose modal state
	const [composeModalOpen, setComposeModalOpen] = useState(false);
	const [selectedEmail, setSelectedEmail] = useState<string>("");

	const handleEmailClick = (email: string) => {
		setSelectedEmail(email);
		setComposeModalOpen(true);
	};

	return (
		<div
			className={cn(
				"min-h-8 relative group/grid-item flex gap-2 items-center py-1 px-2 rounded-md hover:bg-muted/30 transition-colors border border-transparent hover:border-border",
				onClick && "cursor-pointer"
			)}
			onClick={onClick}
		>
			{icon && <div className="">{icon}</div>}
			
			<div className="flex-1 min-w-0">
				<div className="flex items-center gap-2 justify-between">
					{address && value && typeof value === "object" && !React.isValidElement(value) && 'street' in value ? (
						<div className="text-sm truncate">
							{(value as Address).street ? (
								<div className="flex flex-col">
									<span className="text-foreground">
										{(value as Address).street}
										{(value as Address).street2 && `, ${(value as Address).street2}`}
									</span>
									{((value as Address).city || (value as Address).state || (value as Address).zip) && (
										<span className="text-muted-foreground">
											{" • "}
											{[(value as Address).city, (value as Address).state, (value as Address).zip]
												.filter(Boolean)
												.join(", ")}
											{(value as Address).country && (value as Address).country !== "United States" && `, ${(value as Address).country}`}
										</span>
									)}
								</div>
							) : (
								<span className="text-muted-foreground">Incomplete address</span>
							)}
						</div>
					) : (
						<div className="text-sm truncate">
							{React.isValidElement(value) && 
								'type' in value && 
								value.type === 'a' && 
								'props' in value && 
								typeof value.props === 'object' && 
								value.props !== null &&
								'href' in value.props &&
								typeof value.props.href === 'string' &&
								'children' in value.props &&
								typeof value.props.children === 'string' ? (
																	value.props.href.startsWith('tel:') ? (
									<CopyableValue 
										value={formatPhoneNumber(value.props.children)} 
										type="phone"
										className="!bg-muted/50 !border-muted !text-foreground hover:!bg-muted/80"
									/>
								) : value.props.href.startsWith('mailto:') ? (
									<CopyableValue 
										value={value.props.children} 
										type="email"
										className="!bg-muted/50 !border-muted !text-foreground hover:!bg-muted/80"
										onEmailClick={handleEmailClick}
									/>
								) : value
								) : value}
						</div>
					)}
					{badge && (
						<div className={cn("text-xs text-muted-foreground cursor-default group-hover/grid-item:opacity-0 transition-opacity duration-100", badgeClassName)}>{badge}</div>
					)}
				</div>
			</div>

			{hasActions && (
				<>
					{/* Gradient overlay that appears on hover */}
					<div className="absolute top-0 right-0 bottom-0 pointer-events-none flex justify-end gap-2 px-4 items-center group-hover/grid-item:opacity-100 opacity-0 transition-all duration-100 bg-gradient-to-l from-muted/50 to-transparent w-full rounded-r-md" />
					
					{/* Action buttons that slide in from the right */}
					<div className="absolute top-0 right-0 bottom-0 translate-x-full group-hover/grid-item:translate-x-0 flex justify-end gap-1 px-2 items-center group-hover/grid-item:opacity-100 opacity-0 transition-all duration-100 rounded-r-md">
						{edit && onEdit && (
							<button
								onClick={(e) => {
									e.stopPropagation();
									onEdit();
								}}
								className="p-1 rounded-md hover:bg-muted/80 transition-colors"
							>
								{edit}
							</button>
						)}
						{add && onAdd && (
							<button
								onClick={(e) => {
									e.stopPropagation();
									onAdd();
								}}
								className="p-1 rounded-md hover:bg-muted/80 transition-colors"
							>
								{add}
							</button>
						)}
					</div>
				</>
			)}
			
			{/* Email compose modal */}
			<ComposeEmailModal
				open={composeModalOpen}
				onOpenChange={setComposeModalOpen}
				toEmail={selectedEmail}
			/>
		</div>
	);
};

export default GridItem; 