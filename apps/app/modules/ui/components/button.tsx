import { Slot, Slottable } from "@radix-ui/react-slot";
import { Spinner } from "@shared/components/Spinner";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import * as React from "react";

const buttonVariants = cva(
	"flex items-center justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50 [&>svg]:mr-1.5 [&>svg]:opacity-60 [&>svg+svg]:hidden",
	{
		variants: {
			variant: {
				primary:
					"border-transparent bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm shadow-primary/20",
				error: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
				outline:
					"border-secondary/15 hover:bg-secondary/10 border border-border bg-secondary",
				secondary:
					"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/90",
				light: "border-transparent bg-secondary/5 text-foreground hover:bg-secondary/10",
				ghost: "border-transparent text-primary hover:bg-primary/10 hover:text-primary",
				link: "border-transparent text-primary underline-offset-4 hover:underline",
				relio: "transition group h-7 rounded-md gap-1 border !border-border !bg-muted/50 hover:!border hover:!border-border hover:!bg-muted/70",
				action: "bg-blue-500 hover:bg-blue-600 text-white font-medium !rounded-lg",
			},
			size: {
				md: "h-9 rounded-md px-4 text-sm",
				sm: "h-8 rounded-md px-3 text-xs",
				lg: "h-11 rounded-md px-6 text-base",
				icon: "size-7 rounded-md [&>svg]:m-0 [&>svg]:opacity-100",
			},
		},
		defaultVariants: {
			variant: "secondary",
			size: "md",
		},
	},
);

type TooltipSide = "top" | "right" | "bottom" | "left";

interface TooltipWithShortcut {
	content: string;
	shortcut?: string;
	side?: TooltipSide;
	className?: string;
	type: 'shortcut';
}

type TooltipProps = TooltipWithShortcut | { content: string; side?: TooltipSide; className?: string };

export type ButtonProps = {
	asChild?: boolean;
	loading?: boolean;
	tooltip?: string | TooltipProps;
} & React.ButtonHTMLAttributes<HTMLButtonElement> &
	VariantProps<typeof buttonVariants>;

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{
			className,
			children,
			variant,
			size,
			asChild = false,
			loading,
			disabled,
			tooltip,
			...props
		},
		ref,
	) => {
		const Comp = asChild ? Slot : "button";
		const button = (
			<Comp
				className={cn(buttonVariants({ variant, size, className }))}
				ref={ref}
				disabled={disabled || loading}
				{...props}
			>
				{loading && <Spinner className="mr-1.5 size-4 text-inherit" />}
				<Slottable>{children}</Slottable>
			</Comp>
		);

		if (!tooltip) {
			return button;
		}

		const tooltipProps = typeof tooltip === "string" 
			? { content: tooltip, side: "top" as TooltipSide } 
			: tooltip;

		return (
			<TooltipProvider delayDuration={0}>
				<Tooltip>
					<TooltipTrigger asChild>{button}</TooltipTrigger>
					<TooltipContent
						side={tooltipProps.side || "top"}
						className={cn("text-xs dark:bg-sidebar bg-zinc-100", tooltipProps.className)}
					>
						<span>{tooltipProps.content}</span>
						{'type' in tooltipProps && tooltipProps.type === 'shortcut' && tooltipProps.shortcut && (
							<kbd className="px-1.5 py-0.5 text-[10px] font-mono text-muted-foreground border rounded-sm ml-2">
								{tooltipProps.shortcut}
							</kbd>
						)}
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	},
);
Button.displayName = "Button";

export { Button, buttonVariants };
