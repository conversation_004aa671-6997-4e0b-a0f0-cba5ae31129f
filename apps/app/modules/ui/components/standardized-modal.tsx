import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

interface StandardizedModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl";
  hideCloseButton?: boolean;
  preventOutsideClick?: boolean;
  overlay?: boolean;
  position?: "center" | "bottom-right";
  customControls?: React.ReactNode;
}

export function StandardizedModal({
  open,
  onOpenChange,
  title,
  description,
  icon,
  children,
  footer,
  maxWidth = "3xl",
  hideCloseButton = false,
  overlay = true,
  preventOutsideClick = true,
  position = "center",
  customControls,
}: StandardizedModalProps) {
  const maxWidthClasses = {
    sm: "sm:max-w-sm",
    md: "sm:max-w-md", 
    lg: "sm:max-w-lg",
    xl: "sm:max-w-xl",
    "2xl": "sm:max-w-2xl",
    "3xl": "sm:max-w-3xl",
  };

  const positionClasses = {
    center: "sm:flex sm:items-center sm:justify-center",
    "bottom-right": "sm:flex sm:items-end sm:justify-end sm:p-6",
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        overlay={overlay}
        className={cn(
          "!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800",
          position === "center" ? "m-0" : "",
          position === "bottom-right" ? "!fixed !bottom-6 !right-6 !top-auto !left-auto !translate-x-0 !translate-y-0 !m-0" : "",
          maxWidthClasses[maxWidth]
        )}
        noCloseButton={true}
        onPointerDownOutside={preventOutsideClick ? (e) => e.preventDefault() : undefined}
      >
        <DialogTitle className="sr-only">{title}</DialogTitle>
        {description && (
          <DialogDescription className="sr-only">{description}</DialogDescription>
        )}

        {/* Header */}
        <div className="flex items-center justify-between px-4 pt-2 pb-1">
          <div className="flex flex-row items-center gap-x-2">
            {icon}
            <span className="text-md font-light">{title}</span>
          </div>
          <div className="flex items-center gap-1">
            {customControls}
            {!hideCloseButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-auto p-1"
              >
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col px-2 pt-2 pb-4">
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
            {footer}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

interface StandardizedModalFooterProps {
  children: React.ReactNode;
  leftContent?: React.ReactNode;
}

export function StandardizedModalFooter({ children, leftContent }: StandardizedModalFooterProps) {
  return (
    <>
      {leftContent && (
        <div className="flex items-center gap-2 mr-auto">
          {leftContent}
        </div>
      )}
      {children}
    </>
  );
} 