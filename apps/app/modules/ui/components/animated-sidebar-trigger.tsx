"use client";

import {
	IconLayoutSidebarLeftCollapseFilled,
	IconLayoutSidebarLeftExpand,
} from "@tabler/icons-react";
import { cn } from "@ui/lib";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

const animatedTriggerVariants = cva(
	"relative inline-flex items-center justify-center rounded-md transition-colors duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
	{
		variants: {
			variant: {
				ghost: "hover:bg-accent hover:text-accent-foreground",
				outline:
					"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
				default:
					"bg-primary text-primary-foreground hover:bg-primary/90",
			},
			size: {
				icon: "h-8 w-8",
				sm: "h-7 w-7",
				default: "h-9 w-9",
				lg: "h-10 w-10",
			},
			animation: {
				fade: "[&>div]:transition-opacity [&>div]:duration-200",
				slide: "[&>div]:transition-all [&>div]:duration-200",
				rotate: "[&>div]:transition-all [&>div]:duration-300",
				flip: "[&>div]:transition-all [&>div]:duration-300 [&>div]:transform-gpu",
			},
		},
		defaultVariants: {
			variant: "ghost",
			size: "icon",
			animation: "fade",
		},
	},
);

export interface AnimatedSidebarTriggerProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
		VariantProps<typeof animatedTriggerVariants> {
	expanded?: boolean;
}

const AnimatedSidebarTrigger = React.forwardRef<
	HTMLButtonElement,
	AnimatedSidebarTriggerProps
>(
	(
		{ className, expanded = false, variant, size, animation, ...props },
		ref,
	) => {
		const [isHovered, setIsHovered] = React.useState(false);

		const animationStyles = {
			fade: {
				unfilled: expanded ? "opacity-0" : "opacity-100",
				filled: expanded ? "opacity-100" : "opacity-0",
			},
			slide: {
				unfilled: expanded
					? "-translate-x-2 opacity-0"
					: "translate-x-0 opacity-100",
				filled: expanded
					? "translate-x-0 opacity-100"
					: "translate-x-2 opacity-0",
			},
			rotate: {
				unfilled: expanded
					? "opacity-0 rotate-180"
					: "opacity-100 rotate-0",
				filled: expanded
					? "opacity-100 rotate-0"
					: "opacity-0 -rotate-180",
			},
			flip: {
				unfilled: expanded
					? "opacity-0 rotateY-90"
					: "opacity-100 rotateY-0",
				filled: expanded
					? "opacity-100 rotateY-0"
					: "opacity-0 rotateY-90",
			},
		};

		const currentAnimation = animation || "fade";
		const styles =
			animationStyles[currentAnimation as keyof typeof animationStyles];

		return (
			<button
				ref={ref}
				className={cn(
					animatedTriggerVariants({ variant, size, animation }),
					className,
				)}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				{...props}
			>
				{/* Unfilled icon */}
				<div
					className={cn(
						"absolute inset-0 flex items-center justify-center cursor-pointer",
						styles.unfilled,
					)}
				>
					<IconLayoutSidebarLeftExpand className="size-4" />
				</div>

				{/* Filled icon */}
				<div
					className={cn(
						"absolute inset-0 flex items-center justify-center cursor-pointer",
						styles.filled,
					)}
				>
					<IconLayoutSidebarLeftCollapseFilled className="size-4" />
				</div>

				{/* Hover effect */}
				<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
					<div
						className={cn(
							"size-6 rounded-full bg-primary/10 transition-all duration-300",
							isHovered
								? "scale-150 opacity-20"
								: "scale-0 opacity-0",
						)}
					/>
				</div>

				<span className="sr-only">Toggle Sidebar</span>
			</button>
		);
	},
);
AnimatedSidebarTrigger.displayName = "AnimatedSidebarTrigger";

export { AnimatedSidebarTrigger };
