import { type NextRequest, NextResponse } from "next/server";

const MAPBOX_ACCESS_TOKEN = process.env.MAPBOX_ACCESS_TOKEN;

export async function GET(request: NextRequest) {
	const { searchParams } = new URL(request.url);
	const address = searchParams.get("address");

	if (!address) {
		return NextResponse.json(
			{ error: "Address parameter is required" },
			{ status: 400 },
		);
	}

	if (!MAPBOX_ACCESS_TOKEN) {
		return NextResponse.json(
			{ error: "Mapbox access token not configured" },
			{ status: 500 },
		);
	}

	try {
		const response = await fetch(
			`https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
				address,
			)}.json?access_token=${MAPBOX_ACCESS_TOKEN}&limit=1&types=address,poi`,
		);

		if (!response.ok) {
			throw new Error(`Mapbox API error: ${response.status}`);
		}

		const data = await response.json();

		if (data.features && data.features.length > 0) {
			const feature = data.features[0];
			const [lng, lat] = feature.center;

			return NextResponse.json({
				success: true,
				coordinates: [lng, lat],
				address: feature.place_name,
				confidence: feature.relevance || 1,
			});
		}

		return NextResponse.json({
			success: false,
			error: "No results found for the given address",
		});
	} catch (error) {
		console.error("Geocoding error:", error);
		return NextResponse.json(
			{ error: "Failed to geocode address" },
			{ status: 500 },
		);
	}
}
