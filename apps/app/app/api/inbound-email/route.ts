import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import {
  extractEmailParticipants,
  processEmailForContacts,
  storeForwardedEmail,
  isEmailAlreadyProcessed,
  getOrganizationByForwardingAddress,
  verifyEmailSender
} from "@repo/api/src/lib/email-forwarding";

interface MailgunWebhookData {
  recipient: string;
  sender: string;
  subject: string;
  'body-plain': string;
  'body-html': string;
  'message-id': string;
  timestamp: string;
  'attachment-count': string;
  [key: string]: string | File;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    // Parse Mailgun webhook data
    const to = formData.get('recipient') as string;
    const from = formData.get('sender') as string;
    const subject = formData.get('subject') as string;
    const bodyPlain = formData.get('body-plain') as string;
    const bodyHtml = formData.get('body-html') as string;
    const messageId = formData.get('message-id') as string;
    const timestamp = formData.get('timestamp') as string;
    const attachmentCount = parseInt(formData.get('attachment-count') as string || '0');
    
    logger.info('Received inbound email', {
      to,
      from,
      subject,
      messageId,
      attachmentCount
    });

    if (!to || !from || !messageId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Get organization by forwarding address
    const orgResult = await getOrganizationByForwardingAddress(to);
    if (!orgResult) {
      logger.warn(`Invalid or unknown forwarding address: ${to}`);
      return NextResponse.json({ error: 'Invalid forwarding address' }, { status: 400 });
    }

    const { organization } = orgResult;
    
    // Verify sender authorization
    const { isAuthorized, member } = verifyEmailSender(organization, from);
    if (!isAuthorized || !member) {
      logger.warn(`Unauthorized forwarding attempt from ${from} to ${to}`);
      return NextResponse.json({ error: 'Unauthorized sender' }, { status: 403 });
    }
    
    // Check if email was already processed
    const alreadyProcessed = await isEmailAlreadyProcessed(messageId);
    if (alreadyProcessed) {
      logger.info(`Email already processed: ${messageId}`);
      return NextResponse.json({ success: true, message: 'Already processed' });
    }

    // Process attachments
    const attachments = [];
    for (let i = 1; i <= attachmentCount; i++) {
      const attachment = formData.get(`attachment-${i}`) as File;
      if (attachment) {
        attachments.push({
          name: attachment.name,
          size: attachment.size,
          type: attachment.type,
          // In a real implementation, you'd upload this to your storage service
          // url: await uploadAttachment(attachment)
        });
      }
    }

    // Extract email participants
    const participants = extractEmailParticipants(bodyHtml || bodyPlain, from);
    
    // Process participants and create/link contacts
    const linkedRecords = await processEmailForContacts(
      organization.id,
      member.userId,
      participants
    );
    
    // Store the forwarded email
    const processedEmail = await storeForwardedEmail({
      organizationId: organization.id,
      messageId,
      from,
      to,
      subject,
      body: bodyHtml || bodyPlain,
      attachments: attachments.length > 0 ? attachments : undefined,
      participants,
      linkedRecords,
      forwardedBy: member.userId,
      headers: {
        timestamp,
        // Store other relevant headers
      }
    });
    
    logger.info(`Successfully processed email ${messageId}`, {
      emailId: processedEmail.id,
      linkedRecords,
      participantCount: participants.length
    });
    
    return NextResponse.json({ 
      success: true, 
      emailId: processedEmail.id,
      linkedRecords 
    });
    
  } catch (error) {
    logger.error('Inbound email processing failed:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Verify Mailgun webhook signature (optional)
function verifyMailgunSignature(signature: string | null, body: string): boolean {
  if (!signature || !process.env.MAILGUN_WEBHOOK_SIGNING_KEY) {
    return true; // Skip verification if not configured
  }
  
  const crypto = require('crypto');
  const expectedSignature = crypto
    .createHmac('sha256', process.env.MAILGUN_WEBHOOK_SIGNING_KEY)
    .update(body)
    .digest('hex');
    
  return signature === expectedSignature;
} 