import { getActiveOrganization, getSession } from "@app/auth/lib/server";
import { InviteMemberForm } from "@app/organizations/components/InviteMemberForm";
import { OrganizationMembersBlock } from "@app/organizations/components/OrganizationMembersBlock";
import { SettingsList } from "@app/shared/components/SettingsList";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("organizations.settings.members.title"),
		header: {
			title: t("organizations.settings.title"),
			subtitle: t("organizations.settings.members.description"),
		},
	};
}

export default async function OrganizationSettingsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const session = await getSession();
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	return (
		<SettingsList>
			{isOrganizationAdmin(organization, session?.user) && (
				<InviteMemberForm organizationId={organization.id} />
			)}
			<OrganizationMembersBlock organizationId={organization.id} />
		</SettingsList>
	);
}
