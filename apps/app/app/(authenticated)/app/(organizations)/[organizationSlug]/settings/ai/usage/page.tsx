import { getActiveOrganization } from "@app/auth/lib/server";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { UsageAnalyticsClient } from "./UsageAnalyticsClient";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: "AI Usage Analytics",
		header: {
			title: "AI Usage Analytics",
			subtitle: "Detailed insights into your AI credit consumption",
		},
	};
}

export default async function AIUsagePage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return redirect("/app");
	}

	return (
		<div className="p-6">
			<UsageAnalyticsClient
				organizationId={organization.id}
				organizationSlug={organizationSlug}
			/>
		</div>
	);
}
