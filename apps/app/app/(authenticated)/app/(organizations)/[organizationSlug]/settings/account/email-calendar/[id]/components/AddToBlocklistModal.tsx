"use client";

import { useState } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import { BlocksIcon } from "lucide-react";

interface AddToBlocklistModalProps {
  open: boolean;
  onClose: () => void;
  onAdd: (entries: string[]) => void;
}

export function AddToBlocklistModal({ open, onClose, onAdd }: AddToBlocklistModalProps) {
  const [input, setInput] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim()) return;

    // Parse multiple entries separated by comma or space
    const entries = input
      .split(/[,\s]+/)
      .map(entry => entry.trim())
      .filter(entry => entry.length > 0);

    if (entries.length > 0) {
      onAdd(entries);
      setInput("");
    }
  };

  const handleClose = () => {
    setInput("");
    onClose();
  };

  return (
    <StandardizedModal
      open={open}
      onOpenChange={handleClose}
      title="Add to blocklist"
      description="Add emails or domains to the blocklist"
      icon={<BlocksIcon className="h-5 w-5" />}
      maxWidth="md"
      hideCloseButton={false}
      footer={
        <StandardizedModalFooter>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            size="sm"
            disabled={!input.trim()}
            onClick={handleSubmit}
          >
            Add to blocklist
          </Button>
        </StandardizedModalFooter>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="blocklist-input">
            Type a domain/email to block
          </Label>
          <Input
            id="blocklist-input"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="example.com, <EMAIL>"
            className="w-full"
            autoFocus
          />
        </div>

        <p className="text-sm text-muted-foreground">
          Separate your domains and email addresses by a comma or space to add multiple
        </p>
      </form>
    </StandardizedModal>
  );
} 