"use client";

import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import {
	ArrowLeft,
	BarChart3,
	Bot,
	Calendar,
	Clock,
	MessageSquare,
	TrendingUp,
	Zap,
} from "lucide-react";
import Link from "next/link";

interface UsageData {
	currentPeriod: {
		total: number;
		used: number;
		remaining: number;
		daysInPeriod: number;
		daysRemaining: number;
	};
	breakdown: {
		dataChat: { credits: number; percentage: number; sessions: number };
		taskCreation: { credits: number; percentage: number; sessions: number };
		analytics: { credits: number; percentage: number; sessions: number };
	};
	dailyUsage: Array<{ day: string; credits: number }>;
	topActivities: Array<{ activity: string; credits: number; count: number }>;

	// Admin-only data
	userBreakdown?: Array<{
		user: { id: string; name: string; email: string };
		credits: number;
		sessions: number;
		lastActivity: string;
		percentage: number;
	}>;
	organizationStats?: {
		totalUsers: number;
		activeUsers: number;
		totalCreditsUsed: number;
		averageCreditsPerUser: number;
	};
	permissions?: {
		isAdmin: boolean;
		canViewAllUsers: boolean;
	};
}

interface UsageAnalyticsClientProps {
	organizationId: string;
	organizationSlug: string;
}

export function UsageAnalyticsClient({
	organizationId,
	organizationSlug,
}: UsageAnalyticsClientProps) {
	const {
		data: usageData,
		isLoading,
		error,
	} = useQuery({
		queryKey: ["ai-usage", organizationId],
		queryFn: async (): Promise<UsageData> => {
			const response = await fetch(`/api/ai/usage/${organizationId}`, {
				method: "GET",
				headers: {
					"Content-Type": "application/json",
				},
			});
			if (!response.ok) {
				throw new Error("Failed to fetch usage data");
			}
			return (await response.json()) as UsageData;
		},
		enabled: !!organizationId,
	});

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4 mb-4">
					<Button variant="ghost" asChild size="sm">
						<Link href={`/app/${organizationSlug}/settings/ai`}>
							<ArrowLeft className="h-4 w-4 mr-2" />
							Back to AI Settings
						</Link>
					</Button>
				</div>
				<h1 className="text-2xl font-semibold">AI Usage Analytics</h1>
				<div className="grid gap-6">
					{/* Loading skeleton */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						{[...Array(3)].map((_, i) => (
							<Card key={i}>
								<CardHeader className="pb-3">
									<div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
								</CardHeader>
								<CardContent>
									<div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
									<div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2" />
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</div>
		);
	}

	if (error || !usageData) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4 mb-4">
					<Button variant="ghost" asChild size="sm">
						<Link href={`/app/${organizationSlug}/settings/ai`}>
							<ArrowLeft className="h-4 w-4 mr-2" />
							Back to AI Settings
						</Link>
					</Button>
				</div>
				<h1 className="text-2xl font-semibold">AI Usage Analytics</h1>
				<Card>
					<CardContent className="p-6">
						<div className="text-center space-y-2">
							<p className="text-muted-foreground">
								Failed to load usage analytics
							</p>
							<p className="text-sm text-muted-foreground">
								Please try refreshing the page or contact
								support if the issue persists.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	const usagePercentage =
		(usageData.currentPeriod.used / usageData.currentPeriod.total) * 100;

	return (
		<div className="space-y-6">
			<div>
				<div className="flex items-center gap-4 mb-4">
					<Button variant="ghost" asChild size="sm">
						<Link href={`/app/${organizationSlug}/settings/ai`}>
							<ArrowLeft className="h-4 w-4 mr-2" />
							Back to AI Settings
						</Link>
					</Button>
				</div>
				<h1 className="text-2xl font-semibold">AI Usage Analytics</h1>
				<p className="text-muted-foreground">
					{usageData.permissions?.isAdmin
						? "Comprehensive analytics for your organization including team member breakdown"
						: "Detailed insights into your AI credit consumption and usage patterns"}
				</p>
			</div>

			<div className="grid gap-6">
				{/* Current Period Overview */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<Card>
						<CardHeader className="pb-3">
							<CardTitle className="text-base flex items-center gap-2">
								<Zap className="h-4 w-4 text-blue-500" />
								Credits Used
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{usageData.currentPeriod.used}
							</div>
							<p className="text-xs text-muted-foreground">
								of {usageData.currentPeriod.total} available
							</p>
							<Progress
								value={usagePercentage}
								className="mt-3 h-2"
							/>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-3">
							<CardTitle className="text-base flex items-center gap-2">
								<Calendar className="h-4 w-4 text-green-500" />
								Daily Reset
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{usageData.currentPeriod.daysRemaining > 0
									? "Today"
									: "Due"}
							</div>
							<p className="text-xs text-muted-foreground">
								{usageData.currentPeriod.daysRemaining > 0
									? "Credits reset daily"
									: "Reset pending"}
							</p>
							<div className="mt-3 text-xs text-muted-foreground">
								Daily limit: {usageData.currentPeriod.total}{" "}
								credits
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-3">
							<CardTitle className="text-base flex items-center gap-2">
								<TrendingUp className="h-4 w-4 text-purple-500" />
								Usage Rate
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{Math.round(
									(usageData.currentPeriod.used /
										usageData.currentPeriod.total) *
										100,
								)}
								%
							</div>
							<p className="text-xs text-muted-foreground">
								of daily limit used
							</p>
							<div className="mt-3 text-xs text-muted-foreground">
								{usageData.currentPeriod.remaining} credits
								remaining today
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Usage Breakdown */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<BarChart3 className="h-5 w-5" />
							Usage Breakdown by Feature
						</CardTitle>
						<CardDescription>
							How your credits are being consumed across different
							AI features
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="flex items-center justify-between p-3 border rounded-lg">
								<div className="flex items-center gap-3">
									<MessageSquare className="h-5 w-5 text-blue-500" />
									<div>
										<div className="font-medium">
											Data Chat
										</div>
										<div className="text-sm text-muted-foreground">
											{
												usageData.breakdown.dataChat
													.sessions
											}{" "}
											chat sessions
										</div>
									</div>
								</div>
								<div className="text-right">
									<div className="font-medium">
										{usageData.breakdown.dataChat.credits}{" "}
										credits
									</div>
									<div className="text-sm text-muted-foreground">
										{
											usageData.breakdown.dataChat
												.percentage
										}
										% of usage
									</div>
								</div>
							</div>

							<div className="flex items-center justify-between p-3 border rounded-lg">
								<div className="flex items-center gap-3">
									<Clock className="h-5 w-5 text-green-500" />
									<div>
										<div className="font-medium">
											Task Creation
										</div>
										<div className="text-sm text-muted-foreground">
											{
												usageData.breakdown.taskCreation
													.sessions
											}{" "}
											tasks created
										</div>
									</div>
								</div>
								<div className="text-right">
									<div className="font-medium">
										{
											usageData.breakdown.taskCreation
												.credits
										}{" "}
										credits
									</div>
									<div className="text-sm text-muted-foreground">
										{
											usageData.breakdown.taskCreation
												.percentage
										}
										% of usage
									</div>
								</div>
							</div>

							<div className="flex items-center justify-between p-3 border rounded-lg">
								<div className="flex items-center gap-3">
									<BarChart3 className="h-5 w-5 text-purple-500" />
									<div>
										<div className="font-medium">
											Analytics & Research
										</div>
										<div className="text-sm text-muted-foreground">
											{
												usageData.breakdown.analytics
													.sessions
											}{" "}
											analysis requests
										</div>
									</div>
								</div>
								<div className="text-right">
									<div className="font-medium">
										{usageData.breakdown.analytics.credits}{" "}
										credits
									</div>
									<div className="text-sm text-muted-foreground">
										{
											usageData.breakdown.analytics
												.percentage
										}
										% of usage
									</div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Top Activities */}
				<Card>
					<CardHeader>
						<CardTitle>Top Credit-Consuming Activities</CardTitle>
						<CardDescription>
							Your most frequent AI operations this period
						</CardDescription>
					</CardHeader>
					<CardContent>
						{usageData.topActivities.length === 0 ? (
							<div className="text-center py-8">
								<Bot className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
								<p className="text-muted-foreground">
									No AI activities yet
								</p>
								<p className="text-sm text-muted-foreground">
									Start using AI features to see your usage
									patterns here
								</p>
							</div>
						) : (
							<div className="space-y-3">
								{usageData.topActivities.map(
									(activity, index) => (
										<div
											key={index}
											className="flex items-center justify-between py-2"
										>
											<div className="flex items-center gap-3">
												<Badge className="w-6 h-6 p-0 flex items-center justify-center text-xs">
													{index + 1}
												</Badge>
												<div>
													<div className="font-medium text-sm">
														{activity.activity}
													</div>
													<div className="text-xs text-muted-foreground">
														{activity.count} times
														used
													</div>
												</div>
											</div>
											<div className="text-sm font-medium">
												{activity.credits} credits
											</div>
										</div>
									),
								)}
							</div>
						)}
					</CardContent>
				</Card>

				{/* Weekly Usage Chart */}
				<Card>
					<CardHeader>
						<CardTitle>Daily Usage Pattern</CardTitle>
						<CardDescription>
							Credit consumption over the last 7 days
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="flex items-end justify-between h-32 gap-2">
							{usageData.dailyUsage.map((day, index) => {
								const maxCredits = Math.max(
									...usageData.dailyUsage.map(
										(d) => d.credits,
									),
									1,
								);
								const height = (day.credits / maxCredits) * 100;

								return (
									<div
										key={index}
										className="flex flex-col items-center gap-2 flex-1"
									>
										<div className="text-xs text-muted-foreground">
											{day.credits}
										</div>
										<div
											className="w-full bg-blue-500 rounded-t min-h-[4px]"
											style={{
												height: `${Math.max(height, 4)}%`,
											}}
										/>
										<div className="text-xs font-medium">
											{day.day}
										</div>
									</div>
								);
							})}
						</div>
					</CardContent>
				</Card>

				{/* Actions */}
				<Card>
					<CardHeader>
						<CardTitle>Optimize Your Usage</CardTitle>
						<CardDescription>
							Tips to make the most of your AI credits
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
								<h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
									Batch Your Queries
								</h4>
								<p className="text-sm text-blue-700 dark:text-blue-300">
									Group related questions together to maximize
									efficiency and reduce credit usage.
								</p>
							</div>
							<div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
								<h4 className="font-medium text-green-900 dark:text-green-100 mb-1">
									Use Specific Searches
								</h4>
								<p className="text-sm text-green-700 dark:text-green-300">
									More specific queries often require fewer
									credits than broad, general requests.
								</p>
							</div>
						</div>

						<div className="flex gap-3">
							<Button variant="outline" asChild>
								<Link
									href={`/app/${organizationSlug}/settings/ai/credits`}
								>
									View Credit Details
								</Link>
							</Button>
							<Button asChild>
								<Link
									href={`/app/${organizationSlug}/settings/ai/purchase`}
								>
									Purchase More Credits
								</Link>
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* Admin-only Organization Overview */}
				{usageData.permissions?.isAdmin &&
					usageData.organizationStats && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<BarChart3 className="h-5 w-5" />
									Organization Overview
								</CardTitle>
								<CardDescription>
									Usage statistics across all team members
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
									<div className="text-center">
										<div className="text-2xl font-bold text-blue-600">
											{
												usageData.organizationStats
													.totalUsers
											}
										</div>
										<div className="text-sm text-muted-foreground">
											Total Users
										</div>
									</div>
									<div className="text-center">
										<div className="text-2xl font-bold text-green-600">
											{
												usageData.organizationStats
													.activeUsers
											}
										</div>
										<div className="text-sm text-muted-foreground">
											Active Users
										</div>
									</div>
									<div className="text-center">
										<div className="text-2xl font-bold text-purple-600">
											{
												usageData.organizationStats
													.totalCreditsUsed
											}
										</div>
										<div className="text-sm text-muted-foreground">
											Total Credits Used
										</div>
									</div>
									<div className="text-center">
										<div className="text-2xl font-bold text-orange-600">
											{
												usageData.organizationStats
													.averageCreditsPerUser
											}
										</div>
										<div className="text-sm text-muted-foreground">
											Avg. Per User
										</div>
									</div>
								</div>
							</CardContent>
						</Card>
					)}

				{/* Admin-only User Breakdown */}
				{usageData.permissions?.isAdmin && usageData.userBreakdown && (
					<Card>
						<CardHeader>
							<CardTitle>Team Member Usage</CardTitle>
							<CardDescription>
								Individual usage breakdown for all team members
							</CardDescription>
						</CardHeader>
						<CardContent>
							{usageData.userBreakdown.length === 0 ? (
								<div className="text-center py-8">
									<Bot className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
									<p className="text-muted-foreground">
										No team usage data yet
									</p>
									<p className="text-sm text-muted-foreground">
										Team members will appear here once they
										start using AI features
									</p>
								</div>
							) : (
								<div className="space-y-4">
									{usageData.userBreakdown.map(
										(userStats, index) => (
											<div
												key={userStats.user.id}
												className="flex items-center justify-between p-4 border rounded-lg"
											>
												<div className="flex items-center gap-3">
													<Badge className="w-6 h-6 p-0 flex items-center justify-center text-xs">
														{index + 1}
													</Badge>
													<div>
														<div className="font-medium">
															{
																userStats.user
																	.name
															}
														</div>
														<div className="text-sm text-muted-foreground">
															{
																userStats.user
																	.email
															}
														</div>
														<div className="text-xs text-muted-foreground">
															{userStats.sessions}{" "}
															sessions • Last
															active:{" "}
															{new Date(
																userStats.lastActivity,
															).toLocaleDateString()}
														</div>
													</div>
												</div>
												<div className="text-right">
													<div className="font-medium">
														{userStats.credits}{" "}
														credits
													</div>
													<div className="text-sm text-muted-foreground">
														{userStats.percentage}%
														of total
													</div>
												</div>
											</div>
										),
									)}
								</div>
							)}
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
