import { EmailAccountSettingsClient } from "./client";

interface EmailAccountPageProps {
  params: Promise<{
    organizationSlug: string;
    id: string;
  }>;
  searchParams: Promise<{
    type?: "forwarding" | "connected";
  }>;
}

export default async function EmailAccountPage({ params, searchParams }: EmailAccountPageProps) {
  const { organizationSlug, id } = await params;
  const { type } = await searchParams;

  return (
    <EmailAccountSettingsClient 
      organizationSlug={organizationSlug}
      accountId={id}
      accountType={type}
    />
  );
} 