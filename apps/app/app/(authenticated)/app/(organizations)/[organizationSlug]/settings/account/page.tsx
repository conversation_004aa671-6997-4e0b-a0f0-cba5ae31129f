import { getSession } from "@app/auth/lib/server";
import { ChangeEmailForm } from "@app/settings/components/ChangeEmailForm";
import { ChangeNameForm } from "@app/settings/components/ChangeNameForm";
import { UserAvatarForm } from "@app/settings/components/UserAvatarForm";
import { UserLanguageForm } from "@app/settings/components/UserLanguageForm";
import { SettingsList } from "@app/shared/components/SettingsList";
import { AccountSettingsClient } from "./client";
import { config } from "@repo/config";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("settings.account.title"),
	};
}

export default async function AccountSettingsPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	return (
		<>
			<AccountSettingsClient />
			<SettingsList>
				<UserAvatarForm />
				{config.i18n.enabled && <UserLanguageForm />}
				<ChangeNameForm />
				<ChangeEmailForm />
			</SettingsList>
		</>
	);
}
