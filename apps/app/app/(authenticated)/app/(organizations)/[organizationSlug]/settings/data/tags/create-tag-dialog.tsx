"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@ui/components/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Badge } from "@ui/components/badge";
import { useCreateTag } from "@app/shared/hooks/useTags";
import { PlusIcon } from "lucide-react";
import { toast } from "sonner";
import type { TaggableObjectType } from "@repo/database/src/types/object";

export function CreateTagDialog() {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState("");
  const [color, setColor] = useState("#6b7280");
  const [objectType, setObjectType] = useState<TaggableObjectType>("contact");
  
  const createTag = useCreateTag();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createTag.mutateAsync({ name, color, objectType });
      toast.success("Tag created successfully");
      setOpen(false);
      setName("");
      setColor("#6b7280");
      setObjectType("contact");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create tag");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Tag
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Tag</DialogTitle>
          <DialogDescription>
            Create a new tag that can be applied to your records.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Tag Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter tag name"
              required
            />
          </div>
          <div>
            <Label htmlFor="objectType">Object Type</Label>
            <Select
              value={objectType}
              onValueChange={(value) => setObjectType(value as TaggableObjectType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="contact">Contacts</SelectItem>
                <SelectItem value="company">Companies</SelectItem>
                <SelectItem value="property">Properties</SelectItem>
                <SelectItem value="custom_object">Custom Objects</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="color">Color</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="color"
                type="color"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="w-16 h-10"
              />
              <Badge
                style={{
                  backgroundColor: `${color}20`,
                  borderColor: color,
                  color: color,
                }}
                className="border"
              >
                {name || "Preview"}
              </Badge>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={createTag.isPending || !name.trim()}>
              {createTag.isPending ? "Creating..." : "Create Tag"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 