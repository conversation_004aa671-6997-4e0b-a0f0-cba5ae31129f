import { getActiveOrganization } from "@app/auth/lib/server";
import { getDefaultView } from "@app/object-views/lib/server";
import { objectTypes, pluralToSingularMap, singularToPluralMap } from "@repo/database";
import { searchParamsCache } from "@app/organizations/components/objects/contacts/search-params";
import { ObjectsView } from "@app/organizations/components/objects/ObjectsView";
import { contactsDataOptions } from "@app/organizations/lib/query-options";
import { getQueryClient } from "@shared/lib/server";
import { notFound, redirect } from "next/navigation";

interface ObjectTypePageProps {
	params: Promise<{
		organizationSlug: string;
		objectType: string;
	}>;
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ObjectTypePage({
	params,
	searchParams,
}: ObjectTypePageProps) {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;
	const search = searchParamsCache.parse(resolvedSearchParams);

	const singularObjectType = pluralToSingularMap[resolvedParams.objectType];
	
	if (!singularObjectType || !objectTypes.includes(singularObjectType)) {
		return notFound();
	}

	const organization = await getActiveOrganization(
		resolvedParams.organizationSlug,
	);
	if (!organization) {
		return notFound();
	}

	const queryClient = getQueryClient();

	const defaultView = await getDefaultView(
		singularObjectType,
		organization.id,
	);

	if (singularObjectType === "contact") {
		await queryClient.prefetchInfiniteQuery(
			contactsDataOptions(search as any, resolvedParams.organizationSlug),
		);
	}

	if (defaultView) {
		const pluralForm = singularToPluralMap[singularObjectType];
		const queryString = new URLSearchParams(resolvedSearchParams as Record<string, string>).toString();
		const redirectUrl = `/app/${resolvedParams.organizationSlug}/${pluralForm}/view/${defaultView.id}`;
		redirect(queryString ? `${redirectUrl}?${queryString}` : redirectUrl);
	}

	return (
		<ObjectsView
			objectType={singularObjectType}
			organizationSlug={resolvedParams.organizationSlug}
			showCreateViewOnly={true}
		/>
	);
}
