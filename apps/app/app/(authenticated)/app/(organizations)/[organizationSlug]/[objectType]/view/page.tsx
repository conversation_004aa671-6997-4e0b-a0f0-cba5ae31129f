import { getDefaultView } from "@app/object-views/lib/server";
import { type ObjectType, objectTypes, pluralToSingularMap, singularToPluralMap } from "@repo/database";
import { notFound, redirect } from "next/navigation";

interface ObjectTypeViewPageProps {
	params: Promise<{
		organizationSlug: string;
		objectType: string; // Changed from ObjectType to string to accept plural forms
	}>;
}

export default async function ObjectTypeViewPage({
	params,
}: ObjectTypeViewPageProps) {
	const resolvedParams = await params;

	// Map plural form to singular ObjectType
	const singularObjectType = pluralToSingularMap[resolvedParams.objectType];
	
	if (!singularObjectType || !objectTypes.includes(singularObjectType)) {
		return notFound();
	}

	const defaultView = await getDefaultView(singularObjectType);

	if (defaultView) {
		// Use plural form in redirect URL
		const pluralForm = singularToPluralMap[singularObjectType];
		redirect(
			`/app/${resolvedParams.organizationSlug}/${pluralForm}/view/${defaultView.id}`,
		);
	}

	// Use plural form in redirect URL
	const pluralForm = singularToPluralMap[singularObjectType];
	redirect(
		`/app/${resolvedParams.organizationSlug}/${pluralForm}`,
	);
}
