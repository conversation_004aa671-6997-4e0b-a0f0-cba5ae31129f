import { type ObjectType, objectTypes, pluralToSingularMap } from "@repo/database";
import { searchParamsCache } from "@app/organizations/components/objects/contacts/search-params";
import { ObjectsView } from "@app/organizations/components/objects/ObjectsView";
import { contactsDataOptions } from "@app/organizations/lib/query-options";
import { getQueryClient } from "@shared/lib/server";
import { notFound } from "next/navigation";

interface ObjectViewPageProps {
	params: Promise<{
		organizationSlug: string;
		objectType: string; // Changed from ObjectType to string to accept plural forms
		id: string;
	}>;
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ObjectViewPage({
	params,
	searchParams,
}: ObjectViewPageProps) {
	const resolvedParams = await params;
	const search = searchParamsCache.parse(await searchParams);

	const singularObjectType = pluralToSingularMap[resolvedParams.objectType];
	
	if (!singularObjectType || !objectTypes.includes(singularObjectType)) {
		return notFound();
	}

	const queryClient = getQueryClient();

	if (singularObjectType === "contact") {
		await queryClient.prefetchInfiniteQuery(
			contactsDataOptions(search as any, resolvedParams.organizationSlug),
		);
	}

	return (
		<ObjectsView
			objectType={singularObjectType}
			organizationSlug={resolvedParams.organizationSlug}
			viewId={resolvedParams.id}
		/>
	);
}
