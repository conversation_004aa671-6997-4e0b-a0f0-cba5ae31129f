"use client";

import ContactPage from "@app/contacts/components/ContactPage";
import { fetchFavorites } from "@app/favorites/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { searchParamsCache } from "@app/organizations/components/objects/contacts/search-params";
import { useObjectsData } from "@app/organizations/hooks/use-objects-data";
import {
	IconBuilding,
	IconArrowLeft,
	IconLoader2,
} from "@tabler/icons-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { useRouter, useSearchParams } from "next/navigation";
import { use, useCallback, useEffect, useMemo } from "react";
import { DotsLoader, Loader } from "@ui/components/loader";
import PropertyPage from "@app/properties/components/PropertyPage";

interface ObjectPageProps {
	params: Promise<{
		organizationSlug: string;
		objectType: string;
		id: string;
	}>;
}

const ObjectPage = ({ params }: ObjectPageProps) => {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();
	
	const { id, objectType } = use(params);

	// Parse search params for filters and sorting
	const parsedSearchParams = searchParamsCache.parse(
		Object.fromEntries(searchParams.entries())
	);

	const { 
		data: objectsData,
		hasNextPage,
		fetchNextPage,
		isFetchingNextPage,
		isLoading,
		isError
	} = useObjectsData({
		organizationId: activeOrganization?.id,
		objectType,
		filters: parsedSearchParams,
	});

	// Fetch next page if we're close to the end
	useEffect(() => {
		if (hasNextPage && !isFetchingNextPage) {
			fetchNextPage();
		}
	}, [hasNextPage, isFetchingNextPage, fetchNextPage]);

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", activeOrganization?.id],
		queryFn: () =>
			activeOrganization?.id
				? fetchFavorites(activeOrganization?.id)
				: Promise.resolve([]),
		enabled: !!activeOrganization?.id,
	});

	const isFavorite = favorites.some((f: any) => f.objectId === id && f.objectType === objectType);

	// Get all items from all pages and memoize the result
	const allItems = useMemo(() => {
		return objectsData?.items || [];
	}, [objectsData?.items]);

	// Find current record index and determine if we have previous/next records
	const currentIndex = useMemo(() => {
		return allItems.findIndex((item: any) => item.id === id);
	}, [allItems, id]);

	const hasPrevious = currentIndex > 0;
	const hasNext = currentIndex < (allItems.length - 1);

	// Navigation handler
	const handleNavigate = useCallback((direction: "prev" | "next") => {
		if (!allItems.length) return;

		const newIndex = direction === "prev" ? currentIndex - 1 : currentIndex + 1;
		if (newIndex >= 0 && newIndex < allItems.length) {
			const newId = allItems[newIndex].id;
			const searchString = searchParams.toString();
			router.push(`/app/${activeOrganization?.slug}/${objectType}/${newId}${searchString ? '?' + searchString : ''}`);
		}
	}, [allItems, currentIndex, router, activeOrganization?.slug, objectType, searchParams]);

	// If we're loading or have an error, show appropriate UI
	if (isLoading) {
		return <div className="min-h-screen flex items-center justify-center !text-muted-foreground gap-2">
			<Loader text="Loading" variant="loading-dots" color="muted" />
		</div>;
	}

	if (isError) {
		return <div>Error loading data</div>;
	}

	if (objectType === "contact" || objectType === "contacts") {
		return (
			<ContactPage 
				id={id} 
				activeOrganization={activeOrganization} 
				isFavorite={isFavorite}
				onNavigate={handleNavigate}
				hasPrevious={hasPrevious}
				hasNext={hasNext}
			/>
		);
	}

	if (objectType === "property" || objectType === "properties") {
		return (
			<PropertyPage 
				id={id} 
				activeOrganization={activeOrganization} 
				isFavorite={isFavorite}
				onNavigate={handleNavigate}
				hasPrevious={hasPrevious}
				hasNext={hasNext}
			/>
		);
	}

	return (
		<div className="min-h-screen bg-background flex items-center justify-center">
			<div className="text-center">
				<IconBuilding className="size-16 text-muted-foreground mx-auto mb-4" />
				<h1 className="text-2xl font-bold mb-2">
					{objectType.charAt(0).toUpperCase() + objectType.slice(1)} View
				</h1>
				<p className="text-muted-foreground mb-6">
					{objectType.charAt(0).toUpperCase() + objectType.slice(1)} detail view is coming soon.
				</p>
				<Button variant="outline" onClick={() => router.back()}>
					<IconArrowLeft className="size-4 mr-2" />
					Go Back
				</Button>
			</div>
		</div>
	);
};

export default ObjectPage;