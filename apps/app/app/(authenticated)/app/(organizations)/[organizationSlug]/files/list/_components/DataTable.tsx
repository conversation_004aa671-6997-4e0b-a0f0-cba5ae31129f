"use client";

import EmptyContainer from "@app/shared/components/EmptyContainer";
import {
	type ColumnDef,
	flexRender,
	type HeaderGroup,
	type Row,
	type Table,
} from "@tanstack/react-table";
import { Skeleton } from "@ui/components/skeleton";
import {
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	Table as UITable,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { IconFile, IconFolder } from "@tabler/icons-react";
import React from "react";
import FileBrowser from "./FileBrowser";

interface DataTableProps {
	columns: ColumnDef<any, unknown>[];
	table: Table<any>;
	isLoading: boolean;
	viewMode: "list" | "grid";
	currentFolderId?: string | null;
	onViewModeChange: (mode: "list" | "grid") => void;
	onFolderChange: (folderId: string | null) => void;
	onFilesUpdated?: () => void;
}

export function DataTable({
	columns,
	table,
	isLoading,
	viewMode,
	currentFolderId,
	onViewModeChange,
	onFolderChange,
	onFilesUpdated,
}: DataTableProps) {
	const TableSkeleton = ({ columns }: { columns: ColumnDef<any, unknown>[] }) => (
		<>
			{[...Array(5)].map((_, index) => (
				<TableRow key={index}>
					{columns.map((column, cellIndex) => (
						<TableCell key={cellIndex}>
							<Skeleton className="h-6 w-full" />
						</TableCell>
					))}
				</TableRow>
			))}
		</>
	);

	const handleRowClick = (row: Row<any>) => {
		const item = row.original;
		if (item.type === "folder") {
			onFolderChange(item.id);
		} else if (item.type === "file" && item.url) {
			// Open file in new tab
			window.open(item.url, "_blank");
		}
	};

	// If grid view is selected, use the FileBrowser component
	if (viewMode === "grid") {
		return (
			<FileBrowser
				currentFolderId={currentFolderId}
				viewMode={viewMode}
				onFolderChange={onFolderChange}
				onViewModeChange={onViewModeChange}
				onFileSelect={(file) => {
					if (file.url) {
						window.open(file.url, "_blank");
					}
				}}
				onFolderSelect={(folder) => {
					onFolderChange(folder.id);
				}}
			/>
		);
	}

	// List view using table
	return (
		<div className="space-y-4">
			<div className="overflow-auto">
				<UITable>
					<TableHeader>
						{table.getHeaderGroups().map((headerGroup: HeaderGroup<any>) => (
							<TableRow
								key={headerGroup.id}
								className="border-b hover:bg-transparent"
							>
								{headerGroup.headers.map((header) => (
									<TableHead
										key={header.id}
										colSpan={header.colSpan}
										className="px-4 text-xs font-medium text-zinc-500 !h-8"
									>
										<div className="flex items-center h-full w-full !-mt-4">
											{header.isPlaceholder
												? null
												: flexRender(
														header.column.columnDef.header,
														header.getContext(),
													)}
										</div>
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{isLoading ? (
							<TableSkeleton columns={columns} />
						) : (
							<>
								{table.getRowModel().rows?.length ? (
									table.getRowModel().rows.map((row: Row<any>) => (
										<TableRow
											key={row.id}
											data-state={row.getIsSelected() && "selected"}
											className={cn(
												"cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
												"bg-white dark:bg-transparent",
											)}
											onClick={() => handleRowClick(row)}
										>
											{row.getVisibleCells().map((cell) => (
												<TableCell
													key={cell.id}
													className="px-4 py-2"
												>
													{flexRender(
														cell.column.columnDef.cell,
														cell.getContext(),
													)}
												</TableCell>
											))}
										</TableRow>
									))
								) : (
									<TableRow>
										<TableCell
											colSpan={columns.length}
											className="h-24 text-center rounded-b-lg"
										>
											<EmptyContainer
												title="No files found"
												subtitle="No files or folders in this location."
												button="Upload files"
												icon={IconFile}
											/>
										</TableCell>
									</TableRow>
								)}
							</>
						)}
					</TableBody>
				</UITable>
			</div>
		</div>
	);
}

export default DataTable;
