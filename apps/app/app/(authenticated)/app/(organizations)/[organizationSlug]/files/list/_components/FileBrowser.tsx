"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import {
	IconChevronRight,
	IconFile,
	IconFolder,
	IconFolderOpen,
	IconGrid3x3,
	IconList,
	IconPlus,
	IconSearch,
	IconUpload,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";

interface Folder {
	id: string;
	name: string;
	parentId: string | null;
	isPublic: boolean;
	createdAt: string;
	creator: {
		id: string;
		name: string;
		image?: string;
	};
	_count: {
		files: number;
		children: number;
	};
}

interface File {
	id: string;
	name: string;
	originalName: string;
	size: number;
	mimeType: string;
	fileType: string;
	extension?: string;
	url: string;
	thumbnailUrl?: string;
	folderId: string | null;
	isPublic: boolean;
	createdAt: string;
	uploader: {
		id: string;
		name: string;
		image?: string;
	};
}

interface FileBrowserProps {
	currentFolderId?: string | null;
	viewMode: "list" | "grid";
	onFolderChange: (folderId: string | null) => void;
	onViewModeChange: (mode: "list" | "grid") => void;
	onFileSelect?: (file: File) => void;
	onFolderSelect?: (folder: Folder) => void;
	className?: string;
}

const FileBrowser = ({
	currentFolderId,
	viewMode,
	onFolderChange,
	onViewModeChange,
	onFileSelect,
	onFolderSelect,
	className,
}: FileBrowserProps) => {
	const { organization } = useActiveOrganization();
	const [searchQuery, setSearchQuery] = useState("");

	// Fetch folders
	const {
		data: folders = [],
		isLoading: foldersLoading,
		refetch: refetchFolders,
	} = useQuery({
		queryKey: ["folders", organization?.id, currentFolderId],
		queryFn: async () => {
			if (!organization?.id) return [];
			const params = new URLSearchParams({
				organizationId: organization.id,
			});
			if (currentFolderId) {
				params.append("parentId", currentFolderId);
			} else {
				params.append("parentId", "null");
			}
			const response = await fetch(`/api/folders?${params}`);
			if (!response.ok) throw new Error("Failed to fetch folders");
			return response.json();
		},
		enabled: !!organization?.id,
	});

	// Fetch files
	const {
		data: files = [],
		isLoading: filesLoading,
		refetch: refetchFiles,
	} = useQuery({
		queryKey: ["files", organization?.id, currentFolderId, searchQuery],
		queryFn: async () => {
			if (!organization?.id) return [];
			const params = new URLSearchParams({
				organizationId: organization.id,
			});
			if (currentFolderId) {
				params.append("folderId", currentFolderId);
			} else {
				params.append("folderId", "null");
			}
			if (searchQuery) {
				params.append("search", searchQuery);
			}
			const response = await fetch(`/api/files?${params}`);
			if (!response.ok) throw new Error("Failed to fetch files");
			return response.json();
		},
		enabled: !!organization?.id,
	});

	const isLoading = foldersLoading || filesLoading;

	const handleFolderClick = (folder: Folder) => {
		onFolderChange(folder.id);
		onFolderSelect?.(folder);
	};

	const handleFileClick = (file: File) => {
		onFileSelect?.(file);
	};

	const formatFileSize = (bytes: number) => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	};

	const getFileIcon = (fileType: string) => {
		switch (fileType) {
			case "images":
				return "🖼️";
			case "documents":
				return "📄";
			case "videos":
				return "🎥";
			case "audio":
				return "🎵";
			case "archives":
				return "📦";
			default:
				return "📄";
		}
	};

	return (
		<Card className={cn("h-full", className)}>
			<CardHeader className="pb-4">
				<div className="flex items-center justify-between">
					<CardTitle className="text-lg font-semibold">Files</CardTitle>
					<div className="flex items-center gap-2">
						<Button variant="outline" size="sm">
							<IconPlus className="h-4 w-4 mr-1" />
							New Folder
						</Button>
						<Button variant="outline" size="sm">
							<IconUpload className="h-4 w-4 mr-1" />
							Upload
						</Button>
						<div className="flex items-center border rounded-md">
							<Button
								variant={viewMode === "list" ? "default" : "ghost"}
								size="sm"
								onClick={() => onViewModeChange("list")}
								className="rounded-r-none"
							>
								<IconList className="h-4 w-4" />
							</Button>
							<Button
								variant={viewMode === "grid" ? "default" : "ghost"}
								size="sm"
								onClick={() => onViewModeChange("grid")}
								className="rounded-l-none"
							>
								<IconGrid3x3 className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>
				<div className="relative">
					<IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Search files and folders..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="pl-10"
					/>
				</div>
			</CardHeader>
			<CardContent className="p-0">
				{isLoading ? (
					<div className="p-4 space-y-2">
						{Array.from({ length: 5 }).map((_, i) => (
							<Skeleton key={i} className="h-12 w-full" />
						))}
					</div>
				) : (
					<div className={cn(
						viewMode === "grid" 
							? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 p-4"
							: "divide-y"
					)}>
						{/* Folders */}
						{folders.map((folder: Folder) => (
							<div
								key={folder.id}
								onClick={() => handleFolderClick(folder)}
								className={cn(
									"cursor-pointer hover:bg-muted/50 transition-colors",
									viewMode === "grid"
										? "p-3 rounded-lg border text-center"
										: "flex items-center gap-3 p-3"
								)}
							>
								{viewMode === "grid" ? (
									<>
										<IconFolder className="h-8 w-8 mx-auto text-blue-500" />
										<p className="text-sm font-medium truncate">{folder.name}</p>
										<p className="text-xs text-muted-foreground">
											{folder._count.files} files, {folder._count.children} folders
										</p>
									</>
								) : (
									<>
										<IconFolder className="h-5 w-5 text-blue-500 flex-shrink-0" />
										<div className="flex-1 min-w-0">
											<p className="text-sm font-medium truncate">{folder.name}</p>
											<p className="text-xs text-muted-foreground">
												{folder._count.files} files, {folder._count.children} folders
											</p>
										</div>
										<IconChevronRight className="h-4 w-4 text-muted-foreground" />
									</>
								)}
							</div>
						))}

						{/* Files */}
						{files.map((file: File) => (
							<div
								key={file.id}
								onClick={() => handleFileClick(file)}
								className={cn(
									"cursor-pointer hover:bg-muted/50 transition-colors",
									viewMode === "grid"
										? "p-3 rounded-lg border text-center"
										: "flex items-center gap-3 p-3"
								)}
							>
								{viewMode === "grid" ? (
									<>
										{file.thumbnailUrl ? (
											<img
												src={file.thumbnailUrl}
												alt={file.name}
												className="h-12 w-12 mx-auto object-cover rounded"
											/>
										) : (
											<div className="h-12 w-12 mx-auto flex items-center justify-center text-2xl">
												{getFileIcon(file.fileType)}
											</div>
										)}
										<p className="text-sm font-medium truncate">{file.name}</p>
										<p className="text-xs text-muted-foreground">
											{formatFileSize(file.size)}
										</p>
									</>
								) : (
									<>
										{file.thumbnailUrl ? (
											<img
												src={file.thumbnailUrl}
												alt={file.name}
												className="h-8 w-8 object-cover rounded flex-shrink-0"
											/>
										) : (
											<div className="h-8 w-8 flex items-center justify-center text-lg flex-shrink-0">
												{getFileIcon(file.fileType)}
											</div>
										)}
										<div className="flex-1 min-w-0">
											<p className="text-sm font-medium truncate">{file.name}</p>
											<p className="text-xs text-muted-foreground">
												{formatFileSize(file.size)} • {file.uploader.name}
											</p>
										</div>
									</>
								)}
							</div>
						))}

						{folders.length === 0 && files.length === 0 && !isLoading && (
							<div className="p-8 text-center text-muted-foreground">
								<IconFolder className="h-12 w-12 mx-auto mb-4 opacity-50" />
								<p>No files or folders found</p>
								{searchQuery && (
									<p className="text-sm mt-1">Try adjusting your search terms</p>
								)}
							</div>
						)}
					</div>
				)}
			</CardContent>
		</Card>
	);
};

export default FileBrowser;
