import type { Table } from "@tanstack/react-table";
import React from "react";
import DataTable from "./_components/DataTable";

interface FileListProps {
	columns: import("@tanstack/react-table").ColumnDef<any, unknown>[];
	table: Table<any>;
	isLoading: boolean;
	viewMode: "list" | "grid";
	currentFolderId?: string | null;
	onViewModeChange: (mode: "list" | "grid") => void;
	onFolderChange: (folderId: string | null) => void;
	onFilesUpdated?: () => void;
}

const FileList = ({
	columns,
	table,
	isLoading,
	viewMode,
	currentFolderId,
	onViewModeChange,
	onFolderChange,
	onFilesUpdated,
}: FileListProps) => {
	return (
		<DataTable
			columns={columns}
			table={table}
			isLoading={isLoading}
			viewMode={viewMode}
			currentFolderId={currentFolderId}
			onViewModeChange={onViewModeChange}
			onFolderChange={onFolderChange}
			onFilesUpdated={onFilesUpdated}
		/>
	);
};

export default FileList;
