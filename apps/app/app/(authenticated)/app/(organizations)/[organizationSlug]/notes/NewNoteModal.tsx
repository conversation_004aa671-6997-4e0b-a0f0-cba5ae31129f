"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useSearch } from "@app/search";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import type { Note } from "@repo/database";
import { IconBuilding, IconMapPin, IconUser } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import * as React from "react";
import { createNote } from "@app/notes/lib/api";

interface RecordItem {
	id: string;
	label: string;
	sublabel: string;
	type: "company" | "contact" | "property";
	objectId: string | null;
	objectType: string | null;
	icon: React.ElementType;
	createdAt: string;
	updatedAt: string;
}

interface NewNoteModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSelectRecord: (record: RecordItem | null) => void;
	onNoteCreated?: (note: Note) => void;
}

// Helper function to convert search results to RecordItem format
function mapSearchResultToRecordItem(result: any): RecordItem | null {
	// Skip tasks and notes - we only want linkable objects
	if (result.type === 'task' || result.type === 'note' || result.type === 'user') {
		return null;
	}

	// Map contact results
	if (result.type === 'contact') {
		return {
			id: result.id,
			label: result.name || result.title || 'Unnamed Contact',
			sublabel: result.email || result.jobTitle || '',
			type: 'contact' as const,
			objectId: result.id,
			objectType: 'contact',
			icon: IconUser, // We'll render ContactAvatar separately
			createdAt: result.createdAt,
			updatedAt: result.updatedAt,
		};
	}

	// Map company results
	if (result.type === 'company') {
		return {
			id: result.id,
			label: result.name || result.title || 'Unnamed Company',
			sublabel: result.description || '',
			type: 'company' as const,
			objectId: result.id,
			objectType: 'company',
			icon: IconBuilding,
			createdAt: result.createdAt,
			updatedAt: result.updatedAt,
		};
	}

	// Map property results
	if (result.type === 'property') {
		return {
			id: result.id,
			label: result.name || result.title || 'Unnamed Property',
			sublabel: result.description || '',
			type: 'property' as const,
			objectId: result.id,
			objectType: 'property',
			icon: IconMapPin,
			createdAt: result.createdAt,
			updatedAt: result.updatedAt,
		};
	}

	return null;
}

export function NewNoteModal({
	open,
	onOpenChange,
	onSelectRecord,
	onNoteCreated,
}: NewNoteModalProps) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = React.useState("");
	const [isCreating, setIsCreating] = React.useState(false);
	const [selectedRecord, setSelectedRecord] = React.useState<RecordItem | null>(null);

	// Reset search and selection when modal opens
	React.useEffect(() => {
		if (open) {
			setSearch("");
			setSelectedRecord(null);
		}
	}, [open]);

	// Use search hook only when there's a search term
	const shouldSearch = !!search && search.trim().length >= 2 && !!activeOrganization?.id;
	const {
		data: searchResults,
		isLoading: searchLoading,
	} = useSearch(
		{
			query: search,
			organizationId: activeOrganization?.id || "",
			type: "all",
			limit: 20,
		},
		shouldSearch,
	);

	// Fetch recent records when not searching - increased limits for more records
	const { data: recentContacts, isLoading: contactsLoading } = useQuery({
		queryKey: ["recentContacts", activeOrganization?.id],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const response = await fetch(`/api/objects/contacts?organizationId=${activeOrganization.id}&limit=8`);
			if (!response.ok) return [];
			const result = await response.json();
			return result.contact || [];
		},
		enabled: !shouldSearch && !!activeOrganization?.id,
	});

	const { data: recentCompanies, isLoading: companiesLoading } = useQuery({
		queryKey: ["recentCompanies", activeOrganization?.id],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const response = await fetch(`/api/objects/companies?organizationId=${activeOrganization.id}&limit=8`);
			if (!response.ok) return [];
			const result = await response.json();
			return result.company || [];
		},
		enabled: !shouldSearch && !!activeOrganization?.id,
	});

	const { data: recentProperties, isLoading: propertiesLoading } = useQuery({
		queryKey: ["recentProperties", activeOrganization?.id],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const response = await fetch(`/api/objects/properties?organizationId=${activeOrganization.id}&limit=8`);
			if (!response.ok) return [];
			const result = await response.json();
			return result.property || [];
		},
		enabled: !shouldSearch && !!activeOrganization?.id,
	});

	// Convert search results to RecordItem format
	const searchRecords = React.useMemo(() => {
		if (!searchResults?.results) return [];
		
		return searchResults.results
			.map(mapSearchResultToRecordItem)
			.filter((record): record is RecordItem => record !== null);
	}, [searchResults]);

	// Convert recent records to RecordItem format
	const recentRecords = React.useMemo(() => {
		if (shouldSearch) return [];
		
		const records: RecordItem[] = [];
		
		// Add recent contacts
		if (recentContacts && recentContacts.length > 0) {
			recentContacts.forEach((contact: any) => {
				const name = `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact';
				const emailAddress = Array.isArray(contact.email) && contact.email.length > 0
					? contact.email[0]?.address || contact.email[0]?.email || ''
					: '';
				
				records.push({
					id: contact.id,
					label: name,
					sublabel: emailAddress || contact.title || '',
					type: 'contact' as const,
					objectId: contact.id,
					objectType: 'contact',
					icon: IconUser,
					createdAt: contact.createdAt,
					updatedAt: contact.updatedAt,
				});
			});
		}
		
		// Add recent companies
		if (recentCompanies && recentCompanies.length > 0) {
			recentCompanies.forEach((company: any) => {
				records.push({
					id: company.id,
					label: company.name || 'Unnamed Company',
					sublabel: company.description || '',
					type: 'company' as const,
					objectId: company.id,
					objectType: 'company',
					icon: IconBuilding,
					createdAt: company.createdAt,
					updatedAt: company.updatedAt,
				});
			});
		}
		
		// Add recent properties
		if (recentProperties && recentProperties.length > 0) {
			recentProperties.forEach((property: any) => {
				records.push({
					id: property.id,
					label: property.name || 'Unnamed Property',
					sublabel: property.description || '',
					type: 'property' as const,
					objectId: property.id,
					objectType: 'property',
					icon: IconMapPin,
					createdAt: property.createdAt,
					updatedAt: property.updatedAt,
				});
			});
		}
		
		return records;
	}, [recentContacts, recentCompanies, recentProperties, shouldSearch]);

	// Use search records when searching, recent records otherwise
	const allRecords = shouldSearch ? searchRecords : recentRecords;
	
	// Filter records based on search term for manual filtering
	const records = React.useMemo(() => {
		if (!search || search.length < 2) {
			return allRecords;
		}
		
		// For search results, return as-is since they're already filtered by the API
		if (shouldSearch) {
			return allRecords;
		}
		
		// For recent records, filter manually based on search term
		const searchLower = search.toLowerCase();
		return allRecords.filter(record => 
			record.label.toLowerCase().includes(searchLower) ||
			record.sublabel.toLowerCase().includes(searchLower)
		);
	}, [allRecords, search, shouldSearch]);

	// Group records by type
	const groupedRecords = React.useMemo(() => {
		const groups: Record<string, RecordItem[]> = {};
		records.forEach((record) => {
			if (!groups[record.type]) groups[record.type] = [];
			groups[record.type].push(record);
		});
		return groups;
	}, [records]);

	async function handleCreateStandaloneNote() {
		setIsCreating(true);
		try {
			const note = await createNote({
				title: "Untitled note",
				...(activeOrganization?.id
					? { organizationId: activeOrganization.id }
					: {}),
			});
			onOpenChange(false);
			if (onNoteCreated) {
				onNoteCreated(note);
			}
		} catch (e) {
			// Optionally handle error
		} finally {
			setIsCreating(false);
		}
	}

	return (
		<>
			<CommandDialog
				overlay={false}
				open={open}
				onOpenChange={onOpenChange}
				className="!rounded-2xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 sm:max-w-3xl m-0 bg-clip-padding overflow-y-auto"
			>
				<Command key={open ? 'open' : 'closed'} onValueChange={setSearch} shouldFilter={false}>
					<CommandInput placeholder={search ? "Search for a record" : "Search for a record or browse recent"} />
					<CommandList className="max-h-[450px] min-h-[450px] overflow-y-auto no-scrollbar bg-secondary/20 px-1 pb-16">
						{((shouldSearch && searchLoading) || (!shouldSearch && (contactsLoading || companiesLoading || propertiesLoading))) && (
							<CommandEmpty>Loading...</CommandEmpty>
						)}
						
						{!((shouldSearch && searchLoading) || (!shouldSearch && (contactsLoading || companiesLoading || propertiesLoading))) && records.length === 0 && (
							<CommandEmpty>
								{shouldSearch ? "No records found." : "No recent records found."}
							</CommandEmpty>
						)}
						
						{Object.entries(groupedRecords).map(([recordType, typeRecords], groupIndex) => (
							<React.Fragment key={recordType}>
								{groupIndex > 0 && <CommandSeparator />}
								<CommandGroup 
									heading={
										recordType === 'person' ? 'Contacts' :
										recordType === 'company' ? 'Companies' :
										recordType === 'property' ? 'Properties' :
										recordType
									}
									className="[&_[cmdk-group-heading]]:font-mono [&_[cmdk-group-heading]]:uppercase [&_[cmdk-group-heading]]:text-[10px]"
								>
									{typeRecords
										.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
									.map((record) => (
										<CommandItem
											key={record.id}
											onSelect={() => setSelectedRecord(record)}
											value={record.label}
											data-value={record.label}
											className={`flex items-center justify-between ${
												selectedRecord?.id === record.id ? 'bg-accent' : ''
											}`}
										>
											<div className="flex items-center gap-2">
												<div className="p-1 rounded-md bg-muted border border-border dark:border-zinc-700">
													{record.type === 'contact' ? (
														<ContactAvatar name={record.label} className="h-4 w-4" />
													) : (
														<record.icon className="h-4 w-4" />
													)}
												</div>
												<span>{record.label}</span>
											</div>
											<div className="flex items-center gap-2">
											{(() => {
												const createdAt = new Date(record.createdAt);
												const now = new Date();
												const diffMs = now.getTime() - createdAt.getTime();
												const diffDays = diffMs / (1000 * 60 * 60 * 24);
												if (diffDays <= 3) {
													return (
														<span className="ml-2 rounded-md bg-green-100 px-1 py-0.5 text-[10px] font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
															New
														</span>
													);
												}
												return null;
											})()}
												{record.objectType && (
													<span className="text-xs text-muted-foreground">
														{record.objectType}
													</span>
												)}
											</div>
										</CommandItem>
									))}
								</CommandGroup>
							</React.Fragment>
						))}
					</CommandList>
				</Command>
				<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
					<Button
						variant="link"
						className="text-xs font-normal"
						onClick={handleCreateStandaloneNote}
						disabled={isCreating}
					>
						{isCreating ? "Creating..." : "Create standalone note"}
					</Button>
					<Button
						variant="primary"
						size="sm"
						className="text-xs font-normal"
						disabled={!selectedRecord || isCreating}
						onClick={async () => {
							if (selectedRecord) {
								setIsCreating(true);
								try {
									const note = await createNote({
										title: `Note for ${selectedRecord.label}`,
										objectId: selectedRecord.objectId,
										objectType: selectedRecord.objectType,
										...(activeOrganization?.id
											? { organizationId: activeOrganization.id }
											: {}),
									});
									onOpenChange(false);
									if (onNoteCreated) {
										onNoteCreated(note);
									}
								} catch (e) {
									// Optionally handle error
									console.error('Failed to create note:', e);
								} finally {
									setIsCreating(false);
								}
							}
						}}
					>
						{isCreating ? "Creating..." : "Select Record"}
					</Button>
				</div>
			</CommandDialog>
		</>
	);
}

export type { RecordItem, NewNoteModalProps };
