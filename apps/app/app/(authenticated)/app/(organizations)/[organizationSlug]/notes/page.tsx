"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { columns } from "@app/notes/components/Columns";
import { CreateNoteButton } from "@app/notes/components/CreateNoteButton";
import NoteTable from "@app/notes/components/DataTable";
import { NoteFavorites } from "./NoteFavorites";
import { NoteGrid } from "./NoteGrid";
import { TableSettings } from "@app/notes/components/TableSettings";
import { TableToolbar } from "@app/notes/components/TableToolbar";
import { fetchNotes } from "@app/notes/lib/api";
import {
	getNotePreferences,
	updateNotePreferences,
} from "@app/notes/lib/preferences";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { STORAGE_KEYS } from "@app/shared/lib/constants";
import {
	getLocalStorage,
	setLocalStorage,
} from "@app/shared/lib/local-storage";
import type { Note } from "@repo/database/src/zod";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
	type ColumnDef,
	getCoreRowModel,
	getFacetedRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	type SortingState,
	useReactTable,
} from "@tanstack/react-table";
import type { Filter } from "@ui/components/filters";
import React, { useCallback, useEffect, useMemo, useState } from "react";

const Notes = () => {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();

	const { data: notes = [], isLoading: isNotesLoading } = useQuery<
		Note[] & {
			createdBy?: {
				id: string;
				name: string;
				email: string;
				image: string | null;
			};
		}
	>({
		queryKey: ["notes", activeOrganization?.id],
		queryFn: () =>
			activeOrganization?.id ? fetchNotes() : Promise.resolve([]),
		enabled: !!activeOrganization?.id,
		staleTime: 2 * 60 * 1000, // 2 minutes - data is considered fresh
		gcTime: 10 * 60 * 1000, // 10 minutes - cache garbage collection time
		refetchOnWindowFocus: false, // Don't refetch on window focus to reduce API calls
		refetchOnMount: false, // Don't refetch on component mount if data is fresh
	});

	const users = useMemo(() => [], []);

	const usersRecord = useMemo(() => {
		const record: Record<string, any> = {};
		for (const user of users) {
			record[user] = user;
		}
		return record;
	}, [users]);

	const tableData = useMemo(
		() =>
			(notes as Note[])
				.filter(
					(note: Note) =>
						typeof note === "object" &&
						note !== null &&
						!Array.isArray(note),
				)
				.map((note: Note) => ({ ...note })),
		[notes, users],
	);
	const [isMounted, setIsMounted] = useState(false);

	const preferences = getNotePreferences();
	const [sorting, setSorting] = useState<SortingState>(
		preferences.sortBy
			? [
					{
						id: preferences.sortBy,
						desc: preferences.sortOrder === "desc",
					},
				]
			: [],
	);

	const [rowSelection, setRowSelection] = useState({});
	const [selectedTasks, setSelectedTasks] = useState<any[]>([]);
	const [value, setValue] = useState<string>(preferences.view);
	const [groupBy, setGroupBy] = useState(preferences.groupBy || "status");
	const [showFavorites, setShowFavorites] = useState(
		preferences.showFavorites ?? false,
	);

	const [filters, setFilters] = useState<Filter[]>([]);

	useEffect(() => {
		updateNotePreferences({ view: value });
	}, [value]);

	useEffect(() => {
		if (sorting.length > 0 && sorting[0]) {
			updateNotePreferences({
				sortBy: sorting[0]?.id ?? "",
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
			});
		}
	}, [sorting]);

	useEffect(() => {
		updateNotePreferences({ groupBy });
	}, [groupBy]);

	useEffect(() => {
		updateNotePreferences({ showFavorites: showFavorites });
	}, [showFavorites]);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	useEffect(() => {
		setLocalStorage(STORAGE_KEYS.NOTES_VIEW ?? "", value ?? "");
	}, [value]);

	useEffect(() => {
		const storedValue = getLocalStorage(STORAGE_KEYS.NOTES_VIEW ?? "");
		if (typeof storedValue === "string" && storedValue) {
			setValue(storedValue);
		}
	}, []);

	const tableConfig = useMemo(() => ({
		data: tableData as unknown as any[],
		columns: columns as ColumnDef<any>[],
		state: {
			sorting,
			rowSelection,
		},
		enableRowSelection: true,
		enableSorting: true,
		enableMultiSort: false,
		onRowSelectionChange: setRowSelection,
		onSortingChange: setSorting,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
	}), [tableData, sorting, rowSelection]);

	const table = useReactTable(tableConfig as any);

	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		if (tableData !== undefined && isMounted) {
			setIsLoading(false);
		}
	}, [tableData, isMounted]);

	// Memoize the selected rows calculation to prevent infinite loops
	const selectedRows = useMemo(() => {
		if (!isMounted) return [];
		return table.getFilteredSelectedRowModel().rows;
	}, [rowSelection, isMounted]);

	// Update selected tasks when rowSelection changes
	useEffect(() => {
		if (isMounted) {
			const selectedTasksData = selectedRows.map(
				(row) => row.original as any,
			);
			setSelectedTasks(selectedTasksData);
		}
	}, [selectedRows, isMounted]);

	if (!isMounted) {
		return null;
	}

	return (
		<>
			<div
				className={
					"border-b border-muted p-2 w-full flex justify-between items-center"
				}
			>
				<TableToolbar
					table={table}
					groupBy={groupBy}
					onGroupByChange={setGroupBy}
					filters={filters}
					setFilters={setFilters}
					sortField={sorting[0]?.id ?? ""}
					sortDirection={sorting[0]?.desc ? "desc" : "asc"}
					onSortFieldChange={(field) =>
						setSorting([
							{ id: field, desc: sorting[0]?.desc ?? false },
						])
					}
					onSortDirectionChange={(direction) =>
						setSorting([
							{
								id: sorting[0]?.id ?? "",
								desc: direction === "desc",
							} as any,
						])
					}
				/>
				<div
					className={
						"flex justify-end space-x-2 flex-row items-center"
					}
				>
					<TableSettings
						showFavorites={showFavorites}
						onShowFavoritesChange={setShowFavorites}
						table={table}
						view={value}
						onViewChange={setValue}
						groupBy={groupBy}
						onGroupByChange={setGroupBy}
						user={user}
						users={usersRecord}
					/>
					<CreateNoteButton icon={false} />
				</div>
			</div>

			{showFavorites && (
				<div className="-mt-4">
					<NoteFavorites />
				</div>
			)}

			<div className="pt-4">
				{value === "list" && (
					<NoteTable
						columns={columns}
						table={table}
						isLoading={isNotesLoading}
						groupBy={groupBy}
					/>
				)}

				{value === "grid" && (
					<NoteGrid 
						notes={table.getRowModel().rows.map(row => row.original) as typeof notes} 
						groupBy={groupBy}
					/>
				)}
			</div>

			{/* TODO: Add bottom bar and figure out why batch actions are messing with auth */}
			{/* <BottomBar
        selectedTasks={selectedTasks}
        onDeselectAll={() => {
          setRowSelection({});
          setSelectedTasks([]);
        }}
      /> */}
		</>
	);
};

export default Notes;
