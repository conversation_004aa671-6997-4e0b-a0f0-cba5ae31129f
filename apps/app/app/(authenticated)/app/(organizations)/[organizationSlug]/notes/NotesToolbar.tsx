import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { LayoutGrid, List, Plus, Settings } from "lucide-react";
import { useState } from "react";

interface NotesToolbarProps {
	view: "list" | "grid";
	onViewChange: (view: "list" | "grid") => void;
	onNewNote: () => void;
	onSettings: () => void;
}

export function NotesToolbar({
	view,
	onViewChange,
	onNewNote,
	onSettings,
}: NotesToolbarProps) {
	return (
		<div className="flex items-center gap-2 justify-end w-full px-8 pt-6 pb-2">
			<div className="flex items-center gap-1 bg-neutral-800 rounded-lg p-1">
				<Button
					variant={view === "list" ? "secondary" : "ghost"}
					size="icon"
					className={cn(
						"rounded-md",
						view === "list" && "bg-neutral-700",
					)}
					onClick={() => onViewChange("list")}
					aria-label="List view"
				>
					<List className="h-5 w-5" />
				</Button>
				<Button
					variant={view === "grid" ? "secondary" : "ghost"}
					size="icon"
					className={cn(
						"rounded-md",
						view === "grid" && "bg-neutral-700",
					)}
					onClick={() => onViewChange("grid")}
					aria-label="Grid view"
				>
					<LayoutGrid className="h-5 w-5" />
				</Button>
			</div>
			<Button
				variant="outline"
				className="flex items-center gap-2 text-base font-normal border-neutral-700 bg-neutral-900 hover:bg-neutral-800"
				onClick={onSettings}
			>
				<Settings className="h-5 w-5 mr-1" /> View settings
			</Button>
			<Button
				variant="primary"
				className="flex items-center gap-2 text-base font-normal"
				onClick={onNewNote}
			>
				<Plus className="h-5 w-5 mr-1" /> New note
			</Button>
		</div>
	);
}
