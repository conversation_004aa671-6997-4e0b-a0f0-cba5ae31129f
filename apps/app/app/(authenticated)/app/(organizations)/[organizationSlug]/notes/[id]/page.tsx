"use client";

import { fetchFavorites } from "@app/favorites/lib/api";
import { CoverImageModal } from "@app/notes/components/CoverImageModal";
import { AIChatSidebar } from "@app/notes/components/AIChatSidebar";
import { useNote, useUpdateNote } from "@app/notes/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import Editor from "@shared/components/Editor";
import PageHeader from "@shared/components/PageHeader";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "@ui/components/skeleton";
import React, { Suspense } from "react";
import Cover from "../NoteCover";
import Toolbar from "../NoteToolbar";

interface NoteIdPageParams {
	params: Promise<{
		id: string;
	}>;
}

const NoteIdPage = ({ params }: NoteIdPageParams) => {
	const { id } = React.use(params);
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;
	const { data: note } = useNote(id);
	const { mutate: update } = useUpdateNote(organizationId);
	const updateNote = useUpdateNote(organizationId);
	const queryClient = useQueryClient();

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	const isFavorite = favorites.some(
		(fav) => fav.objectType === "note" && fav.objectId === id,
	);
	const objectType = "note";

	const handleTitleUpdate = (noteId: string, title: string) => {
		updateNote.mutate(
			{
				id: noteId,
				title,
				organizationId: organizationId || "",
			},
			{
				onSuccess: () => {
					queryClient.invalidateQueries({
						queryKey: ["note", noteId],
					});
				},
				onError: (error) => {
					console.error("Title update failed:", error);
				},
			},
		);
	};

	const onChange = (content: string) => {
		update({ id, content });
	};

	return (
		<main className={"flex-1 p-1 h-full w-full"}>
			<Suspense fallback={<Skeleton className="h-full w-full" />}>
				<div
					className={
						"rounded-xl border border-zinc-200 dark:border-muted overflow-hidden h-full"
					}
				>
					<PageHeader
						activeOrganization={activeOrganization}
						data={note}
						isFavorite={isFavorite}
						objectType={objectType}
						onTitleUpdate={() => {}}
					/>

					{!note ? (
						<div>
							<Cover.Skeleton />
							<div className="md:max-w-3xl lg:max-w-4xl mx-auto mt-10">
								<div className="space-y-4 pl-8 pt-4">
									<Skeleton className="h-14 w-[50%]" />
									<Skeleton className="h-4 w-[80%]" />
									<Skeleton className="h-4 w-[40%]" />
									<Skeleton className="h-4 w-[60%]" />
								</div>
							</div>
						</div>
					) : (
						<div>
							<div className="pb-40">
								<Cover
									modal={false}
									url={note.coverImage || undefined}
								/>
								<div className="md:max-w-3xl lg:max-w-4xl mx-auto">
									<Toolbar
										initialData={note}
										activeOrganization={activeOrganization}
									/>
									<div>
										<Editor
											initialContent={note.content || ""}
											onChange={onChange}
											editable={true}
											onBlur={() => {}}
											placeholder="Start typing, or create a template and link to #"
										/>
									</div>
								</div>
							</div>

							<CoverImageModal
								noteId={note.id}
								organizationId={activeOrganization?.id || ""}
								existingCoverUrl={note.coverImage || undefined}
							/>
						</div>
					)}
				</div>
			</Suspense>
		</main>
	);
};

export default NoteIdPage;
