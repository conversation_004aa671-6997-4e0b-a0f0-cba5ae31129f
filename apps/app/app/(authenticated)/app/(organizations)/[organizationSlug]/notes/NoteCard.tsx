import { fetchFavorites } from "@app/favorites/lib/api";
import { useNote } from "@app/notes/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import type { Note, ObjectType } from "@repo/database";
import Editor from "@shared/components/Editor";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconWorld } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import {
	HoverCard,
	HoverCardContent,
	HoverCardTrigger,
} from "@ui/components/hover-card";
import { MultiTimezoneAbsoluteTime } from "@ui/components/relative-time";
import { cn } from "@ui/lib";
import { formatDistanceToNow } from "date-fns";
import { type FC, useState } from "react";
import { NoteModal } from "./NoteModal";
import { NoteMoreDropdown } from "./NoteMoreDropdown";
import { CompanyBadge, ContactBadge, PropertyBadge } from "@ui/components/badge";

interface NoteCardProps {
	note: (Note | null) & {
		createdBy?: {
			id: string;
			name: string;
			email: string;
			image: string | null;
		},
		objectRecord?: {
			id: string;
			name: string;
			image: string | null;
			type: ObjectType;
		};
	};
	className?: string;
}

export const NoteCard: FC<NoteCardProps> = ({ note: initialNote, className }) => {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;
	const [open, setOpen] = useState(false);

	const { data: liveNote } = useNote(initialNote?.id);
	const note = liveNote
		? { ...liveNote, createdBy: initialNote?.createdBy }
		: initialNote;

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	const isFavorite = favorites.some(
		(fav) => fav.objectType === "note" && fav.objectId === note?.id,
	);

	const createdBy = note?.createdBy || { name: "Unknown", image: null };

	return (
		<>
			<div
				className={cn(
					"group",
					"cursor-pointer",
					"rounded-2xl border border-neutral-200 dark:border-zinc-700 px-4 py-2 shadow-sm flex flex-col justify-between min-h-[180px]",
					"hover:bg-muted/50 transition-all duration-300",
					"w-full md:max-w-xs",
					className,
				)}
				onClick={() => setOpen(true)}
			>
				<div>
					<div className="flex items-center justify-between mb-2">
						{note?.objectId ? (
							<div className="flex items-center gap-2">
								{note?.objectRecord?.type === "contact" ? (
									<ContactBadge className="!text-xs" size="xs" value={note?.objectRecord?.name} avatar={note?.objectRecord?.image || undefined} />
								) : note?.objectRecord?.type === "company" ? (
									<CompanyBadge value={note?.objectRecord?.name} logo={note?.objectRecord?.image || undefined} />
								) : (
									<PropertyBadge value={note?.objectRecord?.name} avatar={note?.objectRecord?.image || undefined} />
								)}
							</div>
						) : (
							<div className="flex items-center gap-1">
								<span className="text-xs font-medium text-primary">
									Personal Note
								</span>
								{note?.isPublished && (
									<div className="flex items-center gap-1">
										<IconWorld className="h-4 w-4 text-blue-500" />
									</div>
								)}
							</div>
						)}
						<NoteMoreDropdown
							note={note}
							isFavorite={isFavorite}
							organizationId={organizationId}
						/>
					</div>
					<div className="flex items-center gap-x-2">
						<span className="-mt-1">{note.icon}</span>
						<div className="text-sm font-semibold text-primary mb-1 -ml-1">
							{note.title || "Untitled note"}
						</div>
					</div>
					<div className="text-xs text-muted-foreground mb-4 max-h-[100px] overflow-hidden px-1">
						{note.content ? (
							<Editor
								key={`${note.id}-${note.content?.length || 0}`}
								initialContent={note.content}
								editable={false}
							/>
						) : (
							"This note has no content."
						)}
					</div>
				</div>
				<div className="flex items-center justify-between border-t border-neutral-200 dark:border-zinc-700 pt-3 mt-2">
					<div className="flex items-center gap-2">
						<UserAvatar
							name={createdBy.name}
							avatarUrl={createdBy.image}
							className="h-4 w-4"
						/>
						<span className="text-xs text-primary">
							{createdBy.name}
						</span>
					</div>
					<span className="text-xs text-muted-foreground">
						<HoverCard>
							<HoverCardTrigger asChild>
								<div className="flex items-center cursor-default">
									<span className="ml-2">
										{note.createdAt
											? formatDistanceToNow(
													new Date(note.createdAt),
													{ addSuffix: true },
												)
											: ""}
									</span>
								</div>
							</HoverCardTrigger>
							<HoverCardContent className="w-auto max-w-xs">
								{note.createdAt ? (
									<MultiTimezoneAbsoluteTime
										time={new Date(note.createdAt)}
									/>
								) : null}
							</HoverCardContent>
						</HoverCard>
					</span>
				</div>
			</div>
			<NoteModal
				isFavorite={isFavorite}
				open={open}
				onOpenChange={setOpen}
				note={note as any}
				noteToEdit={null}
			/>
		</>
	);
};
