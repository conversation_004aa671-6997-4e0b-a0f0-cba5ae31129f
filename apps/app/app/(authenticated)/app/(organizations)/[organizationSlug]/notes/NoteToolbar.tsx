"use client";

import { useCoverImage } from "@app/notes/hooks/useCoverImage";
import { useUpdateNote } from "@app/notes/lib/api";
import type { ActiveOrganization } from "@repo/auth";
import type { Note } from "@repo/database";
import { useEdgeStore } from "@repo/storage";
import {
	EmojiPicker,
	EmojiPickerContent,
	EmojiPickerFooter,
	EmojiPickerSearch,
} from "@shared/components/EmojiPicker";
// import { useUpdateNote } from "@app/notes/hooks/use-update-note";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { ImageIcon, Smile, X } from "lucide-react";
import React, { type ComponentRef, useRef, useState } from "react";
import TextareaAutosize from "react-textarea-autosize";

interface ToolbarProps {
	initialData: Note;
	preview?: boolean;
	activeOrganization: ActiveOrganization;
}

const Toolbar = ({
	initialData,
	preview,
	activeOrganization,
}: ToolbarProps) => {
	const inputRef = useRef<ComponentRef<"input">>(null);
	const [isEditing, setIsEditing] = useState(false);
	const [value, setValue] = useState(initialData.title);
	const [isOpen, setIsOpen] = useState(false);
	const [isAddIconOpen, setIsAddIconOpen] = useState(false);
	const [title, setTitle] = React.useState(
		initialData.title || "Untitled note",
	);
	const [isTitleDirty, setIsTitleDirty] = React.useState(false);
	const updateNoteMutation = useUpdateNote(activeOrganization?.id);
	const { edgestore } = useEdgeStore();

	const noteIdRef = React.useRef(initialData.id);
	const organizationIdRef = React.useRef(activeOrganization?.id);
	const updateMutationRef = React.useRef(updateNoteMutation.mutate);

	// Update refs when props change
	React.useEffect(() => {
		noteIdRef.current = initialData.id;
		organizationIdRef.current = activeOrganization?.id;
		updateMutationRef.current = updateNoteMutation.mutate;
	}, [initialData.id, activeOrganization?.id, updateNoteMutation.mutate]);

	const lastSavedTitleRef = React.useRef(initialData.title);

	// Update title when initialData changes (from cache updates)
	React.useEffect(() => {
		const newTitle = initialData.title || "Untitled note";
		if (newTitle !== lastSavedTitleRef.current && !isTitleDirty) {
			setTitle(newTitle);
			lastSavedTitleRef.current = newTitle;
		}
	}, [initialData.title, isTitleDirty]);

	// TODO: Implement update note
	// const { mutate: update } = useUpdateNote();

	const coverImage = useCoverImage();

	const enableInput = () => {
		if (preview) return;

		setIsEditing(true);
		setTimeout(() => {
			setValue(initialData.title);
			inputRef.current?.focus();
		}, 0);
	};

	const disableInput = () => setIsEditing(false);

	const onInput = (value: string) => {
		setValue(value);

		// update({
		//   id: initialData.id,
		//   title: value || "Untitled",
		// });
	};

	const onKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
		if (event.key === "Enter") {
			event.preventDefault();
			disableInput();
		}
	};

	const onIconSelect = (icon: string) => {
		if (noteIdRef.current) {
			updateMutationRef.current({
				id: noteIdRef.current,
				icon,
				organizationId: organizationIdRef.current || "",
			});
		}
	};

	const onRemoveIcon = () => {
		if (noteIdRef.current) {
			updateMutationRef.current({
				id: noteIdRef.current,
				icon: "",
				organizationId: organizationIdRef.current || "",
			});
		}
	};

	const handleTitleChange = React.useCallback((newTitle: string) => {
		setTitle(newTitle);
		setIsTitleDirty(true);
	}, []);

	const handleTitleBlur = React.useCallback(() => {
		setIsEditing(false);
		setIsTitleDirty(false);

		if (
			noteIdRef.current &&
			title.trim() !== "" &&
			title !== lastSavedTitleRef.current
		) {
			lastSavedTitleRef.current = title.trim();
			updateMutationRef.current({
				id: noteIdRef.current,
				title: title.trim(),
				organizationId: organizationIdRef.current || "",
			});
		}
	}, [title]);

	const TitleInput = React.useMemo(() => {
		return (
			<Input
				ref={inputRef}
				value={title}
				onChange={(e) => {
					handleTitleChange(e.target.value);
				}}
				onBlur={handleTitleBlur}
				onKeyDown={(e) => {
					if (e.key === "Enter") {
						handleTitleBlur();
					}
				}}
				className="!-ml-3 w-full bg-transparent !text-4xl font-bold text-primary !outline-none !border-none !ring-0 !shadow-none"
			/>
		);
	}, [title, handleTitleChange, handleTitleBlur]);

	return (
		<div className="mb-4 group relative">
			{initialData.icon && !preview && (
				<div className="flex items-center gap-x-2 group/icon pt-6">
					<Popover onOpenChange={setIsOpen} open={isOpen}>
						<PopoverTrigger asChild>
							<p className="text-6xl hover:opacity-75 transition">
								{initialData.icon}
							</p>
						</PopoverTrigger>
						<PopoverContent className="w-fit p-0">
							<EmojiPicker
								className="h-[342px]"
								onEmojiSelect={({ emoji }) => {
									setIsOpen(false);
									onIconSelect(emoji);
								}}
							>
								<EmojiPickerSearch />
								<EmojiPickerContent />
								<EmojiPickerFooter />
							</EmojiPicker>
						</PopoverContent>
					</Popover>
					<Button
						onClick={onRemoveIcon}
						className="rounded-full opacity-0 group-hover/icon:opacity-100 transition text-muted-foreground text-xs"
						variant="outline"
						size="icon"
					>
						<X className="h-4 w-4" />
					</Button>
				</div>
			)}
			{!!initialData.icon && preview && (
				<p className="text-6xl pt-6">{initialData.icon}</p>
			)}
			<div className="opacity-0 group-hover:opacity-100 flex items-center gap-x-1 py-4">
				{!initialData.icon && !preview && (
					<Popover
						open={isAddIconOpen}
						onOpenChange={setIsAddIconOpen}
					>
						<PopoverTrigger asChild>
							<Button
								className="text-muted-foreground text-xs gap-2"
								variant="relio"
								size="sm"
							>
								<Smile className="size-4" />
								Add icon
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-fit p-0">
							<EmojiPicker
								className="h-[342px]"
								onEmojiSelect={({ emoji }) => {
									setIsAddIconOpen(false);
									onIconSelect(emoji);
								}}
							>
								<EmojiPickerSearch />
								<EmojiPickerContent />
								<EmojiPickerFooter />
							</EmojiPicker>
						</PopoverContent>
					</Popover>
				)}
				{!initialData.coverImage && !preview && (
					<Button
						onClick={coverImage.onOpen}
						className="text-muted-foreground text-xs gap-2"
						variant="relio"
						size="sm"
					>
						<ImageIcon className="size-4" />
						Add cover
					</Button>
				)}
			</div>
			{TitleInput}
		</div>
	);
};

export default Toolbar;
