import {
	fetchFavoriteFolders,
	fetchFavorites,
	useToggleFavorite,
	useUpdateFavorite,
} from "@app/favorites/lib/api";
import { useDeleteNote, useUpdateNote } from "@app/notes/lib/api";
import type { Favorite, Note } from "@repo/database";
import {
	IconDotsVertical,
	IconFolder,
	IconSquareRoundedCheckFilled,
	IconStar,
	IconStarOff,
	IconTrash,
	IconWorld,
	IconWorldOff,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import type { FC } from "react";
import { toast } from "sonner";

interface NoteMoreDropdownProps {
	note: Note;
	isFavorite: boolean;
	organizationId?: string;
	className?: string;
	onDelete?: () => void;
}

export const NoteMoreDropdown: FC<NoteMoreDropdownProps> = ({
	note,
	isFavorite,
	organizationId,
	className,
	onDelete,
}) => {
	const toggleFavorite = useToggleFavorite(organizationId || "");
	const deleteNote = useDeleteNote(organizationId);
	const updateNoteMutation = useUpdateNote(organizationId);
	const updateFavoriteMutation = useUpdateFavorite(organizationId);

	// Fetch favorite folders and current favorite
	const { data: favoriteFolders = [] } = useQuery({
		queryKey: ["favoriteFolders", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavoriteFolders(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId && isFavorite,
	});

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId && isFavorite,
	});

	const currentFavorite = favorites.find(
		(f: Favorite) => f.objectId === note.id && f.objectType === "note",
	);
	const currentFolderId = currentFavorite?.folderId;

	const handleToggleFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!organizationId) return;
		toggleFavorite.mutate({
			objectId: note.id,
			objectType: "note",
			organizationId,
		});
	};

	const handleDelete = (e: React.MouseEvent) => {
		e.stopPropagation();
		deleteNote.mutate(note.id);
		onDelete?.();
	};

	const handleMoveToFolder = (folderId: string | null) => {
		if (!currentFavorite) {
			return;
		}

		const payload = {
			id: currentFavorite.id,
			folderId,
		};

		updateFavoriteMutation.mutate(payload, {
			onSuccess: (result) => {
				toast.success("Favorite moved to folder");
			},
			onError: (error) => {
				toast.error("Failed to move favorite to folder");
			},
		});
	};

	return (
		<span onClick={(e) => e.stopPropagation()}>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<button
						className={cn(
							"cursor-pointer",
							"flex items-center justify-center rounded-md p-1 hover:bg-muted/60 transition",
							className,
						)}
						aria-label="Open menu"
					>
						<IconDotsVertical className="w-4 h-4 text-muted-foreground" />
					</button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					sideOffset={8}
					className={cn(
						"z-50 min-w-[180px] rounded-xl border border-border bg-popover text-popover-foreground shadow-lg focus:outline-none animate-in fade-in-0 slide-in-from-top-1",
						"bg-sidebar",
					)}
					align="end"
				>
					{/* Favorite actions */}
					{!isFavorite ? (
						<DropdownMenuItem
							className="flex items-center gap-2"
							onClick={handleToggleFavorite}
						>
							<IconStar className="w-4 h-4" />
							Add to favorites
						</DropdownMenuItem>
					) : (
						<>
							{favoriteFolders.length > 0 ? (
								<DropdownMenuSub>
									<DropdownMenuSubTrigger className="flex items-center gap-2 cursor-pointer">
										<IconStar className="w-4 h-4" />
										Move favorite to
									</DropdownMenuSubTrigger>
									<DropdownMenuSubContent>
										<DropdownMenuItem
											onClick={() =>
												handleMoveToFolder(null)
											}
											className="flex items-center gap-2"
										>
											{!currentFolderId && (
												<span className="ml-auto">
													✓
												</span>
											)}
											No folder
										</DropdownMenuItem>
										<Separator className="my-0.5" />
										{favoriteFolders.map((folder) => (
											<DropdownMenuItem
												key={folder.id}
												onClick={() =>
													handleMoveToFolder(
														folder.id,
													)
												}
												className="flex items-center gap-2"
											>
												<IconFolder className="w-4 h-4" />
												{folder.name}
												{currentFolderId ===
													folder.id && (
													<span className="ml-auto">
														<IconSquareRoundedCheckFilled className="w-4 h-4 text-blue-400" />
													</span>
												)}
											</DropdownMenuItem>
										))}
									</DropdownMenuSubContent>
								</DropdownMenuSub>
							) : null}
							<DropdownMenuItem
								className="flex items-center gap-2"
								onClick={handleToggleFavorite}
							>
								<IconStarOff className="w-4 h-4" />
								Remove from favorites
							</DropdownMenuItem>
						</>
					)}

					{/* Publishing actions */}
					<DropdownMenuItem
						className="flex items-center gap-2"
						onClick={() => {
							if (note?.isPublished) {
								updateNoteMutation.mutate({
									id: note?.id || "",
									isPublished: false,
									organizationId,
								});
							} else {
								updateNoteMutation.mutate({
									id: note?.id || "",
									isPublished: true,
									organizationId,
								});
							}
						}}
					>
						{note?.isPublished ? (
							<IconWorldOff className="size-4" />
						) : (
							<IconWorld className="size-4" />
						)}
						{note?.isPublished ? "Make private" : "Make public"}
					</DropdownMenuItem>

					<Separator className="my-0.5" />

					{/* Delete action */}
					<DropdownMenuItem
						className="flex items-center gap-2 text-red-500 hover:!text-red-600"
						onClick={handleDelete}
					>
						<IconTrash className="w-4 h-4" />
						Delete note
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</span>
	);
};
