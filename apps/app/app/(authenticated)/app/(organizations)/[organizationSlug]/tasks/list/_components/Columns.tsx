import { fetchFavorites, useToggleFavorite } from "@app/favorites/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { TASK_PRIORITY, TASK_STATUS } from "@app/shared/lib/constants";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { UserAvatar } from "@shared/components/UserAvatar";
import { 
	IconStar, 
	IconStarFilled, 
	IconUser, 
	IconBuilding, 
	IconMapPin, 
	IconLink 
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { Checkbox } from "@ui/components/checkbox";
import {
	HoverCard,
	HoverCardContent,
	HoverCardTrigger,
} from "@ui/components/hover-card";
import { MultiTimezoneAbsoluteTime } from "@ui/components/relative-time";
import { cn } from "@ui/lib";
import { format, formatDistanceToNow, isPast } from "date-fns";
import { useRouter } from "next/navigation";
import React from "react";
import { DataTableColumnHeader } from "./ColumnHeader";
import { ToggleStar } from "@app/favorites/components/ToggleStar";
import { CompanyBadge, ContactBadge, PropertyBadge } from "@ui/components/badge";

function includesString(rowValue: string, filterValue: string | string[]) {
	if (typeof rowValue !== "string") {
		return false;
	}

	if (Array.isArray(filterValue)) {
		return filterValue.some((value) =>
			rowValue.toLowerCase().includes(value.toLowerCase()),
		);
	}

	if (typeof filterValue !== "string") {
		return false;
	}

	return rowValue.toLowerCase().includes(filterValue.toLowerCase());
}

function filterWithOperator(
	rowValue: string,
	filterValue: { value: string[]; operator: string } | string | string[],
) {
	let result;
	if (
		filterValue &&
		typeof filterValue === "object" &&
		"value" in filterValue &&
		"operator" in filterValue
	) {
		const { value, operator } = filterValue;
		if (!value || value.length === 0) {
			result = true;
			return result;
		}
		// Normalize both rowValue and filter values to lowercase for comparison
		const rowVal = String(rowValue).toLowerCase();
		const filterVals = value.map((v) => String(v).toLowerCase());
		switch (operator) {
			case "is":
				result = filterVals.includes(rowVal);
				break;
			case "is not":
				result = !filterVals.includes(rowVal);
				break;
			case "is any of":
				result = filterVals.includes(rowVal);
				break;
			default:
				result = true;
		}
		return result;
	}

	result = includesString(rowValue, filterValue as string | string[]);
	return result;
}

const RelatedCell = React.memo(({ task }: { task: any }) => {
	const router = useRouter();
	const { activeOrganization } = useActiveOrganization();

	if (!task.relatedObjectId || !task.relatedObjectType) {
		return null;
	}

	const handleClick = () => {
		router.push(
			`/${activeOrganization?.slug}/${task.relatedObjectType}s/view/${task.relatedObjectId}`,
		);
	};

	const renderIcon = () => {
		switch (task.relatedObjectType) {
			case 'contact':
				return task.relatedObject ? (
					<ContactAvatar 
						name={task.relatedObject.name} 
						className="h-4 w-4 mr-2" 
					/>
				) : (
					<IconUser className="h-4 w-4 mr-2" />
				);
			case 'company':
				return <IconBuilding className="h-4 w-4 mr-2" />;
			case 'property':
				return <IconMapPin className="h-4 w-4 mr-2" />;
			default:
				return <IconLink className="h-4 w-4 mr-2" />;
		}
	};

	const displayName = task.relatedObject?.name || task.relatedObjectId;

	return task.relatedObjectType === 'contact' ? (
		<ContactBadge
			value={displayName}
			avatar={task.relatedObject?.image}
			className="max-w-[200px] cursor-pointer"
			onClick={handleClick}
		/>
	) : task.relatedObjectType === 'company' ? (
		<CompanyBadge
			value={displayName}
			logo={task.relatedObject?.logo}
			className="max-w-[200px] cursor-pointer"
			onClick={handleClick}
		/>
	) : task.relatedObjectType === 'property' ? (
		<PropertyBadge
			value={displayName}
			avatar={task.relatedObject?.image}
			className="max-w-[200px] cursor-pointer"
			onClick={handleClick}
		/>
	) : (
		<div className="flex items-center gap-1">
			<IconLink className="h-4 w-4 mr-2" />
			<span className="truncate text-sm">{displayName}</span>
		</div>
	);
});

RelatedCell.displayName = "RelatedCell";

const FavoriteButton = React.memo(
	({ taskId, favorites, organizationId }: { taskId: string; favorites: any[]; organizationId: string }) => {
		const isFavorited = favorites.some((f) => f.objectId === taskId);
		return (
			<ToggleStar 
				id={taskId}
				isFavorite={isFavorited}
				objectType="task"
			/>
		);
	},
);

FavoriteButton.displayName = "FavoriteButton";

const TitleCell = React.memo(({ task }: { task: any }) => {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;
	const taskId = task?.id;

	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	const toggleFavorite = useToggleFavorite(organizationId);

	const isFavorited = favorites.some((f: any) => f.objectId === taskId);

	const handleToggleFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!organizationId || !taskId) return;
		toggleFavorite.mutate({
			objectId: taskId,
			objectType: "task",
			organizationId,
		});
	};

	return (
		<div className="flex flex-row items-center space-x-2 px-2 group">
			{taskId && (
				<button
					onClick={handleToggleFavorite}
					className={cn(
						"cursor-pointer",
						"hover:bg-muted/50 p-2 rounded-lg",
						"opacity-0 group-hover:opacity-100 transition-opacity",
						"hover:text-yellow-500",
						isFavorited && "opacity-100 text-yellow-500",
					)}
					aria-label={isFavorited ? "Unfavorite" : "Favorite"}
				>
					{isFavorited ? (
						<IconStarFilled className="size-4" />
					) : (
						<IconStar className="size-4" />
					)}
				</button>
			)}
			<span className="max-w-[500px] truncate font-medium cursor-pointer">
				{task?.title}
			</span>
		</div>
	);
});

TitleCell.displayName = "TitleCell";

const AssigneeCell = React.memo(({ row }: { row: any }) => {
	const assignee = row.original?.assignee;

	if (!assignee || !assignee.name) return null;

	return (
		<div className="flex items-center gap-1">
			<UserAvatar
				name={assignee.name}
				avatarUrl={assignee.avatarUrl || assignee.image}
				className="size-5"
			/>
			<span className="text-sm">{assignee.name}</span>
		</div>
	);
});

AssigneeCell.displayName = "AssigneeCell";

const priorityOrder: Record<string, number> = {};
TASK_PRIORITY.forEach((priority, index) => {
	priorityOrder[priority.value] = index;
});

export const columns = (filters: any[]) => [
	{
		id: "select",
		header: ({ table }: { table: any }) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && true)
				}
				onCheckedChange={(value) =>
					table.toggleAllPageRowsSelected(!!value)
				}
				aria-label="Select all"
				className="translate-y-[2px] !h-4 !w-4"
			/>
		),
		cell: ({ row }: { row: any }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onClick={(e) => {
					e.stopPropagation();
				}}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
				className="translate-y-[2px] !h-4 !w-4"
			/>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "title",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Title" />
		),
		cell: ({ row }: { row: any }) => <TitleCell task={row.original} />,
		enableSorting: true,
		sortingFn: (rowA: any, rowB: any) => {
			const a = rowA.original?.title || "";
			const b = rowB.original?.title || "";
			return a.localeCompare(b);
		},
	},
	{
		accessorKey: "status",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Status" />
		),
		cell: ({ row }: { row: any }) => {
			const status = row.original?.status;
			const statusObj = TASK_STATUS.find((item) => item.value === status);
			const StatusLabel = statusObj?.label || "No Status";
			const StatusIcon = statusObj?.icon || TASK_STATUS[0].icon;

			return (
				<div className="flex items-center gap-2">
					{React.createElement(StatusIcon, {
						className: cn("size-4", statusObj?.color),
						"aria-hidden": true,
					})}
					<span>{StatusLabel}</span>
				</div>
			);
		},
		filterFn: (row: any, columnId: string, filterValue: any) => {
			const filterObj = filters.find(
				(f) => f.type.toLowerCase() === columnId,
			);
			const operator = filterObj?.operator || "is";
			const rowVal = String(row.getValue(columnId)).toLowerCase();
			const filterVals = (
				Array.isArray(filterValue) ? filterValue : [filterValue]
			).map((v) => String(v).toLowerCase());
			switch (operator) {
				case "is":
					return filterVals.includes(rowVal);
				case "is not":
					return !filterVals.includes(rowVal);
				default:
					return true;
			}
		},
		enableSorting: true,
		sortingFn: (rowA: any, rowB: any) => {
			const a = rowA.original?.status || "";
			const b = rowB.original?.status || "";
			return a.localeCompare(b);
		},
	},
	{
		accessorKey: "priority",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Priority" />
		),
		cell: ({ row }: { row: any }) => {
			const priority = row.original?.priority;
			const priorityObj = TASK_PRIORITY.find(
				(item) => item.value === priority,
			);
			const PriorityLabel = priorityObj?.label;
			const PriorityIcon = priorityObj?.icon;

			if (!PriorityIcon) {
				console.error(`No icon found for priority ${priority}`);
				return null;
			}

			return (
				<div className="flex items-center gap-2">
					{React.createElement(PriorityIcon, {
						className: cn("size-4", priorityObj?.color),
						"aria-hidden": true,
					})}
					<span>{PriorityLabel}</span>
				</div>
			);
		},
		filterFn: (row: any, columnId: string, filterValue: any) =>
			filterWithOperator(row.getValue(columnId), filterValue),
		enableSorting: true,
		sortingFn: (rowA: any, rowB: any) => {
			const a = rowA.original?.priority || "no_priority";
			const b = rowB.original?.priority || "no_priority";
			return (priorityOrder?.[a] ?? 0) - (priorityOrder?.[b] ?? 0);
		},
	},
	{
		accessorKey: "dueDate",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Date Due" />
		),
		cell: ({ row }: { row: any }) => {
			const rawDueDate = row.original?.dueDate;
			const dateDue = rawDueDate
				? typeof rawDueDate === 'object' && '$date' in rawDueDate
					? new Date(rawDueDate.$date)
					: new Date(rawDueDate)
				: null;

			const formatDueDate = (date: Date) => {
				const today = new Date();
				const tomorrow = new Date(today);
				tomorrow.setDate(tomorrow.getDate() + 1);
				const yesterday = new Date(today);
				yesterday.setDate(yesterday.getDate() - 1);

				// Reset time portions for comparison
				today.setHours(0, 0, 0, 0);
				tomorrow.setHours(0, 0, 0, 0);
				yesterday.setHours(0, 0, 0, 0);
				const compareDate = new Date(date);
				compareDate.setHours(0, 0, 0, 0);

				if (compareDate.getTime() === today.getTime()) return "Today";
				if (compareDate.getTime() === tomorrow.getTime())
					return "Tomorrow";
				if (compareDate.getTime() === yesterday.getTime())
					return "Yesterday";

				const diffInDays = Math.ceil(
					(compareDate.getTime() - today.getTime()) /
						(1000 * 60 * 60 * 24),
				);

				if (diffInDays > 0 && diffInDays <= 7) return "Next week";

				if (compareDate.getFullYear() === today.getFullYear()) {
					return format(date, "MMM d");
				}
				return format(date, "MMM d yyyy");
			};

			const getDateColor = (date: Date) => {
				const today = new Date();
				const compareDate = new Date(date);

				// Reset time portions for comparison
				today.setHours(0, 0, 0, 0);
				compareDate.setHours(0, 0, 0, 0);

				if (compareDate.getTime() === today.getTime())
					return "text-yellow-500";
				if (
					compareDate.getTime() < today.getTime() &&
					row.original?.status !== "done"
				)
					return "text-red-500";
				return "";
			};



			return (
				<HoverCard>
					<HoverCardTrigger asChild>
						<div className="flex items-center cursor-default">
							<span className="ml-2">
								{dateDue ? (
									<div className={cn("flex items-center gap-2", getDateColor(dateDue))}>
										{formatDueDate(dateDue)} at {format(dateDue, "h:mm a")}
									</div>
								) : null}
							</span>
						</div>
					</HoverCardTrigger>
					<HoverCardContent className="w-auto max-w-xs">
						{dateDue ? (
							<MultiTimezoneAbsoluteTime time={dateDue} />
						) : null}
					</HoverCardContent>
				</HoverCard>
			);
		},
		enableSorting: true,
		sortingFn: (rowA: any, rowB: any) => {
			const parseDate = (rawDate: any) => {
				if (!rawDate) return 0;
				if (typeof rawDate === 'object' && '$date' in rawDate) {
					return new Date(rawDate.$date).getTime();
				}
				return new Date(rawDate).getTime();
			};

			const a = parseDate(rowA.original?.dueDate);
			const b = parseDate(rowB.original?.dueDate);
			return a - b;
		},
	},
	{
		accessorKey: "relatedObjectId",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Related" />
		),
		cell: ({ row }: { row: any }) => {
			return <RelatedCell task={row.original} />;
		},
	},
	{
		accessorKey: "assignee",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Assigned to" />
		),
		cell: ({ row }: { row: any }) => <AssigneeCell row={row} />,
		filterFn: (row: any, columnId: string, filterValue: any) => {
			const assignee = row.original?.assignee;
			if (
				!filterValue ||
				!filterValue.value ||
				filterValue.value.length === 0
			)
				return true;
			const { value, operator } = filterValue;
			if (!assignee) return false;
			switch (operator) {
				case "is":
					return value.includes(assignee.id);
				case "is not":
					return !value.includes(assignee.id);
				case "is any of":
					return value.includes(assignee.id);
				default:
					return true;
			}
		},
		enableSorting: true,
		sortingFn: (rowA: any, rowB: any) => {
			const aName = rowA.original?.assignee?.name || "";
			const bName = rowB.original?.assignee?.name || "";
			return aName.localeCompare(bName);
		},
	},
	{
		accessorKey: "createdAt",
		header: ({ column }: { column: any }) => (
			<DataTableColumnHeader column={column} title="Created" />
		),
		cell: ({ row }: { row: any }) => {
			const createdAt = row.original?.createdAt
				? new Date(row.original.createdAt)
				: null;
			if (!createdAt) {
				return null;
			}

			return (
				<HoverCard>
					<HoverCardTrigger asChild>
						<div className="flex items-center cursor-default">
							<span className="ml-2">
								{formatDistanceToNow(createdAt, {
									addSuffix: true,
								})}
							</span>
						</div>
					</HoverCardTrigger>
					<HoverCardContent className="w-auto max-w-xs">
						<MultiTimezoneAbsoluteTime time={createdAt} />
					</HoverCardContent>
				</HoverCard>
			);
		},
		enableSorting: true,
		sortingFn: (rowA: any, rowB: any) => {
			const a = rowA.original?._creationTime || 0;
			const b = rowB.original?._creationTime || 0;
			return a - b;
		},
	},
];
