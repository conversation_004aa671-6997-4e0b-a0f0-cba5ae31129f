import type { TaskStatus } from "@app/shared/lib/constants";
import type { Task } from "@repo/database/src/zod";
import { CalendarContent } from "./_components/calendar-content";
import { CalendarProvider } from "./_components/calendar-context";
import { CalendarHeader } from "./_components/calendar-header";
import { SidePanelProvider } from "./_components/side-panel";

export default function CalendarPage({
	tasks,
	onTasksUpdated,
	onStatusVisibilityChange,
}: {
	tasks: Task[];
	onTasksUpdated: () => void;
	onStatusVisibilityChange: (status: TaskStatus, visible: boolean) => void;
}) {
	return (
		<SidePanelProvider>
			<CalendarProvider>
				<div className="flex flex-col">
					<CalendarHeader />
					<CalendarContent
						tasks={tasks}
						onTasksUpdated={onTasksUpdated}
						onStatusVisibilityChange={onStatusVisibilityChange}
					/>
				</div>
			</CalendarProvider>
		</SidePanelProvider>
	);
}
