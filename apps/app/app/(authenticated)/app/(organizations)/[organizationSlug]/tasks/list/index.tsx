import type { Table } from "@tanstack/react-table";
import React from "react";
import TaskTable from "./_components/DataTable";

interface TaskListProps {
	columns: import("@tanstack/react-table").ColumnDef<any, unknown>[];
	table: Table<any>;
	isLoading: boolean;
	groupBy: string;
	showCompletedTasks: boolean;
	onStatusVisibilityChange: (status: any, visible: boolean) => void;
	onTasksUpdated?: () => void;
}

const TaskList = ({
	columns,
	table,
	isLoading,
	groupBy,
	showCompletedTasks,
	onStatusVisibilityChange,
	onTasksUpdated,
}: TaskListProps) => {
	return (
		<TaskTable
			columns={columns}
			table={table}
			isLoading={isLoading}
			groupBy={groupBy}
			showCompletedTasks={showCompletedTasks}
			onStatusVisibilityChange={onStatusVisibilityChange}
			onTasksUpdated={onTasksUpdated}
		/>
	);
};

export default TaskList;
