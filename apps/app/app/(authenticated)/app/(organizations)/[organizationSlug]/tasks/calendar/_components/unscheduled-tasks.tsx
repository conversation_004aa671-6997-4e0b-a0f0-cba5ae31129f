import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { Task } from "@repo/database/src/zod";
import { CalendarTask } from "./calendar-task";

interface UnscheduledTaskProps {
	task: Task;
}

function UnscheduledTaskItem({ task }: UnscheduledTaskProps) {
	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({
			id: task.id,
		});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div ref={setNodeRef} style={style} {...attributes} {...listeners}>
			<CalendarTask task={task} organizationId={task.organizationId} />
		</div>
	);
}

interface UnscheduledTasksListProps {
	tasks: Task[];
}

export function UnscheduledTasksList({ tasks }: UnscheduledTasksListProps) {
	return (
		<div className="h-full flex flex-col bg-zinc-100 dark:bg-sidebar/50 hover:bg-zinc-100 dark:hover:bg-sidebar/50 rounded-lg">
			<h3 className="font-medium text-sm p-4 border-b border-zinc-200 dark:border-zinc-800">
				Unscheduled Tasks
			</h3>
			<div className="flex-1 min-h-0 overflow-y-auto p-4 space-y-2">
				{tasks.map((task) => (
					<UnscheduledTaskItem key={task.id} task={task} />
				))}
			</div>
		</div>
	);
}
