"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	TASK_STATUS,
	type TaskPriority,
	type TaskStatus,
} from "@app/shared/lib/constants";
import { useColumnPreferences } from "@app/tasks/column-preferences/api";
import BottomBarTasks from "@app/tasks/components/BottomBar";
import { CreateTaskModal } from "@app/tasks/components/CreateTaskModal";
import { useUpdateTask } from "@app/tasks/lib/api";
import {
	DndContext,
	type DragEndEvent,
	type DragOverEvent,
	DragOverlay,
	type DragStartEvent,
	MeasuringStrategy,
	MouseSensor,
	TouchSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import type { Task } from "@repo/database/src/zod";
import type { Table } from "@tanstack/react-table";
import confetti from "canvas-confetti";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { KanbanCard } from "./_components/card";
import { KanbanColumn } from "./_components/column";

interface TaskToEdit {
	id: any;
	title: string;
	description?: string;
	status: TaskStatus;
	priority: TaskPriority;
	dueDate?: number;
	assignee?: { id: any; name: string } | null;
	related?: { id: string; name: string; recordType: "contact" | "company" | "property" } | null;
}

interface TasksBoardProps {
	tasks: Task[];
	table: Table<Task>;
	statusVisibility: Record<TaskStatus, boolean>;
	onStatusVisibilityChange: (status: TaskStatus, visible: boolean) => void;
}

export default function TasksBoard({
	tasks,
	table,
	statusVisibility,
	onStatusVisibilityChange,
}: TasksBoardProps) {
	const params = useParams();
	const [activeTask, setActiveTask] = useState<Task | null>(null);
	const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
	const [selectedTaskObjects, setSelectedTaskObjects] = useState<Task[]>([]);
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const [defaultStatus, setDefaultStatus] = useState<Task["status"] | null>(
		null,
	);
	const [taskToEdit, setTaskToEdit] = useState<TaskToEdit | null>(null);
	const { activeOrganization } = useActiveOrganization();
	const { data: columnPreferences = [] } = useColumnPreferences(
		activeOrganization?.id,
	);
	const updateTaskMutation = useUpdateTask(activeOrganization?.id);

	// Apply table filters, sorting, and grouping to tasks
	const processedTasks = useMemo(() => {
		// Get current column filters and sorting state
		const columnFilters = table.getState().columnFilters;
		const sorting = table.getState().sorting[0]; // Get primary sort column

		// First apply filters
		let result = tasks?.filter((task: Task) => {
			// Check if task's status is visible
			if (!statusVisibility[task.status]) {
				return false;
			}

			// Apply each column filter
			return columnFilters.every((filter) => {
				const value = filter.value;

				// Handle title filter
				if (filter.id === "title" && typeof value === "string") {
					return task.title
						.toLowerCase()
						.includes(value.toLowerCase());
				}

				// Handle status filter
				if (filter.id === "status" && Array.isArray(value)) {
					return value.length === 0 || value.includes(task.status);
				}

				// Handle priority filter
				if (filter.id === "priority" && Array.isArray(value)) {
					return value.length === 0 || value.includes(task.priority);
				}

				// Handle assignee filter
				if (filter.id === "assignee" && Array.isArray(value)) {
					return (
						value.length === 0 ||
						(task.assigneeId && value.includes(task.assigneeId))
					);
				}

				return true;
			});
		});

		// Then apply sorting if specified
		if (sorting) {
			result = [...result].sort((a, b) => {
				let comparison = 0;

				switch (sorting.id) {
					case "title":
						comparison = (a.title || "").localeCompare(
							b.title || "",
						);
						break;
					case "dueDate": {
						const aDue = a.dueDate
							? new Date(a.dueDate).getTime()
							: 0;
						const bDue = b.dueDate
							? new Date(b.dueDate).getTime()
							: 0;
						comparison = aDue - bDue;
						break;
					}
					case "priority": {
						const priorityOrder = {
							high: 3,
							medium: 2,
							low: 1,
							no_priority: 0,
						};
						comparison =
							(priorityOrder[
								a.priority as keyof typeof priorityOrder
							] || 0) -
							(priorityOrder[
								b.priority as keyof typeof priorityOrder
							] || 0);
						break;
					}
					case "assignee":
						comparison = (a.assigneeId || "").localeCompare(
							b.assigneeId || "",
						);
						break;
					case "createdAt": {
						const aCreated = a.createdAt
							? new Date(a.createdAt).getTime()
							: 0;
						const bCreated = b.createdAt
							? new Date(b.createdAt).getTime()
							: 0;
						comparison = aCreated - bCreated;
						break;
					}
					default:
						comparison = 0;
				}

				return sorting.desc ? -comparison : comparison;
			});
		}

		// Finally, sort by position within each status
		result = result.sort((a, b) => {
			if (a.status === b.status) {
				return (a.position ?? 0) - (b.position ?? 0);
			}
			return 0;
		});

		return result;
	}, [
		tasks,
		table.getState().columnFilters,
		table.getState().sorting,
		statusVisibility,
	]);

	// Map of status to preferences
	const columnPrefsMap = useMemo(() => {
		const prefsMap: Record<string, any> = {
			backlog: {
				trackTimeInStatus: false,
				showConfetti: false,
				hidden: false,
				targetTimeInStatus: null,
			},
			todo: {
				trackTimeInStatus: false,
				showConfetti: false,
				hidden: false,
				targetTimeInStatus: null,
			},
			in_progress: {
				trackTimeInStatus: false,
				showConfetti: false,
				hidden: false,
				targetTimeInStatus: null,
			},
			review: {
				trackTimeInStatus: false,
				showConfetti: false,
				hidden: false,
				targetTimeInStatus: null,
			},
			done: {
				trackTimeInStatus: false,
				showConfetti: false,
				hidden: false,
				targetTimeInStatus: null,
			},
		};

		// Update with actual preferences if available
		columnPreferences.forEach((pref: any) => {
			if (pref.column in prefsMap) {
				prefsMap[pref.column] = pref;
			}
		});

		return prefsMap;
	}, [columnPreferences]);

	const handleSelectTask = useCallback((taskId: string) => {
		setSelectedTasks((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(taskId)) {
				newSet.delete(taskId);
			} else {
				newSet.add(taskId);
			}
			return newSet;
		});
	}, []);

	const handleEditTask = useCallback(
		(taskId: string) => {
			if (!tasks || tasks.length === 0) {
				console.warn("Tasks not loaded yet");
				return;
			}

			const task = tasks.find((t) => t.id === taskId);
			if (!task) {
				console.warn("Task not found:", taskId);
				return;
			}

			try {
				const formattedTask: TaskToEdit = {
					id: task.id,
					title: task.title,
					description: task.description ?? "",
					status: task.status,
					priority: task.priority,
					dueDate: task.dueDate
						? new Date(task.dueDate).getTime()
						: 0,
					assignee: (task as any).assignee
						? { id: (task as any).assignee.id, name: (task as any).assignee.name }
						: null,
					related:
						task.relatedObjectId && task.relatedObjectType
							? { 
								id: task.relatedObjectId,
								name: (task as any).relatedObject?.name || "Unnamed Object",
								recordType: task.relatedObjectType as "contact" | "company" | "property",
							}
							: null,
				};
				setTaskToEdit(formattedTask);
				setIsCreateModalOpen(true);
			} catch (error) {
				console.error("Error formatting task for edit:", error);
			}
		},
		[tasks],
	);

	const handleCreateTask = useCallback((status: Task["status"]) => {
		setTaskToEdit(null);
		setDefaultStatus(status);
		setIsCreateModalOpen(true);
	}, []);

	const handleModalClose = useCallback((open: boolean) => {
		setIsCreateModalOpen(open);
		if (!open) {
			setTaskToEdit(null);
			setDefaultStatus(null);
		}
	}, []);

	// Update selectedTaskObjects whenever selectedTasks changes
	useEffect(() => {
		const selectedTasksList = Array.from(selectedTasks)
			.map((taskId) => tasks.find((task) => task.id === taskId))
			.filter(
				(task): task is NonNullable<typeof task> => task !== undefined,
			);

		setSelectedTaskObjects(selectedTasksList);
	}, [selectedTasks, tasks]);

	// Sensors for drag and drop
	const mouseSensor = useSensor(MouseSensor, {
		activationConstraint: {
			distance: 3, // Reduce distance to make it more responsive
		},
	});
	const touchSensor = useSensor(TouchSensor, {
		activationConstraint: {
			delay: 100, // Reduce delay to make it more responsive
			tolerance: 5,
		},
	});
	const sensors = useSensors(mouseSensor, touchSensor);

	const handleDragStart = useCallback(
		(event: DragStartEvent) => {
			const { active } = event;

			const task = tasks.find((t) => t.id === active.id);
			if (task) setActiveTask(task);
		},
		[tasks],
	);

	const handleDragOver = useCallback((event: DragOverEvent) => {
		const { active, over } = event;
	}, []);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			if (!over) return;
			const activeTask = tasks.find((t) => t.id === active.id);
			if (!activeTask) return;
			const tasksToMove = selectedTasks.has(activeTask.id)
				? tasks.filter((task) => selectedTasks.has(task.id))
				: [activeTask];

			// Handle vertical reordering within the same column
			if (
				!over.id.toString().startsWith("column-") &&
				over.data.current?.type === "task"
			) {
				const overTask = tasks.find((t) => t.id === over.id);
				if (!overTask) return;

				// If moving to a different status column, we'll handle that differently
				if (activeTask.status !== overTask.status) {
					// Handle status change + position change together
					const positionedTasks = tasks
						.filter((t) => t.status === overTask.status)
						.sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

					const newPosition = overTask.position ?? 0;

					// Update positions for all tasks after the target position
					tasksToMove.forEach((task) => {
						updateTaskMutation.mutate({
							id: task.id,
							status: overTask.status,
							position: newPosition ?? null,
						});
					});

					positionedTasks
						.filter(
							(t) =>
								(t.position ?? 0) >= newPosition &&
								!tasksToMove.some((m) => m.id === t.id),
						)
						.forEach((task, index) => {
							updateTaskMutation.mutate({
								id: task.id,
								position:
									newPosition + index + tasksToMove.length,
							});
						});

					return;
				}

				// Reordering within the same column/status
				// Get all tasks in the current column, sorted by position
				const columnTasks = tasks
					.filter((t) => t.status === activeTask.status)
					.sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

				const activeIndex = columnTasks.findIndex(
					(t) => t.id === activeTask.id,
				);
				const overIndex = columnTasks.findIndex(
					(t) => t.id === overTask.id,
				);

				if (activeIndex === -1 || overIndex === -1) {
					return;
				}

				// Create a new ordered array
				const newOrder = arrayMove(columnTasks, activeIndex, overIndex);

				newOrder.forEach((task, index) => {
					updateTaskMutation.mutate({
						id: task.id,
						position: index ?? null,
					});
				});

				return;
			}

			// Handle moving to a different column
			let newStatus: Task["status"] | undefined;
			let newPosition: number | undefined;

			if (over.id.toString().startsWith("column-")) {
				// Direct drop on column
				newStatus = over.id
					.toString()
					.replace("column-", "") as Task["status"];
				// Get the highest position in the target column and add 1
				newPosition =
					Math.max(
						-1,
						...tasks
							.filter((t) => t.status === newStatus)
							.map((t) => t.position ?? -1),
					) + 1;
			} else {
				// This case shouldn't happen with the improved logic above,
				// but keeping as a fallback
				const overTask = tasks.find((t) => t.id === over.id);
				if (overTask) {
					newStatus = overTask.status;
					newPosition = overTask.position ?? 0;
				}
			}

			if (!newStatus) {
				return;
			}

			// Update all tasks in parallel
			tasksToMove.forEach((task) => {
				if (task.status !== newStatus) {
					updateTaskMutation.mutate({
						id: task.id,
						status: newStatus,
						position: newPosition ?? null,
					});
				}
			});

			// Get column preferences for the target column
			const columnPrefs = columnPrefsMap[newStatus];

			// Trigger confetti if enabled for this column
			if (columnPrefs?.showConfetti) {
				const colors = ["#EE342F", "#F3B01C", "#8D2676"];
				const defaultOpts = {
					particleCount: 50,
					colors: colors,
					spread: 90,
					startVelocity: 45,
					decay: 0.92,
					scalar: 1,
				};

				// Fire from each corner
				const fireCorner = (x: number, y: number, opts: any = {}) => {
					confetti({
						...defaultOpts,
						...opts,
						origin: { x, y },
					});
				};

				// Top left
				fireCorner(0, 0);
				fireCorner(0.2, 0);

				// Top right
				fireCorner(0.8, 0);
				fireCorner(1, 0);

				// Bottom left
				fireCorner(0, 1);
				fireCorner(0.2, 1);

				// Bottom right
				fireCorner(0.8, 1);
				fireCorner(1, 1);

				// Center burst
				confetti({
					...defaultOpts,
					origin: { x: 0.5, y: 0.5 },
					particleCount: 100,
					spread: 360,
				});
			}

			setActiveTask(null);
		},
		[tasks, selectedTasks, updateTaskMutation, columnPrefsMap],
	);

	const handleStatusChange = useCallback(
		async (taskId: string, newStatus: Task["status"]) => {
			const task = tasks.find((t) => t.id === taskId);
			if (!task || task.status === newStatus) return;

			updateTaskMutation.mutate({
				id: task.id,
				status: newStatus,
				position: task.position ?? null,
			});
		},
		[tasks, updateTaskMutation],
	);

	const handlePriorityChange = useCallback(
		async (taskId: string, newPriority: Task["priority"]) => {
			const task = tasks.find((t) => t.id === taskId);
			if (!task || task.priority === newPriority) return;

			updateTaskMutation.mutate({
				id: task.id,
				priority: newPriority,
			});
		},
		[tasks, updateTaskMutation],
	);

	const handleDeleteTask = useCallback(
		async (taskId: string) => {
			setSelectedTasks((prev) => {
				const newSet = new Set(prev);
				newSet.delete(taskId);
				return newSet;
			});

			// TODO: Implement or import deleteTask from your tasks API
		},
		[tasks],
	);

	return (
		<div className="h-full px-4">
			<DndContext
				sensors={sensors}
				onDragStart={handleDragStart}
				onDragOver={handleDragOver}
				onDragEnd={handleDragEnd}
				measuring={{
					droppable: {
						strategy: MeasuringStrategy.Always,
					},
				}}
			>
				<div className="grid grid-cols-5 gap-2 h-[calc(100vh-8.5rem)] mx-auto w-full overflow-x-auto overflow-y-none">
					{TASK_STATUS.map(
						(status) =>
							statusVisibility[status.value] && (
								<KanbanColumn
									key={status.value}
									id={`column-${status.value}`}
									title={status.label}
									icon={status.icon}
									color={status.color}
									tasks={processedTasks.filter(
										(task) => task.status === status.value,
									)}
									selectedTasks={selectedTasks}
									onSelectTask={handleSelectTask}
									onStatusChange={handleStatusChange}
									onPriorityChange={handlePriorityChange}
									onEdit={handleEditTask}
									onCreateTask={handleCreateTask}
									onDelete={handleDeleteTask}
									onStatusVisibilityChange={
										onStatusVisibilityChange
									}
									columnPrefs={
										columnPrefsMap[status.value] ?? {
											trackTimeInStatus: false,
											showConfetti: false,
											hidden: false,
											targetTimeInStatus: null,
										}
									}
									organizationId={
										activeOrganization?.id || ""
									}
								/>
							),
					)}
				</div>

				<DragOverlay>
					{activeTask &&
						(selectedTasks.has(activeTask.id) &&
						selectedTasks.size > 1 ? (
							// If dragging a selected task and there are multiple selections
							<div className="relative space-y-2">
								{/* Show the active task on top */}
								<div className="relative rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 rotate-2 shadow-lg">
									<KanbanCard task={activeTask} />
								</div>
								{/* Show a stack effect for additional selected tasks */}
								<div className="absolute inset-0 -rotate-2 rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 opacity-70 -translate-y-1" />
								{selectedTasks.size > 2 && (
									<div className="absolute inset-0 rotate-1 rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 opacity-50 -translate-y-2" />
								)}
							</div>
						) : (
							// Single task drag
							<div className="relative rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 rotate-2 shadow-lg">
								<KanbanCard task={activeTask} />
							</div>
						))}
				</DragOverlay>
			</DndContext>

			{/* TODO: Add bottom bar and figure out why batch actions are messing with auth */}
			{/* <BottomBarTasks
				selectedTasks={selectedTaskObjects}
				onDeselectAll={() => {
					setSelectedTasks(new Set());
					setSelectedTaskObjects([]);
				}}
			/> */}

			<CreateTaskModal
				open={isCreateModalOpen}
				onOpenChange={handleModalClose}
				defaultStatus={defaultStatus ?? "todo"}
				taskToEdit={taskToEdit || undefined}
			/>
		</div>
	);
}
