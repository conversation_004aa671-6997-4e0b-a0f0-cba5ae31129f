"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import type { TaskStatus } from "@app/shared/lib/constants";
import { updateTask } from "@app/tasks/lib/api";
import { SidePanelProvider } from "@app/tasks/lib/sidepanel-provider";
import {
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import type { Task } from "@repo/database/src/zod";
import { cn } from "@ui/lib";
import {
	addDays,
	eachDayOfInterval,
	endOfMonth,
	endOfWeek,
	format,
	isSameDay,
	isToday,
	startOfMonth,
	startOfWeek,
} from "date-fns";
import { useState } from "react";
import { useCalendar } from "./calendar-context";
import { CalendarDay } from "./calendar-day";
import { CalendarTask } from "./calendar-task";
import { UnscheduledTasksList } from "./unscheduled-tasks";

export function CalendarContent({
	tasks,
	onTasksUpdated,
	onStatusVisibilityChange,
}: {
	tasks: Task[];
	onTasksUpdated: () => void;
	onStatusVisibilityChange: (status: TaskStatus, visible: boolean) => void;
}) {
	const { currentDate, view } = useCalendar();
	const [activeTask, setActiveTask] = useState<Task | null>(null);
	const { activeOrganization } = useActiveOrganization();

	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
	);

	const getTasksForDate = (date: Date) => {
		return (
			tasks?.filter((task) => {
				if (!task.dueDate) {
					return view === "day" && isSameDay(date, currentDate);
				}
				return isSameDay(new Date(task.dueDate), date);
			}) || []
		);
	};

	const unscheduledTasks = tasks?.filter((task) => !task.dueDate) || [];

	const handleDragStart = (event: DragStartEvent) => {
		const task = tasks?.find((t) => t.id === event.active.id);
		if (task) {
			setActiveTask(task);
			document.body.style.cursor = "grabbing";
		}
	};

	const handleDragEnd = async (event: DragEndEvent) => {
		document.body.style.cursor = "";
		setActiveTask(null);
		const { active, over } = event;

		if (!over) return;

		const taskId = active.id;
		const dropDate = over.id;

		if (typeof dropDate === "string" && dropDate.startsWith("date-")) {
			const date = new Date(dropDate.replace("date-", ""));

			await updateTask({
				id: taskId as any,
				dueDate: date.toISOString(),
			});
		}
	};

	const renderMonthView = () => {
		const start = startOfWeek(startOfMonth(currentDate));
		const end = endOfWeek(endOfMonth(currentDate));
		const days = eachDayOfInterval({ start, end });

		const weekDays = Array.from(Array(7)).map((_, i) => {
			const date = addDays(startOfWeek(new Date()), i);
			return format(date, "EEE");
		});

		return (
			<div className="flex flex-col h-full min-h-0">
				<div className="grid grid-cols-7 text-sm shrink-0 border-b border-zinc-200 dark:border-zinc-800 bg-zinc-100 dark:bg-zinc-800/50">
					{weekDays.map((day) => (
						<div
							key={day}
							className="py-2 text-center font-medium text-muted-foreground"
						>
							{day}
						</div>
					))}
				</div>

				<div className="grid grid-cols-7 text-sm flex-1 min-h-0 overflow-auto">
					{days.map((day, i) => {
						const dayTasks = getTasksForDate(day);
						return (
							<CalendarDay
								key={i}
								date={day}
								tasks={dayTasks}
								isLastColumn={i % 7 === 6}
								isLastRow={i >= days.length - 7}
							/>
						);
					})}
				</div>
			</div>
		);
	};

	const renderWeekView = () => {
		const start = startOfWeek(currentDate);
		const days = Array.from(Array(7)).map((_, i) => addDays(start, i));

		return (
			<div className="grid grid-cols-7 p-4 gap-4 h-full">
				{days.map((day, i) => {
					const dayTasks = getTasksForDate(day);
					const isCurrentDay = isToday(day);

					return (
						<div key={i} className="flex flex-col h-full">
							<div className="text-center flex items-center justify-center gap-2">
								<div
									className={cn(
										"mx-auto h-8 w-8 items-center justify-center rounded-full text-sm flex flex-row gap-2",
										isCurrentDay &&
											"text-primary font-medium",
									)}
								>
									{format(day, "EEE")}{" "}
									<span>{format(day, "d")}</span>
								</div>
							</div>
							<div
								className={cn(
									"flex-1 rounded-lg border p-2 space-y-2 overflow-y-auto",
									isCurrentDay && "bg-accent/5",
								)}
							>
								{dayTasks.map((task) => (
									<CalendarTask
										key={task.id}
										task={task}
										organizationId={
											activeOrganization?.id as string
										}
									/>
								))}
							</div>
						</div>
					);
				})}
			</div>
		);
	};

	const renderDayView = () => {
		const hours = Array.from(Array(24)).map((_, i) => i);
		const dayTasks = getTasksForDate(currentDate);
		const isCurrentDay = isToday(currentDate);

		const allDayTasks = dayTasks.filter((task) => !task.dueDate);
		const timedTasks = dayTasks.filter((task) => task.dueDate);

		return (
			<div className="flex flex-col h-full p-4">
				<div className="text-center flex items-center justify-center gap-2">
					<div
						className={cn(
							"mx-auto h-8 w-8 items-center justify-center rounded-full text-sm flex flex-row gap-2",
							isCurrentDay &&
								"bg-primary text-primary-foreground font-medium",
						)}
					>
						<span>{format(currentDate, "EEEE")}</span>{" "}
						{format(currentDate, "d")}
					</div>
				</div>

				<div className="grid grid-cols-[100px_1fr] gap-1 p-4">
					<div className="text-sm text-muted-foreground text-right py-2">
						All day
					</div>
					<div
						className={cn(
							"rounded-lg border min-h-[60px] space-y-2 p-2",
							isCurrentDay && "bg-accent/5",
						)}
					>
						{allDayTasks.map((task) => (
							<CalendarTask
								key={task.id}
								task={task}
								organizationId={
									activeOrganization?.id as string
								}
							/>
						))}
					</div>
				</div>

				<div className="flex-1 space-y-2 overflow-y-auto px-1">
					{hours.map((hour) => (
						<div
							key={hour}
							className="grid grid-cols-[100px_1fr] gap-4"
						>
							<div className="text-sm text-muted-foreground text-right py-2">
								{format(new Date().setHours(hour), "h a")}
							</div>
							<div
								className={cn(
									"rounded-lg border min-h-[60px] gap-y-2 space-y-2 p-2",
									isCurrentDay && "bg-accent/5",
								)}
							>
								{timedTasks
									.filter((task) => {
										const taskHour = task.dueDate
											? new Date(task.dueDate).getHours()
											: null;
										return taskHour === hour;
									})
									.map((task) => (
										<CalendarTask
											key={task.id}
											task={task}
											organizationId={
												activeOrganization?.id as string
											}
										/>
									))}
							</div>
						</div>
					))}
				</div>
			</div>
		);
	};

	return (
		<SidePanelProvider>
			<DndContext
				sensors={sensors}
				onDragStart={handleDragStart}
				onDragEnd={handleDragEnd}
			>
				<div className="h-[calc(100vh-10rem)] flex flex-col p-6">
					<div className="flex gap-6 flex-1 min-h-0">
						<div className="w-80 shrink-0 overflow-y-auto bg-siderbar/50 border border-border rounded-xl">
							<UnscheduledTasksList tasks={unscheduledTasks} />
						</div>
						<div className="flex-1 flex flex-col bg-zinc-200/50 dark:bg-sidebar/50 border border-zinc-300 dark:border-zinc-800 rounded-xl overflow-hidden">
							{view === "month" && renderMonthView()}
							{view === "week" && renderWeekView()}
							{view === "day" && renderDayView()}
						</div>
					</div>
				</div>
				<DragOverlay>
					{activeTask && (
						<div className="opacity-80">
							<CalendarTask
								task={activeTask}
								isDraggable={false}
								organizationId={
									activeOrganization?.id as string
								}
							/>
						</div>
					)}
				</DragOverlay>
			</DndContext>
		</SidePanelProvider>
	);
}
