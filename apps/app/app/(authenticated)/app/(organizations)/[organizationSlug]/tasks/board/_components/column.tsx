"use client";

import { useDebounce } from "@app/shared/hooks/useDebounce";
import { useUpsertColumnPreference } from "@app/tasks/column-preferences/api";
import { useDroppable } from "@dnd-kit/core";
import {
	SortableContext,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import type { Task } from "@repo/database/src/zod";
import {
	IconClock,
	IconConfetti,
	IconDotsVertical,
	IconEyeOff,
	IconPlus,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import { Switch } from "@ui/components/switch";
import { useCallback, useMemo, useState } from "react";
import { KanbanCard } from "./card";

interface KanbanColumnProps {
	id: string;
	title: string;
	icon: React.ComponentType<any>;
	color: string;
	tasks: Task[];
	selectedTasks: Set<string>;
	onSelectTask: (taskId: string) => void;
	onStatusChange?: (taskId: string, newStatus: Task["status"]) => void;
	onPriorityChange?: (taskId: string, newPriority: Task["priority"]) => void;
	onEdit?: (taskId: string) => void;
	onCreateTask?: (status: Task["status"]) => void;
	onDelete?: (taskId: string) => void;
	onStatusVisibilityChange?: (
		status: Task["status"],
		visible: boolean,
	) => void;
	columnPrefs: {
		trackTimeInStatus: boolean;
		showConfetti: boolean;
		hidden: boolean;
		targetTimeInStatus: number | null;
	};
	organizationId: string;
}

export function KanbanColumn({
	id,
	title,
	icon: Icon,
	color,
	tasks,
	selectedTasks,
	onSelectTask,
	onStatusChange,
	onPriorityChange,
	onEdit,
	onCreateTask,
	onDelete,
	onStatusVisibilityChange,
	columnPrefs,
	organizationId,
}: KanbanColumnProps) {
	const { setNodeRef, isOver, active } = useDroppable({
		id,
		data: {
			type: "column",
			status: id.replace("column-", ""),
		},
	});

	const [isHovered, setIsHovered] = useState(false);

	// Add a class when column is being hovered over
	const columnClassName = `p-4 flex flex-col h-full overflow-y-auto rounded-xl border ${
		isOver
			? "border-blue-500 dark:bg-blue-900/10"
			: "border-zinc-200 bg-white dark:border-zinc-800 dark:bg-sidebar/50"
	} text-zinc-950 shadow-sm dark:text-zinc-50`;

	const status = id.replace("column-", "") as Task["status"];

	// Tasks are already sorted by the parent component, so we just need to memoize them
	const sortedTasks = useMemo(() => tasks, [tasks]);

	// Memoize the tasks array to prevent unnecessary re-renders
	const sortableItems = useMemo(
		() => sortedTasks.map((task) => task.id),
		[sortedTasks],
	);

	const { mutate: upsertColumnPref } = useUpsertColumnPreference();
	const [localTargetTime, setLocalTargetTime] = useState<number | null>(
		columnPrefs.targetTimeInStatus,
	);
	const updateTargetTime = useCallback(
		(value: number | null) => {
			upsertColumnPref({
				organizationId,
				column: status,
				targetTimeInStatus: value,
			});
		},
		[organizationId, status, upsertColumnPref],
	);
	const debouncedUpdateTargetTime = useDebounce(updateTargetTime, 500);

	const handlePreferenceChange = useCallback(
		(key: string, value: boolean | number) => {
			if (key === "targetTimeInStatus") {
				setLocalTargetTime(value as number);
				debouncedUpdateTargetTime(value as number);
			} else {
				upsertColumnPref({
					organizationId,
					column: status,
					[key]: value,
				});
			}
		},
		[organizationId, status, upsertColumnPref, debouncedUpdateTargetTime],
	);

	const showColumn = tasks.length > 0 || isOver || active;

	return (
		<div
			ref={setNodeRef}
			className={columnClassName}
			data-droppable-id={id}
			data-status={status}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			<div className="flex items-center justify-between gap-2 mb-4">
				<div className="flex items-center gap-2">
					<Icon
						className={`flex items-center justify-center w-4 h-4 ${color}`}
					/>
					<h2 className="text-xs font-light">{title}</h2>
					{tasks.length > 0 && (
						<span className="rounded-sm bg-zinc-100 dark:bg-sidebar px-1 py-0 text-xs font-medium border border-input font-mono">
							{tasks.length}
						</span>
					)}
				</div>
				<div className="flex items-center gap-y-1">
					<>
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => onCreateTask?.(status)}
						>
							<IconPlus className="h-4 w-4" />
						</Button>

						<DropdownMenu>
							<DropdownMenuTrigger asChild className="h-4 w-4">
								<Button
									variant="ghost"
									size="icon"
									className="h-6 w-6"
								>
									<IconDotsVertical className="h-4 w-4" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end" className="w-56">
								<div className="flex items-center justify-between px-2 py-2">
									<div className="flex items-center gap-2">
										<IconClock className="w-4 h-4" />
										<span className="text-sm">
											Track time in status
										</span>
									</div>
									<Switch
										checked={columnPrefs.trackTimeInStatus}
										onCheckedChange={(checked) =>
											handlePreferenceChange(
												"trackTimeInStatus",
												checked,
											)
										}
									/>
								</div>
								{columnPrefs.trackTimeInStatus && (
									<div className="px-2 py-2">
										<Input
											type="number"
											placeholder="Target days in status"
											className="w-full px-2 py-1 text-sm border rounded-lg"
											value={localTargetTime || ""}
											onChange={(e) =>
												handlePreferenceChange(
													"targetTimeInStatus",
													Number.parseInt(
														e.target.value,
													) || 0,
												)
											}
										/>
									</div>
								)}
								<DropdownMenuSeparator />
								<div className="flex items-center justify-between px-2 py-2">
									<div className="flex items-center gap-2">
										<IconConfetti className="w-4 h-4" />
										<span className="text-sm">
											Show confetti
										</span>
									</div>
									<Switch
										checked={columnPrefs.showConfetti}
										onCheckedChange={(checked) =>
											handlePreferenceChange(
												"showConfetti",
												checked,
											)
										}
									/>
								</div>
								<DropdownMenuSeparator />
								<DropdownMenuItem
									onClick={() =>
										onStatusVisibilityChange?.(
											status,
											false,
										)
									}
								>
									<IconEyeOff className="w-4 h-4 mr-2" />
									Hide column
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</>
				</div>
			</div>

			<div
				className="space-y-2 min-h-[200px] flex-grow relative rounded-lg overflow-y-auto"
				style={{
					transition: "background-color 150ms ease",
				}}
			>
				{showColumn ? (
					<SortableContext
						items={sortableItems}
						strategy={verticalListSortingStrategy}
					>
						<div className="space-y-2">
							{sortedTasks.map((task) => (
								<KanbanCard
									key={task.id}
									task={task}
									isSelected={selectedTasks.has(task.id)}
									selectedTasks={selectedTasks}
									onSelect={onSelectTask}
									icon={<Icon className="h-4 w-4" />}
									{...(onEdit ? { onEdit } : {})}
									{...(onDelete ? { onDelete } : {})}
									{...(onStatusChange
										? { onStatusChange }
										: {})}
									{...(onPriorityChange
										? { onPriorityChange }
										: {})}
									columnPrefs={{
										trackTimeInStatus:
											columnPrefs.trackTimeInStatus,
										targetTimeInStatus:
											columnPrefs.targetTimeInStatus,
									}}
								/>
							))}
						</div>
					</SortableContext>
				) : (
					<div
						className="absolute inset-0 flex items-center justify-center text-zinc-400 dark:text-zinc-600 pointer-events-none"
						aria-hidden="true"
					>
						<div className="text-center">
							<div className="flex justify-center">
								<Icon
									className={`w-6 h-6 mb-2 opacity-30 ${color}`}
								/>
							</div>
							<p className="text-xs">Drop tasks here</p>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
