import {
	fetchFavorites,
	useCreateFavorite,
	useDeleteFavorite,
} from "@app/favorites/lib/api";
import { PrioritySelector } from "@app/shared/components/PrioritySelector";
import { TaskContextMenu } from "@app/tasks/components/TaskContextMenu";
import { useDeleteTask, useUpdateTask } from "@app/tasks/lib/api";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { Task } from "@repo/database/src/zod";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconClock } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { useMemo } from "react";

interface CalendarTaskProps {
	task: Task;
	className?: string;
	isDraggable?: boolean;
	onEdit?: () => void;
	organizationId: string;
}

export function CalendarTask({
	task,
	className,
	isDraggable = true,
	onEdit,
	organizationId,
}: CalendarTaskProps) {
	const updateTask = useUpdateTask(organizationId);
	const deleteTask = useDeleteTask(organizationId);
	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () => fetchFavorites(organizationId),
		enabled: !!organizationId,
	});
	const createFavorite = useCreateFavorite(organizationId);
	const removeFavorite = useDeleteFavorite(organizationId);

	const isFavorite = useMemo(() => {
		return favorites.some(
			(f) => f.objectId === task.id && f.objectType === "task",
		);
	}, [favorites, task.id]);

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: task.id,
		disabled: !isDraggable,
	});

	const style = useMemo(() => {
		const transformString = transform
			? CSS.Transform.toString(transform)
			: "";
		const styleObj: React.CSSProperties = {
			transform: isDragging
				? `rotate(2deg) ${transformString}`
				: transformString,
			transition,
			opacity: isDragging ? 0.5 : task.status === "done" ? 0.5 : 1,
			cursor: isDragging ? "grabbing" : isDraggable ? "grab" : "default",
			touchAction: "none",
			position: "relative",
			zIndex: isDragging ? 50 : "auto",
		};
		return styleObj;
	}, [transform, transition, isDragging, task.status, isDraggable]);

	const handleStatusChange = async (
		taskId: string,
		newStatus: Task["status"],
	) => {
		await updateTask.mutateAsync({
			id: taskId,
			status: newStatus,
			// Optionally update statusHistory if needed
		});
	};

	const handlePriorityChange = async (
		taskId: string,
		newPriority: Task["priority"],
	) => {
		await updateTask.mutateAsync({
			id: taskId,
			priority: newPriority,
		});
	};

	const handleDelete = async (e: React.MouseEvent, taskId: string) => {
		e.stopPropagation();
		await deleteTask.mutateAsync(taskId);
	};

	const handleFavorite = async (e: React.MouseEvent, taskId: string) => {
		e.stopPropagation();
		const favorite = favorites.find(
			(f) => f.objectId === taskId && f.objectType === "task",
		);
		if (favorite) {
			await removeFavorite.mutateAsync(favorite.id);
		} else {
			await createFavorite.mutateAsync({
				objectId: taskId,
				objectType: "task",
				organizationId,
			});
		}
	};

	return (
		<TaskContextMenu
			task={task}
			isFavorite={isFavorite}
			onEdit={(e) => onEdit?.()}
			onFavorite={handleFavorite}
			onStatusChange={handleStatusChange}
			onPriorityChange={handlePriorityChange}
			onDelete={handleDelete}
			organizationId={organizationId}
		>
			<div
				ref={setNodeRef}
				style={style}
				{...(isDraggable ? { ...attributes, ...listeners } : {})}
				className={cn(
					"group relative flex flex-col gap-1 rounded-lg border bg-zinc-100 dark:bg-sidebar/50 dark:hover:bg-muted/50 hover:bg-zinc-100 p-2",
					isDragging ? "border-blue-500 shadow-lg" : "border-accent",
					task.status === "done" && "opacity-50",
					isDraggable && "hover:cursor-grab active:cursor-grabbing",
					className,
				)}
				onClick={(e) => {
					e.stopPropagation();
					onEdit?.();
				}}
			>
				<div className="flex items-start justify-between gap-2">
					<div className="flex flex-col min-w-0 truncate">
						{task.dueDate && (
							<div className="text-xs text-zinc-500 flex items-center gap-1">
								<IconClock className="h-3 w-3" />
								{format(new Date(task.dueDate), "h:mm a")}
							</div>
						)}
						<p className="text-sm text-zinc-800 dark:text-zinc-100 truncate">
							{task.title}
						</p>
					</div>
					<div className="flex items-center gap-1">
						<PrioritySelector
							priority={task.priority}
							taskId={task.id}
							onPriorityChange={handlePriorityChange}
						/>
						<UserAvatar
							name={task.assigneeId ?? ""}
							avatarUrl={task.assigneeId ?? ""}
						/>
					</div>
				</div>
			</div>
		</TaskContextMenu>
	);
}
