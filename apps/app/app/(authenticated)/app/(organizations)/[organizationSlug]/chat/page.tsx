import { AiChatInterface } from "@app/ai/components/AiChatInterface";
import { getActiveOrganization } from "@app/auth/lib/server";
import { redirect } from "next/navigation";

export default async function ChatPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		redirect("/app");
	}

	return (
		<div className="h-full">
			<AiChatInterface
				organizationId={organization.id}
				organizationSlug={organizationSlug}
			/>
		</div>
	);
}
