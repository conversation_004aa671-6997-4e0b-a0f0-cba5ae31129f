"use client";

import { usePublicNote } from "@app/notes/lib/api";
import type { Note } from "@repo/database/src/zod";
import Editor from "@shared/components/Editor";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconArrowLeft, IconLock, IconWorld } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import * as React from "react";

type PublicNote = Note & {
	createdBy?: {
		id: string;
		name: string;
		email: string;
		image: string | null;
	};
};

export default function PublicNotePage() {
	const params = useParams();
	const noteId = params.id as string;

	const { data: note, isLoading, error } = usePublicNote(noteId);

	if (isLoading) {
		return (
			<div className="min-h-screen bg-background">
				<Skeleton className="h-48 w-full mb-8" />
				<div className="max-w-6xl mx-auto px-4">
					<Skeleton className="h-8 w-1/3 mb-4" />
					<Skeleton className="h-4 w-1/4 mb-8" />
					<Skeleton className="h-64 w-full" />
				</div>
			</div>
		);
	}

	if (error || !note) {
		return (
			<div className="min-h-screen bg-background flex items-center justify-center">
				<div className="text-center">
					<IconLock className="size-16 text-muted-foreground mx-auto mb-4" />
					<h1 className="text-2xl font-bold mb-2">
						Note not available
					</h1>
					<p className="text-muted-foreground mb-6">
						This note is either private or doesn't exist.
					</p>
					<Link href="/app">
						<Button variant="outline">
							<IconArrowLeft className="size-4 mr-2" />
							Back to Dashboard
						</Button>
					</Link>
				</div>
			</div>
		);
	}

	const publicNote = note as PublicNote;

	return (
		<div className="min-h-screen bg-background">
			{publicNote.coverImage ? (
				<div className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] h-48 md:h-64 lg:h-80 mb-8">
					<Image
						src={publicNote.coverImage}
						alt={`Cover image for ${publicNote.title}`}
						fill
						className="object-cover"
						priority
					/>
					<div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-background/20" />
				</div>
			) : null}

			<div className="max-w-6xl mx-auto px-4 md:px-6 lg:px-8">
				{publicNote.icon ? (
					<div className="mb-6">
						<div className="text-6xl leading-none">
							{publicNote.icon}
						</div>
					</div>
				) : null}

				<h1
					className={cn(
						"text-4xl md:text-5xl font-bold mb-6 leading-tight",
						!publicNote.coverImage && "mt-8",
					)}
				>
					{publicNote.title || "Untitled"}
				</h1>

				<div className="flex items-center gap-4 text-sm text-muted-foreground mb-12">
					<div className="flex items-center gap-2">
						<UserAvatar
							name={publicNote.createdBy?.name || "Unknown"}
							avatarUrl={publicNote.createdBy?.image || null}
							className="h-7 w-7"
						/>
						<span className="font-medium">
							by {publicNote.createdBy?.name || "Unknown"}
						</span>
					</div>
					<span className="text-muted-foreground/60">•</span>
					<span>
						{format(new Date(publicNote.createdAt), "MMMM d, yyyy")}
					</span>
					{publicNote.updatedAt &&
						new Date(publicNote.updatedAt).getTime() !==
							new Date(publicNote.createdAt).getTime() && (
							<>
								<span className="text-muted-foreground/60">
									•
								</span>
								<span>
									Updated{" "}
									{format(
										new Date(publicNote.updatedAt),
										"MMMM d, yyyy",
									)}
								</span>
							</>
						)}
				</div>

				<div className="prose prose-lg max-w-none prose-zinc dark:prose-invert">
					{publicNote.content ? (
						<Editor
							editable={false}
							initialContent={publicNote.content}
						/>
					) : (
						<div className="flex items-center justify-center py-16">
							<p className="text-muted-foreground italic text-lg">
								This note is empty.
							</p>
						</div>
					)}
				</div>

				<div className="mt-20 pt-8 border-t border-border">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<div className="flex items-center gap-2">
								<IconWorld className="size-4 text-muted-foreground" />
								<span className="text-sm text-muted-foreground">
									Shared via Relio
								</span>
							</div>
						</div>
						<Link href="/app">
							<Button variant="outline" size="sm">
								Create your own notes
							</Button>
						</Link>
					</div>
				</div>
			</div>
		</div>
	);
}
