import { SessionProvider } from "@app/auth/components/SessionProvider";
import { AuthWrapper } from "@app/shared/components/AuthWrapper";
import type { PropsWithChildren } from "react";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default function AuthLayout({ children }: PropsWithChildren) {
	return (
		<SessionProvider>
			<AuthWrapper>{children}</AuthWrapper>
		</SessionProvider>
	);
}
