@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";
@import "@repo/tailwind-config/theme.css";
@import "@repo/tailwind-config/tailwind-animate.css";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@source "../node_modules/fumadocs-ui/dist/**/*.js";

@variant dark (&:where(.dark, .dark *));

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply bg-card! top-[4.5rem] md:h-[calc(100dvh-4.5rem)]!;

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply no-underline!;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply bg-muted!;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
}
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --scrollbar-track: oklch(0.81 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  --scrollbar-track: oklch(0.205 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee 25s linear infinite;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes marquee {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-100%);
    }
  }
}

@keyframes bounce-x {
  0%, 100% { transform: translateX(0); }
  20% { transform: translateX(-6px); }
  50% { transform: translateX(8px); }
  80% { transform: translateX(-2px); }
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-x {
  animation: bounce-x 0.6s cubic-bezier(0.4, 0, 0.6, 1);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-track) transparent;
  }

  *::-webkit-scrollbar {
    width: 4px;
  }

  *::-webkit-scrollbar-track {
    background: transparent;
  }

  *::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-track);
    border-radius: var(--radius-sm);
  }

  /* Hide number input spinners */
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}

body {
  letter-spacing: var(--tracking-normal);
}

[data-sidebar="sidebar"][data-mobile="true"].fixed.p-6 {
  padding: 0 !important;
}

svg {
  margin-right: 0 !important;
}

input {
  caret-color: theme('colors.blue.500');
}

textarea {
  caret-color: theme('colors.blue.500');
}

@keyframes typing {
0%,
100% {
  transform: translateY(0);
  opacity: 0.5;
}
50% {
  transform: translateY(-2px);
  opacity: 1;
}
}

@keyframes loading-dots {
0%,
100% {
  opacity: 0;
}
50% {
  opacity: 1;
}
}

@keyframes wave {
0%,
100% {
  transform: scaleY(1);
}
50% {
  transform: scaleY(0.6);
}
}

@keyframes blink {
0%,
100% {
  opacity: 1;
}
50% {
  opacity: 0;
}
}

@keyframes text-blink {
0%,
100% {
  color: var(--primary);
}
50% {
  color: var(--muted-foreground);
}
}

@keyframes bounce-dots {
0%,
100% {
  transform: scale(0.8);
  opacity: 0.5;
}
50% {
  transform: scale(1.2);
  opacity: 1;
}
}

@keyframes thin-pulse {
0%,
100% {
  transform: scale(0.95);
  opacity: 0.8;
}
50% {
  transform: scale(1.05);
  opacity: 0.4;
}
}

@keyframes pulse-dot {
0%,
100% {
  transform: scale(1);
  opacity: 0.8;
}
50% {
  transform: scale(1.5);
  opacity: 1;
}
}

@keyframes shimmer-text {
0% {
  background-position: 150% center;
}
100% {
  background-position: -150% center;
}
}

@keyframes wave-bars {
0%,
100% {
  transform: scaleY(1);
  opacity: 0.5;
}
50% {
  transform: scaleY(0.6);
  opacity: 1;
}
}

@keyframes shimmer {
0% {
  background-position: 200% 50%;
}
100% {
  background-position: -200% 50%;
}
}

@keyframes spinner-fade {
0% {
  opacity: 0;
}
100% {
  opacity: 1;
}
}

.mapboxgl-ctrl-logo,
.mapboxgl-ctrl-attrib {
    display: none !important;
}

/* TipTap editor improvements */
.tiptap {
  @apply outline-none;
}

.tiptap p {
  @apply m-0;
}

.tiptap p.is-editor-empty:first-child::before {
  @apply text-muted-foreground;
  content: attr(data-placeholder);
  @apply float-left h-0 pointer-events-none;
}

/* Mention styling */
.mention {
  @apply bg-blue-500/10 text-blue-500 px-1 py-0.5 rounded-md no-underline font-medium;
}

.mention:hover {
  @apply bg-blue-500/20;
}

/* Tippy Theme for Mentions */
.tippy-box[data-theme~='custom'] {
  @apply bg-transparent border-none shadow-none;
}

.tippy-box[data-theme~='custom'] .tippy-content {
  @apply p-0;
}

/* Mention List Styles */
.mention-list-item {
  @apply flex items-center gap-2 px-3 py-2 text-sm cursor-pointer hover:bg-muted/50 rounded-lg;
}

.mention-list-item.is-selected {
  @apply bg-muted;
}