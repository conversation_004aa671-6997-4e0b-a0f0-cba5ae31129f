import { infiniteQueryOptions, keepPreviousData } from "@tanstack/react-query";
import type {
	BaseObjectSchema,
	InfiniteQueryMeta,
	InfiniteQueryResponse,
	ObjectType,
} from "../../object-views/lib/types";

interface CreateQueryOptionsConfig<
	TData extends BaseObjectSchema,
	TMeta = any,
> {
	objectType: ObjectType;
	searchParamsSerializer: (params: any) => string;
	apiEndpoint: string;
	queryKeyPrefix?: string; // Optional override for query key prefix (e.g., "contacts-infinite" instead of "contact-infinite")
}

/**
 * Normalizes field names for backend API compatibility
 * Maps nested field names to their top-level equivalents where appropriate
 */
function normalizeFieldNameForAPI(fieldName: string, objectType: ObjectType): string {
	if (objectType === "property") {
		// Map physicalDetails nested fields to top-level fields for backend compatibility
		if (fieldName === "physicalDetails.units") return "units";
		if (fieldName === "physicalDetails.bedrooms") return "bedrooms";
		if (fieldName === "physicalDetails.bathrooms") return "bathrooms";
		if (fieldName === "physicalDetails.squareFootage") return "squareFootage";
		if (fieldName === "physicalDetails.yearBuilt") return "yearBuilt";
		if (fieldName === "physicalDetails.lotSize") return "lotSize";
		
		// Map financials nested fields to top-level fields for backend compatibility
		if (fieldName === "financials.price") return "price";
		
		// Keep address.location nested fields as-is for backend compatibility
		if (fieldName.startsWith("address.location.")) return fieldName;
	}
	
	// Return original field name if no mapping needed
	return fieldName;
}

export function createInfiniteQueryOptions<
	TData extends BaseObjectSchema,
	TMeta = any,
>(config: CreateQueryOptionsConfig<TData, TMeta>) {
	return (search: any, orgId: string) => {
		const queryKeyPrefix = config.queryKeyPrefix || `${config.objectType}-infinite`;
		
		return infiniteQueryOptions({
			queryKey: [
				queryKeyPrefix,
				orgId,
				config.searchParamsSerializer({
					...search,
					id: null,
					live: null,
				}),
			],
			queryFn: async ({ pageParam }) => {
				const cursor = pageParam.cursor as string;
				const direction = pageParam.direction as
					| "next"
					| "prev"
					| undefined;

				// Build query parameters properly
				const params = new URLSearchParams();
				params.append("organizationId", orgId);
				params.append("objectType", config.objectType);

				if (cursor) {
					params.append("cursor", new Date(cursor).toISOString());
				}

				params.append("direction", direction || "next");
				
				// Use a higher limit for map views to load all properties
				// Check if this is being called from a map context by looking for map-specific parameters
				const isMapView = search.viewType === 'map' || search.mapView === true;
				const limit = isMapView ? "10000" : "100"; // 10k for map, 100 for tables
				params.append("limit", limit);

				// Add search filters with proper formatting and field name normalization
				Object.entries(search).forEach(([key, value]) => {
					if (value !== null && value !== undefined && value !== "") {
						// Normalize field name for backend compatibility
						const normalizedKey = normalizeFieldNameForAPI(key, config.objectType);
						
						// Special handling for createdAt to preserve timestamp format
						if (key === "createdAt" && Array.isArray(value)) {
							// Convert Date objects back to timestamps and join with colon
							const timestamps = value.map((date) =>
								date instanceof Date ? date.getTime() : date,
							);
							params.append(normalizedKey, timestamps.join(":"));
						} else {
							params.append(normalizedKey, String(value));
						}
					}
				});

				const finalUrl = `${config.apiEndpoint}?${params.toString()}`;

				const response = await fetch(finalUrl, {
					headers: {
						"Content-Type": "application/json",
					},
				});

				if (!response.ok) {
					throw new Error(`Failed to fetch ${config.objectType}`);
				}

				const json = await response.json();

				// The API now returns the expected format directly
				const transformedResponse: InfiniteQueryResponse<
					TData[],
					TMeta
				> = {
					data: json.data || [],
					meta: json.meta || {
						totalRowCount: 0,
						filterRowCount: 0,
						chartData: [],
						facets: {},
						metadata: {
							objectType: config.objectType,
							totalCount: 0,
							hasNextPage: false,
						} as TMeta,
					},
					prevCursor: json.prevCursor || null,
					nextCursor: json.nextCursor || null,
				};

				return transformedResponse;
			},
			initialPageParam: {
				cursor: new Date().toISOString(),
				direction: "next",
			},
			getPreviousPageParam: (firstPage, _pages) => {
				if (!firstPage.prevCursor) return null;
				return { cursor: firstPage.prevCursor, direction: "prev" };
			},
			getNextPageParam: (lastPage, _pages) => {
				if (!lastPage.nextCursor) return null;
				return { cursor: lastPage.nextCursor, direction: "next" };
			},
			refetchOnWindowFocus: false,
			placeholderData: keepPreviousData,
		});
	};
}

// Helper function to create faceted filter functions
export function createFacetedFilterHelpers<TMeta = any>(
	facets?: Record<string, any>,
) {
	const getFacetedUniqueValues = (
		_: any,
		columnId: string,
	): Map<string, number> => {
		return new Map(
			facets?.[columnId]?.rows?.map(({ value, total }: any) => [
				value,
				total,
			]) || [],
		);
	};

	const getFacetedMinMaxValues = (
		_: any,
		columnId: string,
	): [number, number] | undefined => {
		const min = facets?.[columnId]?.min;
		const max = facets?.[columnId]?.max;
		if (typeof min === "number" && typeof max === "number")
			return [min, max];
		if (typeof min === "number") return [min, min];
		if (typeof max === "number") return [max, max];
		return undefined;
	};

	return {
		getFacetedUniqueValues,
		getFacetedMinMaxValues,
	};
}
