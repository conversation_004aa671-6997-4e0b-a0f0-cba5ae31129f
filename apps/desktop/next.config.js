/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  distDir: 'out',
  images: {
    unoptimized: true,
  },
  experimental: {
    esmExternals: 'loose',
  },
  transpilePackages: [
    '@repo/api',
    '@repo/auth',
    '@repo/config',
    '@repo/database',
    '@repo/i18n',
    '@repo/logs',
    '@repo/mail',
    '@repo/payments',
    '@repo/storage',
    '@repo/utils',
  ],
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
