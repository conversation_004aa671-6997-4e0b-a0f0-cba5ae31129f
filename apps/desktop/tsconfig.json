{"extends": "@repo/tsconfig/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "out/types/**/*.ts"], "exclude": ["node_modules", "src-tauri"]}