{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "<PERSON><PERSON>", "version": "0.1.0", "identifier": "com.relio.desktop", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build"}, "app": {"windows": [{"title": "Relio - Commercial Real Estate CRM", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "category": "Business", "shortDescription": "Commercial Real Estate CRM", "longDescription": "A comprehensive commercial real estate customer relationship management system."}, "plugins": {"shell": {"open": true}}}