[package]
name = "relio-desktop"
version = "0.1.0"
description = "Relio Desktop - Commercial Real Estate CRM"
authors = ["Relio Team"]
license = "MIT"
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "relio_desktop_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.6.2", features = [] }
tauri-plugin-log = "2"
tauri-plugin-shell = "2"

tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
tauri-plugin-notification = "2"
