import type { Metada<PERSON> } from "next";
import type { PropsWithChildren } from "react";
import "../styles/globals.css";

export const metadata: Metadata = {
  title: {
    absolute: "Relio Desktop - Commercial Real Estate CRM",
    default: "Relio Desktop - Commercial Real Estate CRM",
    template: "%s | Relio Desktop",
  },
  description: "Desktop application for Relio Commercial Real Estate CRM",
};

export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div id="root" className="relative flex min-h-screen flex-col">
          {children}
        </div>
      </body>
    </html>
  );
}
