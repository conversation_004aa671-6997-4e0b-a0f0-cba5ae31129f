"use client";

import { useEffect, useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DesktopLayout } from "@/components/desktop/DesktopLayout";
import { DesktopMenu } from "@/components/desktop/DesktopMenu";
import { FileSystemIntegration } from "@/components/desktop/FileSystemIntegration";
import { tauriUtils } from "@/lib/tauri";

export default function HomePage() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/v1/guides/features/command
    setGreetMsg(await invoke("greet", { name }));
  }

  useEffect(() => {
    // Set initial greeting
    greet();
  }, []);

  const handleShowNotification = async () => {
    await tauriUtils.showNotification(
      "Test Notification",
      "This is a test notification from Relio Desktop!"
    );
  };

  const handleOpenUrl = async () => {
    await tauriUtils.openUrl("https://reliocrm.com");
  };

  return (
    <DesktopLayout>
      <DesktopMenu />
      <main className="container mx-auto flex min-h-full flex-col items-center justify-center p-4 space-y-6">
        <div className="w-full max-w-4xl space-y-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight">
              Welcome to Relio Desktop
            </h1>
            <p className="mt-2 text-muted-foreground">
              Commercial Real Estate CRM
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
                <CardDescription>
                  Your desktop CRM application is ready to use.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium">
                    Enter your name:
                  </label>
                  <input
                    id="name"
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    onChange={(e) => setName(e.currentTarget.value)}
                    placeholder="Enter a name..."
                    value={name}
                  />
                </div>
                <Button onClick={greet} className="w-full">
                  Greet
                </Button>
                {greetMsg && (
                  <p className="text-center text-sm text-muted-foreground">
                    {greetMsg}
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Desktop Features</CardTitle>
                <CardDescription>
                  Test native desktop functionality.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleShowNotification} className="w-full" variant="outline">
                  Show Notification
                </Button>
                <Button onClick={handleOpenUrl} className="w-full" variant="outline">
                  Open Website
                </Button>
                <div className="text-xs text-muted-foreground">
                  Platform: {tauriUtils.isTauri() ? "Desktop" : "Web"}
                </div>
              </CardContent>
            </Card>
          </div>

          <FileSystemIntegration />

          <div className="text-center text-xs text-muted-foreground">
            <p>Built with Tauri + Next.js + React</p>
          </div>
        </div>
      </main>
    </DesktopLayout>
  );
}
