"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { tauriUtils } from "@/lib/tauri";

export function FileSystemIntegration() {
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string | null>(null);

  const handleOpenFile = async () => {
    if (!tauriUtils.isTauri()) {
      console.log("File system access only available in desktop app");
      return;
    }

    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const { readTextFile } = await import("@tauri-apps/plugin-fs");

      const selected = await open({
        multiple: false,
        filters: [
          {
            name: "Text Files",
            extensions: ["txt", "md", "json"],
          },
        ],
      });

      if (selected && typeof selected === "string") {
        setSelectedFile(selected);
        const content = await readTextFile(selected);
        setFileContent(content);
        
        await tauriUtils.showNotification(
          "File Opened",
          `Successfully opened: ${selected.split("/").pop()}`
        );
      }
    } catch (error) {
      console.error("Failed to open file:", error);
      await tauriUtils.showNotification(
        "Error",
        "Failed to open file"
      );
    }
  };

  const handleSaveFile = async () => {
    if (!tauriUtils.isTauri() || !fileContent) {
      return;
    }

    try {
      const { save } = await import("@tauri-apps/plugin-dialog");
      const { writeTextFile } = await import("@tauri-apps/plugin-fs");

      const filePath = await save({
        filters: [
          {
            name: "Text Files",
            extensions: ["txt", "md", "json"],
          },
        ],
      });

      if (filePath) {
        await writeTextFile(filePath, fileContent);
        setSelectedFile(filePath);
        
        await tauriUtils.showNotification(
          "File Saved",
          `Successfully saved: ${filePath.split("/").pop()}`
        );
      }
    } catch (error) {
      console.error("Failed to save file:", error);
      await tauriUtils.showNotification(
        "Error",
        "Failed to save file"
      );
    }
  };

  if (!tauriUtils.isTauri()) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>File System Access</CardTitle>
          <CardDescription>
            File system features are only available in the desktop app.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>File System Integration</CardTitle>
        <CardDescription>
          Access local files and folders with native file dialogs.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={handleOpenFile}>
            Open File
          </Button>
          <Button 
            onClick={handleSaveFile} 
            disabled={!fileContent}
            variant="outline"
          >
            Save File
          </Button>
        </div>

        {selectedFile && (
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Selected: {selectedFile.split("/").pop()}
            </p>
            <p className="text-xs text-muted-foreground">
              Path: {selectedFile}
            </p>
          </div>
        )}

        {fileContent && (
          <div className="space-y-2">
            <label className="text-sm font-medium">File Content:</label>
            <textarea
              value={fileContent}
              onChange={(e) => setFileContent(e.target.value)}
              className="w-full h-32 p-2 border rounded-md text-sm font-mono"
              placeholder="File content will appear here..."
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
