"use client";

import { useEffect, useState } from "react";
import { tauriUtils } from "@/lib/tauri";
import { desktopConfig } from "@/lib/config";

interface DesktopLayoutProps {
  children: React.ReactNode;
}

export function DesktopLayout({ children }: DesktopLayoutProps) {
  const [platform, setPlatform] = useState<string>("unknown");
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Get platform information
    tauriUtils.getPlatform().then(setPlatform);

    // Monitor online status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  return (
    <div className="flex h-screen flex-col">
      {/* Desktop-specific title bar (if needed) */}
      {tauriUtils.isTauri() && (
        <div className="flex h-8 items-center justify-between bg-background/95 px-4 text-xs text-muted-foreground border-b">
          <div className="flex items-center gap-2">
            <span>{desktopConfig.app.name}</span>
            <span>•</span>
            <span className="capitalize">{platform}</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`h-2 w-2 rounded-full ${
                isOnline ? "bg-green-500" : "bg-red-500"
              }`}
            />
            <span>{isOnline ? "Online" : "Offline"}</span>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
}
