"use client";

import { useEffect } from "react";
import { tauriUtils } from "@/lib/tauri";

export function DesktopMenu() {
  useEffect(() => {
    if (!tauriUtils.isTauri()) return;

    // Set up native menu items
    const setupMenu = async () => {
      try {
        // This would typically be done in Rust, but we can trigger it from here
        console.log("Setting up desktop menu...");
        
        // Example: Show welcome notification
        await tauriUtils.showNotification(
          "Relio Desktop",
          "Welcome to Relio Desktop CRM!"
        );
      } catch (error) {
        console.error("Failed to setup menu:", error);
      }
    };

    setupMenu();
  }, []);

  // This component doesn't render anything visible
  // It's just for setting up desktop-specific functionality
  return null;
}

// Menu actions that can be called from Rust or keyboard shortcuts
export const menuActions = {
  async openPreferences() {
    console.log("Opening preferences...");
    // TODO: Implement preferences dialog
  },

  async openAbout() {
    console.log("Opening about dialog...");
    // TODO: Implement about dialog
  },

  async quit() {
    if (tauriUtils.isTauri()) {
      try {
        const { getCurrentWindow } = await import("@tauri-apps/api/window");
        const window = getCurrentWindow();
        await window.close();
      } catch (error) {
        console.error("Failed to quit application:", error);
      }
    }
  },

  async openDevTools() {
    if (tauriUtils.isTauri() && process.env.NODE_ENV === "development") {
      // Open dev tools in development
      console.log("Opening dev tools...");
    }
  },

  async toggleFullscreen() {
    if (tauriUtils.isTauri()) {
      try {
        const { getCurrentWindow } = await import("@tauri-apps/api/window");
        const window = getCurrentWindow();
        const isFullscreen = await window.isFullscreen();
        await window.setFullscreen(!isFullscreen);
      } catch (error) {
        console.error("Failed to toggle fullscreen:", error);
      }
    }
  },

  async minimizeWindow() {
    if (tauriUtils.isTauri()) {
      try {
        const { getCurrentWindow } = await import("@tauri-apps/api/window");
        const window = getCurrentWindow();
        await window.minimize();
      } catch (error) {
        console.error("Failed to minimize window:", error);
      }
    }
  },

  async maximizeWindow() {
    if (tauriUtils.isTauri()) {
      try {
        const { getCurrentWindow } = await import("@tauri-apps/api/window");
        const window = getCurrentWindow();
        const isMaximized = await window.isMaximized();
        if (isMaximized) {
          await window.unmaximize();
        } else {
          await window.maximize();
        }
      } catch (error) {
        console.error("Failed to maximize window:", error);
      }
    }
  },
};
