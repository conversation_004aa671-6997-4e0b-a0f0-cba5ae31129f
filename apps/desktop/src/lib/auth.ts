"use client";

import { authClient } from "@repo/auth/client";
import { getBaseUrl } from "@repo/utils";

// Desktop-specific auth configuration
export const desktopAuthClient = authClient;

// For desktop app, we need to configure the base URL properly
// This will be used for API calls to the backend
export const getAuthBaseUrl = () => {
  // In development, use localhost
  if (process.env.NODE_ENV === "development") {
    return "http://localhost:3000";
  }
  
  // In production, use the actual backend URL
  return getBaseUrl();
};

// Desktop-specific auth hooks and utilities
export const useDesktopAuth = () => {
  const session = desktopAuthClient.useSession();
  
  return {
    ...session,
    isDesktop: true,
  };
};

export { authClient as auth } from "@repo/auth/client";
