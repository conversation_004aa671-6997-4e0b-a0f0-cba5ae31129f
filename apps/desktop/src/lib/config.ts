"use client";

// Desktop-specific configuration
export const desktopConfig = {
  app: {
    name: "Relio Desktop",
    version: "0.1.0",
    platform: "desktop",
  },
  api: {
    baseUrl: process.env.NODE_ENV === "development" 
      ? "http://localhost:3000/api" 
      : "https://app.reliocrm.com/api",
  },
  auth: {
    baseUrl: process.env.NODE_ENV === "development" 
      ? "http://localhost:3000" 
      : "https://app.reliocrm.com",
  },
  features: {
    // Desktop-specific feature flags
    offlineMode: true,
    nativeNotifications: true,
    fileSystemAccess: true,
    systemTray: true,
  },
} as const;

export type DesktopConfig = typeof desktopConfig;
