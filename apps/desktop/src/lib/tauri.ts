"use client";

import { invoke } from "@tauri-apps/api/core";
import { sendNotification } from "@tauri-apps/plugin-notification";
import { open } from "@tauri-apps/plugin-shell";

// Tauri-specific utilities
export const tauriUtils = {
  // Invoke Rust commands
  async greet(name: string): Promise<string> {
    return invoke("greet", { name });
  },

  // Show native notifications
  async showNotification(title: string, body: string) {
    try {
      await sendNotification({
        title,
        body,
      });
    } catch (error) {
      console.error("Failed to show notification:", error);
    }
  },

  // Open URLs in default browser
  async openUrl(url: string) {
    try {
      await open(url);
    } catch (error) {
      console.error("Failed to open URL:", error);
    }
  },

  // Check if running in Tauri
  isTauri(): boolean {
    return typeof window !== "undefined" && "__TAURI__" in window;
  },

  // Get platform info
  async getPlatform(): Promise<string> {
    if (!this.isTauri()) return "web";

    try {
      // Use navigator.platform as fallback
      return navigator.platform || "unknown";
    } catch {
      return "unknown";
    }
  },
};

export default tauriUtils;
