{"name": "@repo/desktop", "private": true, "version": "0.0.0", "scripts": {"dev": "next dev", "build": "next build", "desktop": "tauri build", "desktop:dev": "tauri dev", "preview": "next start", "tauri": "tauri", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@tauri-apps/api": "^2.1.1", "@tauri-apps/plugin-shell": "^2.0.1", "@tauri-apps/plugin-fs": "^2.0.1", "@tauri-apps/plugin-dialog": "^2.0.1", "@tauri-apps/plugin-notification": "^2.0.1", "react": "19.0.0", "react-dom": "19.0.0", "next": "15.3.3", "tailwindcss": "4.0.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.476.0", "@radix-ui/react-slot": "^1.2.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@tauri-apps/cli": "^2.1.0", "@types/node": "22.13.10", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.3.4", "@tailwindcss/postcss": "^4.0.12", "autoprefixer": "10.4.21", "postcss": "8.5.3", "typescript": "5.8.2", "vite": "^6.0.7"}}