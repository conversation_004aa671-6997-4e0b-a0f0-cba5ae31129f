{"extends": "@repo/tsconfig/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "paths": {"@analytics": ["./modules/analytics"], "@homepage/*": ["./modules/homepage/*"], "@ui/*": ["./modules/ui/*"], "@i18n": ["./modules/i18n"], "@i18n/*": ["./modules/i18n/*"], "@shared/*": ["./modules/shared/*"], "content-collections": ["./.content-collections/generated"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.mjs", ".next/types/**/*.ts"], "exclude": ["node_modules"]}