import { getLocale, getTranslations } from "next-intl/server";
import { FAQPage } from "../../../modules/home/<USER>/FAQPage";

export async function generateMetadata() {
	const t = await getTranslations();
	return {
		title: t("blog.title"),
	};
}

export default async function BlogListPage() {
	const locale = await getLocale();
	const t = await getTranslations();

	return (
		<div className="container max-w-6xl pt-32 pb-16">
			<div className="mb-12 pt-8 text-center">
				<h1 className="mb-2 font-bold text-5xl">{t("faq.title")}</h1>
				<p className="text-lg opacity-50">{t("faq.description")}</p>
			</div>

			<FAQPage />
		</div>
	);
}
