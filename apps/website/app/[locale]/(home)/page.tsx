import { getTranslations, setRequestLocale } from "next-intl/server";
import { FAQ } from "../../../../modules/homepage/home/<USER>/FAQ";
import { Feature1 } from "../../../../modules/homepage/home/<USER>/Feature1";
import { Feature2 } from "../../../../modules/homepage/home/<USER>/Feature2";
import { Feature3 } from "../../../../modules/homepage/home/<USER>/Feature3";
import { Features } from "../../../../modules/homepage/home/<USER>/Features";
import Hero from "../../../../modules/homepage/home/<USER>/Hero";
import Logos from "../../../../modules/homepage/home/<USER>/Logos";
import { Newsletter } from "../../../../modules/homepage/home/<USER>/Newsletter";
import Pricing from "../../../../modules/homepage/home/<USER>/Pricing";
import Testimonials from "../../../../modules/homepage/home/<USER>/Testimonials";

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	const t = await getTranslations();

	return (
		<>
			<Hero />
			<Logos />
			<Feature1 />
			<Feature2 />
			<Feature3 />
			<Testimonials />
			<div className="mb-12 text-center">
				<h1 className="mb-2 font-bold text-5xl">
					{t("pricing.title")}
				</h1>
				<p className="text-lg opacity-50">{t("pricing.description")}</p>
			</div>
			<Pricing />
			<div className="py-16">
				<FAQ />
			</div>
			<Newsletter />
		</>
	);
}
