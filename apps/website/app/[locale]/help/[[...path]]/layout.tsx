import { DocsLayout } from "fumadocs-ui/layouts/docs";
import { getTranslations } from "next-intl/server";
import type { PropsWithChildren } from "react";
import { helpSource } from "../../../help-source";

export default async function HelpLayout({
	children,
	params,
}: PropsWithChildren<{
	params: Promise<{ locale: string }>;
}>) {
	const t = await getTranslations();
	const { locale } = await params;

	return (
		<div className="pt-[4.5rem]">
			<DocsLayout
				tree={helpSource.pageTree[locale]}
				disableThemeSwitch
				i18n
				nav={{
					title: <strong>{t("help.title")}</strong>,
					url: "/help",
				}}
				sidebar={{
					defaultOpenLevel: 1,
				}}
			>
				{children}
			</DocsLayout>
		</div>
	);
}
