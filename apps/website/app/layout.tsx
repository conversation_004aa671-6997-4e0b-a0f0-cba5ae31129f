import type { Metadata } from "next";
import type { PropsWithChildren } from "react";
import "../styles/globals.css";

export const metadata: Metadata = {
	title: {
		absolute: "Relio - Commercial Real Estate CRM",
		default: "Relio - Commercial Real Estate CRM",
		template: "%s | Relio - Commercial Real Estate CRM",
	},
};

export default function RootLayout({ children }: PropsWithChildren) {
	return children;
}
