{"dependencies": {"@content-collections/core": "^0.8.2", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "^0.2.6", "@fumadocs/content-collections": "^1.1.8", "@mdx-js/mdx": "^3.1.0", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/utils": "workspace:*", "@shikijs/rehype": "^3.1.0", "canvas-confetti": "^1.9.3", "content-collections": "^0.2.0", "fumadocs-core": "^14.6.0", "fumadocs-ui": "^14.6.0", "geist": "^1.3.1", "jotai": "^2.10.3", "lucide-react": "^0.476.0", "mdx": "^0.3.1", "next": "15.3.3", "next-intl": "^3.26.5", "next-themes": "^0.4.4", "nextjs-toploader": "^3.8.16", "nuqs": "^2.2.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "rehype-img-size": "^1.0.1", "usehooks-ts": "^3.1.0", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@types/canvas-confetti": "^1.9.0", "@types/mdx": "^2.0.13", "@types/node": "22.13.10", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "autoprefixer": "10.4.21", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "markdown-toc": "^1.2.0", "postcss": "8.5.3", "tailwindcss": "4.0.12", "tw-animate-css": "^1.3.4"}, "name": "@repo/website", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start --port 3001", "type-check": "tsc --noEmit"}, "version": "0.0.0"}