import { LocaleLink } from "@i18n/routing";
import { slugifyHeadline } from "@shared/lib/content";
import { Alert } from "@ui/components/alert";
import type { MDXComponents } from "mdx/types";
import type { ImageProps } from "next/image";
import Image from "next/image";
import * as React from "react";

export const mdxComponents: MDXComponents = {
	// Custom components
	Alert: ({ children, ...props }) => <Alert {...props}>{children}</Alert>,

	a: (props: any) => {
		const { href, children, ...rest } = props;
		const isInternalLink =
			href && (href.startsWith("/") || href.startsWith("#"));

		return isInternalLink ? (
			<LocaleLink href={href} {...rest}>
				{children}
			</LocaleLink>
		) : (
			<a target="_blank" rel="noopener noreferrer" href={href} {...rest}>
				{children}
			</a>
		);
	},
	img: (props) => {
		if (!props.src) return null;

		const width = Number(props.width) || 800;
		const height = Number(props.height) || (width * 0.5625) | 0;

		return (
			<div
				className="relative w-full my-6"
				style={{ paddingBottom: "56.25%" }}
			>
				<Image
					{...(props as ImageProps)}
					width={width}
					height={height}
					alt={props.alt || ""}
					className="rounded-2xl shadow object-cover absolute inset-0 w-full h-full"
					loading="lazy"
				/>
			</div>
		);
	},
	h1: ({ children, ...rest }) => (
		<h1
			id={slugifyHeadline(children as string)}
			className="mb-6 font-bold text-4xl"
			{...rest}
		>
			{children}
		</h1>
	),
	h2: ({ children, ...rest }) => (
		<h2
			id={slugifyHeadline(children as string)}
			className="mb-4 font-bold text-2xl"
			{...rest}
		>
			{children}
		</h2>
	),
	h3: ({ children, ...rest }) => (
		<h3
			id={slugifyHeadline(children as string)}
			className="mb-4 font-bold text-xl"
			{...rest}
		>
			{children}
		</h3>
	),
	h4: ({ children, ...rest }) => (
		<h4
			id={slugifyHeadline(children as string)}
			className="mb-4 font-bold text-lg"
			{...rest}
		>
			{children}
		</h4>
	),
	h5: ({ children, ...rest }) => (
		<h5
			id={slugifyHeadline(children as string)}
			className="mb-4 font-bold text-base"
			{...rest}
		>
			{children}
		</h5>
	),
	h6: ({ children, ...rest }) => (
		<h6
			id={slugifyHeadline(children as string)}
			className="mb-4 font-bold text-sm"
			{...rest}
		>
			{children}
		</h6>
	),
	p: ({ children, ...rest }) => (
		<p className="mb-6 text-foreground/60 leading-relaxed" {...rest}>
			{children}
		</p>
	),
	ul: ({ children, ...rest }) => (
		<ul className="mb-6 list-inside list-disc space-y-2 pl-4" {...rest}>
			{children}
		</ul>
	),
	ol: ({ children, ...rest }) => (
		<ol className="mb-6 list-inside list-decimal space-y-2 pl-4" {...rest}>
			{children}
		</ol>
	),
	li: ({ children, ...rest }) => <li {...rest}>{children}</li>,
} satisfies MDXComponents;
