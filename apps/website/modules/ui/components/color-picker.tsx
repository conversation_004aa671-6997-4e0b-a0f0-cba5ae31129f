"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { Check } from "lucide-react";
import * as React from "react";

// 12 predefined colors matching the image
export const DEFAULT_COLORS = [
	"#ef4444", // red
	"#f97316", // orange
	"#f59e0b", // amber
	"#84cc16", // lime
	"#22c55e", // green
	"#06b6d4", // cyan
	"#3b82f6", // blue
	"#8b5cf6", // violet
	"#a855f7", // purple
	"#ec4899", // pink
	"#eab308", // yellow
	"#6b7280", // gray
] as const;

interface ColorPickerProps {
	value?: string;
	onValueChange?: (color: string) => void;
	colors?: readonly string[];
	disabled?: boolean;
	className?: string;
	children?: React.ReactNode;
}

function ColorPicker({
	value,
	onValueChange,
	colors = DEFAULT_COLORS,
	disabled = false,
	className,
	children,
}: ColorPickerProps) {
	const [open, setOpen] = React.useState(false);

	const handleColorSelect = (color: string) => {
		onValueChange?.(color);
		setOpen(false);
	};

	const trigger = children || (
		<Button
			variant="outline"
			size="sm"
			disabled={disabled}
			className={cn("w-12 h-12 p-0 border-2", className)}
			style={{ backgroundColor: value }}
		>
			{!value && (
				<div className="w-full h-full bg-gradient-to-br from-red-500 via-yellow-500 to-blue-500 rounded opacity-20" />
			)}
		</Button>
	);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>{trigger}</PopoverTrigger>
			<PopoverContent className="w-auto p-3" align="start">
				<div className="grid grid-cols-4 gap-2">
					{colors.map((color) => (
						<button
							key={color}
							type="button"
							className={cn(
								"relative w-8 h-8 rounded-lg border-2 transition-all hover:scale-110",
								"focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
								value === color
									? "border-primary shadow-lg scale-110"
									: "border-border hover:border-primary/50",
							)}
							style={{ backgroundColor: color }}
							onClick={() => handleColorSelect(color)}
							disabled={disabled}
						>
							{value === color && (
								<Check className="absolute inset-0 m-auto h-4 w-4 text-white drop-shadow-sm" />
							)}
						</button>
					))}
				</div>

				{/* Optional: Add custom color input */}
				{/* <div className="mt-3 pt-3 border-t border-border">
					<input
						type="color"
						value={value || "#000000"}
						onChange={(e) => handleColorSelect(e.target.value)}
						className="w-full h-8 rounded-lg cursor-pointer"
						disabled={disabled}
					/>
				</div> */}
			</PopoverContent>
		</Popover>
	);
}

// Utility function to get a random color from the default palette
export function getRandomColor(): string {
	return DEFAULT_COLORS[Math.floor(Math.random() * DEFAULT_COLORS.length)];
}

// Utility function to get multiple random colors (useful for creating multiple stages)
export function getRandomColors(count: number): string[] {
	const shuffled = [...DEFAULT_COLORS].sort(() => 0.5 - Math.random());
	return shuffled.slice(0, Math.min(count, DEFAULT_COLORS.length));
}

export { ColorPicker };
export type { ColorPickerProps };
