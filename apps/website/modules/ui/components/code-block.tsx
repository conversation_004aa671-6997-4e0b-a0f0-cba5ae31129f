import { cn } from "@ui/lib";
import { Check, Copy } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Button } from "./button";

interface CodeBlockProps {
	children: React.ReactNode;
	className?: string;
}

export function CodeBlock({ children, className }: CodeBlockProps) {
	return <div className={cn("relative group", className)}>{children}</div>;
}

interface CodeBlockCodeProps {
	code: string;
	language: string;
	className?: string;
}

export function CodeBlockCode({
	code,
	language,
	className,
}: CodeBlockCodeProps) {
	const [copied, setCopied] = useState(false);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(code);
			setCopied(true);
			toast.success("Code copied to clipboard");
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			toast.error("Failed to copy code");
		}
	};

	return (
		<div className="relative">
			<div className="flex items-center justify-between bg-muted/50 px-4 py-2 border-b">
				<span className="text-xs text-muted-foreground font-mono">
					{language}
				</span>
				<Button
					variant="ghost"
					size="sm"
					onClick={handleCopy}
					className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
				>
					{copied ? (
						<Check className="h-3 w-3" />
					) : (
						<Copy className="h-3 w-3" />
					)}
				</Button>
			</div>
			<pre
				className={cn(
					"overflow-x-auto p-4 text-sm bg-muted/20",
					"rounded-b-md border border-t-0",
					className,
				)}
			>
				<code className={`language-${language}`}>{code}</code>
			</pre>
		</div>
	);
}
