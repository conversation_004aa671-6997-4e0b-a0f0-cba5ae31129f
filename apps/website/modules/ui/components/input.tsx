"use client";

import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import * as React from "react";
import { useState, useRef, useEffect } from "react";

function Input({
	className,
	type,
	button,
	leftIcon,
	rightIcon,
	...props
}: React.ComponentProps<"input"> & {
	button?: React.ReactNode;
	leftIcon?: React.ReactNode;
	rightIcon?: React.ReactNode;
}) {
	return (
		<div className={cn("relative flex items-center")}>
			{leftIcon && (
				<div className="absolute left-3 flex items-center pointer-events-none text-muted-foreground h-4 w-4">
					{leftIcon}
				</div>
			)}
			<input
				type={type}
				data-slot="input"
				className={cn(
					"file:text-foreground placeholder:text-muted-foreground rounded-lg selection:bg-primary/20 selection:text-foreground border-input flex h-9 w-full min-w-0 border !bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
					"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] selection:!bg-blue-500/50",
					"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
					leftIcon && "pl-10",
					rightIcon && "pr-10",
					className,
				)}
				{...props}
			/>
			{rightIcon && (
				<div className="absolute right-3 flex items-center pointer-events-none text-muted-foreground">
					{rightIcon}
				</div>
			)}
		</div>
	);
}

function AutoSizingInput({
	className,
	type,
	value = "",
	onChange,
	placeholder = "",
	minWidth = 60,
	...props
}: React.ComponentProps<"input"> & {
	minWidth?: number;
}) {
	const [width, setWidth] = useState(minWidth);
	const spanRef = useRef<HTMLSpanElement>(null);

	useEffect(() => {
		if (spanRef.current) {
			const textToMeasure = value || placeholder;
			spanRef.current.textContent = textToMeasure as string;
			const newWidth = Math.max(spanRef.current.offsetWidth + 16, minWidth); // +16 for padding
			setWidth(newWidth);
		}
	}, [value, placeholder, minWidth]);

	return (
		<div className="relative flex items-center">
			{/* Hidden span to measure text width */}
			<span
				ref={spanRef}
				className={cn(
					"absolute invisible whitespace-pre text-xl font-semibold",
					className
				)}
				style={{ fontSize: 'inherit', fontFamily: 'inherit' }}
			/>
			<input
				type={type}
				value={value}
				onChange={onChange}
				className={cn(
					"file:text-foreground placeholder:text-muted-foreground rounded-md selection:bg-primary/20 selection:text-foreground border-input flex h-9 min-w-0 border !bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
					"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] selection:!bg-blue-500/50",
					"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
					className,
				)}
				style={{ width: `${width}px` }}
				placeholder={placeholder}
				{...props}
			/>
		</div>
	);
}

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode;
  check?: React.ReactNode;
  symbol?: string;
  kbd?: string;
  divCn?: string;
}

function DetailInput({ className, icon, type, check, symbol, kbd, divCn, ...props }: InputProps) {
  return (
    <div className={cn('flex-row h-8 items-center flex w-full rounded-lg border border-transparent bg-transparent text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 hover:!bg-zinc-200 dark:hover:!bg-accent/50', divCn)}>
      {icon && <div className='pl-3'>{icon}</div>}
      {type === 'number' ? (
        <Input
          type="number"
          className={cn("cursor-default w-full flex bg-transparent px-3 py-1 text-md transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50", className)}
          {...props}
        />
      ) : (
        <input
          type={type}
          autoComplete='off'
          autoCapitalize='off'
          spellCheck='false'
          className={cn("cursor-default w-full flex bg-transparent px-3 py-1 text-md transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50", className)}
          {...props}
        />
      )}
      {check && <div className='mr-2 cursor-default w-[4rem] flex justify-end'>{check}</div>}
      {kbd && <kbd className='text-xs cursor-default'>{kbd}</kbd>}
      {symbol && <div className="text-muted-foreground text-xs mr-1">{symbol}</div>}
    </div>
  );
}

export { Input, AutoSizingInput, DetailInput };
