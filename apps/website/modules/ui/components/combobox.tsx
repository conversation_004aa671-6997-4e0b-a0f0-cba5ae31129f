"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@ui/components/popover";
import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";

export interface ComboBoxItem {
  id: string;
  [key: string]: any;
}

interface ComboBoxProps<T extends ComboBoxItem> {
  items: T[];
  value?: string;
  onValueChange: (value: string) => void;
  onSearchChange?: (search: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  groupLabel?: string;
  disabled?: boolean;
  className?: string;
  buttonClassName?: string;
  itemClassName?: string;
  renderButton?: (selectedItem: T | null, placeholder: string) => React.ReactNode;
  renderItem: (item: T, isSelected: boolean) => React.ReactNode;
  getSearchableText: (item: T) => string;
  getDisplayText?: (item: T) => string;
  icon?: boolean;
}

export function ComboBox<T extends ComboBoxItem>({
  items,
  value,
  onValueChange,
  onSearchChange,
  placeholder = "Select item...",
  searchPlaceholder = "Search items...",
  emptyMessage = "No items found",
  groupLabel = "Items",
  disabled = false,
  className,
  buttonClassName,
  itemClassName,
  renderButton,
  renderItem,
  getSearchableText,
  getDisplayText,
  icon = false
}: ComboBoxProps<T>) {
  const [open, setOpen] = React.useState(false);
  const [buttonWidth, setButtonWidth] = React.useState<number>();
  const buttonRef = React.useRef<HTMLButtonElement>(null);

  const selectedItem = value 
    ? items.find((item) => item.id === value) ?? null
    : null;

  React.useEffect(() => {
    if (buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth);
    }
  }, [open]);

  const defaultRenderButton = (selectedItem: T | null, placeholder: string) => (
    <>
      {selectedItem ? (
        <span className="text-sm truncate">
          {getDisplayText ? getDisplayText(selectedItem) : getSearchableText(selectedItem)}
        </span>
      ) : (
        <span className="text-muted-foreground">{placeholder}</span>
      )}
      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
    </>
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={buttonRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between bg-sidebar hover:bg-muted/50 border-border",
            buttonClassName
          )}
          disabled={disabled}
        >
          {renderButton ? renderButton(selectedItem, placeholder) : defaultRenderButton(selectedItem, placeholder)}
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className={cn(
          "p-0 bg-sidebar border border-border rounded-xl shadow-lg",
          className
        )}
        align="start"
        style={{ width: buttonWidth ? `${buttonWidth}px` : undefined }}
      >
        <Command className="bg-transparent">
          <CommandInput 
            placeholder={searchPlaceholder} 
            className="!h-9"
            divClassName="!h-9 !border-none"
            onValueChange={onSearchChange}
          />
          <CommandList className="overflow-y-auto">
            <CommandEmpty>
              <div className="flex items-center justify-center gap-2 bg-transparent border border-dashed border-border rounded-lg m-2 px-3 py-4 text-left w-auto">
                <span className="text-sm text-muted-foreground">{emptyMessage}</span>
              </div>
            </CommandEmpty>
            <CommandGroup className="p-1">
              <div className="text-muted-foreground text-xs font-medium p-1 mb-1">
                {groupLabel}
              </div>
              {items.map((item) => (
                <CommandItem
                  key={item.id}
                  value={getSearchableText(item)}
                  onSelect={() => {
                    onValueChange(item.id === value ? "" : item.id);
                    setOpen(false);
                  }}
                  className={cn(
                    "flex items-center gap-2 border border-transparent rounded-lg m-0 px-3 py-2 text-left w-full cursor-pointer hover:bg-muted/50 hover:!border hover:!border-border",
                    "data-[selected=true]:bg-muted/50 data-[selected=true]:border data-[selected=true]:border-border",
                    itemClassName
                  )}
                >
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    {renderItem(item, value === item.id)}
                  </div>
                  
                  <IconSquareRoundedCheckFilled
                    className={cn(
                      "ml-auto h-4 w-4 text-blue-500",
                      value === item.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 