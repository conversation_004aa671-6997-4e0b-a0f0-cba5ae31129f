"use client";

import { CompanyAvatar } from "@shared/components/CompanyAvatar";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { IconCopy, IconX } from "@tabler/icons-react";
import { cn } from "@ui/lib";
import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type React from "react";
import { useEffect, useState } from "react";

export const badge = cva(
	["inline-block", "rounded-lg", "px-3", "py-1", "text-xs", "leading-tight"],
	{
		variants: {
			status: {
				success: ["bg-emerald-500/10", "text-emerald-500", "border border-emerald-500/50"],
				info: ["bg-primary/10", "text-primary"],
				warning: ["bg-amber-500/10", "text-amber-500"],
				error: ["bg-rose-500/10", "text-rose-500"],
				feature: ["bg-sky-500/10", "text-sky-500"],
				improvement: ["bg-amber-500/10", "text-amber-500"],
				fix: ["bg-rose-500/10", "text-rose-500"],
				release: ["bg-emerald-500/10", "text-emerald-500"],
			},
			variant: {
				views: "!py-0.5 !px-1.5",
			},
		},
		defaultVariants: {
			status: "info",
		},
	},
);

export type BadgeProps = React.HtmlHTMLAttributes<HTMLDivElement> &
	VariantProps<typeof badge>;

export const Badge = ({
	children,
	className,
	status,
	variant,
	...props
}: BadgeProps) => (
	<span className={cn(badge({ status, variant }), className)} {...props}>
		{children}
	</span>
);

Badge.displayName = "Badge";

interface NumberBadgeProps {
	number: React.ReactNode;
	className?: string;
	color?:
		| "indigo"
		| "zinc"
		| "blue"
		| "red"
		| "green"
		| "yellow"
		| "purple"
		| "sky";
	variant?: "views";
}

export const NumberBadge = ({
	number,
	className,
	color = "blue",
	variant,
}: NumberBadgeProps) => {
	const colorClasses = {
		indigo: "bg-indigo-200 dark:bg-indigo-700 border-indigo-300 dark:border-indigo-600 text-indigo-900 dark:text-indigo-100",
		zinc: "bg-zinc-200 dark:bg-zinc-700 border-zinc-300 dark:border-zinc-600 text-sidebar dark:text-zinc-100",
		blue: "bg-blue-200 dark:bg-blue-700 border-blue-300 dark:border-blue-600 text-blue-900 dark:text-blue-100",
		red: "bg-red-200 dark:bg-red-700 border-red-300 dark:border-red-600 text-red-900 dark:text-red-100",
		green: "bg-green-200 dark:bg-green-700 border-green-300 dark:border-green-600 text-green-900 dark:text-green-100",
		yellow: "bg-yellow-200 dark:bg-yellow-700 border-yellow-300 dark:border-yellow-600 text-yellow-900 dark:text-yellow-100",
		purple: "bg-purple-200 dark:bg-purple-700 border-purple-300 dark:border-purple-600 text-purple-900 dark:text-purple-100",
		sky: "bg-sky-200 dark:bg-sky-700 border-sky-300 dark:border-sky-600 text-sky-900 dark:text-sky-100",
	};

	return (
		<div
			className={cn(
				"flex items-center justify-center",
				"w-fit px-1 h-4 rounded-sm",
				"border",
				"text-xs font-medium font-mono",
				colorClasses[color],
				className,
			)}
		>
			{number}
		</div>
	);
};

NumberBadge.displayName = "NumberBadge";

interface BadgeEmailProps {
	email: string;
	onRemove?: () => void;
	className?: string;
}

export const BadgeEmail = ({ email, onRemove, className }: BadgeEmailProps) => {
	const [isHovered, setIsHovered] = useState(false);
	const [showCopyIcon, setShowCopyIcon] = useState(false);
	const [isCopied, setIsCopied] = useState(false);

	useEffect(() => {
		let timeout: NodeJS.Timeout;
		if (isHovered) {
			timeout = setTimeout(() => {
				setShowCopyIcon(true);
			}, 1000);
		} else {
			setShowCopyIcon(false);
		}
		return () => clearTimeout(timeout);
	}, [isHovered]);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(email);
			setIsCopied(true);
			setTimeout(() => setIsCopied(false), 2000);
		} catch (err) {
			console.error("Failed to copy email:", err);
		}
	};

	return (
		<div
			className={cn(
				"inline-flex items-center gap-1 px-3 py-1.5 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg text-sm font-medium transition-all duration-200",
				"hover:bg-blue-500/30 hover:border-blue-500/50",
				"cursor-pointer select-none",
				className,
			)}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			<span
				className={cn(
					"transition-all duration-300 ease-in-out",
					showCopyIcon ? "max-w-[120px] truncate" : "max-w-none",
				)}
			>
				{email}
			</span>

			<div
				className={cn(
					"flex items-center gap-1 transition-all duration-300 ease-in-out",
					showCopyIcon
						? "translate-x-0 opacity-100 w-auto"
						: "translate-x-4 opacity-0 w-0",
				)}
			>
				<button
					onClick={handleCopy}
					className={cn(
						"p-0.5 hover:bg-blue-500/20 rounded transition-colors",
						isCopied && "text-green-400",
					)}
					title={isCopied ? "Copied!" : "Copy email"}
				>
					<IconCopy className="h-3 w-3" />
				</button>

				{onRemove && (
					<button
						onClick={onRemove}
						className="p-0.5 hover:bg-red-500/20 hover:text-red-400 rounded transition-colors"
						title="Remove email"
					>
						<svg
							className="h-3 w-3"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				)}
			</div>
		</div>
	);
};

BadgeEmail.displayName = "BadgeEmail";

const ContactBadgeVariants = cva(
	"cursor-pointer inline-flex items-center bg-transparent text-muted-foreground hover:bg-muted/50 !w-fit",
	{
		variants: {
			variant: {
				default: "rounded-full gap-x-1 py-1 px-1",
				pill: "rounded-full gap-x-1 py-1 pl-1 pr-1",
				avatar: "rounded-full gap-2 px-1 py-1",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	},
);

interface ContactBadgeProps
	extends React.HTMLAttributes<HTMLSpanElement>,
		VariantProps<typeof ContactBadgeVariants> {
	label?: string;
	value?: string;
	avatar?: string;
	children?: React.ReactNode;
	onRemove?: () => void;
	size?: "xs" | "sm" | "md" | "lg";
}

export function ContactBadge({
	className,
	variant,
	label,
	value,
	avatar,
	children,
	onRemove,
	size = "sm",
	...props
}: ContactBadgeProps) {
	if (variant === "avatar") {
		return (
			<span
				className={cn(ContactBadgeVariants({ variant }), className)}
				{...props}
			>
				<ContactAvatar
					name={value || ""}
					avatarUrl={avatar}
					className={cn(
						"h-5 w-5",
						size === "xs" ? "h-4 w-4 !text-xs" :
						size === "sm" ? "h-5 w-5 !text-sm" :
						size === "lg" ? "h-6 w-6 !text-lg" :
						"h-5 w-5 !text-sm"
					)}
				/>
				{children}
				{onRemove && (
					<button
						type="button"
						onClick={onRemove}
						className="flex size-5 items-center justify-center rounded-full text-muted-foreground hover:bg-muted hover:text-foreground"
						aria-label="Remove"
					>
						<IconX className="size-4 shrink-0" aria-hidden={true} />
					</button>
				)}
			</span>
		);
	}

	return (
		<span
			className={cn(ContactBadgeVariants({ variant }), className)}
			{...props}
		>
			{label && (
				<>
					<span className="h-4 bg-border" />
					<span className="font-medium text-foreground">{label}</span>
				</>
			)}
			<ContactAvatar
				name={value || ""}
				avatarUrl={avatar}
				className={cn(
					size === "xs" ? "h-4 w-4 !text-xs" :
					size === "sm" ? "h-5 w-5 !text-sm" :
					size === "lg" ? "h-6 w-6 !text-lg" :
					"h-5 w-5 !text-sm"
				)}
			/>
			<span className="font-medium text-foreground">{value}</span>
			{onRemove && (
				<button
					type="button"
					onClick={onRemove}
					className={cn(
						"flex size-5 items-center justify-center text-muted-foreground hover:bg-muted hover:text-foreground",
						variant === "pill" ? "rounded-full" : "rounded",
					)}
					aria-label="Remove"
				>
					<IconX className="size-4 shrink-0" aria-hidden={true} />
				</button>
			)}
		</span>
	);
}

ContactBadge.displayName = "ContactBadge";

const CompanyBadgeVariants = cva(
	"cursor-pointer inline-flex items-center bg-transparent text-muted-foreground hover:bg-muted/50 !w-fit",
	{
		variants: {
			variant: {
				default: "rounded-full gap-x-1 py-1 px-1",
				pill: "rounded-full gap-x-1 py-1 pl-1 pr-1",
				avatar: "rounded-full gap-2 px-1 py-1",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	},
);

interface CompanyBadgeProps
	extends React.HTMLAttributes<HTMLSpanElement>,
		VariantProps<typeof CompanyBadgeVariants> {
	label?: string;
	value?: string;
	logo?: string;
	children?: React.ReactNode;
	onRemove?: () => void;
	size?: "xs" | "sm" | "md" | "lg";
}

export function CompanyBadge({
	className,
	variant,
	label,
	value,
	logo,
	children,
	onRemove,
	size = "sm",
	...props
}: CompanyBadgeProps) {
	if (variant === "avatar") {
		return (
			<span
				className={cn(CompanyBadgeVariants({ variant }), className)}
				{...props}
			>
				<CompanyAvatar
					name={value || ""}
					logoUrl={logo}
					className="h-4 w-4"
				/>
				{children}
				{onRemove && (
					<button
						type="button"
						onClick={onRemove}
						className="flex size-5 items-center justify-center rounded-full text-muted-foreground hover:bg-muted hover:text-foreground"
						aria-label="Remove"
					>
						<IconX className="size-4 shrink-0" aria-hidden={true} />
					</button>
				)}
			</span>
		);
	}

	return (
		<span
			className={cn(CompanyBadgeVariants({ variant }), className)}
			{...props}
		>
			{label && (
				<>
					<span className="h-4 bg-border" />
					<span className="font-medium text-foreground">{label}</span>
				</>
			)}
			<CompanyAvatar
				name={value || ""}
				logoUrl={logo}
				className={cn(
					size === "xs" ? "h-4 w-4 !text-xs" :
					size === "sm" ? "h-5 w-5 !text-sm" :
					size === "lg" ? "h-6 w-6 !text-lg" :
					"h-5 w-5 !text-sm"
				)}
			/>
			<span className="font-medium text-foreground">{value}</span>
			{onRemove && (
				<button
					type="button"
					onClick={onRemove}
					className={cn(
						"flex size-5 items-center justify-center text-muted-foreground hover:bg-muted hover:text-foreground",
						variant === "pill" ? "rounded-full" : "rounded",
					)}
					aria-label="Remove"
				>
					<IconX className="size-4 shrink-0" aria-hidden={true} />
				</button>
			)}
		</span>
	);
}

CompanyBadge.displayName = "CompanyBadge";

const PropertyBadgeVariants = cva(
	"cursor-pointer inline-flex items-center bg-transparent text-muted-foreground hover:bg-muted/50 !w-fit",
	{
		variants: {
			variant: {
				default: "rounded-full gap-x-1 py-1 px-1",
				pill: "rounded-full gap-x-1 py-1 pl-1 pr-1",
				avatar: "rounded-full gap-2 px-1 py-1",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	},
);

interface PropertyBadgeProps
	extends React.HTMLAttributes<HTMLSpanElement>,
		VariantProps<typeof PropertyBadgeVariants> {
	label?: string;
	value?: string;
	avatar?: string;
	children?: React.ReactNode;
	onRemove?: () => void;
	size?: "xs" | "sm" | "md" | "lg";
}

export function PropertyBadge({
	className,
	variant,
	label,
	value,
	avatar,
	children,
	onRemove,
	size = "sm",
	...props
}: PropertyBadgeProps) {
	if (variant === "avatar") {
		return (
			<span
				className={cn(PropertyBadgeVariants({ variant }), className)}
				{...props}
			>
				<PropertyAvatar
					name={value || ""}
					avatarUrl={avatar}
					className={cn(
						size === "xs" ? "h-4 w-4 !text-xs" :
						size === "sm" ? "h-5 w-5 !text-sm" :
						size === "lg" ? "h-6 w-6 !text-lg" :
						"h-5 w-5 !text-sm"
					)}
				/>
				{children}
				{onRemove && (
					<button
						type="button"
						onClick={onRemove}
						className="flex size-5 items-center justify-center rounded-full text-muted-foreground hover:bg-muted hover:text-foreground"
						aria-label="Remove"
					>
						<IconX className="size-4 shrink-0" aria-hidden={true} />
					</button>
				)}
			</span>
		);
	}

	return (
		<span
			className={cn(PropertyBadgeVariants({ variant }), className)}
			{...props}
		>
			{label && (
				<>
					<span className="h-4 bg-border" />
					<span className="font-medium text-foreground">{label}</span>
				</>
			)}
			<PropertyAvatar
				name={value || ""}
				avatarUrl={avatar}
				className={cn(
					size === "xs" ? "h-4 w-4 !text-xs" :
					size === "sm" ? "h-5 w-5 !text-sm" :
					size === "lg" ? "h-6 w-6 !text-lg" :
					"h-5 w-5 !text-sm"
				)}
			/>
			<span className="font-medium text-foreground">{value}</span>
			{onRemove && (
				<button
					type="button"
					onClick={onRemove}
					className={cn(
						"flex size-5 items-center justify-center text-muted-foreground hover:bg-muted hover:text-foreground",
						variant === "pill" ? "rounded-full" : "rounded",
					)}
					aria-label="Remove"
				>
					<IconX className="size-4 shrink-0" aria-hidden={true} />
				</button>
			)}
		</span>
	);
}

PropertyBadge.displayName = "PropertyBadge";