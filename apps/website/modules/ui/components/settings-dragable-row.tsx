"use client";

import { flexRender } from "@tanstack/react-table"
import { CSS } from "@dnd-kit/utilities"
import { useSortable } from "@dnd-kit/sortable"
import { Row } from "@tanstack/react-table"
import { TableRow, TableCell } from "@ui/components/table"
import { cn } from "@ui/lib";

export function SettingsDraggableRow({ row, onClick }: { row: Row<any>, onClick?: (event: any, row: any) => void }) {
  const { transform, transition, setNodeRef, isDragging } = useSortable({
    id: row.original.id,
  })

  return (
    <TableRow
      data-state={row.getIsSelected() && "selected"}
      data-dragging={isDragging}
      ref={setNodeRef}
      className={cn("relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80", onClick && "cursor-pointer")}
      onClick={(event: any) => onClick?.(event, row.original)}
      style={{
        transform: CSS.Transform.toString(transform),
        transition: transition,
      }}
    >
      {row.getVisibleCells().map((cell) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  )
}