"use client";

import React, { useState } from "react";
import { CopyableValue } from "./CopyableValue";
import { ComposeEmailModal } from "./ComposeEmailModal";

interface EmailCellProps {
	email: string;
	recipientName?: string;
	subject?: string;
	avatarUrl?: string;
	type?: string;
	allEmails?: string[];
}

export function EmailCell({ 
	email, 
	recipientName, 
	subject, 
	avatarUrl, 
	type,
	allEmails 
}: EmailCellProps) {
	const [composeModalOpen, setComposeModalOpen] = useState(false);

	const handleEmailClick = (emailAddress: string) => {
		setComposeModalOpen(true);
	};

	return (
		<>
			<CopyableValue 
				value={email} 
				type="email" 
				onEmailClick={handleEmailClick}
			/>
			<ComposeEmailModal
				open={composeModalOpen}
				onOpenChange={setComposeModalOpen}
				toEmail={email}
				toName={recipientName}
				toAvatarUrl={avatarUrl}
				toType={type}
				toAllEmails={allEmails}
				subject={subject}
			/>
		</>
	);
} 