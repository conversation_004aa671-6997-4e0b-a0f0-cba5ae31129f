"use client";

import { cn } from "@ui/lib";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export function Logo({
	withLabel = true,
	className,
}: {
	className?: string;
	withLabel?: boolean;
}) {
	const { theme, resolvedTheme } = useTheme();
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	const isDark = mounted && resolvedTheme === "dark";

	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>
			<svg
				className="size-8"
				viewBox="0 0 2048 2048"
				width="40"
				height="40"
				xmlns="http://www.w3.org/2000/svg"
			>
				<title>relio</title>
				{isDark ? (
					<>
						<path
							transform="translate(1026,25)"
							d="m0 0 42 2 44 3 41 5 57 9 34 7 47 12 36 11 41 14 35 14 30 13 35 17 27 14 23 13 17 10 22 14 24 16 20 14 21 16 16 12 13 11 11 9 14 12 26 24 10 10 8 7 16 17 8 8 7 8 13 14 9 11 13 15 9 11 10 13 16 21 26 38 12 19 13 21 16 28 17 32 16 33 18 42 11 29 15 43 14 49 10 43 7 36 6 38 5 46 2 26 1 33 1 24v14l-1 18-1 27-1 15-2 25-5 38-8 50-6 31-13 52-9 30-16 47-13 33-9 21-13 28-12 25-18 34-17 28-12 20-10 15-13 19-13 18-12 16-11 14-13 16-11 13-9 10-7 8-28 30-24 24-8 7-7 7-8 7-15 13-13 11-10 8-13 10-14 11-19 14-28 19-15 10-28 17-21 12-24 13-27 14-28 13-33 14-29 11-28 10-42 13-38 10-36 8-38 7-43 6-43 4-9 1-17 1h-12l-18 1h-23l-62-2-28-3-6-2-1-3 5-30 18-115 17-108 9-57 13-84 13-83 30-190 4-25 8-5 31-13 35-15 48-20 37-16 41-17 30-13 36-15 42-18 33-14 29-12 42-18 36-15 35-15 43-18 18-8h2l3-22 17-109 9-56 15-97 13-82 12-76 8-52 1-9-21 8-28 12-111 47-36 15-35 15-26 11-31 13-42 18-36 15-47 20-85 36-36 15-30 13-36 15-40 17-99 42-36 15-47 20-99 42-36 15-54 23-2 1-3 14-11 71-11 69-13 84-10 64-9 56-12 77-17 109-11 69-13 84-19 121-6 38-2 5-12-6-17-11-16-11-11-8-21-16-12-9-22-18-13-11-10-9-8-7-12-11-15-14-19-19-7-8-10-10-3-6 6-40 9-56 9-58 13-83 11-70 13-83 9-58 10-62 8-53 15-94 10-65 12-76 8-50 2-4 32-14 29-12 35-15 43-18 42-18 41-17 30-13 118-50 41-17 30-13 36-15 28-12 31-13 80-34 36-15 42-18 29-12 35-15 18-8v-4l-34-5-37-6-33-5-30-5-21-1-20 8-28 12-163 69-78 33-43 18-35 15-26 11-161 68-38 16-35 15-36 15-28 12-8 4-6 35-10 63-11 72-12 75-30 192-57 364-3 18h-3l-4-4-11-16-10-15-11-17-16-27-13-23-17-33-16-33-13-30-16-42-12-35-12-41-11-44-7-35-8-51-4-37-3-43-1-25v-44l1-31 1-14 2-27 5-38 9-57 7-34 11-43 11-37 13-38 15-38 10-23 12-27 8-15 9-19 13-23 15-26 12-19 11-17 8-12 12-17 14-19 8-10 14-18 12-14 9-11 11-12 7-8 9-10 16-16 4-5 8-7 20-20 8-7 10-9 8-7 13-11 11-9 15-12 38-28 36-24 16-10 27-16 18-10 34-18 27-13 30-13 28-11 24-9 49-16 53-14 49-10 36-6 30-4 47-4 39-2z"
							fill="currentColor"
						/>
						<path
							transform="translate(1206,495)"
							d="m0 0 8 1-4 4-4 1z"
							fill="#FEFEFE"
						/>
					</>
				) : (
					<path
						transform="translate(1026,25)"
						d="m0 0 59 3 48 4 66 10 28 5 33 8 24 6 35 10 35 12 32 12 19 8 26 11 41 20 23 12 23 13 25 15 23 15 15 10 17 12 32 24 16 13 13 11 11 9 12 11 8 7 7 7 8 7 33 33 7 8 16 17 9 11 13 15 14 17 12 16 14 19 14 20 14 21 15 24 17 29 13 24 14 27 15 32 15 36 11 29 15 44 10 35 9 36 8 38 7 43 5 40 3 38 1 19 1 55-1 37-4 53-7 49-8 48-6 28-10 39-8 28-14 41-10 27-15 36-8 18-16 33-14 27-14 24-10 17-15 24-13 19-11 16-14 19-10 13-11 14-11 13-9 11-11 12-7 8-12 13-21 21-4 5-8 7-16 16-8 7-10 9-11 9-9 8-28 22-18 14-20 14-39 26-28 17-23 13-34 18-40 19-38 16-27 10-23 8-31 10-36 10-47 11-38 7-42 6-51 5-39 2h-60l-53-3-15-2-5-2 1-9 12-77 10-64 10-63 10-64 9-58 10-64 11-70 8-50 9-58 12-77 6-36 2-7 8-4 36-15 28-12 33-14 24-10 23-10 36-15 26-11 35-15 41-17 30-13 38-16 21-9 26-11 41-17 63-27 30-12 4-2 3-12 12-76 15-96 13-83 11-70 13-83 12-76v-6l-16 6-31 13-35 15-36 15-42 18-41 17-30 13-38 16-24 10-30 13-36 15-26 11-30 13-41 17-28 12-111 47-36 15-42 18-36 15-37 16-29 12-26 11-35 15-36 15-35 15-48 20-30 13-26 11h-2l-1 10-12 77-13 83-11 70-13 83-11 70-9 58-15 95-26 166-7 45-12 76-5 27-4 1-24-15-16-11-18-13-21-16-14-11-11-9-13-11-14-12-15-14-8-7-17-17-8-7-9-10-12-12-5-6 1-13 17-109 13-82 15-97 11-69 12-77 20-127 15-96 13-83 15-95 3-7 12-6 76-32 35-15 22-9 37-16 46-19 51-22 29-12 28-12 40-17 29-12 37-16 39-16 30-13 28-12 29-12 28-12 38-16 24-10 42-18 41-17 30-13 6-2v-4l-18-2-88-14-33-5-16-1-20 8-37 16-36 15-42 18-41 17-30 13-43 18-54 23-31 13-28 12-45 19-36 15-42 18-36 15-30 13-41 17-42 18-41 17-25 11-9 56-12 77-9 58-14 89-10 64-17 108-9 57-11 71-18 115-18 114-2 10h-4l-10-13-25-38-16-27-14-24-8-16-9-17-13-28-13-29-14-36-12-34-7-21-11-39-6-24-8-36-6-34-5-36-3-25-3-36v-9l-1-8v-80l2-29v-9l5-46 11-70 6-29 13-51 12-39 12-35 8-20 11-27 10-23 8-16 9-19 10-19 10-18 9-15 6-11 12-19 22-33 12-17 9-12 10-13 12-15 9-11 12-14 7-8 9-10 7-8 15-16 29-29 5-4 5-5 14-13 8-7 10-9 9-7 13-11 19-14 18-14 19-13 14-10 16-10 14-9 22-13 21-12 25-13 19-10 30-14 29-12 36-14 36-12 29-9 27-7 28-7 34-7 43-7 22-3 47-4 41-2z"
						fill="currentColor"
					/>
				)}
			</svg>
			{withLabel && (
				<span className="ml-3 hidden text-2xl tracking-tighter md:block">
					relio
				</span>
			)}
		</span>
	);
}
