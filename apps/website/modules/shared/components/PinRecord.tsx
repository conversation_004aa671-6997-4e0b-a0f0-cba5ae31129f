import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useCreatePin, useDeletePin, useIsPinned } from "@app/pins/lib/api";
import { ObjectType } from "@repo/database";
import { IconPin, IconPinnedOff } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import React from "react";

interface PinRecordProps {
	objectId: string;
	objectType: ObjectType;
	name: string;
}

function PinRecord({ objectId, objectType, name }: PinRecordProps) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;

	const isPinned = useIsPinned(organizationId, objectType, objectId);
	const createPin = useCreatePin(organizationId);
	const deletePin = useDeletePin(organizationId);

	const handleTogglePin = () => {
		if (!organizationId) return;
		if (isPinned) {
			deletePin.mutate({ objectId, objectType, organizationId });
		} else {
			createPin.mutate({ objectId, objectType, name, organizationId });
		}
	};

	const isLoading = createPin.isPending || deletePin.isPending;

	return (
		<div>
			<Button
				disabled={isLoading}
				variant={isPinned ? "ghost" : "ghost"}
				onClick={handleTogglePin}
				size="icon"
				className="text-muted-foreground hover:text-primary hover:bg-muted/80 h-8 w-8"
				tooltip={{
					content: isPinned ? "Unpin from tabs" : "Add to tabs",
				}}
			>
				{isPinned ? <IconPinnedOff className="size-4" /> : <IconPin className="size-4" />}
			</Button>
		</div>
	);
}

export default PinRecord;
