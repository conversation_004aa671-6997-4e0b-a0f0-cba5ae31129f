"use client";

import {
	IconBrandFacebook,
	IconBrandX,
	IconCode,
	IconMail,
	IconShare,
	IconWorld,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Switch } from "@ui/components/switch";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Check, Copy } from "lucide-react";
import { useId, useRef, useState } from "react";

interface ShareNoteProps {
	noteId: string;
	isPublished: boolean;
	onTogglePublished: () => void;
	className?: string;
}

export function ShareNote({
	noteId,
	isPublished,
	onTogglePublished,
	className,
}: ShareNoteProps) {
	const id = useId();
	const [copied, setCopied] = useState<boolean>(false);
	const inputRef = useRef<HTMLInputElement>(null);

	const shareUrl =
		typeof window !== "undefined"
			? `${window.location.origin}/app/note/${noteId}`
			: `/app/note/${noteId}`;

	const handleCopy = () => {
		if (inputRef.current) {
			navigator.clipboard.writeText(inputRef.current.value);
			setCopied(true);
			setTimeout(() => setCopied(false), 1500);
		}
	};

	const handleTwitterShare = () => {
		const url = `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent("Check out this note!")}`;
		window.open(url, "_blank");
	};

	const handleFacebookShare = () => {
		const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
		window.open(url, "_blank");
	};

	const handleEmailShare = () => {
		const subject = "Check out this note!";
		const body = `I wanted to share this note with you: ${shareUrl}`;
		const url = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
		window.open(url, "_blank");
	};

	const handleEmbedShare = () => {
		const embedCode = `<iframe src="${shareUrl}" width="100%" height="600" frameborder="0"></iframe>`;
		navigator.clipboard.writeText(embedCode);
		// You could add a toast notification here
	};

	return (
		<div className={cn("flex flex-col gap-4", className)}>
			<Popover>
				<PopoverTrigger asChild>
					<Button
						tooltip={isPublished
							? "Note is currently published and visible to everyone"
							: "Share note"}
						variant="relio"
						size="sm"
						className="h-8 gap-2 group"
					>
						{!isPublished ? (
							<IconShare className="size-4 text-muted-foreground group-hover:text-primary" />
						) : (
							<IconWorld className="size-4 text-blue-500" />
						)}
						<span>{isPublished ? "Published" : "Share note"}</span>
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-72">
					<div className="flex flex-col gap-3">
						{/* Public/Private Toggle */}
						<div className="flex items-center justify-between">
							<div className="flex flex-col gap-1">
								<span className="text-sm font-medium">
									Make public
								</span>
								<span className="text-xs text-muted-foreground">
									Anyone with the link can view this note
								</span>
							</div>
							<Switch
								checked={isPublished}
								onCheckedChange={onTogglePublished}
							/>
						</div>

						{isPublished && (
							<>
								{/* Share buttons */}
								<div className="text-center">
									<div className="text-sm font-medium mb-2">
										Share note
									</div>
									<div className="flex flex-wrap justify-center gap-2">
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														size="icon"
														variant="outline"
														onClick={
															handleEmbedShare
														}
													>
														<IconCode size={16} />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Copy embed code
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>

										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														size="icon"
														variant="outline"
														onClick={
															handleTwitterShare
														}
													>
														<IconBrandX size={16} />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Share on X (Twitter)
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>

										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														size="icon"
														variant="outline"
														onClick={
															handleFacebookShare
														}
													>
														<IconBrandFacebook
															size={16}
														/>
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Share on Facebook
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>

										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														size="icon"
														variant="outline"
														onClick={
															handleEmailShare
														}
													>
														<IconMail size={16} />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Share via email
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</div>
								</div>

								{/* Copy link input */}
								<div className="space-y-2">
									<div className="relative">
										<Input
											ref={inputRef}
											id={id}
											className="pe-9"
											type="text"
											value={shareUrl}
											aria-label="Share link"
											readOnly
										/>
										<TooltipProvider delayDuration={0}>
											<Tooltip>
												<TooltipTrigger asChild>
													<button
														onClick={handleCopy}
														className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-lg border border-transparent text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus-visible:text-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed"
														aria-label={
															copied
																? "Copied"
																: "Copy to clipboard"
														}
														disabled={copied}
													>
														<div
															className={cn(
																"transition-all",
																copied
																	? "scale-100 opacity-100"
																	: "scale-0 opacity-0",
															)}
														>
															<Check
																className="stroke-emerald-500"
																size={16}
																strokeWidth={2}
																aria-hidden="true"
															/>
														</div>
														<div
															className={cn(
																"absolute transition-all",
																copied
																	? "scale-0 opacity-0"
																	: "scale-100 opacity-100",
															)}
														>
															<Copy
																size={16}
																strokeWidth={2}
																aria-hidden="true"
															/>
														</div>
													</button>
												</TooltipTrigger>
												<TooltipContent className="px-2 py-1 text-xs">
													Copy to clipboard
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</div>
								</div>
							</>
						)}
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
}
