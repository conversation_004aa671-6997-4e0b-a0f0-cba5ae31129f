import { ToggleStar } from "@app/favorites/components/ToggleStar";
import { NoteMoreDropdown } from "../../../app/(authenticated)/app/(organizations)/[organizationSlug]/notes/NoteMoreDropdown";
import { RecordMoreDropdown } from "./RecordMoreDropdown";
import { useToggleNotePublished } from "@app/notes/lib/api";
import { useDebounce } from "@app/shared/hooks/useDebounce";
import type { ActiveOrganization } from "@repo/auth";
import { ObjectType } from "@repo/database";
import AddToTabs from "@shared/components/PinRecord";
import { ShareNote } from "@shared/components/ShareNote";
import { restoreCountDown } from "@shared/lib/utils";
import { IconX, IconArrowUp, IconArrowDown } from "@tabler/icons-react";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertD<PERSON>ogHeader,
	AlertDialogT<PERSON>le,
	AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useRef, useState } from "react";
import PinRecord from "@shared/components/PinRecord";
import { RecordNavigation } from "@app/shared/components/RecordNavigation";
import { singularToPluralMap } from "@repo/database";
import { DropDrawer, DropDrawerContent, DropDrawerItem, DropDrawerSub, DropDrawerSubContent, DropDrawerSubTrigger, DropDrawerTrigger } from "@ui/components/dropdrawer";

interface PageHeaderProps {
	activeOrganization: ActiveOrganization;
	data: any;
	isFavorite: boolean;
	objectType: string;
	onTitleUpdate?: (
		id: string,
		titleOrFields: string | { firstName: string; lastName: string },
		field: string
	) => Promise<void> | void;
	onNavigate?: (direction: "prev" | "next") => void;
	hasPrevious?: boolean;
	hasNext?: boolean;
	onConfigurePage?: () => void;
}

const PageHeader = ({
	activeOrganization,
	data,
	isFavorite,
	objectType,
	onTitleUpdate,
	onNavigate,
	hasPrevious = false,
	hasNext = false,
	onConfigurePage,
}: PageHeaderProps) => {
	const [openDelete, setOpenDelete] = useState(false);
	const router = useRouter();
	const pathname = usePathname();
	const searchParams = useSearchParams();
	
	const getDisplayTitle = useCallback((data: any) => {
		if (!data) return "Untitled";
		
		switch (objectType) {
			case "contact":
				// For contacts, prefer constructed name from firstName/lastName, then name, then title
				if (data.firstName || data.lastName) {
					return `${data.firstName || ""} ${data.lastName || ""}`.trim() || "Unnamed Contact";
				}
				return data.name || data.title || "Unnamed Contact";
			
			case "company":
			case "property":
				// Companies and properties typically use "name"
				return data.name || data.title || "Untitled";
			
			case "note":
			case "custom_object":
				// Notes and custom objects typically use "title"
				return data.title || data.name || "Untitled";
			
			default:
				// Generic fallback - try title first, then name
				return data.title || data.name || "Untitled";
		}
	}, [objectType]);

	const getUpdateField = useCallback(() => {
		switch (objectType) {
			case "company":
			case "property":
				return "name";
			case "note":
			case "custom_object":
				return "title";
			case "contact":
				// For contacts, we might want to update the name field or handle firstName/lastName separately
				return "name";
			default:
				return "title";
		}
	}, [objectType]);

	const [title, setTitle] = useState(getDisplayTitle(data));
	const [isEditing, setIsEditing] = useState(false);
	const [isTitleDirty, setIsTitleDirty] = useState(false);
	const inputRef = useRef<HTMLInputElement>(null);
	const lastSavedTitleRef = useRef(getDisplayTitle(data));
	const contactEditRef = useRef<HTMLDivElement>(null);

	const debouncedTitle = useDebounce(title, 1000);
	const toggleNotePublished = useToggleNotePublished();

	const [firstName, setFirstName] = useState(data?.firstName || "");
	const [lastName, setLastName] = useState(data?.lastName || "");

	useEffect(() => {
		const newTitle = getDisplayTitle(data);
		if (newTitle !== lastSavedTitleRef.current && !isTitleDirty) {
			setTitle(newTitle);
			lastSavedTitleRef.current = newTitle;
		}
	}, [data, isTitleDirty, getDisplayTitle]);

	useEffect(() => {
		if (objectType === "contact") {
			setFirstName(data?.firstName || "");
			setLastName(data?.lastName || "");
		}
	}, [data, isTitleDirty, getDisplayTitle, objectType]);

	const handleTitleChange = useCallback((newTitle: string) => {
		setTitle(newTitle);
		setIsTitleDirty(true);
	}, []);

	const handleTitleBlur = useCallback(() => {
		setIsEditing(false);
		setIsTitleDirty(false);

		if (
			data?.id &&
			title.trim() !== "" &&
			title !== lastSavedTitleRef.current &&
			onTitleUpdate
		) {
			lastSavedTitleRef.current = title.trim();
			// Pass the field name that should be updated
			const fieldToUpdate = getUpdateField();
			onTitleUpdate(data.id, title.trim(), fieldToUpdate);
		}
	}, [title, data?.id, onTitleUpdate, getUpdateField]);

	const handleContactBlur = useCallback(() => {
		setIsEditing(false);
		setIsTitleDirty(false);
		if (
			data?.id &&
			(onTitleUpdate &&
				(firstName !== data.firstName || lastName !== data.lastName) &&
				(firstName.trim() !== "" || lastName.trim() !== ""))
		) {
			onTitleUpdate(data.id, { firstName: firstName.trim(), lastName: lastName.trim() }, "name");
		}
	}, [firstName, lastName, data, onTitleUpdate]);

	const handleClickOutside = useCallback((event: MouseEvent) => {
		if (
			contactEditRef.current &&
			!contactEditRef.current.contains(event.target as Node)
		) {
			handleContactBlur();
		}
	}, [handleContactBlur]);

	useEffect(() => {
		if (isEditing && objectType === "contact") {
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}
	}, [isEditing, objectType, handleClickOutside]);

	useEffect(() => {
		if (
			data?.id &&
			isTitleDirty &&
			debouncedTitle !== undefined &&
			debouncedTitle !== lastSavedTitleRef.current &&
			!isEditing &&
			debouncedTitle.trim() !== "" &&
			onTitleUpdate
		) {
			lastSavedTitleRef.current = debouncedTitle.trim();
			const fieldToUpdate = getUpdateField();
			onTitleUpdate(data.id, debouncedTitle.trim(), fieldToUpdate);
			setIsTitleDirty(false);
		}
	}, [debouncedTitle, isEditing, isTitleDirty, data?.id, onTitleUpdate, getUpdateField]);

	useEffect(() => {
		if (isEditing && inputRef.current) {
			inputRef.current.focus();
			inputRef.current.select();
		}
	}, [isEditing]);

	const handleClose = () => {
		router.push(`/app/${activeOrganization?.slug}/${singularToPluralMap[objectType as keyof typeof singularToPluralMap]}`);
	};

	if (!data) {
		return (
			<div className="flex items-center justify-between p-4 w-full animate-pulse">
				<div className="flex items-center grid-rows-2 gap-2">
					<div className="w-8 h-8 bg-zinc-700 rounded-full" />
					<div className="w-7 h-7 bg-zinc-700 rounded-full" />
					<div className="w-40 h-6 bg-zinc-700 rounded-lg" />
					<div className="w-6 h-6 bg-zinc-700 rounded-lg" />
				</div>
				<div className="flex items-center space-x-2 mr-2">
					<div className="w-24 h-5 bg-zinc-700 rounded-lg" />
					<div className="w-5 h-5 bg-zinc-700 rounded-lg" />
					<div className="w-5 h-5 bg-zinc-700 rounded-lg" />
					<div className="w-5 h-5 bg-zinc-700 rounded-lg" />
					<div className="w-2 h-5 bg-zinc-700 rounded-lg" />
				</div>
			</div>
		);
	}

	const viewName = searchParams.get("viewName");

	return (
		<div
			className={cn(
				"flex items-center justify-between p-4 w-full border-b border-border",
			)}
		>
			<div className="flex items-center grid-rows-2 gap-1">
				<Button
					variant="ghost"
					size="icon"
					className="text-muted-foreground hover:text-primary hover:bg-muted/80 h-8 w-8"
					aria-label="Close"
					tabIndex={2}
					onClick={handleClose}
				>
					<IconX className="size-4" />
				</Button>

				{isEditing && objectType === "contact" ? (
					<div
						ref={contactEditRef}
						className="flex gap-2"
					>
						<Input
							ref={inputRef}
							value={firstName}
							onChange={e => { setFirstName(e.target.value); setIsTitleDirty(true); }}
							size={Math.max(firstName.length, 3)}
							onKeyDown={e => {
								if (e.key === "Enter") handleContactBlur();
								else if (e.key === "Escape") {
									setFirstName(data.firstName || "");
									setLastName(data.lastName || "");
									setIsEditing(false);
									setIsTitleDirty(false);
								}
							}}
							placeholder="First"
							className="!text-xl font-semibold text-primary"
						/>
						<Input
							value={lastName}
							onChange={e => { setLastName(e.target.value); setIsTitleDirty(true); }}
							size={Math.max(lastName.length, 3)}
							onKeyDown={e => {
								if (e.key === "Enter") handleContactBlur();
								else if (e.key === "Escape") {
									setFirstName(data.firstName || "");
									setLastName(data.lastName || "");
									setIsEditing(false);
									setIsTitleDirty(false);
								}
							}}
							placeholder="Last"
							className="!text-xl font-semibold text-primary"
						/>
					</div>
				) : isEditing ? (
					<Input
						ref={inputRef}
						value={title}
						onChange={(e) => handleTitleChange(e.target.value)}
						onBlur={handleTitleBlur}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								handleTitleBlur();
							} else if (e.key === "Escape") {
								setTitle(lastSavedTitleRef.current);
								setIsEditing(false);
								setIsTitleDirty(false);
							}
						}}
						className="bg-transparent !text-xl font-semibold text-primary w-fit max-w-fit py-1"
					/>
				) : (
					<button
						onClick={() => setIsEditing(true)}
						className="!cursor-pointer text-xl font-semibold text-primary hover:bg-secondary/50 px-2 py-1 rounded-md transition-colors text-left min-w-0 max-w-lg truncate"
						title={title}
					>
						{title}
					</button>
				)}

				<ToggleStar
					id={data?.id}
					isFavorite={isFavorite}
					objectType={objectType}
				/>
			</div>

			{
				//     primaryCompany?.contactId && (
				//     <div
				//         onClick={() => router.push(`/companies/${primaryCompany?.companyId?.id}`)}
				//         className='cursor-pointer hover:bg-purple-900 text-sm font-normal border border-purple-500 px-2 rounded-lg bg-purple-500/50'
				//     >
				//         {primaryCompany?.companyId?.name}
				//     </div>
				// )
			}

			{data?.isDeleted === true ? (
				<div className="flex items-center grid-rows-2 gap-2 text-xs">
					{data?.isDeleted === true && (
						<div className="text-sm font-normal text-gray-500">
							{typeof data.deletedTime === "number" ||
							(typeof data.deletedTime === "string" &&
								!isNaN(Date.parse(data.deletedTime)))
								? `${restoreCountDown(data.deletedTime)} days left`
								: "Invalid delete date"}
						</div>
					)}
					<Button
						className="!bg-transparent hover:!hover-bg"
						onClick={() => {
							// TODO: Implement restore record
						}}
					>
						Restore
					</Button>
					<AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
						<AlertDialogTrigger asChild>
							<Button
								variant="error"
								size="sm"
								onClick={() => setOpenDelete(true)}
							>
								Delete Permanently
							</Button>
						</AlertDialogTrigger>
						<AlertDialogContent>
							<AlertDialogHeader>
								<AlertDialogTitle>
									Are you absolutely sure?
								</AlertDialogTitle>
								<AlertDialogDescription>
									This action cannot be undone. This will
									permanently delete this {objectType}
									and remove it from our servers.
								</AlertDialogDescription>
							</AlertDialogHeader>
							<AlertDialogFooter>
								<AlertDialogCancel
									onClick={() => setOpenDelete(false)}
								>
									Cancel
								</AlertDialogCancel>
								<AlertDialogAction
									className="!bg-destructive text-white hover:!bg-red-950"
									onClick={() => {
										// TODO: Implement delete record
									}}
								>
									Delete Permanently
								</AlertDialogAction>
							</AlertDialogFooter>
						</AlertDialogContent>
					</AlertDialog>
				</div>
			) : (
				<div className="flex items-center space-x-1">
					{objectType !== "note" && data?.id && (
						<div className="flex items-center gap-1">
							<div className="flex items-center gap-1">
								<RecordNavigation
									objectType={objectType}
									recordId={data?.id}
									organizationId={activeOrganization?.id || ""}
									viewName={viewName || undefined}
								/>
							</div>
							<PinRecord objectId={data.id} objectType={objectType as ObjectType} name={getDisplayTitle(data)} />
							
						</div>
					)}
					{objectType === "note" && data?.id && (
						<ShareNote
							noteId={data.id}
							isPublished={data.isPublished || false}
							onTogglePublished={() => {
								toggleNotePublished.mutate({
									id: data.id,
									isPublished: !data.isPublished,
								});
							}}
						/>
					)}
					{objectType === "note" ? (
						<NoteMoreDropdown
							note={data}
							isFavorite={isFavorite}
							organizationId={activeOrganization?.id}
						/>
					) : (
						<RecordMoreDropdown
							record={data}
							isFavorite={isFavorite}
							organizationId={activeOrganization?.id}
							objectType={objectType as ObjectType}
							onDelete={() => {
								// TODO: Implement delete record
							}}
							onConfigurePage={onConfigurePage}
						/>
					)}
				</div>
			)}
		</div>
	);
};

export default PageHeader;
