"use client";

import React from "react";
import { IconMail, IconMaximize, IconX } from "@tabler/icons-react";
import { useEmailCompose } from "./EmailComposeProvider";

export function GlobalMinimizedEmail() {
	const { isMinimized, maximizeCompose, savedDraft, closeCompose } = useEmailCompose();

	if (!isMinimized) {
		return null;
	}

	return (
		<div className="fixed bottom-0 right-2 z-50">
			<div className="border-l border-r border-t rounded-t-lg bg-sidebar w-64 hover:bg-muted/10 hover:ring-3 hover:ring-muted/50 hover:border-border cursor-pointer relative group" onClick={() => maximizeCompose()}>
				<div className="flex items-center gap-2 p-2 w-full justify-between">
					<div className="flex items-center gap-2">
						<IconMail className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm text-muted-foreground">{savedDraft ? "Draft email" : "New email"}</span>
					</div>
					<div className="flex items-center gap-2">
						<IconMaximize className="h-4 w-4 text-muted-foreground" />
					</div>
				</div>
				<button 
					className="absolute -top-2 -left-2 w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center hover:bg-primary/90 transition-colors opacity-0 group-hover:opacity-100 cursor-pointer hover:scale-110"
					onClick={(e) => {
						e.stopPropagation();
						closeCompose();
					}}
				>
					<IconX className="h-3 w-3" />
				</button>
			</div>
		</div>
	);
} 