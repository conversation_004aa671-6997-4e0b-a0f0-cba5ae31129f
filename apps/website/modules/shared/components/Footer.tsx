"use client";

import { LocaleLink } from "@i18n/routing";
import { DoNotSellDialog } from "@shared/components/DoNotSellDialog";
import { Logo } from "@shared/components/Logo";

export function Footer() {
	return (
		<footer className="border-t py-8 text-foreground/60 text-sm">
			<div className="container grid grid-cols-1 gap-6 lg:grid-cols-3">
				<div>
					<Logo className="opacity-70 grayscale" />
					<p className="mt-3 text-sm opacity-70">
						© {new Date().getFullYear()} Relio. All rights reserved.
					</p>
				</div>

				<div className="flex flex-col gap-2">
					<LocaleLink href="/pricing" className="block">
						Pricing
					</LocaleLink>

					<LocaleLink href="/faq" className="block">
						FAQ
					</LocaleLink>

					<LocaleLink href="/blog" className="block">
						Blog
					</LocaleLink>

					<LocaleLink href="/changelog" className="block">
						Changelog
					</LocaleLink>

					<LocaleLink href="/contact" className="block">
						Contact
					</LocaleLink>

					{/* <LocaleLink href="/help" className="block">
						Help
					</LocaleLink> */}
				</div>

				<div className="flex flex-col gap-2">
					<LocaleLink href="/legal/privacy-policy" className="block">
						Privacy Policy
					</LocaleLink>

					<LocaleLink href="/legal/terms" className="block">
						Terms and Conditions
					</LocaleLink>

					<LocaleLink
						href="/legal/google-services-agreement"
						className="block"
					>
						Google Services Agreement
					</LocaleLink>

					<DoNotSellDialog
						trigger={
							<button
								type="button"
								className="block text-left w-full px-0 py-0 bg-transparent border-0 text-inherit cursor-pointer"
							>
								Do not sell my personal information
							</button>
						}
					/>
				</div>
			</div>
		</footer>
	);
}
