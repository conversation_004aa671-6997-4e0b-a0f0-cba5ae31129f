import React from "react";
import { Button } from "@ui/components/button";
import { IconArrowLeft, IconAlertTriangle, IconFileX } from "@tabler/icons-react";
import { useRouter } from "next/navigation";

interface AccessDeniedErrorProps {
	message?: string;
	onBack?: () => void;
}

export const AccessDeniedError: React.FC<AccessDeniedErrorProps> = ({ 
	message = "You don't have access to view this record", 
	onBack 
}) => {
	const router = useRouter();

	const handleBack = () => {
		if (onBack) {
			onBack();
		} else {
			router.back();
		}
	};

	return (
		<div className="min-h-screen bg-background flex items-center justify-center p-4">
			<div className="text-center max-w-md mx-auto">
				<div className="mb-6">
					<IconAlertTriangle className="size-16 text-red-500 mx-auto mb-4" />
				</div>
				<h1 className="text-2xl font-bold mb-2 text-foreground">
					Access Denied
				</h1>
				<p className="text-muted-foreground mb-6">
					{message}
				</p>
				<Button variant="outline" onClick={handleBack}>
					<IconArrowLeft className="size-4 mr-2" />
					Go Back
				</Button>
			</div>
		</div>
	);
};

interface RecordNotFoundErrorProps {
	recordType?: string;
	message?: string;
	onBack?: () => void;
}

export const RecordNotFoundError: React.FC<RecordNotFoundErrorProps> = ({ 
	recordType = "record", 
	message,
	onBack 
}) => {
	const router = useRouter();

	const defaultMessage = `No ${recordType} to show`;
	const displayMessage = message || defaultMessage;

	const handleBack = () => {
		if (onBack) {
			onBack();
		} else {
			router.back();
		}
	};

	return (
		<div className="min-h-screen bg-muted/50 flex items-center justify-center p-4">
			<div className="flex flex-col items-center justify-center">
				<h1 className="text-lg font-bold text-foreground">
					{recordType.charAt(0).toUpperCase() + recordType.slice(1)} Not Found
				</h1>
				<p className="text-muted-foreground text-sm mb-4">
					{displayMessage}
				</p>
				<Button variant="primary" onClick={handleBack} className="gap-2">
					<IconArrowLeft className="size-4" />
					Go Back
				</Button>
			</div>
		</div>
	);
}; 