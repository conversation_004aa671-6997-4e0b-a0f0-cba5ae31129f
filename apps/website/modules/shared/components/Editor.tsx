"use client";

import type {
	J<PERSON><PERSON>ontent,
	Editor as TiptapEditor,
} from "@ui/components/editor";
import { EditorProvider } from "@ui/components/editor";
import {
	forwardRef,
	useEffect,
	useImperativeHandle,
	useRef,
	useState,
} from "react";

interface EditorProps {
	onChange?: (value: string) => void;
	initialContent?: string;
	editable?: boolean;
	onBlur?: () => void;
	placeholder?: string;
}

export interface EditorRef {
	focus: () => void;
}

const Editor = forwardRef<EditorRef, EditorProps>(
	({ onChange, initialContent, editable, onBlur, placeholder }, ref) => {
		const [content, setContent] = useState<JSONContent>(() => {
			if (initialContent) {
				try {
					return JSON.parse(initialContent);
				} catch {
					return { type: "doc", content: [] };
				}
			}
			return { type: "doc", content: [] };
		});

		useEffect(() => {
			if (initialContent) {
				try {
					const newContent = JSON.parse(initialContent);
					setContent(newContent);
				} catch {
					setContent({ type: "doc", content: [] });
				}
			} else {
				setContent({ type: "doc", content: [] });
			}
		}, [initialContent]);

		const editorRef = useRef<TiptapEditor | null>(null);

		useImperativeHandle(ref, () => ({
			focus: () => {
				if (editorRef.current) {
					editorRef.current.chain().focus().run();
				}
			},
		}));

		const handleUpdate = ({ editor }: { editor: TiptapEditor }) => {
			const json = editor.getJSON();
			setContent(json);
			if (onChange) {
				onChange(JSON.stringify(json));
			}
		};

		const handleCreate = ({ editor }: { editor: TiptapEditor }) => {
			editorRef.current = editor;
		};

		return (
			<EditorProvider
				content={content}
				placeholder={placeholder}
				className="h-full w-full overflow-y-auto"
				immediatelyRender={false}
				onUpdate={handleUpdate}
				onCreate={handleCreate}
				editable={editable}
				editorProps={{
					handleDOMEvents: {
						blur: () => {
							if (onBlur) onBlur();
							return false;
						},
					},
					handleKeyDown: (_view, event) => {
						event.stopPropagation();
						return false;
					},
				}}
			/>
		);
	},
);

Editor.displayName = "Editor";

export default Editor;
