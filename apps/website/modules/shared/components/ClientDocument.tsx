"use client";

import { AnalyticsScript } from "@analytics";
import { config } from "@repo/config";
import { ApiClientProvider } from "@shared/components/ApiClientProvider";
import { ConsentBanner } from "@shared/components/ConsentBanner";
import { ConsentProvider } from "@shared/components/ConsentProvider";
import { Toaster } from "@ui/components/toast";
import { cn } from "@ui/lib";
import { GeistSans } from "geist/font/sans";
import { Provider as Jo<PERSON>Provider } from "jotai";
import Script from "next/script";
import { ThemeProvider } from "next-themes";
import NextTopLoader from "nextjs-toploader";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import type { PropsWithChildren } from "react";

// Google Maps Script Component
function GoogleMapsScript() {
	const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

	if (!googleMapsApiKey) {
		return null;
	}

	return (
		<Script
			src={`https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=geometry`}
			strategy="afterInteractive"
			id="google-maps-api"
		/>
	);
}

export function ClientDocument({
	children,
	locale,
	initialConsent,
}: PropsWithChildren<{ locale: string; initialConsent: boolean }>) {
	return (
		<html
			lang={locale}
			suppressHydrationWarning
			className={GeistSans.variable}
		>
			<body
				className={cn(
					"min-h-screen bg-background text-foreground antialiased",
				)}
			>
				<NuqsAdapter>
					<ConsentProvider initialConsent={initialConsent}>
						<ThemeProvider
							attribute="class"
							disableTransitionOnChange
							enableSystem
							defaultTheme={config.ui.defaultTheme}
							themes={config.ui.enabledThemes}
						>
							<ApiClientProvider>
								<JotaiProvider>{children}</JotaiProvider>
							</ApiClientProvider>
						</ThemeProvider>
						<Toaster position="bottom-center" />
						<ConsentBanner />
						<AnalyticsScript />
						<GoogleMapsScript />
					</ConsentProvider>
				</NuqsAdapter>
			</body>
		</html>
	);
}
