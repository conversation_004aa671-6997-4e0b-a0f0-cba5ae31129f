"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export interface ConnectedAccount {
	id: string;
	provider: string;
	accountId: string;
	email: string;
	name: string;
	picture: string;
	services: string[];
	status: string;
	accessTokenExpiresAt: Date | null;
	scope: string | null;
	refreshToken?: string | null; // Add refresh token field
	createdAt: Date;
}

interface ConnectedAccountsResponse {
	accounts: ConnectedAccount[];
}

// Hook to fetch all connected accounts
export function useConnectedAccounts() {
	return useQuery({
		queryKey: ["connected-accounts"],
		queryFn: async (): Promise<ConnectedAccountsResponse> => {
			const response = await fetch("/api/connected-accounts", {
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to fetch connected accounts");
			}

			return response.json();
		},
	});
}

// Hook to fetch a specific connected account
export function useConnectedAccount(accountId: string) {
	return useQuery({
		queryKey: ["connected-accounts", accountId],
		queryFn: async (): Promise<ConnectedAccount> => {
			const response = await fetch(`/api/connected-accounts/${accountId}`, {
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to fetch connected account");
			}

			return response.json();
		},
		enabled: !!accountId,
	});
}

// Hook to reconnect a connected account
export function useReconnectAccount() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (accountId: string) => {
			// Since we're now using authClient.signIn.social directly,
			// this function mainly serves to invalidate queries after reconnection
			// The actual reconnection happens through the OAuth flow
			return { success: true, accountId };
		},
		onSuccess: (data) => {
			toast.success("Reconnection initiated. You'll be redirected to complete the OAuth flow.");
			// Invalidate queries to refresh the UI after reconnection
			queryClient.invalidateQueries({ queryKey: ["connected-accounts"] });
		},
		onError: (error) => {
			console.error("Error initiating reconnection:", error);
			toast.error("Failed to initiate reconnection");
		},
	});
}

// Hook to remove a connected account
export function useRemoveAccount() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (accountId: string) => {
			const response = await fetch(`/api/connected-accounts/${accountId}`, {
				method: "DELETE",
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to remove account");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Email account removed successfully");
			queryClient.invalidateQueries({ queryKey: ["connected-accounts"] });
		},
		onError: (error) => {
			console.error("Error removing account:", error);
			toast.error("Failed to remove email account");
		},
	});
} 