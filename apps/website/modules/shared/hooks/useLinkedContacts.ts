import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types for linked contacts using the new ObjectRelationship model
export interface LinkedContactData {
  id: string;
  contact1Id: string;
  contact1Relation: string;
  contact2Id: string;
  contact2Relation: string;
  // The linked contact's information (either contact1 or contact2, depending on perspective)
  linkedContact: {
    id: string;
    firstName?: string;
    lastName?: string;
    email?: Array<{ value: string; isPrimary?: boolean }> | string;
    phone?: Array<{ value: string; isPrimary?: boolean }> | string;
    image?: string | null;
  };
  // The relation from current contact's perspective
  relation: string;
  // The relation from linked contact's perspective
  reverseRelation: string;
}

export interface LinkContactPayload {
  contactId: string;
  object1Role: string; // Relation from current contact's perspective
  object2Role: string; // Relation from linked contact's perspective
}

export interface UnlinkContactPayload {
  relationshipId: string; // ID of the ObjectRelationship record
}

export interface UpdateContactRelationPayload {
  relationshipId: string; // ID of the ObjectRelationship record
  object1Role: string;
  object2Role: string;
}

// Utility functions to handle different data formats
export function getPrimaryEmail(email: any): string {
  if (!email) return '';
  
  if (typeof email === 'string') return email;
  
  if (Array.isArray(email)) {
    const primary = email.find(e => e.isPrimary);
    if (primary) return primary.value || primary.address || '';
    return email[0]?.value || email[0]?.address || '';
  }
  
  return '';
}

export function getPrimaryPhone(phone: any): string {
  if (!phone) return '';
  
  if (typeof phone === 'string') return phone;
  
  if (Array.isArray(phone)) {
    const primary = phone.find(p => p.isPrimary);
    if (primary) return primary.value || primary.number || '';
    return phone[0]?.value || phone[0]?.number || '';
  }
  
  return '';
}

// Fetch linked contacts for a contact
async function fetchLinkedContacts(contactId: string, organizationId: string): Promise<LinkedContactData[]> {
  const res = await fetch(`/api/objects/contacts/${contactId}/linked-contacts?organizationId=${organizationId}`);
  if (!res.ok) {
    const error = await res.json();
    throw new Error(error.error || "Failed to fetch linked contacts");
  }
  const result = await res.json();
  return result.data || [];
}

// React Query hooks
export function useLinkedContacts(contactId: string, organizationId: string) {
  return useQuery({
    queryKey: ['linkedContacts', contactId, organizationId],
    queryFn: async (): Promise<LinkedContactData[]> => {
      const response = await fetch(
        `/api/objects/contacts/${contactId}/linked-contacts?organizationId=${organizationId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch linked contacts');
      }

      const result = await response.json();
      
      // Transform the raw relationship data to the expected format
      return (result.data || []).map((item: any) => {
        // Determine which contact is the "linked" one (not the current contact)
        const isCurrentContact1 = item.contact1Id === contactId;
        const linkedContact = isCurrentContact1 ? item.contact2 : item.contact1;
        const relation = isCurrentContact1 ? item.contact1Relation : item.contact2Relation;
        const reverseRelation = isCurrentContact1 ? item.contact2Relation : item.contact1Relation;

        return {
          id: item.id,
          contact1Id: item.contact1Id,
          contact1Relation: item.contact1Relation,
          contact2Id: item.contact2Id,
          contact2Relation: item.contact2Relation,
          linkedContact: {
            id: linkedContact?.id || '',
            firstName: linkedContact?.firstName || '',
            lastName: linkedContact?.lastName || '',
            email: linkedContact?.email || [],
            phone: linkedContact?.phone || [],
            image: linkedContact?.image || null,
          },
          relation,
          reverseRelation,
        };
      });
    },
    enabled: !!contactId && !!organizationId,
  });
}

export function useLinkContactToContact(contactId: string, organizationId: string) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: LinkContactPayload) => {
      const response = await fetch(
        `/api/objects/contacts/${contactId}/linked-contacts?organizationId=${organizationId}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to link contacts');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['linkedContacts', contactId, organizationId] });
      queryClient.invalidateQueries({ queryKey: ['linkedContacts'] });
    },
  });
}

export function useUnlinkContactFromContact(contactId: string, organizationId: string) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: UnlinkContactPayload) => {
      const response = await fetch(
        `/api/objects/contacts/${contactId}/linked-contacts?organizationId=${organizationId}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to unlink contacts');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['linkedContacts', contactId, organizationId] });
      queryClient.invalidateQueries({ queryKey: ['linkedContacts'] });
    },
  });
}

export function useUpdateContactRelation(contactId: string, organizationId: string) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: UpdateContactRelationPayload) => {
      const response = await fetch(
        `/api/objects/contacts/${contactId}/linked-contacts?organizationId=${organizationId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contact relationship');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['linkedContacts', contactId, organizationId] });
      queryClient.invalidateQueries({ queryKey: ['linkedContacts'] });
    },
  });
} 