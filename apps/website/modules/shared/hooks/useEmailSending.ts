"use client";

import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";

export interface SendEmailRequest {
	organizationId: string;
	fromAccountId: string; // Connected account ID to send from
	to: string;
	cc?: string[];
	bcc?: string[];
	subject: string;
	body: string;
}

export interface SendEmailFormData {
	organizationId: string;
	fromAccountId: string;
	to: string;
	cc: string;
	bcc: string;
	subject: string;
	body: string;
}

export interface SendEmailResponse {
	success: boolean;
	messageId: string;
	emailActivityId: string;
}

const sendEmailSchema = z.object({
	organizationId: z.string(),
	fromAccountId: z.string(),
	to: z.string().email("Please enter a valid email address"),
	cc: z.string(),
	bcc: z.string(),
	subject: z.string().min(1, "Subject is required"),
	body: z.string().min(1, "Email body is required"),
});

// Form data type is defined above

// Hook to send an email from a connected account
export function useSendEmail() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (emailData: SendEmailRequest): Promise<SendEmailResponse> => {
			// Validate the email data
			const validatedData = sendEmailSchema.parse(emailData);

			const response = await fetch("/api/send-email", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify(validatedData),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.message || "Failed to send email");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Email sent successfully");
			// Invalidate email activities to refresh the list
			queryClient.invalidateQueries({ queryKey: ["email-activities"] });
		},
		onError: (error) => {
			console.error("Error sending email:", error);
			
			// Check if it's a token expiration error
			const errorMessage = error.message || "Failed to send email";
			if (errorMessage.includes("access token") || errorMessage.includes("refresh token") || errorMessage.includes("reconnect")) {
				toast.error("Your email account connection has expired. Please reconnect your account in Settings.");
			} else {
				toast.error(errorMessage);
			}
		},
	});
}

// Validation schema export for form validation
export { sendEmailSchema };

// New hook for fetching user data by emails
export interface UserByEmailResult {
	email: string;
	name: string;
	avatarUrl?: string | null;
	type: "user" | "contact" | "unknown";
	allEmails: string[];
}

export function useUsersByEmails(emails: string[], organizationId: string, enabled: boolean = true) {
	return useQuery({
		queryKey: ["users-by-emails", emails, organizationId],
		queryFn: async (): Promise<UserByEmailResult[]> => {
			if (!emails.length || !organizationId) return [];

			const response = await fetch("/api/users-by-emails", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					emails,
					organizationId,
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to fetch users by emails");
			}

			const data = await response.json();
			return data.users || [];
		},
		enabled: enabled && emails.length > 0 && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
} 