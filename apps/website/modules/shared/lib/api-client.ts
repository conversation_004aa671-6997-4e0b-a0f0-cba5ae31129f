import type { AppRouter } from "@repo/api";
import { getBaseUrl } from "@repo/utils";
import { hc } from "hono/client";

// Get the API base URL - use current domain in browser, fallback to getBaseUrl() for server-side
const getApiBaseUrl = () => {
	// In the browser, always use the current domain to avoid CORS issues
	if (typeof window !== "undefined") {
		return window.location.origin;
	}
	
	// On the server, use the configured base URL
	return getBaseUrl();
};

export const apiClient = hc<AppRouter>(getApiBaseUrl(), {
	init: {
		credentials: "include",
	},
}).api;
