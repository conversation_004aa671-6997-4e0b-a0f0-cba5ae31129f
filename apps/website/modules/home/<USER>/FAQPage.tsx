"use client";

import {
	Accordion,
	Accordion<PERSON>ontent,
	AccordionItem,
	AccordionTrigger,
} from "@ui/components/accordion";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";

type Category = "Support" | "Account" | "Features" | "Security";

interface FAQItem {
	question: string;
	answer: string;
	category: Category;
}

const getTranslatedFaqItems = (
	t: ReturnType<typeof useTranslations>,
): FAQItem[] => [
	{
		category: "Support",
		question: t("left.one.question"),
		answer: t("left.one.answer"),
	},
	{
		category: "Support",
		question: t("left.two.question"),
		answer: t("left.two.answer"),
	},
	{
		category: "Support",
		question: t("left.three.question"),
		answer: `${t("left.three.answerOne")} <EMAIL> ${t("left.three.answerTwo")}`,
	},
	{
		category: "Features",
		question: t("left.four.question"),
		answer: t("left.four.answer"),
	},
	{
		category: "Features",
		question: t("left.five.question"),
		answer: t("left.five.answer"),
	},
	{
		category: "Features",
		question: t("right.one.question"),
		answer: t("right.one.answer"),
	},
	// Security (faq.right.two, three, four)
	{
		category: "Security",
		question: t("right.two.question"),
		answer: t("right.two.answer"),
	},
	{
		category: "Security",
		question: t("right.three.question"),
		answer: t("right.three.answer"),
	},
	{
		category: "Security",
		question: t("right.four.question"),
		answer: t("right.four.answer"),
	},
	// Account (hardcoded for now)
	{
		category: "Account",
		question: t("account.one.question"),
		answer: t("account.one.answer"),
	},
	{
		category: "Account",
		question: t("account.two.question"),
		answer: t("account.two.answer"),
	},
	{
		category: "Account",
		question: t("account.three.question"),
		answer: t("account.three.answer"),
	},
	{
		category: "Account",
		question: t("account.four.question"),
		answer: t("account.four.answer"),
	},
];

const categories: Category[] = ["Support", "Account", "Features", "Security"];

const TOP_PADDING = 300;

const categoryKeys: Record<Category, string> = {
	Support: "category.support",
	Account: "category.account",
	Features: "category.features",
	Security: "category.security",
};

export const FAQPage = () => {
	const t = useTranslations("faq");
	const faqItems = getTranslatedFaqItems(t);
	const [activeCategory, setActiveCategory] = useState<Category>("Support");
	const observerRef = useRef<IntersectionObserver | null>(null);
	const isScrollingRef = useRef(false);
	const categoryRefs = useRef<Record<Category, HTMLDivElement | null>>({
		Support: null,
		Account: null,
		Features: null,
		Security: null,
	});

	const setupObserver = useCallback(() => {
		observerRef.current?.disconnect();

		let debounceTimeout: NodeJS.Timeout;

		observerRef.current = new IntersectionObserver(
			(entries) => {
				// Skip if we're programmatically scrolling
				if (isScrollingRef.current) return;

				// Clear any pending timeout
				if (debounceTimeout) {
					clearTimeout(debounceTimeout);
				}

				// Debounce the category update
				debounceTimeout = setTimeout(() => {
					const intersectingEntries = entries.filter(
						(entry) => entry.isIntersecting,
					);

					// Find the entry that's closest to being 100px from the top
					const entry = intersectingEntries.reduce(
						(closest, current) => {
							const rect = current.boundingClientRect;
							const distanceFromThreshold = Math.abs(
								rect.top - TOP_PADDING,
							);
							const closestDistance = closest
								? Math.abs(
										closest.boundingClientRect.top -
											TOP_PADDING,
									)
								: Number.POSITIVE_INFINITY;

							return distanceFromThreshold < closestDistance
								? current
								: closest;
						},
						null as IntersectionObserverEntry | null,
					);

					if (entry) {
						const category = entry.target.getAttribute(
							"data-category",
						) as Category;
						if (category) {
							setActiveCategory(category);
						}
					}
				}, 150);
			},
			{
				root: null,
				rootMargin: `-${TOP_PADDING}px 0px -100% 0px`,
				threshold: [0, 0.25, 0.5, 0.75, 1],
			},
		);

		Object.entries(categoryRefs.current).forEach(([category, element]) => {
			if (element) {
				element.setAttribute("data-category", category);
				observerRef.current?.observe(element);
			}
		});

		return () => {
			if (debounceTimeout) {
				clearTimeout(debounceTimeout);
			}
		};
	}, []);

	useEffect(() => {
		const cleanup = setupObserver();
		return () => {
			cleanup();
			observerRef.current?.disconnect();
		};
	}, [setupObserver]);

	const handleCategoryClick = (category: Category) => {
		setActiveCategory(category);
		isScrollingRef.current = true;

		const element = document.getElementById(
			`faq-${category.toLowerCase()}`,
		);
		if (element) {
			element.style.scrollMargin = `${TOP_PADDING}px`;
			element.scrollIntoView({ behavior: "smooth", block: "start" });

			setTimeout(() => {
				isScrollingRef.current = false;
			}, 1000);
		}
	};

	return (
		<section className="bg-sand-100 min-h-screen">
			<div className="container max-w-6xl">
				<div className="grid max-w-5xl gap-8 md:mt-12 md:grid-cols-[200px_1fr] md:gap-12 lg:mt-16">
					{/* Sidebar */}
					<div className="sticky top-24 flex h-fit flex-col gap-4 max-md:hidden">
						{categories.map((category) => (
							<Button
								variant="ghost"
								key={category}
								onClick={() => handleCategoryClick(category)}
								className={`justify-start text-left text-xl transition-colors ${
									activeCategory === category
										? "font-semibold"
										: "font-normal hover:opacity-75"
								}`}
							>
								{t(categoryKeys[category] as any)}
							</Button>
						))}
					</div>

					{/* FAQ Items by Category */}
					<div className="space-y-6">
						{categories.map((category) => {
							const categoryItems = faqItems.filter(
								(item) => item.category === category,
							);

							return (
								<div
									key={category}
									id={`faq-${category.toLowerCase()}`}
									ref={(el) => {
										categoryRefs.current[category] = el;
									}}
									className={cn(
										"rounded-xl",
										activeCategory === category
											? "bg-secondary"
											: "bg-background/40",
										"px-6",
									)}
									style={{
										scrollMargin: `${TOP_PADDING}px`,
									}}
								>
									<Accordion
										type="single"
										collapsible
										defaultValue={`${categories[0]}-0`}
										className="w-full"
									>
										{categoryItems.map((item, i) => (
											<AccordionItem
												key={i}
												value={`${category}-${i}`}
												className="border-muted border-b last:border-0"
											>
												<AccordionTrigger className="text-base font-medium hover:no-underline">
													{item.question}
												</AccordionTrigger>
												<AccordionContent className="text-muted-foreground text-base font-medium">
													{item.answer}
												</AccordionContent>
											</AccordionItem>
										))}
									</Accordion>
								</div>
							);
						})}
					</div>
				</div>
			</div>
		</section>
	);
};
