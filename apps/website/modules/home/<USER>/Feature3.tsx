"use client";

import {
	IconFileDatabase,
	IconHomeDollar,
	IconLibrary,
	IconListCheck,
	IconUsersGroup,
} from "@tabler/icons-react";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import React, { useEffect, useState } from "react";

export const Feature3 = () => {
	const t = useTranslations("home.featureSection3");
	const { resolvedTheme } = useTheme();
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	function getImage(light: string, dark: string) {
		if (!mounted) return light;
		return resolvedTheme === "dark" ? dark : light;
	}

	const FEATURES = [
		{
			title: t("one.title"),
			description: t("one.description"),
			content: {
				title: t("one.content.title"),
				description: t("one.content.description"),
				image: getImage(
					"/images/features/tasks-columns-light.png",
					"/images/features/tasks-columns-dark.png",
				),
				className: "",
			},
			icon: IconListCheck,
		},
		{
			title: t("two.title"),
			description: t("two.description"),
			content: {
				title: t("two.content.title"),
				description: t("two.content.description"),
				image: getImage(
					"/images/features/notes-light.png",
					"/images/features/notes-dark.png",
				),
			},
			icon: IconLibrary,
		},
		{
			title: t("three.title"),
			description: t("three.description"),
			content: {
				title: t("three.content.title"),
				description: t("three.content.description"),
				image: getImage(
					"/images/features/contact-light.png",
					"/images/features/contact-dark.png",
				),
			},
			icon: IconFileDatabase,
		},
		{
			title: t("four.title"),
			description: t("four.description"),
			content: {
				title: t("four.content.title"),
				description: t("four.content.description"),
				image: getImage(
					"/images/features/import-light.png",
					"/images/features/import-dark.png",
				),
			},
			icon: IconHomeDollar,
		},
	];

	return (
		<section id="feature3" className="bg-mint-50 py-16 md:py-28 lg:py-32">
			<div className="container">
				<div className="flex flex-col gap-3 md:flex-row">
					<h2 className="flex-1 text-3xl font-semibold tracking-tight text-balance md:text-4xl lg:text-5xl">
						{t("title")}
					</h2>
					<p className="text-muted-foreground flex-1 text-lg font-medium md:max-w-md md:self-end">
						{t("description")}
					</p>
				</div>

				<Tabs
					defaultValue={FEATURES[0].title}
					orientation="vertical"
					className="mt-8 flex gap-4 max-lg:flex-col-reverse md:mt-12 lg:mt-20"
				>
					<TabsList className="!border-b-0 flex h-auto justify-start overflow-x-auto rounded-xl bg-black/[0.03] p-1.5 lg:basis-1/4 lg:flex-col">
						{FEATURES.map((feature) => (
							<TabsTrigger
								key={feature.title}
								value={feature.title}
								className="w-full min-w-[200px] flex-1 justify-start rounded-lg px-4 py-3 text-start whitespace-normal text-gray-700 transition-colors duration-300 data-[state=active]:text-black lg:px-6 lg:py-4 dark:text-gray-300 dark:data-[state=active]:text-white data-[state=active]:dark:!border-zinc-800 data-[state=active]:border-zinc-200"
							>
								<div>
									<feature.icon className="size-7 md:size-8 lg:size-9" />
									<h3 className="mt-3 font-semibold">
										{feature.title}
									</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										{feature.description}
									</p>
								</div>
							</TabsTrigger>
						))}
					</TabsList>

					{FEATURES.map((feature) => (
						<TabsContent
							className={cn(
								"bg-background m-0 flex-1 overflow-hidden rounded-xl",
								feature.content.className,
							)}
							key={feature.title}
							value={feature.title}
						>
							<div className="max-w-2xl p-5 text-lg text-balance lg:p-7">
								<h4 className="inline font-semibold">
									{feature.content.title}{" "}
								</h4>
								<span className="text-muted-foreground mt-2 font-medium text-pretty">
									{feature.content.description}
								</span>
							</div>
							<div className="relative h-[420px] rounded-lg lg:h-[500px] lg:flex-1">
								<Image
									src={feature.content.image}
									alt={feature.title}
									fill
									className="object-cover object-left-top"
								/>
							</div>
						</TabsContent>
					))}
				</Tabs>
			</div>
		</section>
	);
};
