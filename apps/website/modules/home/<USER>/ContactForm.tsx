"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
	type ContactFormValues,
	contactFormSchema,
} from "@repo/api/src/routes/contact/types";
import {
	IconBrandInstagram,
	IconBrandLinkedin,
	IconBrandTwitter,
	IconBrandX,
} from "@tabler/icons-react";
import { Alert, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import {
	Facebook,
	Linkedin,
	Mail,
	MailCheck,
	MapPin,
	Twitter,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { useContactFormMutation } from "../lib/api";

export function ContactForm() {
	const t = useTranslations("contact");
	const contactFormMutation = useContactFormMutation();

	const form = useForm<ContactFormValues>({
		resolver: zodResolver(contactFormSchema),
		defaultValues: {
			name: "",
			email: "",
			message: "",
			companyName: "",
			employeeCount: "",
		},
	});

	const onSubmit = form.handleSubmit(async (values) => {
		try {
			await contactFormMutation.mutateAsync(values);
		} catch {
			form.setError("root", {
				message: t("form.notifications.error"),
			});
		}
	});

	return (
		<div className="max-w-6xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
			{form.formState.isSubmitSuccessful ? (
				<Alert variant="success" className="max-w-2xl mx-auto">
					<MailCheck className="size-6" />
					<AlertTitle>{t("form.notifications.success")}</AlertTitle>
				</Alert>
			) : (
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
					{/* Left Column - Contact Info */}
					<div className="space-y-8 w-full lg:mx-auto">
						<div>
							<h3 className="text-lg font-medium mb-4">
								{t("corporateOffice")}
							</h3>
							<div className="flex items-start">
								<MapPin className="h-6 w-6 text-zinc-400 mr-3 mt-0.5 flex-shrink-0" />
								<p className="text-zinc-600 whitespace-pre-line">
									19800 MacArthur Blvd
									<br />
									Suite 150
									<br />
									Irvine, CA 92612
								</p>
							</div>
						</div>

						<div>
							<h3 className="text-lg font-medium mb-4">
								{t("emailUs")}
							</h3>
							<div className="space-y-2">
								<div className="flex items-center">
									<Mail className="h-5 w-5 text-zinc-400 mr-3" />
									<a
										href="mailto:<EMAIL>"
										className="text-zinc-600 hover:text-sidebar"
									>
										<EMAIL>
									</a>
								</div>
								<div className="flex items-center">
									<Mail className="h-5 w-5 text-zinc-400 mr-3" />
									<a
										href="mailto:<EMAIL>"
										className="text-zinc-600 hover:text-sidebar"
									>
										<EMAIL>
									</a>
								</div>
							</div>
						</div>

						<div>
							<h3 className="text-lg font-medium mb-4">
								{t("followUs")}
							</h3>
							<div className="flex space-x-4">
								<a
									href="https://www.instagram.com/reliocrm"
									target="_blank"
									className="text-zinc-400 hover:text-zinc-500"
									rel="noopener"
								>
									<span className="sr-only">
										{t("instagram")}
									</span>
									<IconBrandInstagram className="h-6 w-6" />
								</a>
								<a
									href="https://x.com/reliocrm"
									target="_blank"
									className="text-zinc-400 hover:text-zinc-500"
									rel="noopener"
								>
									<span className="sr-only">
										{t("twitter")}
									</span>
									<IconBrandX className="h-6 w-6" />
								</a>
								<a
									href="https://www.linlkedin.com/company/reliocrm"
									target="_blank"
									className="text-zinc-400 hover:text-zinc-500"
									rel="noopener"
								>
									<span className="sr-only">
										{t("linkedin")}
									</span>
									<IconBrandLinkedin className="h-6 w-6" />
								</a>
							</div>
						</div>
					</div>

					{/* Right Column - Form with vertical separator */}
					<div className="lg:border-l lg:border-zinc-200 lg:pl-12">
						<h3 className="text-lg font-medium dark:text-zinc-100 text-sidebar mb-6">
							Inquiries
						</h3>
						<Form {...form}>
							<form onSubmit={onSubmit} className="space-y-6">
								{form.formState.errors.root?.message && (
									<Alert variant="error">
										<Mail className="size-6" />
										<AlertTitle>
											{form.formState.errors.root.message}
										</AlertTitle>
									</Alert>
								)}

								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("form.name")}
											</FormLabel>
											<FormControl>
												<Input
													placeholder={t("form.name")}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("form.email")}
											</FormLabel>
											<FormControl>
												<Input
													placeholder="<EMAIL>"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="companyName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("form.companyName")}
											</FormLabel>
											<FormControl>
												<Input
													placeholder={t(
														"form.companyName",
													)}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="employeeCount"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("form.employeeCount")}
											</FormLabel>
											<FormControl>
												<Input
													placeholder={t(
														"form.employeeCount",
													)}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="message"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("form.yourMessage")}
											</FormLabel>
											<FormControl>
												<Textarea
													placeholder={t(
														"form.yourMessage",
													)}
													className="min-h-[120px]"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="flex justify-end">
									<Button
										type="submit"
										variant="primary"
										loading={form.formState.isSubmitting}
									>
										{t("form.submit")}
									</Button>
								</div>
							</form>
						</Form>
					</div>
				</div>
			)}
		</div>
	);
}
