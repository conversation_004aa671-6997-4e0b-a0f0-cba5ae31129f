"use client";

import { Button } from "@ui/components/button";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@ui/components/collapsible";
import { Check, ChevronsUpDown } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface FeatureSection {
	category: string;
	features: {
		name: string;
		free: string | boolean;
		growth: string | boolean;
		enterprise: string | boolean;
	}[];
}

const pricingPlans = [
	{
		name: "Free",
		button: {
			text: "Get started",
			variant: "outline" as const,
		},
	},
	{
		name: "Growth",
		button: {
			text: "Get started",
			variant: "outline" as const,
		},
	},
	{
		name: "Enterprise",
		button: {
			text: "Contact sales",
			variant: "outline" as const,
		},
	},
];

const comparisonFeatures: FeatureSection[] = [
	{
		category: "Usage",
		features: [
			{
				name: "Members",
				free: "1",
				growth: "Unlimited",
				enterprise: "Unlimited",
			},
			{
				name: "Contacts",
				free: "500",
				growth: "Unlimited",
				enterprise: "Unlimited",
			},
			{
				name: "Properties",
				free: "1000",
				growth: "Unlimited",
				enterprise: "Unlimited",
			},
			{
				name: "Pipelines",
				free: "1",
				growth: "Unlimited",
				enterprise: "Unlimited",
			},
			{
				name: "Tasks",
				free: "Unlimited",
				growth: "Unlimited",
				enterprise: "Unlimited",
			},
			{
				name: "Organizations",
				free: "1",
				growth: "Unlimited",
				enterprise: "Unlimited",
			},
		],
	},
	{
		category: "Features",
		features: [
			{
				name: "Reporting",
				free: false,
				growth: true,
				enterprise: true,
			},
			{
				name: "Analytics",
				free: true,
				growth: true,
				enterprise: true,
			},
			{
				name: "Import and export",
				free: true,
				growth: true,
				enterprise: true,
			},
			{
				name: "Integrations",
				free: false,
				growth: true,
				enterprise: true,
			},
			{
				name: "Relio AI",
				free: false,
				growth: true,
				enterprise: true,
			},
			{
				name: "Admin roles",
				free: false,
				growth: true,
				enterprise: true,
			},
			{
				name: "Audit log",
				free: false,
				growth: true,
				enterprise: true,
			},
		],
	},
	{
		category: "Support",
		features: [
			{
				name: "Support / Helpdesk",
				free: "3-5 business days",
				growth: "Priority support",
				enterprise: "24/7 priority support",
			},
			{
				name: "Account Manager",
				free: false,
				growth: false,
				enterprise: true,
			},
			{
				name: "Uptime SLA",
				free: false,
				growth: false,
				enterprise: true,
			},
		],
	},
];

const Pricing2 = () => {
	const [selectedPlan, setSelectedPlan] = useState(1); // Default to Growth plan

	return (
		<section className="pb-16 md:pb-28 lg:pb-32">
			<div className="container">
				<PlanHeaders
					selectedPlan={selectedPlan}
					onPlanChange={setSelectedPlan}
				/>
				<div className="mt-6 lg:mt-12">
					<FeatureSections selectedPlan={selectedPlan} />
				</div>
			</div>
		</section>
	);
};

const PlanHeaders = ({
	selectedPlan,
	onPlanChange,
}: {
	selectedPlan: number;
	onPlanChange: (index: number) => void;
}) => {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<div>
			{/* Mobile View */}
			<div className="md:hidden">
				<Collapsible open={isOpen} onOpenChange={setIsOpen}>
					<div className="flex items-center justify-between py-4">
						<CollapsibleTrigger
							className="flex items-center gap-2"
							aria-label="Toggle pricing plans"
						>
							<h3 className="text-2xl font-semibold">
								{pricingPlans[selectedPlan].name}
							</h3>
							<ChevronsUpDown
								className={`size-5 transition-transform ${isOpen ? "rotate-180" : ""}`}
								aria-hidden="true"
							/>
						</CollapsibleTrigger>
						<Link href="/#earlyAccess">
							<Button variant="outline" size="sm">
								{pricingPlans[selectedPlan].button.text}
							</Button>
						</Link>
					</div>
					<CollapsibleContent className="flex flex-col space-y-2 p-2">
						{pricingPlans.map(
							(plan, index) =>
								index !== selectedPlan && (
									<Button
										size="lg"
										variant="secondary"
										key={index}
										onClick={() => {
											onPlanChange(index);
											setIsOpen(false);
										}}
									>
										{plan.name}
									</Button>
								),
						)}
					</CollapsibleContent>
				</Collapsible>
			</div>

			{/* Desktop View */}
			<div className="grid grid-cols-4 max-md:hidden">
				<div className="col-span-1" />
				{pricingPlans.map((plan, index) => (
					<div key={index} className="flex flex-col">
						<h3 className="mb-4 text-2xl font-semibold">
							{plan.name}
						</h3>
						<Link href="/#earlyAccess">
							<Button variant="outline" className="w-fit">
								{plan.button.text}
							</Button>
						</Link>
					</div>
				))}
			</div>
		</div>
	);
};

const FeatureSections = ({ selectedPlan }: { selectedPlan: number }) => (
	<>
		{comparisonFeatures.map((section, sectionIndex) => (
			<div key={sectionIndex} className="mb-8">
				<div className="bg-muted-foreground/5 px-2 py-4">
					<h3 className="text-lg font-semibold">
						{section.category}
					</h3>
				</div>
				{section.features.map((feature, featureIndex) => (
					<div
						key={featureIndex}
						className="text-primary/90 grid grid-cols-2 border-b py-2 font-medium max-md:last:border-b-0 md:grid-cols-4"
					>
						<span className="flex items-center py-3">
							{feature.name}
						</span>
						{/* Mobile View - Only Selected Plan */}
						<div className="md:hidden">
							<div className="flex items-center gap-1 py-3">
								{(() => {
									const value = [
										feature.free,
										feature.growth,
										feature.enterprise,
									][selectedPlan];
									return typeof value === "boolean" ? (
										value ? (
											<Check className="text-primary/80 size-5" />
										) : null
									) : (
										<div className="flex items-center gap-1">
											<Check className="text-primary/80 size-4" />
											<span>{value}</span>
										</div>
									);
								})()}
							</div>
						</div>
						{/* Desktop View - All Plans */}
						<div className="hidden md:col-span-3 md:grid md:grid-cols-3">
							{[
								feature.free,
								feature.growth,
								feature.enterprise,
							].map((value, i) => (
								<div key={i} className="flex items-center py-3">
									{typeof value === "boolean" ? (
										value ? (
											<Check className="text-primary/80 size-5" />
										) : null
									) : (
										<div className="flex items-center gap-1">
											<Check className="text-primary/80 size-4" />
											<span>{value}</span>
										</div>
									)}
								</div>
							))}
						</div>
					</div>
				))}
			</div>
		))}
	</>
);

export default Pricing2;
