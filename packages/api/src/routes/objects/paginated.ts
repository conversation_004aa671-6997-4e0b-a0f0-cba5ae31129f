import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	getDbTable,
	OBJECT_CONFIG,
	type ObjectType,
	normalizeObjectType,
} from "./config";

export const objectsPaginatedRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Middleware to validate object type
const validateObjectTypeMiddleware = async (c: any, next: any) => {
	const objectTypeParam = c.req.param("objectType");
	const objectType = normalizeObjectType(objectTypeParam);

	if (!objectType) {
		return c.json({ error: "Invalid object type" }, 400);
	}

	c.set("objectType" as any, objectType);
	await next();
};

// Paginated query endpoint with advanced filtering
objectsPaginatedRouter.get(
	"/objects/:objectType/paginated",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;

			// Parse query parameters
			const organizationId = c.req.query("organizationId");
			const limit = Math.min(
				Number.parseInt(c.req.query("limit") || "25"),
				100,
			);
			const offset = Number.parseInt(c.req.query("offset") || "0");
			const search = c.req.query("search");
			const createdAt = c.req.query("createdAt");

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Build base where conditions
			const whereConditions: any = {
				organizationId,
				isDeleted: false,
			};

			// Track address filters for special handling
			const addressFilters: Record<string, string> = {};

			// Process all query parameters for filtering
			logger.info(`Processing query parameters for ${objectType}:`, c.req.query());
			
			// Handle configured filter fields
			Object.keys(config.filterFields).forEach((field) => {
				const value = c.req.query(field);
				if (value && field !== "createdAt") {
					logger.info(`Processing configured filter: ${field} = ${value}`);
					const filterType = (config.filterFields as any)[field];

					if (filterType === "string") {
						if (field === "name" && objectType === "contact") {
							// Special handling for name in contacts
							whereConditions.OR = [
								{
									firstName: {
										contains: value,
										mode: "insensitive",
									},
								},
								{
									lastName: {
										contains: value,
										mode: "insensitive",
									},
								},
							];
						} else if (field.startsWith("address.location.") && objectType === "property") {
							// Handle address location filters
							const addressField = field.replace("address.location.", "");
							addressFilters[addressField] = value;
						} else {
							whereConditions[field] = {
								contains: value,
								mode: "insensitive",
							};
						}
					} else if (filterType === "number") {
						// Handle numeric filters (can be ranges)
						try {
							if (value.includes(",")) {
								// Range filter like "100000,500000"
								const [min, max] = value.split(",").map(Number);
								if (!isNaN(min) || !isNaN(max)) {
									const numericFilter: any = {};
									if (!isNaN(min)) numericFilter.gte = min;
									if (!isNaN(max)) numericFilter.lte = max;
									
									// Map to correct nested relations for property fields
									if (objectType === "property") {
										if (field === "price") {
											whereConditions.financials = {
												...whereConditions.financials,
												price: numericFilter,
											};
										} else if (["bedrooms", "bathrooms", "squareFootage", "yearBuilt", "lotSize", "units"].includes(field)) {
											whereConditions.physicalDetails = {
												...whereConditions.physicalDetails,
												[field]: numericFilter,
											};
										} else {
											whereConditions[field] = numericFilter;
										}
									} else {
										whereConditions[field] = numericFilter;
									}
								}
							} else {
								// Single value filter
								const numValue = Number(value);
								if (!isNaN(numValue)) {
									if (objectType === "property") {
										if (field === "price") {
											whereConditions.financials = {
												...whereConditions.financials,
												price: numValue,
											};
										} else if (["bedrooms", "bathrooms", "squareFootage", "yearBuilt", "lotSize", "units"].includes(field)) {
											whereConditions.physicalDetails = {
												...whereConditions.physicalDetails,
												[field]: numValue,
											};
										} else {
											whereConditions[field] = numValue;
										}
									} else {
										whereConditions[field] = numValue;
									}
								}
							}
						} catch (error) {
							logger.warn(`Invalid numeric filter value for ${field}:`, value);
						}
					} else if (
						filterType === "relation" &&
						field === "company" &&
						objectType === "contact"
					) {
						whereConditions.company = {
							name: { contains: value, mode: "insensitive" },
						};
					} else {
						// Handle array filters like propertyType and status
						if (filterType === "stringArray" || Array.isArray(value) || value.includes(",")) {
							const values = Array.isArray(value) ? value : value.split(",").map((v: string) => v.trim()).filter(Boolean);
							if (values.length > 0) {
								whereConditions[field] = {
									in: values,
								};
							}
						} else {
							whereConditions[field] = value;
						}
					}
				}
			});

			// Handle additional query parameters not in config.filterFields
			const allQueryParams = c.req.query();
			const configuredFields = Object.keys(config.filterFields);
			const systemFields = ["organizationId", "limit", "offset", "search", "createdAt", "tags", "size", "start", "sort", "id"];
			
			Object.keys(allQueryParams).forEach((queryField) => {
				const value = allQueryParams[queryField];
				if (value && !configuredFields.includes(queryField) && !systemFields.includes(queryField)) {
					logger.info(`Processing unconfigured query field: ${queryField} = ${value}`);
					
					// Handle address location filtering for properties
					if (objectType === "property" && queryField.startsWith("address.location.")) {
						const addressField = queryField.replace("address.location.", "");
						addressFilters[addressField] = value;
					}
				}
			});

			// Handle tags filtering
			const tags = c.req.query("tags");
			if (tags) {
				const tagNames = tags.split(",").map(tag => tag.trim()).filter(Boolean);
				if (tagNames.length > 0) {
					whereConditions.objectTags = {
						some: {
							tag: {
								name: {
									in: tagNames,
								},
								organizationId,
							},
						},
					};
				}
			}

			// Handle search across configured search fields
			if (search) {
				whereConditions.OR = config.searchFields.map((field) => ({
					[field]: { contains: search, mode: "insensitive" },
				}));
			}

			// Handle createdAt date range filter
			if (createdAt) {
				try {
					let startDate: Date | null = null;
					let endDate: Date | null = null;

					const parseDate = (dateStr: string): Date | null => {
						try {
							if (/^\d+$/.test(dateStr.trim())) {
								const date = new Date(Number.parseInt(dateStr.trim()));
								if (!isNaN(date.getTime())) return date;
							}
							
							const date = new Date(dateStr.trim());
							if (!isNaN(date.getTime())) return date;
							
							return null;
						} catch (error) {
							logger.error("Error parsing date:", { dateStr, error });
							return null;
						}
					};

					if (createdAt.includes(",")) {
						const [startString, endString] = createdAt.split(",");
						startDate = parseDate(startString);
						endDate = parseDate(endString);
					} else if (createdAt.includes(":")) {
						const [startString, endString] = createdAt.split(":");
						startDate = parseDate(startString);
						endDate = parseDate(endString);
					} else {
						const date = parseDate(createdAt);
						if (date) {
							startDate = new Date(date);
							startDate.setUTCHours(0, 0, 0, 0);
							endDate = new Date(date);
							endDate.setUTCHours(23, 59, 59, 999);
						}
					}

					if (startDate && endDate) {
						whereConditions.createdAt = {
							gte: startDate,
							lte: endDate,
						};
					} else {
						logger.warn("Invalid date range:", { createdAt });
					}
				} catch (error) {
					logger.error("Error parsing createdAt filter:", error);
				}
			}

			// For properties with address filters, use Prisma with post-query filtering
			let results: any[];
			let filteredTotal: number;

			if (objectType === "property" && Object.keys(addressFilters).length > 0) {
				logger.info("Using raw MongoDB queries for address filtering on properties:", addressFilters);
				
				try {
					// Step 1: Find PropertyLocation documents using raw MongoDB query with pagination
					const locationMatchPipeline = [
						{
							$match: {
								// Build address filters directly for JSON fields
								...Object.entries(addressFilters).reduce((acc, [field, value]) => {
									acc[`address.${field}`] = { $regex: value, $options: "i" };
									return acc;
								}, {} as any)
							}
						},
						{
							$project: {
								propertyId: 1
							}
						},
						{
							$skip: offset
						},
						{
							$limit: limit
						}
					];

					const locationMatches = await db.propertyLocation.aggregateRaw({
						pipeline: locationMatchPipeline
					});

					// Extract property IDs from raw MongoDB results and convert ObjectIds to strings
					const locationResults = locationMatches as unknown as any[];
					const matchingPropertyIds = locationResults.map(loc => {
						// Handle MongoDB ObjectId format: {$oid: "string"} or direct string
						if (loc.propertyId && typeof loc.propertyId === 'object' && loc.propertyId.$oid) {
							return loc.propertyId.$oid;
						}
						return loc.propertyId;
					}).filter(id => id); // Remove any null/undefined values
					logger.info(`Found ${matchingPropertyIds.length} property IDs for this page`);

					if (matchingPropertyIds.length === 0) {
						// No properties match the address filters for this page
						results = [];
					} else {
						// Step 2: Fetch only the properties for this page (much smaller ID list)
						const finalWhereConditions = {
							...whereConditions,
							id: {
								in: matchingPropertyIds
							}
						};

						results = await table.findMany({
							where: finalWhereConditions,
							include: config.include,
							orderBy: { createdAt: "desc" },
						});
					}

					// Step 3: Get total count separately (without trying to fetch all IDs)
					const countPipeline = [
						{
							$match: {
								// Same address filters as above
								...Object.entries(addressFilters).reduce((acc, [field, value]) => {
									acc[`address.${field}`] = { $regex: value, $options: "i" };
									return acc;
								}, {} as any)
							}
						},
						{
							$count: "totalMatches"
						}
					];

					const countResult = await db.propertyLocation.aggregateRaw({
						pipeline: countPipeline
					});

					const countResults = countResult as unknown as any[];
					filteredTotal = countResults.length > 0 ? countResults[0].totalMatches : 0;

					logger.info(`Raw MongoDB approach returned ${results.length} results with ${filteredTotal} total filtered count`);

				} catch (error) {
					logger.error("Raw MongoDB approach failed, falling back to standard query:", error);
					
					// Fallback to standard query without filtering
					const [fetchedResults, totalCount] = await Promise.all([
						table.findMany({
							where: whereConditions,
							include: config.include,
							orderBy: { createdAt: "desc" },
							skip: offset,
							take: limit,
						}),
						table.count({
							where: whereConditions,
						})
					]);

					results = fetchedResults;
					filteredTotal = totalCount;
				}
			} else {
				// Standard query without address filtering
				const baseQuery = {
					where: whereConditions,
					include: config.include,
					orderBy: { createdAt: "desc" },
				};

				// Execute both queries in parallel
				[results, filteredTotal] = await Promise.all([
					table.findMany({
						...baseQuery,
						skip: offset,
						take: limit,
					}),
					table.count({
						where: whereConditions,
					}),
				]);
			}

			// Get total count (unfiltered)
			const total = await table.count({
				where: {
					organizationId,
					isDeleted: false,
				},
			});

			// Transform the data
			let transformedData: any[] = results;
			if (config.transformResult) {
				transformedData = results.map((item: any) => {
					try {
						return config.transformResult(item);
					} catch (error) {
						logger.error(`Error transforming ${objectType}:`, error);
						return item;
					}
				});
			}

			// Calculate facets for filters
			const facets: Record<string, any> = {};

			// Generate tags facets for all object types that support tags
			const allTags = await db.tag.findMany({
				where: {
					organizationId,
					objectType,
				},
				select: {
					id: true,
					name: true,
				},
			});

			if (allTags.length > 0) {
				const tagUsageCounts = await db.objectTag.groupBy({
					by: ["tagId"],
					where: {
						objectType,
						tag: {
							organizationId,
						},
					},
					_count: { tagId: true },
				});

				const usageCountsMap = new Map(
					tagUsageCounts.map((item: any) => [item.tagId, item._count.tagId])
				);

				const facetRows = allTags.map((tag) => ({
					value: tag.name,
					total: usageCountsMap.get(tag.id) || 0,
				}));
				
				facets.tags = {
					rows: facetRows,
					total: tagUsageCounts.reduce(
						(sum: number, item: any) => sum + (item._count.tagId as number),
						0,
					),
				};
			}

			// Add object-specific facets
			if (objectType === "contact") {
				const statusCounts = await db.contact.groupBy({
					by: ["status"],
					where: { organizationId, isDeleted: false },
					_count: { status: true },
				});

				facets.status = {
					rows: statusCounts.map((item: any) => ({
						value: (item.status as string) || "Unknown",
						total: (item._count.status as number) || 0,
					})),
				};
			} else if (objectType === "property") {
				// Property type facet
				const propertyTypeFacet = await db.property.groupBy({
					by: ["propertyType"],
					where: {
						organizationId,
						isDeleted: false,
						propertyType: { not: null },
					},
					_count: { propertyType: true },
				});

				facets.propertyType = {
					rows: propertyTypeFacet.map((item: any) => ({
						value: (item.propertyType as string) || "Unknown",
						total: (item._count.propertyType as number) || 0,
					})),
					total: propertyTypeFacet.reduce(
						(sum: number, item: any) => sum + item._count.propertyType,
						0,
					),
				};

				// Status facet
				const statusFacet = await db.property.groupBy({
					by: ["status"],
					where: {
						organizationId,
						isDeleted: false,
						status: { not: null },
					},
					_count: { status: true },
				});

				facets.status = {
					rows: statusFacet.map((item: any) => ({
						value: (item.status as string) || "Unknown",
						total: (item._count.status as number) || 0,
					})),
					total: statusFacet.reduce(
						(sum: number, item: any) => sum + (item._count.status as number),
						0,
					),
				};
			}

			// Calculate pagination info
			const hasMore = offset + limit < filteredTotal;

			logger.info(`Paginated query for ${objectType} returned ${results.length} of ${filteredTotal} filtered results (${total} total)`);

			return c.json({
				data: transformedData,
				meta: {
					totalRowCount: total,
					filterRowCount: filteredTotal,
					facets,
				},
				pagination: {
					total,
					filteredTotal,
					limit,
					offset,
					hasMore,
				},
			});
		} catch (error) {
			logger.error("Failed to fetch paginated data:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

export type ObjectsPaginatedRouter = typeof objectsPaginatedRouter; 