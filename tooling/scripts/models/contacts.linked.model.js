import mongoose from 'mongoose';

const LinkedContactSchema = mongoose.Schema({
 masterRelation:String,
 childRelation:String,
 contact1:{type:mongoose.Schema.Types.ObjectId,ref:'ContactDetails'},
 contact2:{type:mongoose.Schema.Types.ObjectId,ref:'ContactDetails'},
},{timestamps:true})

const LinkedContactDetails = mongoose.model('LinkedContactDetails', LinkedContactSchema);

export default LinkedContactDetails;