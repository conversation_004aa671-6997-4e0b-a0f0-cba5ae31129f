import { PrismaClient } from '@prisma/client';
import mongoose from 'mongoose';
import LinkedContactDetails from '../models/contacts.linked.model.js';

const prisma = new PrismaClient();

interface OldLinkedContact {
  _id: string;
  masterRelation?: string;
  childRelation?: string;
  contact1?: string;
  contact2?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Cache for organizations and users to reduce database queries
const organizationCache = new Map<string, boolean>();
const userCache = new Map<string, boolean>();
const contactCache = new Map<string, boolean>();

async function validateReferences(linkedContact: OldLinkedContact): Promise<{ 
  validContact1: boolean; 
  validContact2: boolean;
  contact1OrgId?: string;
  contact2OrgId?: string;
}> {
  let validContact1 = false;
  let validContact2 = false;
  let contact1OrgId: string | undefined;
  let contact2OrgId: string | undefined;
  
  // Check if contact1 exists and get its organization
  if (linkedContact.contact1) {
    let contactExists = contactCache.get(linkedContact.contact1);
    if (contactExists === undefined) {
      const contact = await prisma.contact.findUnique({
        where: { id: linkedContact.contact1 },
        select: { id: true, organizationId: true }
      });
      contactExists = !!contact;
      contactCache.set(linkedContact.contact1, contactExists);
      if (contact) {
        contact1OrgId = contact.organizationId;
      }
    } else if (contactExists) {
      // Get org ID from another query if we haven't cached it
      const contact = await prisma.contact.findUnique({
        where: { id: linkedContact.contact1 },
        select: { organizationId: true }
      });
      if (contact) {
        contact1OrgId = contact.organizationId;
      }
    }
    validContact1 = contactExists;
  }
  
  // Check if contact2 exists and get its organization
  if (linkedContact.contact2) {
    let contactExists = contactCache.get(linkedContact.contact2);
    if (contactExists === undefined) {
      const contact = await prisma.contact.findUnique({
        where: { id: linkedContact.contact2 },
        select: { id: true, organizationId: true }
      });
      contactExists = !!contact;
      contactCache.set(linkedContact.contact2, contactExists);
      if (contact) {
        contact2OrgId = contact.organizationId;
      }
    } else if (contactExists) {
      // Get org ID from another query if we haven't cached it
      const contact = await prisma.contact.findUnique({
        where: { id: linkedContact.contact2 },
        select: { organizationId: true }
      });
      if (contact) {
        contact2OrgId = contact.organizationId;
      }
    }
    validContact2 = contactExists;
  }
  
  return { validContact1, validContact2, contact1OrgId, contact2OrgId };
}

async function migrateLinkedContactBatch(linkedContacts: OldLinkedContact[]): Promise<{ success: number; skipped: number; failed: number }> {
  let success = 0;
  let skipped = 0;
  let failed = 0;
  
  // Fallback user ID (all zeros ObjectId)
  const fallbackUserId = '000000000000000000000000';
  
  for (const linkedContact of linkedContacts) {
    try {
      // Skip if linked contact already exists
      const existingLinkedContact = await prisma.linkedContact.findUnique({
        where: { id: linkedContact._id }
      });
      
      if (existingLinkedContact) {
        skipped++;
        continue;
      }
      
      // Validate that both contacts exist
      const { validContact1, validContact2, contact1OrgId, contact2OrgId } = await validateReferences(linkedContact);
      
      if (!validContact1 || !validContact2 || !linkedContact.contact1 || !linkedContact.contact2) {
        skipped++;
        continue;
      }
      
      // Ensure both contacts are in the same organization
      if (contact1OrgId !== contact2OrgId || !contact1OrgId) {
        skipped++;
        continue;
      }
      
      // Create the linked contact relationship
      await prisma.linkedContact.create({
        data: {
          id: linkedContact._id, // Preserve original ID
          contact1Id: linkedContact.contact1,
          contact1Relation: linkedContact.masterRelation || 'Related',
          contact2Id: linkedContact.contact2,
          contact2Relation: linkedContact.childRelation || 'Related',
          organizationId: contact1OrgId,
          createdBy: fallbackUserId, // Use fallback since original doesn't have this
          createdAt: linkedContact.createdAt || new Date(),
          updatedAt: linkedContact.updatedAt || new Date()
        }
      });
      
      success++;
      
    } catch (error) {
      console.error(`❌ Error migrating linked contact ${linkedContact._id}:`, error);
      failed++;
    }
  }
  
  return { success, skipped, failed };
}

function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

async function fullMigration() {
  const startTime = Date.now();
  
  try {
    console.log('🚀 Starting full linked contacts migration...');
    
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://pbdb:<EMAIL>/propbear?retryWrites=true&w=majority");
    console.log('✅ Connected to MongoDB');
    
    // Get total count of linked contacts to migrate
    const totalLinkedContacts = await LinkedContactDetails.countDocuments({});
    
    console.log(`📊 Found ${totalLinkedContacts.toLocaleString()} linked contacts to migrate`);
    
    if (totalLinkedContacts === 0) {
      console.log('No linked contacts found to migrate.');
      return;
    }
    
    // Batch processing configuration
    const BATCH_SIZE = 50; // Smaller batch size due to relationship validation
    const MEMORY_BATCH_SIZE = 500;
    
    let totalSuccess = 0;
    let totalSkipped = 0;
    let totalFailed = 0;
    let processed = 0;
    
    console.log(`🚀 Starting migration with batch size: ${BATCH_SIZE}`);
    
    // Process in batches
    let skip = 0;
    while (skip < totalLinkedContacts) {
      const memoryBatch = await LinkedContactDetails.find({})
        .skip(skip)
        .limit(MEMORY_BATCH_SIZE)
        .lean();
      
      // Process memory batch in smaller processing batches
      for (let i = 0; i < memoryBatch.length; i += BATCH_SIZE) {
        const processingBatch = memoryBatch.slice(i, i + BATCH_SIZE) as OldLinkedContact[];
        
        const batchStart = Date.now();
        const { success, skipped, failed } = await migrateLinkedContactBatch(processingBatch);
        const batchTime = (Date.now() - batchStart) / 1000;
        
        totalSuccess += success;
        totalSkipped += skipped;
        totalFailed += failed;
        processed += processingBatch.length;
        
        // Calculate progress and ETA
        const progress = (processed / totalLinkedContacts) * 100;
        const elapsedTime = (Date.now() - startTime) / 1000;
        const estimatedTotal = (elapsedTime / processed) * totalLinkedContacts;
        const eta = estimatedTotal - elapsedTime;
        const rate = processed / elapsedTime;
        
        console.log(`📊 Progress: ${progress.toFixed(1)}% (${processed.toLocaleString()}/${totalLinkedContacts.toLocaleString()}) | ` +
          `✅ ${totalSuccess.toLocaleString()} | ` +
          `⏭️ ${totalSkipped.toLocaleString()} | ` +
          `❌ ${totalFailed.toLocaleString()} | ` +
          `Rate: ${rate.toFixed(1)}/s | ` +
          `ETA: ${formatTime(eta)}`);
        
        // Clear caches periodically to prevent memory issues
        if (processed % 1000 === 0 && processed > 0) {
          contactCache.clear();
          console.log('🧹 Cleared contact cache to free memory');
        }
      }
      
      skip += MEMORY_BATCH_SIZE;
      
      // Small delay to prevent overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const totalTime = (Date.now() - startTime) / 1000;
    const finalCount = await prisma.linkedContact.count();
    
    console.log(`\n🎉 Full migration completed!`);
    console.log(`⏱️ Total time: ${formatTime(totalTime)}`);
    console.log(`✅ Successfully migrated: ${totalSuccess.toLocaleString()}`);
    console.log(`⏭️ Skipped (missing contacts/mismatched orgs/duplicates): ${totalSkipped.toLocaleString()}`);
    console.log(`❌ Failed: ${totalFailed.toLocaleString()}`);
    console.log(`📊 Total linked contacts in database: ${finalCount.toLocaleString()}`);
    console.log(`🚀 Average rate: ${(processed / totalTime).toFixed(1)} linked contacts/second`);
    
    // Show some statistics
    const relationshipTypes = await prisma.linkedContact.groupBy({
      by: ['contact1Relation'],
      _count: { contact1Relation: true }
    });
    
    console.log(`\n📈 Relationship type breakdown:`);
    relationshipTypes.forEach(({ contact1Relation, _count }) => {
      console.log(`  ${contact1Relation}: ${_count.contact1Relation.toLocaleString()}`);
    });
    
  } catch (error) {
    console.error('💥 Full migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    await prisma.$disconnect();
    console.log('🔌 Disconnected from databases');
  }
}

fullMigration(); 