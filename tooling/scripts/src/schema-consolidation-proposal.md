# Object Relationship Consolidation Proposal

## Current Problem
Currently we have separate models for different types of relationships:
- `LinkedContact` - for contact-to-contact relationships
- `LinkedProperty` - for contact-to-property relationships

This approach doesn't scale well and requires creating new models for each new relationship type (company-to-company, property-to-property, etc.).

## Proposed Solution: ObjectRelationship Model

Replace both `LinkedContact` and `LinkedProperty` with a single dynamic `ObjectRelationship` model:

```prisma
model ObjectRelationship {
  id             String   @id @map("_id") @db.ObjectId
  
  // Object 1 (the "source" of the relationship)
  object1Id      String   @db.ObjectId
  object1Type    String   // 'contact', 'company', 'property', 'custom_object', etc.
  object1Relation String  // The relationship from object1's perspective
  
  // Object 2 (the "target" of the relationship)
  object2Id      String   @db.ObjectId
  object2Type    String   // 'contact', 'company', 'property', 'custom_object', etc.
  object2Relation String  // The relationship from object2's perspective
  
  // Optional metadata
  strength       Float?   // Relationship strength (0.0-1.0) for ML/analytics
  isActive       Boolean  @default(true) // Can deactivate without deleting
  notes          String?  // Additional context about the relationship
  
  // Organization scoping
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("ObjectRelationshipCreator", fields: [createdBy], references: [id])
  updatedBy      String?  @db.ObjectId
  updater        User?    @relation("ObjectRelationshipUpdater", fields: [updatedBy], references: [id])
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([object1Id, object1Type])
  @@index([object2Id, object2Type])
  @@index([organizationId])
  @@index([organizationId, object1Type, object2Type])
  @@index([organizationId, object1Type])
  @@index([organizationId, object2Type])
  @@index([organizationId, isActive])
  @@unique([object1Id, object1Type, object2Id, object2Type, organizationId]) // Prevent duplicate links
  @@map("object_relationship")
}
```

## Migration Strategy

### 1. Add ObjectRelationship model to schema
First, add the new model alongside existing ones.

### 2. Migrate existing data
Create migration scripts to copy data from existing models:

**LinkedContact → ObjectRelationship:**
```typescript
// For each LinkedContact record:
{
  object1Id: linkedContact.contact1Id,
  object1Type: 'contact',
  object1Relation: linkedContact.contact1Relation,
  object2Id: linkedContact.contact2Id,
  object2Type: 'contact', 
  object2Relation: linkedContact.contact2Relation,
  organizationId: linkedContact.organizationId,
  // ... other fields
}
```

**LinkedProperty → ObjectRelationship:**
```typescript
// For each LinkedProperty record:
{
  object1Id: linkedProperty.contactId,
  object1Type: 'contact',
  object1Relation: linkedProperty.relation, // e.g., "owns"
  object2Id: linkedProperty.propertyId,
  object2Type: 'property',
  object2Relation: getInverseRelation(linkedProperty.relation), // e.g., "owned_by"
  organizationId: linkedProperty.organizationId,
  // ... other fields
}
```

### 3. Update User Relations
Add these relations to the User model:
```prisma
model User {
  // ... existing fields
  createdObjectRelationships ObjectRelationship[] @relation("ObjectRelationshipCreator")
  updatedObjectRelationships ObjectRelationship[] @relation("ObjectRelationshipUpdater")
}
```

### 4. Update Organization Relations
Add this relation to the Organization model:
```prisma
model Organization {
  // ... existing fields
  objectRelationships ObjectRelationship[]
}
```

## Benefits

### 1. Dynamic Flexibility
- Can handle any object-to-object relationship without schema changes
- Support for future object types (tasks, notes, files, etc.)
- No need to create new models for each relationship type

### 2. Consistent API
- Single API endpoint for all relationship types
- Unified querying and filtering
- Consistent audit trails

### 3. Advanced Features
- Relationship strength for analytics
- Bi-directional relationships with different perspectives
- Easy to add metadata (notes, tags, etc.)

### 4. Better Analytics
- Can analyze relationship patterns across all object types
- Network analysis becomes possible
- ML features can leverage relationship data

## API Examples

### Creating Relationships
```typescript
// Contact-to-Contact relationship (spouse)
await createObjectRelationship({
  object1Id: "contact1_id",
  object1Type: "contact",
  object1Relation: "spouse",
  object2Id: "contact2_id", 
  object2Type: "contact",
  object2Relation: "spouse"
})

// Contact-to-Property relationship
await createObjectRelationship({
  object1Id: "contact_id",
  object1Type: "contact", 
  object1Relation: "owns",
  object2Id: "property_id",
  object2Type: "property",
  object2Relation: "owned_by"
})

// Company-to-Company relationship
await createObjectRelationship({
  object1Id: "company1_id",
  object1Type: "company",
  object1Relation: "subsidiary_of", 
  object2Id: "company2_id",
  object2Type: "company",
  object2Relation: "parent_of"
})
```

### Querying Relationships
```typescript
// Get all relationships for a contact
const contactRelationships = await prisma.objectRelationship.findMany({
  where: {
    OR: [
      { object1Id: contactId, object1Type: "contact" },
      { object2Id: contactId, object2Type: "contact" }
    ],
    isActive: true
  }
})

// Get all properties owned by contacts
const ownedProperties = await prisma.objectRelationship.findMany({
  where: {
    object1Type: "contact",
    object2Type: "property", 
    object1Relation: "owns",
    isActive: true
  }
})
```

## Implementation Steps

1. ✅ **Create migration script for LinkedContact** (completed)
2. 🔄 **Add ObjectRelationship model to schema**
3. 🔄 **Create migration script: LinkedContact → ObjectRelationship** 
4. 🔄 **Create migration script: LinkedProperty → ObjectRelationship**
5. 🔄 **Update frontend components to use new model**
6. 🔄 **Update API endpoints**
7. 🔄 **Remove old models after verification**

## Backward Compatibility

During migration, we can maintain backward compatibility by:
1. Keeping old models temporarily
2. Creating views/resolvers that map to new structure
3. Gradual migration of frontend components
4. Remove old models only after full verification 